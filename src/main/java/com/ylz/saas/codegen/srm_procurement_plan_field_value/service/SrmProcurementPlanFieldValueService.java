package com.ylz.saas.codegen.srm_procurement_plan_field_value.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.srm_procurement_plan_field_value.entity.SrmProcurementPlanFieldValueEntity;
import com.ylz.saas.codegen.srm_procurement_plan_field_value.vo.SrmProcurementPlanFieldValueVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface SrmProcurementPlanFieldValueService extends IService<SrmProcurementPlanFieldValueEntity> {
    /**
     * 新增采购计划字段值表
     *
     * @param srmProcurementPlanFieldValueVo 采购计划字段值表
     */
    boolean saveSrmProcurementPlanFieldValue(SrmProcurementPlanFieldValueVo srmProcurementPlanFieldValueVo);

    /**
     * 修改采购计划字段值表
     *
     * @param srmProcurementPlanFieldValueVo 采购计划字段值表
     */
    boolean updateSrmProcurementPlanFieldValue(SrmProcurementPlanFieldValueVo srmProcurementPlanFieldValueVo);

    /**
     * 导入模板下载
     *
     * @param response
     */
    void template(HttpServletResponse response);

    /**
     * 数据导入
     *
     * @param file
     * @param response
     */
    void uploadData(MultipartFile file, HttpServletResponse response);

    /**
     * 物理删除采购字段
     *
     * @param planDetailIds
     * @param tenantId
     */
    void physicsRemoveByPlanDetailIds(List<Long> planDetailIds, Long tenantId);
}