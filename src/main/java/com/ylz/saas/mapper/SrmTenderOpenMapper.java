package com.ylz.saas.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylz.saas.entity.SrmTenderOpen;
import com.ylz.saas.resp.SrmOpenTenderMaterialResp;
import com.ylz.saas.resp.SrmOpenTenderSupplierResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tender_open(开标表)】的数据库操作Mapper
* @createDate 2025-06-18 11:29:38
* @Entity com.ylz.saas.entity.SrmTenderOpen
*/
@Mapper
//@DataPermission(userEnabled = true, dataPermissionRuleList = "[{\"dataTypeCode\":\"PROJECT\",\"targetField\":\"project_id\",\"dataSourceField\":\"ID\"}]")
public interface SrmTenderOpenMapper extends BaseMapper<SrmTenderOpen> {

    List<SrmOpenTenderMaterialResp> selectOpenTenderMaterials(@Param("tenantSupplierId") Long tenantSupplierId,
                                                              @Param("projectId") Long projectId
            , @Param("noticeId") Long noticeId, @Param("sectionId") Long sectionId
            , @Param("roundNo") Integer roundNo, @Param("materialName") String materialName);

    List<SrmOpenTenderSupplierResp> selectOpenTenderSuppliers(@Param("tenantSupplierId") Long tenantSupplierId,
                                                              @Param("projectId") Long projectId, @Param("noticeId") Long noticeId
            , @Param("sectionId") Long sectionId, @Param("roundNo") Integer roundNo, @Param("supplierName") String supplierName);
}




