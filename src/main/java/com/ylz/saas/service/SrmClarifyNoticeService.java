package com.ylz.saas.service;

import com.ylz.saas.entity.SrmClarifyNotice;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.req.ClarifyNoticeAddReq;
import com.ylz.saas.req.ClarifyNoticeReq;
import com.ylz.saas.resp.SrmClarifyNoticeResp;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_clarify_notice(澄清公告)】的数据库操作Service
* @createDate 2025-07-10 14:53:22
*/
public interface SrmClarifyNoticeService extends IService<SrmClarifyNotice> {

    /**
     * 查询澄清公告
     * @param clarifyNoticeReq
     * @return
     */
    List<SrmClarifyNoticeResp> queryClarifyNotice(ClarifyNoticeReq clarifyNoticeReq);

    /**
     * 查询澄清公告详情
     *
     * @param id
     * @return
     */
    SrmClarifyNoticeResp queryClarifyDetail(Long id);

    /**
     * 添加澄清公告
     *
     * @param addReq
     */
    void clarifyNoticeUpsert(ClarifyNoticeAddReq addReq);
}
