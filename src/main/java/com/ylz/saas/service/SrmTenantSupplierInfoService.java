package com.ylz.saas.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.entity.SrmTenantSupplierInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.vo.SrmTenantSupplierInfoVo;
import com.ylz.saas.vo.SupplierBizFlowUpdateVo;
import com.ylz.saas.vo.SupplierCompleteInfoVo;
import com.ylz.saas.vo.SupplierInviteRequestVo;
import com.ylz.saas.vo.SupplierQueryVo;

/**
* <AUTHOR>
* @description 针对表【srm_tenant_supplier_info(租户供应商信息表)】的数据库操作Service
* @createDate 2025-06-10 18:08:09
*/
public interface SrmTenantSupplierInfoService extends IService<SrmTenantSupplierInfo> {

    /**
     * 启用/禁用供应商状态
     *
     * @param id 供应商ID
     * @return 操作结果
     */
    Object switchStatus(Long id);

    /**
     * 分页查询我的租户供应商信息
     * 根据当前用户的部门权限筛选供应商列表
     *
     * @param queryVo 查询条件，包含供应商名称、类型、企业性质、启用状态等
     * @param pageParams 分页参数
     * @return 分页供应商信息列表
     */
    IPage<SrmTenantSupplierInfoVo> getMyTenantSupplierPage(SupplierQueryVo queryVo, Page<SrmTenantSupplierInfo> pageParams);

    /**
     * 分页查询注册租户供应商信息
     * 查询当前租户下所有状态的供应商信息
     *
     * @param queryVo 查询条件，包含供应商名称、类型、企业性质、启用状态等
     * @param pageParams 分页参数
     * @return 分页供应商信息列表
     */
    IPage<SrmTenantSupplierInfoVo> getRegisterTenantSupplierPage(SupplierQueryVo queryVo, Page<SrmTenantSupplierInfo> pageParams);

    /**
     * 邀请供应商建立部门关联关系
     * 主动与tenantSupplierPage接口展示的供应商建立部门关联关系
     *
     * @param supplierInviteRequestVo 邀请供应商请求VO
     * @return 是否邀请成功
     */
    boolean inviteSupplier(SupplierCompleteInfoVo supplierInviteRequestVo);

    /**
     * 分页查询租户供应商信息（用于邀请功能）
     * 查询已审核通过的供应商，并标识当前用户部门的邀请状态
     *
     * @param queryVo 查询条件，包含供应商名称、类型、企业性质、启用状态等
     * @param pageParams 分页参数
     * @return 分页供应商信息列表（含邀请状态）
     */
    IPage<SrmTenantSupplierInfoVo> getTenantSupplierPageWithInviteStatus(SupplierQueryVo queryVo, Page<SrmTenantSupplierInfo> pageParams);

    /**
     * 业务审批流执行后的更新方法
     *
     * @param supplierBizFlowUpdateVo 供应商业务审批流执行后的更新实体
     * @return 是否更新成功
     */
    boolean bizFlowUpdate(SupplierBizFlowUpdateVo supplierBizFlowUpdateVo);


    boolean supplierAuditPass(String supplierCode,Long supplierDeptId);

    boolean supplierAuditUnpass(String supplierCode,Long supplierDeptId);

}
