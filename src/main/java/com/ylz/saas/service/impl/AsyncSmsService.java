package com.ylz.saas.service.impl;

import com.ylz.saas.admin.api.dto.MessageSmsDTO;
import com.ylz.saas.admin.api.feign.RemoteMessageService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class AsyncSmsService {

    @Resource
    private RemoteMessageService remoteMessageService;

    @Async
    public void sendSmsAsync(MessageSmsDTO messageSmsDTO) {
        try {
            remoteMessageService.sendSms(messageSmsDTO);
            log.info("短信发送成功，手机号: {}", messageSmsDTO.getMobiles());
        } catch (Exception e) {
            log.error("短信发送失败，手机号: {}", messageSmsDTO.getMobiles(), e);
        }
    }
}
