package com.ylz.saas.controller;

import com.ylz.saas.common.core.util.R;
import com.ylz.saas.req.SrmEnsilageAddReq;
import com.ylz.saas.service.SrmTenderEnsilageService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 青贮创建
 */
@RestController
@RequestMapping("/ensilage")
public class SrmTenderEnsilageController {

    @Resource
    private SrmTenderEnsilageService ensilageService;


    /**
     * 新增青贮信息
     *
     * @param req 筛选条件
     * @return 查询结果
     */
    @PostMapping("/upsert")
    public R<Void> upsertEnsilage(@RequestBody @Validated SrmEnsilageAddReq req) {
        ensilageService.upsertEnsilage(req);
        return R.ok();
    }

    /**
     * 查询青贮信息
     *
     * @param noticeId 筛选条件
     * @return 查询结果
     */
    @PostMapping("/detail")
    public R<SrmEnsilageAddReq> detailEnsilage(@RequestParam("noticeId") Long noticeId) {
        SrmEnsilageAddReq result = ensilageService.detailEnsilage(noticeId);
        return R.ok(result);
    }

}

