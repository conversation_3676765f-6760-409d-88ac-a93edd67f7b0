package com.ylz.saas.codegen.base_material_info.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.admin.api.entity.SysDept;
import com.ylz.saas.codegen.base_material_info.entity.BaseMaterialInfoEntity;
import com.ylz.saas.codegen.base_material_info.excel.BaseMaterialInfoResp;
import com.ylz.saas.codegen.base_material_info.param.BaseMaterialInfoParam;
import com.ylz.saas.codegen.base_material_info.service.BaseMaterialInfoService;
import com.ylz.saas.codegen.base_material_info.vo.BaseMaterialInfoVo;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.common.excel.annotation.ResponseExcel;
import com.ylz.saas.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * 物料基础信息表
 *
 * <AUTHOR>
 * @date 2025-06-03 18:00:37
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/baseMaterialInfo" )
@Tag(description = "baseMaterialInfo" , name = "物料基础信息表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class BaseMaterialInfoController {

    private final BaseMaterialInfoService baseMaterialInfoService;
    /**
     * 启用/禁用
     * @return R
     */
    @Operation(summary = "启用/禁用" , description = "启用/禁用" )
    @PostMapping("/switch/{id}")
    public R switchStatus(@PathVariable("id") Long id) {
        return R.ok(baseMaterialInfoService.switchStatus(id));
    }
    /**
     * 分页查询（支持拼音搜索）
     * @param page 分页对象
     * @param param 物料基础信息查询参数
     * @return
     */
    @Operation(summary = "分页查询（支持拼音搜索）" , description = "分页查询，物料名称支持中文、全拼音、拼音首字母模糊搜索" )
    @PostMapping("/page" )
    public R getBaseMaterialInfoPage(@RequestBody BaseMaterialInfoParam param, Page page) {
        return R.ok(baseMaterialInfoService.pageWithPinyinSearch(page, param));
    }


    /**
     * 通过id查询物料基础信息表（包含组织名称）
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询（包含组织名称）" , description = "通过id查询，包含组织名称信息" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(baseMaterialInfoService.getByIdWithDeptName(id));
    }

    /**
     * 新增物料基础信息表
     * @param baseMaterialInfoVo 物料基础信息表
     * @return R
     */
    @Operation(summary = "新增物料基础信息表" , description = "新增物料基础信息表" )
    @SysLog("新增物料基础信息表" )
    @PostMapping("/save")
    public R save(@Valid @RequestBody BaseMaterialInfoVo baseMaterialInfoVo) {
        return R.ok(baseMaterialInfoService.saveBaseMaterialInfo(baseMaterialInfoVo));
    }

    /**
     * 修改物料基础信息表
     * @param baseMaterialInfoVo 物料基础信息表
     * @return R
     */
    @Operation(summary = "修改物料基础信息表" , description = "修改物料基础信息表" )
    @SysLog("修改物料基础信息表" )
    @PostMapping("/update")
    public R updateBy( @RequestBody BaseMaterialInfoVo baseMaterialInfoVo) {
        return R.ok(baseMaterialInfoService.updateBaseMaterialInfo(baseMaterialInfoVo));
    }

    /**
     * 通过id删除物料基础信息表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除物料基础信息表" , description = "通过id删除物料基础信息表" )
    @SysLog("通过id删除物料基础信息表" )
    @GetMapping("remove")
    public R removeById(@RequestParam List<Long> ids) {
		boolean remove = baseMaterialInfoService.newRemoveByIds(ids);
		return R.ok(remove);
    }


    /**
     * 导出excel 表格（包含组织名称）
     * @param param 查询条件
     * @return excel 文件流
     */
    @ResponseExcel
    @PostMapping("/export")
	@Operation(summary = "导出excel（包含组织名称）" , description = "导出excel，包含组织名称信息" )
    public List<BaseMaterialInfoResp> export(@RequestBody BaseMaterialInfoParam param) {
		List<BaseMaterialInfoEntity> entityList = baseMaterialInfoService.listWithDeptName(param);
        return BeanUtil.copyToList(entityList, BaseMaterialInfoResp.class);
    }
	
	/**
     * 导入excel 表格
     * @param file
     * @param response
     * @return
     */
    @PostMapping("/import")
    @Operation(summary = "导入excel" , description = "导入excel" )
    public R uploadData(@RequestParam("file") MultipartFile file,HttpServletResponse response) {
        baseMaterialInfoService.uploadData(file,response);
        return R.ok();
    }

    /**
     * excel模板下载
     * @param response
     */
    @GetMapping("/template")
    @Operation(summary = "excel模板下载" , description = "excel模板下载" )
    public void template(HttpServletResponse response) {
        baseMaterialInfoService.template(response);
    }
}