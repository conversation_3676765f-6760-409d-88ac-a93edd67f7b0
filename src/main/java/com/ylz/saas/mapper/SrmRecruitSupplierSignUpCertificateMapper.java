package com.ylz.saas.mapper;

import com.ylz.saas.entity.SrmRecruitSupplierSignUpCertificate;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 供应商招募报名表Mapper
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Mapper
public interface SrmRecruitSupplierSignUpCertificateMapper extends SaasBaseMapper<SrmRecruitSupplierSignUpCertificate> {

    List<SrmRecruitSupplierSignUpCertificate> getBySignUpId(@Param("signUpId")Long signUpId);
}
