package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ylz.saas.common.core.util.TenantTable;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.ReviewEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 评标专家打分
 * @TableName srm_tender_bid_evaluation_scoring
 */
@TableName(value = "srm_tender_bid_evaluation_scoring")
@Data
@TenantTable
@Schema(description = "评标专家打分")
public class SrmTenderBidEvaluationScoring implements Serializable {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "部门ID")
    private Long deptId;

    /**
     * 采购立项ID
     */
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 标段ID
     */
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 评标委员会ID
     */
    @Schema(description = "评标委员会ID")
    private Long evaluationId;

    /**
     * 专家用户id
     */
    @Schema(description = "专家用户id")
    private Long userId;

    /**
     * 评分详情id
     */
    @Schema(description = "评分详情id")
    private Long scoringDetailId;

    /**
     * 投标响应表ID
     */
    @Schema(description = "投标响应表ID")
    private Long responseId;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id")
    private Long tenantSupplierId;

    /**
     * 分值（评分项）
     */
    @Schema(description = "分值（评分项）")
    private Integer score;

    /**
     * 状态：待提交 已提交（评审完成） 已审核（已汇总）
     */
    @Schema(description = "状态：待提交 已提交（评审完成） 已审核（已汇总）")
    private SubmitStatus status;

    /**
     * 是否符合（评审项）
     */
    @Schema(description = "是否符合（评审项）")
    private Integer isConform;

    /**
     * 类型（评审项、评分项）
     */
    @Schema(description = "类型（评审项、评分项）")
    private ReviewEnum type;

    /**
     * 评分类型（专家评分、组长汇总）
     */
    @Schema(description = "评分类型（专家评分、组长汇总）")
    private ScoringType scoringType;
    /**
     * 当前报价轮次
     */
    @Schema(description = "当前报价轮次")
    private Integer currentRound;
    /**
     * 评审结论
     */
    @Schema(description = "评审结论")
    private String conclusion;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "删除标识（0-正常、1-删除）")
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人名称")
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人名称")
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;


    @Getter
    @AllArgsConstructor
    public enum SubmitStatus{

        WAIT_SUBMIT("待评审"),
        SUBMITTED("已评审"),
        SUMMARIZED("已总结");

        private  final String desc;

    }

    @Getter
    @AllArgsConstructor
    public enum ScoringType{

        EXPERT_SCORING("专家评分"),
        LEADER_SUMMARY("组长汇总");

        private final String desc;

    }

    private static final long serialVersionUID = 1L;
}
