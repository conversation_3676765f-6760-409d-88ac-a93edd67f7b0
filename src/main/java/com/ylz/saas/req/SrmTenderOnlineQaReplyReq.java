package com.ylz.saas.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 在线问答表
 * @TableName srm_tender_online_qa
 */
@Data
public class SrmTenderOnlineQaReplyReq {
    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空")
    private Long id;

    /**
     * 回复内容
     */
    @NotBlank(message = "回复内容不能为空")
    private String answerContent;

}