package com.ylz.saas.mapper;

import com.ylz.saas.common.data.datascope.annotation.DataPermission;
import com.ylz.saas.entity.SrmTenderBidderQuoteItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylz.saas.req.QuoteLessThanThresholdSupplierQueryReq;
import com.ylz.saas.vo.QuoteRoundCountVo;
import com.ylz.saas.vo.QuoteStatisticsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【srm_tender_bidder_quote_item(投标报价明细表)】的数据库操作Mapper
 * @createDate 2025-06-13 14:57:59
 * @Entity com.ylz.saas.entity.SrmTenderBidderQuoteItem
 */
@Mapper
//@DataPermission(userEnabled = true, dataPermissionRuleList = "[{\"dataTypeCode\":\"PROJECT\",\"targetField\":\"project_id\",\"dataSourceField\":\"ID\"}]")
public interface SrmTenderBidderQuoteItemMapper extends BaseMapper<SrmTenderBidderQuoteItem> {

    List<QuoteRoundCountVo> queryQuoteRoundCount(@Param("sectionId") Long sectionId,
                                                 @Param("tenantSupplierIds") List<Long> tenantSupplierIds);


    List<QuoteStatisticsVo> queryQuoteStatistics(@Param("sectionId") Long sectionId,
                                                 @Param("roundNo") Integer roundNo,
                                                 @Param("tenantSupplierIds") List<Long> tenantSupplierIds);

    /**
     * 根据noticeId和projectId查询报价轮次统计
     */
    List<QuoteRoundCountVo> queryQuoteRoundCountByNoticeAndProject(@Param("projectId") Long projectId,
                                                                   @Param("noticeId") Long noticeId,
                                                                   @Param("sectionId") Long sectionId);

    /**
     * 根据noticeId和projectId查询报价统计
     */
    List<QuoteStatisticsVo> queryQuoteStatisticsByNoticeAndProject(@Param("projectId") Long projectId,
                                                                   @Param("noticeId") Long noticeId,
                                                                   @Param("sectionId") Long sectionId,
                                                                   @Param("roundNo") Integer roundNo,
                                                                   @Param("tenantSupplierIds") List<Long> tenantSupplierIds);

    /**
     * 查询报价项目列表（联查供应商名称）
     *
     * @param sectionId 标段ID
     * @param roundNo 轮次（可选）
     * @param tenantSupplierId 租户供应商ID（可选）
     * @param projectItemIds 项目物料ID列表
     * @return 报价项目列表（包含供应商名称）
     */
    List<SrmTenderBidderQuoteItem> queryQuoteItemsWithSupplierName(@Param("sectionId") Long sectionId,
                                                                   @Param("roundNo") Integer roundNo,
                                                                   @Param("tenantSupplierId") Long tenantSupplierId,
                                                                   @Param("projectItemIds") List<Long> projectItemIds);

    /**
     * 根据noticeId和projectId查询报价项目列表（联查供应商名称）
     *
     * @param noticeId 招标公告ID
     * @param projectId 采购立项ID
     * @param roundNo 轮次（可选）
     * @param tenantSupplierId 租户供应商ID（可选）
     * @param projectItemIds 项目物料ID列表
     * @return 报价项目列表（包含供应商名称）
     */
    List<SrmTenderBidderQuoteItem> queryQuoteItemsByNoticeAndProject(@Param("noticeId") Long noticeId,
                                                                     @Param("projectId") Long projectId,
                                                                     @Param("sectionId") Long sectionId,
                                                                     @Param("roundNo") Integer roundNo,
                                                                     @Param("awarded") Boolean awarded,
                                                                     @Param("tenantSupplierId") Long tenantSupplierId,
                                                                     @Param("projectItemIds") List<Long> projectItemIds);

    /**
     * 根据noticeId和projectId查询中标明细列表（联查供应商名称）
     *
     * @param noticeId 招标公告ID
     * @param projectId 采购立项ID
     * @param materialCode 物料编码（可选）
     * @param materialName 物料名称（可选）
     * @return 中标明细列表（包含供应商名称）
     */
    List<SrmTenderBidderQuoteItem> queryAwardedItemsWithSupplierName(@Param("noticeId") Long noticeId,
                                                                     @Param("projectId") Long projectId,
                                                                     @Param("materialCode") String materialCode,
                                                                     @Param("materialName") String materialName,
                                                                     @Param("tenantSupplierId") Long tenantSupplierId,
                                                                     @Param("sectionId") Long sectionId
                                                                     );

    /**
     * 根据noticeId和projectId查询中标供应商列表
     *
     * @param noticeId 招标公告ID
     * @param projectId 采购立项ID
     * @param supplierName 供应商名称（可选）
     * @return 中标供应商列表
     */
    List<com.ylz.saas.resp.AwardedSupplierResp> queryAwardedSuppliers(@Param("noticeId") Long noticeId,
                                                                      @Param("projectId") Long projectId,
                                                                      @Param("supplierName") String supplierName);

    List<String> getQuoteLessThanThresholdSupplierList(@Param("params") QuoteLessThanThresholdSupplierQueryReq req);

    /**
     * 查询报价明细（供应商）- 按轮次分组
     *
     * @param projectId 采购立项ID
     * @param sectionId 标段ID
     * @param noticeId 招标公告ID
     * @param supplierName 供应商名称（可选）
     * @return 按轮次分组的供应商报价信息
     */
    List<com.ylz.saas.resp.SrmTenderQuoteDetailBySupplierRoundResp.SupplierQuoteInfo> queryQuoteDetailBySupplier(
            @Param("projectId") Long projectId,
            @Param("sectionId") Long sectionId,
            @Param("noticeId") Long noticeId,
            @Param("supplierName") String supplierName);

    /**
     * 查询报价明细（物料信息）- 按轮次分组
     *
     * @param projectId 采购立项ID
     * @param sectionId 标段ID
     * @param noticeId 招标公告ID
     * @param materialName 物料名称（可选）
     * @param materialCode 物料编码（可选）
     * @return 按轮次分组的物料报价信息
     */
    List<com.ylz.saas.resp.SrmTenderQuoteDetailByMaterialRoundResp.MaterialQuoteInfo> queryQuoteDetailByMaterial(
            @Param("projectId") Long projectId,
            @Param("sectionId") Long sectionId,
            @Param("noticeId") Long noticeId,
            @Param("materialName") String materialName,
            @Param("materialCode") String materialCode);

    /**
     * 查询报价明细（物料信息）- 按轮次分组，限制供应商范围
     *
     * @param projectId 采购立项ID
     * @param sectionId 标段ID
     * @param noticeId 招标公告ID
     * @param materialName 物料名称（可选）
     * @param materialCode 物料编码（可选）
     * @param validSupplierIds 有效的供应商ID列表
     * @return 按轮次分组的物料报价信息
     */
    List<com.ylz.saas.resp.SrmTenderQuoteDetailByMaterialRoundResp.MaterialQuoteInfo> queryQuoteDetailByMaterialWithSuppliers(
            @Param("projectId") Long projectId,
            @Param("sectionId") Long sectionId,
            @Param("noticeId") Long noticeId,
            @Param("materialName") String materialName,
            @Param("materialCode") String materialCode,
            @Param("validSupplierIds") List<Long> validSupplierIds);

}




