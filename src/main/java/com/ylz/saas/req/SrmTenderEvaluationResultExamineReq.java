package com.ylz.saas.req;

import com.ylz.saas.enums.ApproveStatusEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 定标审核
 */
@Data
public class SrmTenderEvaluationResultExamineReq {
    /**
     * 公告id
     */
    @NotNull(message = "公告id不能为空")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 标段ID
     */
    private List<Long> sectionIdList;

    /**
     * 定标报告状态
     * TO_APPROVE("待审批"),
     *     APPROVING("审批中"),
     *     APPROVE("审批通过"),
     *     APPROVE_REJECT("审批驳回"),
     *     APPROVE_REVOKE("审批撤销"),
     */
    @NotNull(message = "定标报告状态不能为空")
    private ApproveStatusEnum awardReportStatus;

    /**
     * 定标审批原因
     */
    private String remark;


}