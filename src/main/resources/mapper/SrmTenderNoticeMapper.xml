<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenderNoticeMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenderNotice">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="projectId" column="project_id" jdbcType="BIGINT"/>
            <result property="noticeTitle" column="notice_title" jdbcType="VARCHAR"/>
            <result property="noticeTemplateId" column="notice_template_id" jdbcType="BIGINT"/>
            <result property="noticeContent" column="notice_content" jdbcType="VARCHAR"/>
            <result property="province" column="province" jdbcType="VARCHAR"/>
            <result property="city" column="city" jdbcType="VARCHAR"/>
            <result property="district" column="district" jdbcType="VARCHAR"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="includeTax" column="include_tax" jdbcType="TINYINT"/>
            <result property="certificateType" column="certificate_type" jdbcType="VARCHAR"/>
            <result property="evaluationMethod" column="evaluation_method" jdbcType="VARCHAR"/>
            <result property="contactPerson" column="contact_person" jdbcType="VARCHAR"/>
            <result property="contactPhone" column="contact_phone" jdbcType="VARCHAR"/>
            <result property="contactFixedPhone" column="contact_fixed_phone" jdbcType="VARCHAR"/>
            <result property="contactEmail" column="contact_email" jdbcType="VARCHAR"/>
            <result property="registerEndTime" column="register_end_time" jdbcType="TIMESTAMP"/>
            <result property="auditEndTime" column="audit_end_time" jdbcType="TIMESTAMP"/>
            <result property="quoteStartTime" column="quote_start_time" jdbcType="TIMESTAMP"/>
            <result property="quoteEndTime" column="quote_end_time" jdbcType="TIMESTAMP"/>
            <result property="currentRound" column="current_round" jdbcType="INTEGER"/>
            <result property="latestQuoteEndTime" column="latest_quote_end_time" jdbcType="TIMESTAMP"/>
            <result property="bidOpenTime" column="bid_open_time" jdbcType="TIMESTAMP"/>
            <result property="bidEndTime" column="bid_end_time" jdbcType="TIMESTAMP"/>
            <result property="quotationNotice" column="quotation_notice" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateById" column="update_by_id" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

</mapper>
