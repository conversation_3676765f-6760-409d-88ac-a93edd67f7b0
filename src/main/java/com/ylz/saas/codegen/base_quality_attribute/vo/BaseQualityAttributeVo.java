package com.ylz.saas.codegen.base_quality_attribute.vo;

import com.ylz.saas.codegen.base_quality_attribute_value.entity.BaseQualityAttributeValueEntity;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 质量属性表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:42:43
 */
@Data
public class BaseQualityAttributeVo implements Serializable {

    /**
  	 * 主键ID
   	 */
    private Long id;

    /**
  	 * 组织ID
   	 */
    private Long deptId;

    /**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
    private String status;

    /**
  	 * 属性编码
   	 */
    private String attributeCode;

    /**
  	 * 属性名称
   	 */
    private String attributeName;

    /**
  	 * 排序
   	 */
    private Integer sortOrder;

    /**
  	 * 属性描述
   	 */
    private String description;

    /**
  	 * 创建人名称
   	 */
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    private String updateByName;

    /**
  	 * 创建人
   	 */
    private String createBy;

    /**
  	 * 创建时间
   	 */
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
    private String updateBy;

    /**
  	 * 修改时间
   	 */
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
    private Long createById;

    /**
  	 * 更新人ID
   	 */
    private Long updateById;
	/**
	* 子表集合:base_quality_attribute_value
	*/
	private List<BaseQualityAttributeValueEntity> baseQualityAttributeValueList;
}