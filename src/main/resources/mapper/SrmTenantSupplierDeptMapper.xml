<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenantSupplierDeptMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenantSupplierDept">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
        <result property="tenantSupplierId" column="tenant_supplier_id" jdbcType="BIGINT"/>
        <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
        <result property="deptName" column="dept_name" jdbcType="VARCHAR"/>
        <result property="relationType" column="relation_type" jdbcType="VARCHAR"/>
        <result property="approvalStatus" column="approval_status" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
        <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateById" column="update_by_id" jdbcType="BIGINT"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,tenant_supplier_id,
        dept_id,relation_type,approval_status,remark,
        del_flag,create_by_id,create_by,create_by_name,
        create_time,update_by_id,update_by,update_by_name,update_time
    </sql>

    <select id="getDeptsByTenantSupplierId" resultMap="BaseResultMap">
        SELECT
            stsd.id,
            stsd.tenant_id,
            stsd.tenant_supplier_id,
            stsd.dept_id,
            stsd.relation_type,
            stsd.approval_status,
            stsd.remark,
            stsd.del_flag,
            stsd.create_by_id,
            stsd.create_by,
            stsd.create_by_name,
            stsd.create_time,
            stsd.update_by_id,
            stsd.update_by,
            stsd.update_by_name,
            stsd.update_time,
            sd.name as dept_name
        FROM srm_tenant_supplier_dept stsd
        LEFT JOIN sys_dept sd ON stsd.dept_id = sd.dept_id
        WHERE stsd.tenant_supplier_id = #{tenantSupplierId}
        AND stsd.del_flag = 0
        ORDER BY stsd.create_time DESC
    </select>

    <select id="getTenantSupplierIdsByDeptId" resultType="java.lang.Long">
        SELECT tenant_supplier_id
        FROM srm_tenant_supplier_dept
        WHERE dept_id = #{deptId}
        AND del_flag = 0
    </select>

    <delete id="physicalDeleteById">
        DELETE FROM srm_tenant_supplier_dept WHERE id = #{id}
    </delete>

    <delete id="physicalDeleteByTenantSupplierId">
        DELETE FROM srm_tenant_supplier_dept WHERE tenant_supplier_id = #{tenantSupplierId}
    </delete>
</mapper>
