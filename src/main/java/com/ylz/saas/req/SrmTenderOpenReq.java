package com.ylz.saas.req;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.entity.SrmTenderOpenLog;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 开标大厅
 */
@Data
public class SrmTenderOpenReq extends Page<SrmTenderOpenLog> {
    /**
     * 公告id
     */
    @NotNull(message = "公告id不能为空")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 标段ID
     */
    private List<Long> sectionIdList;
}