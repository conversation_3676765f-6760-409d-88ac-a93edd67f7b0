package com.ylz.saas.service;

import com.ylz.saas.entity.SrmTenantSupplierMaterial;
import com.ylz.saas.vo.SupplierMaterialInfoVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tenant_supplier_material(租户供应商主营物料表)】的数据库操作Service
* @createDate 2025-06-10 18:08:09
*/
public interface SrmTenantSupplierMaterialService extends IService<SrmTenantSupplierMaterial> {



    /**
     * 保存或更新供应商主营物料信息
     *
     * @param supplierMaterialInfoVo 供应商主营物料信息VO
     * @return 是否保存成功
     */
    boolean saveOrUpdateSupplierMaterialInfo(SupplierMaterialInfoVo supplierMaterialInfoVo);

    /**
     * 根据供应商ID查询主营物料列表
     *
     * @param supplierId 供应商ID
     * @return 主营物料列表
     */
    List<SrmTenantSupplierMaterial> getMaterialsBySupplierId(Long supplierId);



    /**
     * 删除供应商主营物料信息
     *
     * @param id 物料关联ID
     * @return 是否删除成功
     */
    boolean deleteSupplierMaterial(Long id);

    /**
     * 根据供应商ID分页查询主营物料列表
     *
     * @param supplierId 供应商ID
     * @param page 分页参数
     * @return 分页主营物料列表
     */
    Page<SrmTenantSupplierMaterial> getMaterialsBySupplierIdPage(Long supplierId, Page<SrmTenantSupplierMaterial> page);

}
