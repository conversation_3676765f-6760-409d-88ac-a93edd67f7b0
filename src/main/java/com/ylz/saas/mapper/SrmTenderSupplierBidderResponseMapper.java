package com.ylz.saas.mapper;

import com.ylz.saas.common.data.datascope.annotation.DataPermission;
import com.ylz.saas.entity.SrmTenderSupplierBidderResponse;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【srm_tender_supplier_bidder_response(投标响应表)】的数据库操作Mapper
* @createDate 2025-06-11 16:03:22
* @Entity com.ylz.saas.entity.SrmTenderSupplierBidderResponse
*/
@Mapper
//@DataPermission(userEnabled = true, dataPermissionRuleList = "[{\"dataTypeCode\":\"PROJECT\",\"targetField\":\"project_id\",\"dataSourceField\":\"ID\"}]")
public interface SrmTenderSupplierBidderResponseMapper extends BaseMapper<SrmTenderSupplierBidderResponse> {

}




