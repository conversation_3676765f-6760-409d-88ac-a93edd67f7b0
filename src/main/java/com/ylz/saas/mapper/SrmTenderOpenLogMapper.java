package com.ylz.saas.mapper;

import com.ylz.saas.common.data.datascope.annotation.DataPermission;
import com.ylz.saas.entity.SrmTenderOpenLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【srm_tender_open_log(开标记录日志表)】的数据库操作Mapper
* @createDate 2025-06-17 19:14:59
* @Entity com.ylz.saas.entity.SrmTenderOpenLog
*/
@Mapper
//@DataPermission(userEnabled = true, dataPermissionRuleList = "[{\"dataTypeCode\":\"PROJECT\",\"targetField\":\"project_id\",\"dataSourceField\":\"ID\"}]")
public interface SrmTenderOpenLogMapper extends BaseMapper<SrmTenderOpenLog> {

}




