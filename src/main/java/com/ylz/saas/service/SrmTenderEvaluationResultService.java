package com.ylz.saas.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.entity.SrmTenderEvaluationResult;
import com.ylz.saas.req.*;
import com.ylz.saas.resp.BidSectionInfoResp;
import com.ylz.saas.resp.SrmTenderEvaluationResultDetailResp;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tender_evaluation_result(评标结果表)】的数据库操作Service
* @createDate 2025-06-16 15:54:36
*/
public interface SrmTenderEvaluationResultService extends IService<SrmTenderEvaluationResult> {

    /**
     * 评标
     *
     * @param req
     */
    void reviewBid(SrmTenderBidReq req);

    /**
     * 中标公示
     *
     * @param req
     */
    void bidPublicity(SrmTenderBidPublicityReq req);

    /**
     * 中标公告
     *
     * @param req
     */
    void bidPublicNotice(SrmTenderBidPublicNoticeReq req);

    /**
     * 定标审核中
     *
     * @param projectId
     * @param noticeId
     */
    void evaluationResultExamineApproving(String projectId,String noticeId, String sectionId, String tenantSupplierIdsStr);

    /**
     * 定标审核通过
     *
     * @param projectId
     * @param noticeId
     */
    void evaluationResultExaminePass(String projectId,String noticeId, String sectionId);

    /**
     * 定标审核拒绝
     *
     * @param projectId
     * @param noticeId
     */
    void evaluationResultExamineReject(String projectId,String noticeId, String sectionId);

    /**
     * 通过noticeId和projectId获取评标结果详情（包含报价轮次）
     *
     * @param noticeId 招标公告ID
     * @param projectId 采购立项ID
     * @return 评标结果详情
     */
    SrmTenderEvaluationResultDetailResp getEvaluationResultDetail(Long noticeId, Long projectId);


    /**
     * 审核公示公告
     * @param req
     */
    void auditNotice(AuditPublicityNoticeReq req);

    /**
     * 审核中标公告通过
     * @param projectId
     * @param noticeId
     */
    void auditNoticePass(String projectId,String noticeId);


    /**
     * 审核中标公告拒绝
     * @param projectId
     * @param noticeId
     */
    void auditNoticeReject(String projectId,String noticeId);

    /**
     * 审核公式
     * @param req
     */
    void auditPublicity(AuditPublicityNoticeReq req);

    /**
     * 中标公示通过
     * @param projectId
     * @param noticeId
     */
    void auditPublicityPass(String projectId,String noticeId);

    /**
     * 中标公示拒绝
     * @param projectId
     * @param noticeId
     */
    void auditPublicityReject(String projectId,String noticeId);


    /**
     * 根据id查询详情
     *
     * @param projectIdAndNoticeId
     * @return
     */
    List<SrmTenderEvaluationResult> getDetailById(String projectIdAndNoticeId);

    /**
     * 根据id查询详情
     *
     * @param projectIdAndNoticeId
     * @return
     */
    List<SrmTenderEvaluationResult> getDetailById(String projectIdAndNoticeId, String tenantSupplierIdsStr);

    /**
     * 更新公示审批状态为审批中
     * @param evaluationResultId 评标结果ID
     */
    void updatePublicityAuditStatus(String evaluationResultId);

    /**
     * 公示审批处理（通过、拒绝、撤销）
     * @param evaluationResultId 评标结果ID
     * @param result 审批结果（1-通过，2-拒绝，3-撤销）
     */
    void approvePublicityAudit(String evaluationResultId, Integer result);


    /**
     * 获取定标列表
     *
     * @param projectId
     * @param noticeId
     * @return
     */
    List<BidSectionInfoResp> bidSectionList(Long projectId, Long noticeId);

    /**
     * 中标公告审批锁定修改为审批中
     *
     * @param noticeId
     * @param projectId
     * @param sectionId
     */
    void awardApprovingHook(String projectId, String noticeId, String sectionId);

    /**
     * 中标公告审批锁定修改为审批中
     *
     * @param noticeId
     * @param projectId
     */
    void noticePublicApprovingHook(String projectId,String noticeId);


    /**
     * 中标公示审批锁定修改为审批中
     *
     * @param noticeId
     * @param projectId
     */
    void publicityApprovingHook(String projectId,String noticeId);

}
