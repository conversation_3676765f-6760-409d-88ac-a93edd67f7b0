package com.ylz.saas.codegen.srm_procurement_plan_field_value.param;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ylz.saas.codegen.srm_procurement_plan_field_value.entity.SrmProcurementPlanFieldValueEntity;
import com.ylz.saas.common.data.mybatis.helper.Operator;
import com.ylz.saas.common.data.mybatis.helper.QueryWrapperHelper;
import com.ylz.saas.common.data.mybatis.wrapper.CriteriaField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * mybatis-plus的查询帮助类
 * 
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SrmProcurementPlanFieldValueParam {

	/**
  	 * 主键ID
   	 */
 	@CriteriaField(field = "id", operator = Operator.LIKE)
    private Long id;
	/**
  	 * 组织ID
   	 */
 	@CriteriaField(field = "deptId", operator = Operator.LIKE)
    private Long deptId;
	/**
  	 * 采购计划明细ID
   	 */
 	@CriteriaField(field = "planDetailId", operator = Operator.LIKE)
    private Long planDetailId;
	/**
  	 * 服务类型ID
   	 */
 	@CriteriaField(field = "serviceTypeId", operator = Operator.LIKE)
    private Long serviceTypeId;
	/**
  	 * 字段ID
   	 */
 	@CriteriaField(field = "fieldId", operator = Operator.LIKE)
    private Long fieldId;
	/**
  	 * 字段编码
   	 */
 	@CriteriaField(field = "fieldCode", operator = Operator.LIKE)
    private String fieldCode;
	/**
  	 * 字段类型（string-字符串、int-整数、decimal-小数、date-日期、datetime-日期时间、enum-枚举）
   	 */
 	@CriteriaField(field = "fieldType", operator = Operator.LIKE)
    private String fieldType;
	/**
  	 * 字段值
   	 */
 	@CriteriaField(field = "fieldValue", operator = Operator.LIKE)
    private String fieldValue;
	/**
  	 * 创建人名称
   	 */
 	@CriteriaField(field = "createByName", operator = Operator.LIKE)
    private String createByName;
	/**
  	 * 修改人名称
   	 */
 	@CriteriaField(field = "updateByName", operator = Operator.LIKE)
    private String updateByName;
	/**
  	 * 创建人
   	 */
 	@CriteriaField(field = "createBy", operator = Operator.LIKE)
    private String createBy;
	/**
  	 * 创建时间_开始
   	 */
	@CriteriaField(field = "createTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime createTimeStart;
	/**
  	 * 创建时间_结束
   	 */
    @CriteriaField(field = "createTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime createTimeEnd;
	/**
  	 * 修改人
   	 */
 	@CriteriaField(field = "updateBy", operator = Operator.LIKE)
    private String updateBy;
	/**
  	 * 修改时间_开始
   	 */
	@CriteriaField(field = "updateTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime updateTimeStart;
	/**
  	 * 修改时间_结束
   	 */
    @CriteriaField(field = "updateTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime updateTimeEnd;
	/**
  	 * 租户ID
   	 */
 	@CriteriaField(field = "tenantId", operator = Operator.LIKE)
    private Long tenantId;
	/**
  	 * 创建人ID
   	 */
 	@CriteriaField(field = "createById", operator = Operator.LIKE)
    private Long createById;
	/**
  	 * 更新人ID
   	 */
 	@CriteriaField(field = "updateById", operator = Operator.LIKE)
    private Long updateById;

    /**
    * 构建查询
    */
    public QueryWrapper<SrmProcurementPlanFieldValueEntity> toWrapper() {
        QueryWrapper<SrmProcurementPlanFieldValueEntity> queryWrapper = QueryWrapperHelper.fromBean(this, SrmProcurementPlanFieldValueEntity.class)
            .orderByDesc("id");
       return queryWrapper;
    }
}