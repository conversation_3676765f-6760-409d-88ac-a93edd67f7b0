package com.ylz.saas.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.req.SrmAppletNoticePageReq;
import com.ylz.saas.req.SrmEnsilagePageForAppletReq;
import com.ylz.saas.resp.SrmAllNoticePageResp;
import com.ylz.saas.service.SrmAllNoticeTabService;
import com.ylz.saas.service.SrmProcurementProjectService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 小程序使用
 */
@RestController
@RequestMapping("/applet")
@AllArgsConstructor
public class SrmAppletController {

    private final SrmAllNoticeTabService allNoticeTabService;

    private final SrmProcurementProjectService srmProcurementProjectService;


    /**
     * 采购公告/邀请函分页(小程序使用)
     *
     * @param req 筛选条件
     * @return 查询结果
     */
    @PostMapping("/inviteAndNotice")
    public R<Page<SrmAllNoticePageResp>> inviteAndNoticePage(@RequestBody SrmAppletNoticePageReq req) {
        Page<SrmAllNoticePageResp> noticePage = allNoticeTabService.inviteAndNoticePage(req);
        return R.ok(noticePage);
    }


    /**
     * 提供小程序查询使用，青贮查询
     */
    @PostMapping("/pageEnsilageForApplet")
    public R<Page<SrmProcurementProject>> pageEnsilageForApplet(@RequestBody SrmEnsilagePageForAppletReq req, Page<SrmProcurementProject> page) {
        return R.ok(srmProcurementProjectService.pageForApplet(req, page));
    }



}

