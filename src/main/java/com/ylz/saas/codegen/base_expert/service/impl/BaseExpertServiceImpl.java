package com.ylz.saas.codegen.base_expert.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.admin.api.dto.UserDTO;
import com.ylz.saas.admin.api.dto.UserIdentityRoleBindDTO;
import com.ylz.saas.admin.api.dto.UserIdentityRoleCodeBindDTO;
import com.ylz.saas.admin.api.entity.*;
import com.ylz.saas.admin.service.SysUserIdentityRoleRelationService;
import com.ylz.saas.admin.service.SysUserService;
import com.ylz.saas.codegen.base_expert.constants.ExpertCategoryConstants;
import com.ylz.saas.codegen.base_expert.entity.BaseExpertEntity;
import com.ylz.saas.codegen.base_expert.mapper.BaseExpertMapper;
import com.ylz.saas.codegen.base_expert.service.BaseExpertService;
import com.ylz.saas.codegen.base_expert.vo.BaseExpertVo;
import com.ylz.saas.codegen.base_expert.vo.ExpertUserInnerAccountReq;
import com.ylz.saas.codegen.base_expert_achievement.entity.BaseExpertAchievementEntity;
import com.ylz.saas.codegen.base_expert_achievement.service.BaseExpertAchievementService;
import com.ylz.saas.codegen.base_expert_education.entity.BaseExpertEducationEntity;
import com.ylz.saas.codegen.base_expert_education.service.BaseExpertEducationService;
import com.ylz.saas.codegen.base_expert_work_experience.entity.BaseExpertWorkExperienceEntity;
import com.ylz.saas.codegen.base_expert_work_experience.service.BaseExpertWorkExperienceService;
import com.ylz.saas.common.enums.CodeGeneratorPrefixEnum;
import com.ylz.saas.common.enums.CommonSwitchEnum;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.enums.DefaultDeptExtEnum;
import com.ylz.saas.common.security.util.SecurityUtils;
import com.ylz.saas.common.sequence.generator.impl.DefaultCodeGenerator;
import com.ylz.saas.common.utils.DefaultDeptExtUtil;
import com.ylz.saas.enums.DefaultRoleEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 专家信息表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:42:08
 */
@Service
@Slf4j
@AllArgsConstructor
public class BaseExpertServiceImpl extends ServiceImpl<BaseExpertMapper, BaseExpertEntity> implements BaseExpertService {


    @Autowired
    private BaseExpertAchievementService baseExpertAchievementService;
    @Autowired
    private BaseExpertEducationService baseExpertEducationService;
    @Autowired
    private BaseExpertWorkExperienceService baseExpertWorkExperienceService;

    private final SysUserService userService;

    private final SysUserIdentityRoleRelationService sysUserIdentityRoleRelationService;

    private final DefaultDeptExtUtil defaultDeptExtUtil;

    private final DefaultCodeGenerator defaultCodeGenerator;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBaseExpert(BaseExpertVo baseExpertVo) {
        BaseExpertEntity baseExpert = new BaseExpertEntity();
        BeanUtil.copyProperties(baseExpertVo, baseExpert);

//
//        // 获取租户ID（忽略租户限制的接口可以跳过）
        Long tenantId = 0L;
        try {
            tenantId = SecurityUtils.getUser().getTenantId();
        } catch (Exception e) {
            log.warn("获取租户ID失败，使用默认值0: {}", e.getMessage());
        }

        String loginAccount = baseExpertVo.getLoginAccount();
        String expertCategory = baseExpertVo.getExpertCategory();
        baseExpert.setExpertCode(defaultCodeGenerator.generate(CodeGeneratorPrefixEnum.ZJ.name(), CodeGeneratorPrefixEnum.ZJ.getLength()));
        save(baseExpert);

        SysUser user;

        // 根据专家分类进行不同的处理
        if (ExpertCategoryConstants.isInternal(expertCategory)) {
            // 内部专家：关联已有用户账号并更新用户信息
            Long userId = baseExpertVo.getUserId();
            ExceptionUtil.checkNonNull(userId, "内部专家必须关联已有的用户账号");

            user = userService.getById(userId);
            ExceptionUtil.checkNonNull(user, "未查询到用户ID为[" + userId + "]的用户");

            // 更新关联用户的基本信息
            updateExpertUserInfo(baseExpertVo, userId, false);
        } else if (ExpertCategoryConstants.isExternal(expertCategory)) {
            // 外部专家：创建新用户账号
            user = createExternalExpertUser(baseExpertVo, tenantId);
        } else {
            throw new RuntimeException("不支持的专家分类：" + expertCategory + "，支持的分类：" +
                    ExpertCategoryConstants.INTERNAL + "（内部专家）、" +
                    ExpertCategoryConstants.EXTERNAL + "（外部专家）");
        }

        // 设置专家关联的用户ID
        if (user != null) {
            baseExpert.setUserId(user.getUserId());
            updateById(baseExpert);
        }


        if (CollectionUtils.isNotEmpty(baseExpertVo.getBaseExpertAchievementList())) {
            baseExpertVo.getBaseExpertAchievementList().forEach(baseExpertAchievement -> {
                baseExpertAchievement.setExpertId(baseExpert.getId());
            });
            baseExpertAchievementService.saveBatch(baseExpertVo.getBaseExpertAchievementList());
        }
        if (CollectionUtils.isNotEmpty(baseExpertVo.getBaseExpertEducationList())) {
            baseExpertVo.getBaseExpertEducationList().forEach(baseExpertEducation -> {
                baseExpertEducation.setExpertId(baseExpert.getId());
            });
            baseExpertEducationService.saveBatch(baseExpertVo.getBaseExpertEducationList());
        }
        if (CollectionUtils.isNotEmpty(baseExpertVo.getBaseExpertWorkExperienceList())) {
            baseExpertVo.getBaseExpertWorkExperienceList().forEach(baseExpertWorkExperience -> {
                baseExpertWorkExperience.setExpertId(baseExpert.getId());
            });
            baseExpertWorkExperienceService.saveBatch(baseExpertVo.getBaseExpertWorkExperienceList());
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBaseExpert(BaseExpertVo baseExpertVo) {
        BaseExpertEntity baseExpert = getById(baseExpertVo.getId());
        ExceptionUtil.check(baseExpert == null, "500", "专家信息表不存在");

        // 同步更新关联的用户信息（内部专家和外部专家都需要更新）
        if (baseExpert.getUserId() != null) {
            updateExpertUserInfo(baseExpertVo, baseExpert.getUserId(), true);
        }

        // 验证登录账号不能修改（对于外部专家）
        if (ExpertCategoryConstants.isExternal(baseExpert.getExpertCategory())) {
            if (!Objects.equals(baseExpert.getLoginAccount(), baseExpertVo.getLoginAccount())) {
                throw new RuntimeException("外部专家的登录账号不允许修改");
            }
        }

        List<BaseExpertAchievementEntity> oldBaseExpertAchievementList = Optional.ofNullable(baseExpertAchievementService.list(
                        Wrappers.<BaseExpertAchievementEntity>lambdaQuery()
                                .eq(BaseExpertAchievementEntity::getExpertId, baseExpert.getId())
                ))
                .orElse(Collections.emptyList());

        List<BaseExpertAchievementEntity> newBaseExpertAchievementList = Optional.ofNullable(baseExpertVo.getBaseExpertAchievementList())
                .orElse(Collections.emptyList());

        Set<Long> newBaseExpertAchievementIds = newBaseExpertAchievementList.stream()
                .map(BaseExpertAchievementEntity::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        List<Long> idsBaseExpertAchievementToDelete = oldBaseExpertAchievementList.stream()
                .filter(old -> !newBaseExpertAchievementIds.contains(old.getId()))
                .map(BaseExpertAchievementEntity::getId)
                .collect(Collectors.toList());

        if (!idsBaseExpertAchievementToDelete.isEmpty()) {
            baseExpertAchievementService.removeByIds(idsBaseExpertAchievementToDelete);
        }
        for (BaseExpertAchievementEntity baseExpertAchievement : newBaseExpertAchievementList) {
            baseExpertAchievement.setExpertId(baseExpert.getId());
        }
        baseExpertAchievementService.saveOrUpdateBatch(baseExpertVo.getBaseExpertAchievementList());
        List<BaseExpertEducationEntity> oldBaseExpertEducationList = Optional.ofNullable(baseExpertEducationService.list(
                        Wrappers.<BaseExpertEducationEntity>lambdaQuery()
                                .eq(BaseExpertEducationEntity::getExpertId, baseExpert.getId())
                ))
                .orElse(Collections.emptyList());

        List<BaseExpertEducationEntity> newBaseExpertEducationList = Optional.ofNullable(baseExpertVo.getBaseExpertEducationList())
                .orElse(Collections.emptyList());

        Set<Long> newBaseExpertEducationIds = newBaseExpertEducationList.stream()
                .map(BaseExpertEducationEntity::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        List<Long> idsBaseExpertEducationToDelete = oldBaseExpertEducationList.stream()
                .filter(old -> !newBaseExpertEducationIds.contains(old.getId()))
                .map(BaseExpertEducationEntity::getId)
                .collect(Collectors.toList());

        if (!idsBaseExpertEducationToDelete.isEmpty()) {
            baseExpertEducationService.removeByIds(idsBaseExpertEducationToDelete);
        }
        for (BaseExpertEducationEntity baseExpertEducation : newBaseExpertEducationList) {
            baseExpertEducation.setExpertId(baseExpert.getId());
        }
        baseExpertEducationService.saveOrUpdateBatch(baseExpertVo.getBaseExpertEducationList());
        List<BaseExpertWorkExperienceEntity> oldBaseExpertWorkExperienceList = Optional.ofNullable(baseExpertWorkExperienceService.list(
                        Wrappers.<BaseExpertWorkExperienceEntity>lambdaQuery()
                                .eq(BaseExpertWorkExperienceEntity::getExpertId, baseExpert.getId())
                ))
                .orElse(Collections.emptyList());

        List<BaseExpertWorkExperienceEntity> newBaseExpertWorkExperienceList = Optional.ofNullable(baseExpertVo.getBaseExpertWorkExperienceList())
                .orElse(Collections.emptyList());

        Set<Long> newBaseExpertWorkExperienceIds = newBaseExpertWorkExperienceList.stream()
                .map(BaseExpertWorkExperienceEntity::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        List<Long> idsBaseExpertWorkExperienceToDelete = oldBaseExpertWorkExperienceList.stream()
                .filter(old -> !newBaseExpertWorkExperienceIds.contains(old.getId()))
                .map(BaseExpertWorkExperienceEntity::getId)
                .collect(Collectors.toList());

        if (!idsBaseExpertWorkExperienceToDelete.isEmpty()) {
            baseExpertWorkExperienceService.removeByIds(idsBaseExpertWorkExperienceToDelete);
        }
        for (BaseExpertWorkExperienceEntity baseExpertWorkExperience : newBaseExpertWorkExperienceList) {
            baseExpertWorkExperience.setExpertId(baseExpert.getId());
        }
        baseExpertWorkExperienceService.saveOrUpdateBatch(baseExpertVo.getBaseExpertWorkExperienceList());
        BeanUtil.copyProperties(baseExpertVo, baseExpert);
        return updateById(baseExpert);
    }

    @Override
    public BaseExpertEntity getByIdBaseExpert(Long id) {
        BaseExpertEntity baseExpert = getById(id);
        if (baseExpert != null) {
            baseExpert.setBaseExpertAchievementList(baseExpertAchievementService.list(Wrappers.<BaseExpertAchievementEntity>lambdaQuery().eq(BaseExpertAchievementEntity::getExpertId, baseExpert.getId())));
            baseExpert.setBaseExpertEducationList(baseExpertEducationService.list(Wrappers.<BaseExpertEducationEntity>lambdaQuery().eq(BaseExpertEducationEntity::getExpertId, baseExpert.getId())));
            baseExpert.setBaseExpertWorkExperienceList(baseExpertWorkExperienceService.list(Wrappers.<BaseExpertWorkExperienceEntity>lambdaQuery().eq(BaseExpertWorkExperienceEntity::getExpertId, baseExpert.getId())));
        }
        return baseExpert;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIdsBaseExpert(List<Long> ids) {
        List<BaseExpertEntity> baseExpert = lambdaQuery()
                .in(BaseExpertEntity::getId, ids)
                .list();
        if (CollectionUtils.isNotEmpty(baseExpert)) {
            removeByIds(ids);
            List<Long> baseExpertAchievementBaseExpertId = baseExpert.stream().map(BaseExpertEntity::getId).toList();
            baseExpertAchievementService.remove(Wrappers.<BaseExpertAchievementEntity>lambdaQuery().in(BaseExpertAchievementEntity::getExpertId, baseExpertAchievementBaseExpertId));
            List<Long> baseExpertEducationBaseExpertId = baseExpert.stream().map(BaseExpertEntity::getId).toList();
            baseExpertEducationService.remove(Wrappers.<BaseExpertEducationEntity>lambdaQuery().in(BaseExpertEducationEntity::getExpertId, baseExpertEducationBaseExpertId));
            List<Long> baseExpertWorkExperienceBaseExpertId = baseExpert.stream().map(BaseExpertEntity::getId).toList();
            baseExpertWorkExperienceService.remove(Wrappers.<BaseExpertWorkExperienceEntity>lambdaQuery().in(BaseExpertWorkExperienceEntity::getExpertId, baseExpertWorkExperienceBaseExpertId));
        }
        return true;
    }

    /**
     * 创建外部专家用户账号
     *
     * @param baseExpertVo 专家信息
     * @param tenantId 租户ID
     * @return 创建的用户
     */
    private SysUser createExternalExpertUser(BaseExpertVo baseExpertVo, Long tenantId) {
        String loginAccount = baseExpertVo.getLoginAccount();

        // 检查用户名是否已存在

        Long count = userService.lambdaQuery().eq(SysUser::getUsername, baseExpertVo.getLoginAccount()).or()
                .eq(SysUser::getPhone, baseExpertVo.getPhone()).count();
        ExceptionUtil.check(count != 0L, "500", "用户名或手机号已存在");
        //            此处是专家保存
//        SysDept deptByName = defaultDeptExtUtil.getDeptByName(DefaultDeptExtEnum.EXPERT_DEPT.getValue());
//        SysRole roleByCode = defaultDeptExtUtil.getRoleByCode(DefaultDeptExtEnum.EXPERT_ROLE.name());
        SysPost postByCode = defaultDeptExtUtil.getPostByCode(DefaultDeptExtEnum.EXTERNAL_POST.name());

//        ExceptionUtil.checkNonNull(deptByName, "默认外部专家部门未创建");
//        ExceptionUtil.checkNonNull(roleByCode, "默认外部专家角色未创建");
        ExceptionUtil.checkNonNull(postByCode, "默认外部专家岗位未创建");


        // 创建UserDTO对象
        UserDTO userDTO = new UserDTO();
        userDTO.setUsername(loginAccount);

        // 设置密码，如果没有提供则使用默认密码
        String password = StrUtil.isNotBlank(baseExpertVo.getPassword()) ?
                baseExpertVo.getPassword() : "123456"; // 默认密码
        userDTO.setPassword(password); // 不需要手动加密，saveUser方法会处理

        // 设置其他用户信息
        userDTO.setNickname(StrUtil.isNotBlank(baseExpertVo.getNickname()) ?
                baseExpertVo.getNickname() : baseExpertVo.getName());
        userDTO.setName(baseExpertVo.getName()); // 设置姓名
        userDTO.setPhone(baseExpertVo.getPhone());
        userDTO.setEmail(baseExpertVo.getEmail());
        userDTO.setTenantId(tenantId);
        userDTO.setTenantIds(List.of(tenantId));

        // 设置锁定标记，默认为正常状态
        userDTO.setLockFlag(StrUtil.isNotBlank(baseExpertVo.getLockFlag()) ?
                baseExpertVo.getLockFlag() : "0");
        // 设置岗位权限等
        userDTO.setDeptId(baseExpertVo.getDeptTypeId());
//        userDTO.setRole(List.of(roleByCode.getRoleId()));
        userDTO.setPost(List.of(postByCode.getPostId()));
        UserIdentityRoleCodeBindDTO identityRoleCodeBindDTO = new UserIdentityRoleCodeBindDTO();
        identityRoleCodeBindDTO.setIdentityCode(DefaultRoleEnum.IdentifyCode.BID_EXPERT_IDENTITY.name());
        identityRoleCodeBindDTO.setRoleCodeList(List.of(DefaultRoleEnum.Role.BID_EXPERT_ROLE.name()));
        List<UserIdentityRoleCodeBindDTO> bindRoles = List.of(identityRoleCodeBindDTO);

        userDTO.setIdentityRoleCodeBindList(bindRoles);

        try {
            boolean saved = userService.saveUserByCode(userDTO);
            if (!saved) {
                throw new RuntimeException("创建外部专家用户账号失败");
            }

        } catch (Exception e) {
//            ExceptionUtil.check(!saved, "500", "创建外部专家用户账号失败");
            return null;
        }
        // 保存用户
        // 查询创建的用户并返回
        SysUser createdUser = userService.lambdaQuery()
                .eq(SysUser::getUsername, loginAccount)
                .one();


        log.info("成功创建外部专家用户账号，用户名：{}，用户ID：{}", loginAccount, createdUser.getUserId());

        return createdUser;
    }

    @Override
    public List<SysUser> getInnerAccount(ExpertUserInnerAccountReq req) {
//        获取全部已经绑定过的账号名称
        List<BaseExpertEntity> experts = this.lambdaQuery().list();

        List<String> alreadyBindAccount = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(experts)) {
            alreadyBindAccount = experts.stream().map(BaseExpertEntity::getLoginAccount).toList();
        }

//        获取全部未绑定过专家的账号列表
        List<SysUser> user = userService.lambdaQuery()
                .notIn(CollectionUtils.isNotEmpty(alreadyBindAccount), SysUser::getUsername, alreadyBindAccount)
                .notIn(CollectionUtils.isNotEmpty(req.getNotInUserIds()), SysUser::getUserId, req.getNotInUserIds())
                .like(StringUtils.isNotBlank(req.getLoginAccount()), SysUser::getUsername, "%" + req.getLoginAccount() + "%")
                .like(StringUtils.isNotBlank(req.getPhone()), SysUser::getPhone, "%" + req.getPhone() + "%")
                .list();


        return user;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean switchStatus(Long id) {
        BaseExpertEntity baseExpert = getById(id);
        ExceptionUtil.check(baseExpert == null, "500", "专家不存在");
        String newLockFlag;
        if (CommonSwitchEnum.ENABLED.name().equals(baseExpert.getStatus())) {
            baseExpert.setStatus(CommonSwitchEnum.DISABLED.name());
            newLockFlag = "9"; // 锁定
        } else {
            baseExpert.setStatus(CommonSwitchEnum.ENABLED.name());
            newLockFlag = "0"; // 解锁
        }
        this.updateById(baseExpert);
        // 联动修改专家对应用户表的状态
        if (baseExpert.getUserId() != null) {
            userService.lambdaUpdate()
                .eq(SysUser::getUserId, baseExpert.getUserId())
                .set(SysUser::getLockFlag, newLockFlag)
                .update();
        }
        return true;
    }

    /**
     * 更新专家关联的用户信息
     *
     * @param baseExpertVo 专家信息
     * @param userId 用户ID
     * @param allowLockFlagUpdate 是否允许更新锁定标记（外部专家允许，内部专家不允许）
     */
    private void updateExpertUserInfo(BaseExpertVo baseExpertVo, Long userId, boolean allowLockFlagUpdate) {
        try {
            // 获取当前用户信息
            SysUser existingUser = userService.getById(userId);
            if (existingUser == null) {
                log.warn("未找到用户ID为{}的用户信息，跳过用户信息更新", userId);
                return;
            }

            // 校验手机号唯一性（排除当前用户）
            validatePhoneUniqueness(baseExpertVo.getPhone(), existingUser, userId);

            // 校验邮箱唯一性（排除当前用户）
            validateEmailUniqueness(baseExpertVo.getEmail(), existingUser, userId);

            // 构建用户更新信息
            UserDTO userDTO = buildUserUpdateDTO(baseExpertVo, existingUser, allowLockFlagUpdate);

            // 执行用户信息更新
            boolean updated = userService.updateUser(userDTO);
            if (!updated) {
                throw new RuntimeException("更新专家用户信息失败");
            }

            log.info("成功更新专家用户信息，用户ID：{}，姓名：{}", userId, baseExpertVo.getName());

        } catch (Exception e) {
            log.error("更新专家用户信息失败，用户ID：{}，错误信息：{}", userId, e.getMessage(), e);
            throw new RuntimeException("更新专家用户信息失败：" + e.getMessage());
        }
    }

    /**
     * 校验手机号唯一性
     *
     * @param phone 新手机号
     * @param existingUser 当前用户
     * @param userId 用户ID
     */
    private void validatePhoneUniqueness(String phone, SysUser existingUser, Long userId) {
        if (StrUtil.isNotBlank(phone) && !Objects.equals(existingUser.getPhone(), phone)) {
            Long phoneCount = userService.lambdaQuery()
                    .eq(SysUser::getPhone, phone)
                    .ne(SysUser::getUserId, userId)
                    .count();
            if (phoneCount > 0) {
                throw new RuntimeException("手机号已被其他用户使用：" + phone);
            }
        }
    }

    /**
     * 校验邮箱唯一性
     *
     * @param email 新邮箱
     * @param existingUser 当前用户
     * @param userId 用户ID
     */
    private void validateEmailUniqueness(String email, SysUser existingUser, Long userId) {
        if (StrUtil.isNotBlank(email) && !Objects.equals(existingUser.getEmail(), email)) {
            Long emailCount = userService.lambdaQuery()
                    .eq(SysUser::getEmail, email)
                    .ne(SysUser::getUserId, userId)
                    .count();
            if (emailCount > 0) {
                throw new RuntimeException("邮箱已被其他用户使用：" + email);
            }
        }
    }

    /**
     * 构建用户更新DTO
     *
     * @param baseExpertVo 专家信息
     * @param existingUser 当前用户
     * @param allowLockFlagUpdate 是否允许更新锁定标记
     * @return UserDTO
     */
    private UserDTO buildUserUpdateDTO(BaseExpertVo baseExpertVo, SysUser existingUser, boolean allowLockFlagUpdate) {
        UserDTO userDTO = new UserDTO();
        userDTO.setUserId(existingUser.getUserId());
        userDTO.setUsername(existingUser.getUsername()); // 登录账号不允许修改

        // 更新基本信息
        userDTO.setName(baseExpertVo.getName());
        userDTO.setPhone(baseExpertVo.getPhone());
        userDTO.setEmail(baseExpertVo.getEmail());
        userDTO.setNickname(StrUtil.isNotBlank(baseExpertVo.getNickname()) ?
                baseExpertVo.getNickname() : baseExpertVo.getName());
        userDTO.setPassword(existingUser.getPassword());
        // 设置锁定标记（外部专家允许修改，内部专家保持原有）
        if (allowLockFlagUpdate && StrUtil.isNotBlank(baseExpertVo.getLockFlag())) {
            userDTO.setLockFlag(baseExpertVo.getLockFlag());
        } else {
            userDTO.setLockFlag(existingUser.getLockFlag());
        }

        List<SysUserIdentityRoleRelation> sysUserIdentityRoleRelations = sysUserIdentityRoleRelationService.lambdaQuery()
                .eq(SysUserIdentityRoleRelation::getUserId, existingUser.getUserId())
                .list();
        
        // 构建用户身份角色绑定列表
        if (CollectionUtils.isNotEmpty(sysUserIdentityRoleRelations)) {
            List<UserIdentityRoleBindDTO> identityRoleBinds = sysUserIdentityRoleRelations.stream()
                    .map(entry -> {
                        UserIdentityRoleBindDTO bindDTO = new UserIdentityRoleBindDTO();
                        bindDTO.setIdentityId(entry.getIdentityId());
                        bindDTO.setRoleIds(entry.getRoleIdList());
                        return bindDTO;
                    })
                    .toList();
            userDTO.setIdentityRoleBinds(identityRoleBinds);
        } else {
            throw new RuntimeException("用户身份角色关系不存在");
        }

        // 保持原有的部门、租户、角色、岗位等信息不变
        userDTO.setDeptId(existingUser.getDeptId());
        userDTO.setTenantId(existingUser.getTenantId());
        userDTO.setTenantIds(List.of(existingUser.getTenantId()));
        // 设置List.of()会为空，不设置才是保持原有的
//        userDTO.setRole(List.of()); // 保持原有角色
//        userDTO.setPost(List.of()); // 保持原有岗位

        return userDTO;
    }

}