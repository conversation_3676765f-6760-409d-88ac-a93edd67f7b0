package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmClarifyAttachmentDownloadInfo;
import com.ylz.saas.mapper.SrmClarifyAttachmentDownloadInfoMapper;
import com.ylz.saas.req.DownloadLogAddReq;
import com.ylz.saas.resp.ClarifyAttachmentDownloadInfoResp;
import com.ylz.saas.service.SrmClarifyAttachmentDownloadInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_clarify_attachment_download_info(澄清公告附件下载信息)】的数据库操作Service实现
* @createDate 2025-07-10 14:53:29
*/
@Service
public class SrmClarifyAttachmentDownloadInfoServiceImpl extends ServiceImpl<SrmClarifyAttachmentDownloadInfoMapper, SrmClarifyAttachmentDownloadInfo>
    implements SrmClarifyAttachmentDownloadInfoService{

    @Override
    public List<ClarifyAttachmentDownloadInfoResp> querySupplierDownloadInfo(Long clarifyNoticeId, String supplierName) {
        return baseMapper.querySupplierDownloadInfo(clarifyNoticeId, supplierName);
    }

    @Override
    public void supplierDownloadInfoAdd(DownloadLogAddReq addReq) {
        // 添加下载信息
        SrmClarifyAttachmentDownloadInfo clarifyAttachmentDownloadInfo = new SrmClarifyAttachmentDownloadInfo();
        BeanUtils.copyProperties(addReq, clarifyAttachmentDownloadInfo);
        this.save(clarifyAttachmentDownloadInfo);
    }
}




