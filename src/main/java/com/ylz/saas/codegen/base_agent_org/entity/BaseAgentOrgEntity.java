package com.ylz.saas.codegen.base_agent_org.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ylz.saas.codegen.base_agent_user.entity.BaseAgentUserEntity;
import com.ylz.saas.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 代理机构表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:11
 */
@Data
@TableName("base_agent_org")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "代理机构表")
@TenantTable
public class BaseAgentOrgEntity extends Model<BaseAgentOrgEntity> {


    /**
  	 * 主键ID
   	 */
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

    /**
  	 * 组织ID
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="组织ID")
    private Long deptId;

    /**
  	 * 代理机构编码
   	 */
    @Schema(description="代理机构编码")
    private String agentCode;

    /**
     * 社会统一信用代码
     */
    @Schema(description="社会统一信用代码")
    private String socialCreditCode;
    /**
  	 * 代理机构名称
   	 */
    @Schema(description="代理机构名称")
    private String agentName;

    /**
  	 * 省
   	 */
    @Schema(description="省")
    private String province;

    /**
  	 * 市
   	 */
    @Schema(description="市")
    private String city;

    /**
  	 * 区
   	 */
    @Schema(description="区")
    private String district;

    /**
  	 * 详细地址
   	 */
    @Schema(description="详细地址")
    private String address;

    /**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
    @Schema(description="状态（DISABLED-禁用、ENABLED-启用）")
    private String status;

    /**
  	 * 创建人名称
   	 */
    @Schema(description="创建人名称")
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    @Schema(description="修改人名称")
    private String updateByName;

    /**
  	 * 创建人
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
  	 * 创建时间
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

    /**
  	 * 修改时间
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标识（0-正常、1-删除）")
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    @Schema(description="租户ID")
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人ID")
    private Long createById;

    /**
  	 * 更新人ID
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人ID")
    private Long updateById;
	/**
	* 子表集合:base_agent_user
	*/
	@ExcelIgnore
	@TableField(exist = false)
	private List<BaseAgentUserEntity> baseAgentUserList;
}