package com.ylz.saas.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.admin.api.dto.UserDTO;
import com.ylz.saas.admin.api.dto.UserIdentityRoleBindDTO;
import com.ylz.saas.admin.api.dto.UserIdentityRoleCodeBindDTO;
import com.ylz.saas.admin.api.entity.SysDept;
import com.ylz.saas.admin.api.entity.SysPost;
import com.ylz.saas.admin.api.entity.SysRole;
import com.ylz.saas.admin.api.entity.SysUser;
import com.ylz.saas.admin.service.SysUserService;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.data.tenant.TenantContextHolder;
import com.ylz.saas.common.enums.DefaultDeptExtEnum;
import com.ylz.saas.common.security.util.SecurityUtils;
import com.ylz.saas.common.utils.DefaultDeptExtUtil;
import com.ylz.saas.entity.SrmTenantSupplierContact;
import com.ylz.saas.entity.SrmTenantSupplierInfo;
import com.ylz.saas.enums.DefaultRoleEnum;
import com.ylz.saas.mapper.SrmTenantSupplierInfoMapper;
import com.ylz.saas.service.SrmTenantSupplierContactService;
import com.ylz.saas.service.SrmTenantSupplierInfoService;
import com.ylz.saas.mapper.SrmTenantSupplierContactMapper;
import com.ylz.saas.vo.SupplierContactInfoVo;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【srm_tenant_supplier_contact(租户供应商联系人表)】的数据库操作Service实现
 * @createDate 2025-06-10 18:40:08
 */
@Service
@Slf4j
@AllArgsConstructor
public class SrmTenantSupplierContactServiceImpl extends ServiceImpl<SrmTenantSupplierContactMapper, SrmTenantSupplierContact>
        implements SrmTenantSupplierContactService {

    private final SrmTenantSupplierInfoMapper srmTenantSupplierInfoMapper;
    private final SysUserService sysUserService;
    private final DefaultDeptExtUtil defaultDeptExtUtil;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateSupplierContactInfo(SupplierContactInfoVo supplierContactInfoVo) {
        return saveOrUpdateSupplierContactInfo(supplierContactInfoVo, false);
    }

    /**
     * 保存或更新供应商联系人信息
     *
     * @param supplierContactInfoVo 联系人信息
     * @param isSubmitMode 是否为提交模式（true-提交模式，false-保存模式）
     * @return 是否保存成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateSupplierContactInfo(SupplierContactInfoVo supplierContactInfoVo, boolean isSubmitMode) {
        log.info("开始保存供应商联系人信息，联系人姓名：{}，登录账号：{}，提交模式：{}",
                supplierContactInfoVo.getContactName(), supplierContactInfoVo.getLoginAccount(), isSubmitMode);

        // 获取租户ID
        Long tenantId = TenantContextHolder.getTenantId();
//        Long tenantId = SecurityUtils.getUser().getTenantId();

        // 查询租户供应商信息
        SrmTenantSupplierInfo tenantSupplierInfo = srmTenantSupplierInfoMapper.selectById(supplierContactInfoVo.getSupplierId());
        ExceptionUtil.checkNonNull(tenantSupplierInfo, "供应商信息不存在");
        String loginAccount = supplierContactInfoVo.getLoginAccount();

        // 检查登录账号是否在联系人表中已存在
        if (StringUtil.isNotBlank(loginAccount)) {
            SrmTenantSupplierContact existingContact = this.lambdaQuery()
                    .eq(SrmTenantSupplierContact::getLoginAccount, loginAccount)
                    .ne(supplierContactInfoVo.getId() != null, SrmTenantSupplierContact::getId, supplierContactInfoVo.getId())
                    .one();
            if (existingContact != null) {
                ExceptionUtil.check(true, "500", String.format("登录账号%s已存在", loginAccount));
            }
        }

        SrmTenantSupplierContact contact;
        boolean isUpdate = supplierContactInfoVo.getId() != null;

        if (isUpdate) {
            // 更新操作
            contact = this.getById(supplierContactInfoVo.getId());
            if (contact == null) {
                log.error("供应商联系人不存在，ID：{}", supplierContactInfoVo.getId());
                return false;
            }
            BeanUtil.copyProperties(supplierContactInfoVo, contact, "id", "createBy", "createTime");
        } else {
            // 新增操作
            contact = new SrmTenantSupplierContact();
            BeanUtil.copyProperties(supplierContactInfoVo, contact);
        }

        // 设置关联信息
        contact.setTenantSupplierId(tenantSupplierInfo.getId());
        contact.setPlatformSupplierId(tenantSupplierInfo.getPlatformSupplierId());
        contact.setLoginAccount(supplierContactInfoVo.getLoginAccount());
        contact.setAccountStatus(StrUtil.isNotBlank(supplierContactInfoVo.getAccountStatus()) ?
                supplierContactInfoVo.getAccountStatus() : "ENABLED");
        contact.setDelFlag(0);

        // 处理用户账号逻辑
        if (supplierContactInfoVo.getGenerateAccount() != null && supplierContactInfoVo.getGenerateAccount() == 1) {
            if (isSubmitMode) {
                // 提交模式：只对新增联系人（id为null）进行账号检测
                if (!isUpdate) {
                    createSupplierUser(supplierContactInfoVo, tenantId);
                }
                // 已存在的联系人（有id的）跳过用户创建检查
            } else {
                // 保存模式：对所有需要创建账号的联系人进行严格检查
                if (!isUpdate) {
//                    validateUserAccountForSave(supplierContactInfoVo);
                    createSupplierUser(supplierContactInfoVo, tenantId);
                }
            }
        }

        boolean result = this.saveOrUpdate(contact);

        if (result) {
            log.info("供应商联系人信息保存成功，联系人ID：{}", contact.getId());
        } else {
            log.error("供应商联系人信息保存失败");
        }

        return result;
    }

    @Override
    public List<SrmTenantSupplierContact> getContactsBySupplierId(Long supplierId) {
//        this.lambdaQuery()
//                .eq(SrmTenantSupplierContact::getTenantSupplierId, supplierId)
//                .eq(SrmTenantSupplierContact::getDelFlag, 0)
//                .orderByDesc(SrmTenantSupplierContact::getCreateTime)
//                .list();

        return this.baseMapper.getContactsBySupplierId(supplierId);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSupplierContact(Long id) {
        SrmTenantSupplierContact contact = this.getById(id);
        if (contact == null) {
            log.error("供应商联系人不存在，ID：{}", id);
            return false;
        }

        // 使用物理删除
        int result = this.baseMapper.physicalDeleteById(id);

//        这里注释 原因是联系人可能还有别的身份
//        if (result > 0) {
//            log.info("供应商联系人物理删除成功，联系人ID：{}", id);
//            sysUserService.lambdaUpdate()
//                    .in(SysUser::getUserId, id)
//                    .set(SysUser::getLockFlag, "9")
//                    .update();
//        } else {
//            log.error("供应商联系人物理删除失败，联系人ID：{}", id);
//        }

        return result > 0;
    }

    @Override
    public SrmTenantSupplierContact getByUserAccount(String account) {
        return this.baseMapper.getByUserAccount(account);
    }

    @Override
    public Page<SrmTenantSupplierContact> getContactsBySupplierIdPage(Long supplierId, Page<SrmTenantSupplierContact> page) {
        log.info("开始分页查询供应商联系人信息（联查用户密码），供应商ID：{}，页码：{}，页大小：{}",
                supplierId, page.getCurrent(), page.getSize());

        // 使用自定义mapper方法进行联查，获取用户密码信息
        Page<SrmTenantSupplierContact> result = this.baseMapper.getContactsBySupplierIdPageWithUser(supplierId, page);

        log.info("供应商联系人信息查询完成，供应商ID：{}，总记录数：{}，当前页记录数：{}",
                supplierId, result.getTotal(), result.getRecords().size());

        return result;
    }


//    /**
//     * 保存模式下验证用户账号（检查重名并报错）
//     *
//     * @param supplierContactInfoVo 联系人信息
//     */
//    private void validateUserAccountForSave(SupplierContactInfoVo supplierContactInfoVo) {
//        String loginAccount = supplierContactInfoVo.getLoginAccount();
//
//        // 检查用户名和手机号是否已存在（跨租户检查）
//        Long count = sysUserService.lambdaQuery()
//                .eq(SysUser::getUsername, loginAccount)
//                .or()
//                .eq(SysUser::getPhone, supplierContactInfoVo.getContactPhone())
//                .count();
//
//        if (count > 0) {
//            log.warn("用户名或手机号已存在，用户名：{}，手机号：{}", loginAccount, supplierContactInfoVo.getContactPhone());
//            throw new RuntimeException("用户名或手机号已存在");
//        }
//    }


    /**
     * 创建供应商用户账号
     *
     * @param supplierContactInfoVo 联系人信息
     * @param tenantId 租户ID
     */
    public void createSupplierUser(SupplierContactInfoVo supplierContactInfoVo, Long tenantId) {
        String loginAccount = supplierContactInfoVo.getLoginAccount();
        UserDTO userDTO = new UserDTO();
        // 检查用户是否已存在
        List<SysUser> sysUsers = sysUserService.lambdaQuery()
                .eq(SysUser::getUsername, loginAccount).list();
        if (CollUtil.isNotEmpty(sysUsers)) {
            if (sysUsers.size() != 1) {
                ExceptionUtil.check(true, "500", String.format("用户名:%s有多个存在，请检查数据", loginAccount));
            }
            SysUser sysUser = sysUsers.get(0);
            String phone = sysUser.getPhone();
            if (StrUtil.isNotBlank(phone) && !phone.equals(supplierContactInfoVo.getContactPhone())) {
                ExceptionUtil.check(true, "500", String.format("用户名:%s手机号:%s与供应商手机号:%s不一致，请检查数据", loginAccount, phone, supplierContactInfoVo.getContactPhone()));
            }
//            userDTO.setLockFlag("0");
        } else {
//            userDTO.setLockFlag("9");
        }

//        if (count > 0) {
//            log.warn("用户名或手机号已存在，用户名：{}，手机号：{}", loginAccount, supplierContactInfoVo.getContactPhone());
//            throw new RuntimeException("用户名或手机号已存在");
//        }

        // 获取默认部门、角色、岗位信息
//        SysDept deptByName = defaultDeptExtUtil.getDeptByName(DefaultDeptExtEnum.SUPPLIER_CONTACT_DEPT.getValue());
//        SysRole roleByCode = defaultDeptExtUtil.getRoleByCode(DefaultDeptExtEnum.SUPPLIER_CONTACT.name());
        SysPost postByCode = defaultDeptExtUtil.getPostByCode(DefaultDeptExtEnum.EXTERNAL_POST.name());

//        ExceptionUtil.checkNonNull(deptByName, "默认供应商联系人部门未创建");
//        ExceptionUtil.checkNonNull(roleByCode, "默认供应商联系人角色未创建");
        ExceptionUtil.checkNonNull(postByCode, "默认外部岗位未创建");

        // 创建UserDTO对象

        userDTO.setUsername(loginAccount);

        // 设置密码，如果没有提供则使用默认密码
        String password = StrUtil.isNotBlank(supplierContactInfoVo.getPassword()) ?
                supplierContactInfoVo.getPassword() : "123456"; // 默认密码
        userDTO.setPassword(password);

        // 设置其他用户信息
        userDTO.setNickname(StrUtil.isNotBlank(supplierContactInfoVo.getContactName()) ?
                supplierContactInfoVo.getContactName() : loginAccount);
        userDTO.setName(supplierContactInfoVo.getContactName());
        userDTO.setPhone(supplierContactInfoVo.getContactPhone());
        userDTO.setEmail(supplierContactInfoVo.getContactEmail());
        userDTO.setTenantId(tenantId);
        userDTO.setTenantIds(List.of(tenantId));

        // 设置锁定标记，默认为正常状态
//        userDTO.setLockFlag("ENABLED".equals(supplierContactInfoVo.getAccountStatus()) ? "0" : "1");

        // 设置供应商用户的默认部门、角色、岗位
        userDTO.setDeptId(supplierContactInfoVo.getDeptId());
//        userDTO.setRole(List.of(roleByCode.getRoleId()));
        userDTO.setPost(List.of(postByCode.getPostId()));
        UserIdentityRoleCodeBindDTO identityRoleCodeBindDTO = new UserIdentityRoleCodeBindDTO();
        identityRoleCodeBindDTO.setIdentityCode(DefaultRoleEnum.IdentifyCode.SUPPLIER_IDENTITY.name());
        identityRoleCodeBindDTO.setRoleCodeList(List.of(DefaultRoleEnum.Role.SUPPLIER_ROLE.name()));
        List<UserIdentityRoleCodeBindDTO> bindRoles = List.of(identityRoleCodeBindDTO);

        userDTO.setIdentityRoleCodeBindList(bindRoles);
        try {
            boolean saved = sysUserService.saveUserByCode(userDTO);
            if (!saved) {
                throw new RuntimeException("创建供应商用户账号失败");
            }
            log.info("成功创建供应商用户账号，用户名：{}", loginAccount);
        } catch (Exception e) {
            log.error("创建供应商用户账号失败，用户名：{}，错误：{}", loginAccount, e.getMessage());
            throw new RuntimeException("创建供应商用户账号失败：" + e.getMessage());
        }
    }

}




