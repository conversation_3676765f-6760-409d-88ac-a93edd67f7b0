package com.ylz.saas.resp;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 打分模板详情响应类
 */
@Data
public class SrmTenderScoreTemplateDetailResp {
    /**
     * 模板ID
     */
    private Long id;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板编号
     */
    private String templateCode;

    /**
     * 模板描述
     */
    private String templateDesc;

    /**
     * 评审项，按节点分类
     */
    private Map<String, List<TemplateReviewItem>> templateReviewItemsByNode;

    /**
     * 评分项，按节点分类
     */
    private Map<String, List<TemplateScoreItem>> templateScoreItemsByNode;
    @Data
    public static class TemplateReviewItem extends BaseItem{
        /**
         * 不符合否决投标数量(评审)
         */
        private Integer rejectCount;

    }
    @Data
    public static class TemplateScoreItem extends BaseItem{

        /**
         * 总分(评分)
         */
        private BigDecimal totalScore;

        /**
         * 权重(评分)
         */
        private BigDecimal weight;

        /**
         * 分值下限(评分)
         */
        private BigDecimal minScore;

        /**
         * 分值上限(评分)
         */
        private BigDecimal maxScore;

    }
    @Data
    public static class BaseItem {
        /**
         * 主键ID
         */
        private Long id;

        /**
         * 名称
         */
        private String name;

        /**
         * 详情
         */
        private String detail;

        /**
         * 节点名称
         */
        private String node;

        /**
         * 类型(REVIEW-评审项、SCORE-评分项)
         */
        private String type;

        /**
         * 创建时间
         */
        private LocalDateTime createTime;
    }
}
