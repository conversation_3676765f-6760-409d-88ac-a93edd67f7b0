package com.ylz.saas.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 供应商资质信息VO
 *
 * <AUTHOR>
 * @Date 2025/6/11 11:45
 * @Description
 */
@Data
@Schema(description = "供应商资质信息VO")
public class SupplierCertificateInfoVo {

    /**
     * 资质ID（更新时必填，新增时为空）
     */
    @Schema(description = "资质ID（更新时必填，新增时为空）")
    private Long id;

    /**
     * 供应商ID（必填）
     */
//    @NotNull(message = "供应商ID不能为空")
    @Schema(description = "供应商ID", required = true)
    private Long supplierId;

    /**
     * 证书类型（必填）
     */
    @NotBlank(message = "证书类型不能为空")
    @Schema(description = "证书类型", required = true)
    private String certificateType;

    /**
     * 证书名称（必填）
     */
    @NotBlank(message = "证书名称不能为空")
    @Schema(description = "证书名称", required = true)
    private String certificateName;

    /**
     * 证书号
     */
    @Schema(description = "证书号")
    private String certificateNo;

    /**
     * 证书生效日期
     */
    @Schema(description = "证书生效日期")
    private LocalDate validStartDate;

    /**
     * 证书失效日期
     */
    @Schema(description = "证书失效日期")
    private LocalDate validEndDate;

    /**
     * 附件URL
     */
    @Schema(description = "附件URL")
    private String attachmentUrl;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}
