package com.ylz.saas.codegen.base_payment_method_period.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ylz.saas.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;


/**
 * 支付方式账期时间表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:34
 */
@Data
@TableName("base_payment_method_period")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "支付方式账期时间表")
@TenantTable
public class BasePaymentMethodPeriodEntity extends Model<BasePaymentMethodPeriodEntity> {


    /**
  	 * 主键ID
   	 */
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

    /**
  	 * 组织ID
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="组织ID")
    private Long deptId;

    /**
  	 * 支付方式ID
   	 */
    @Schema(description="支付方式ID")
    private Long paymentMethodId;

    /**
  	 * 账期天数
   	 */
    @Schema(description="账期天数")
    private BigDecimal periodDays;

    /**
  	 * 账期名称
   	 */
    @Schema(description="账期名称")
    private String periodName;

    /**
  	 * 账期描述
   	 */
    @Schema(description="账期描述")
    private String description;

    /**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
    @Schema(description="状态（DISABLED-禁用、ENABLED-启用）")
    private String status;

    /**
  	 * 排序
   	 */
    @Schema(description="排序")
    private Integer sortOrder;

    /**
  	 * 创建人名称
   	 */
    @Schema(description="创建人名称")
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    @Schema(description="修改人名称")
    private String updateByName;

    /**
  	 * 创建人
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
  	 * 创建时间
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

    /**
  	 * 修改时间
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标识（0-正常、1-删除）")
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    @Schema(description="租户ID")
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人ID")
    private Long createById;

    /**
  	 * 更新人ID
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人ID")
    private Long updateById;
}