package com.ylz.saas.resp;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.enums.OperationTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 开标记录日志表
 * @TableName srm_tender_open_log
 */
@Data
public class SrmTenderOpenLogResp {

    /**
     * 开标时间
     */
    private LocalDateTime openTime;

    /**
     * 开标状态
     */
    private String openStatus;

    /**
     * 文件递交人数量
     */
    private Integer fileDeliveryCount;

    /**
     * 开标记录信息列表
     */
    private Page<SrmTenderOpenLogInfo> openLogInfoList;


    /**
     * 开标记录信息
     */
    @Data
    public static class SrmTenderOpenLogInfo{
        /**
         * 主键ID
         */
        @TableId(type = IdType.AUTO)
        private Long id;

        /**
         * 租户ID
         */
        private Long tenantId;

        /**
         * 部门ID
         */
        private Long deptId;

        /**
         * 采购立项ID
         */
        private Long projectId;

        /**
         * 招标公告ID
         */
        private Long noticeId;

        /**
         * 标段ID
         */
        private Long sectionId;

        /**
         * 租户供应商ID
         */
        private Long tenantSupplierId;

        /**
         * 供应商名称
         */
        private String supplierName;

        /**
         * 操作类型(LOGIN-登录、LOGOUT-退出、JOIN-进入开标大厅、LEAVE-离开开标大厅、START-开始开标、END-结束开标)
         */
        private OperationTypeEnum operationType;

        /**
         * 操作内容
         */
        private String operationContent;

        /**
         * 操作结果(SUCCESS-成功、FAIL-失败)
         */
        private String operationResult;

        /**
         * 操作时间
         */
        private LocalDateTime operationTime;

        /**
         * 操作人
         */
        private String operationBy;

        /**
         * 操作人名称
         */
        private String operationByName;

        /**
         * 操作IP
         */
        private String operationIp;

        /**
         * 备注
         */
        private String remark;

        /**
         * 删除标识（0-正常、1-删除）
         */
        private Integer delFlag;

        /**
         * 创建人
         */
        private String createBy;

        /**
         * 创建人名称
         */
        private String createByName;

        /**
         * 创建时间
         */
        private LocalDateTime createTime;

        /**
         * 修改人
         */
        private String updateBy;

        /**
         * 修改人名称
         */
        private String updateByName;

        /**
         * 修改时间
         */
        private LocalDateTime updateTime;
    }


}