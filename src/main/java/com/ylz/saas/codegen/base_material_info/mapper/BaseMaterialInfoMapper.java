package com.ylz.saas.codegen.base_material_info.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.codegen.base_material_info.entity.BaseMaterialInfoEntity;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BaseMaterialInfoMapper extends SaasBaseMapper<BaseMaterialInfoEntity> {

    /**
     * 拼音模糊搜索分页查询
     * @param page 分页对象
     * @param materialName 物料名称搜索关键词
     * @return 分页结果
     */
    Page<BaseMaterialInfoEntity> selectPageWithPinyinSearch(Page<BaseMaterialInfoEntity> page,
                                                           @Param("materialName") String materialName,
                                                           @Param("ew") QueryWrapper<BaseMaterialInfoEntity> queryWrapper);

    /**
     * 拼音模糊搜索列表查询
     * @param materialName 物料名称搜索关键词
     * @param queryWrapper 查询条件
     * @return 查询结果
     */
    List<BaseMaterialInfoEntity> selectListWithPinyinSearch(@Param("materialName") String materialName,
                                                           @Param("ew") QueryWrapper<BaseMaterialInfoEntity> queryWrapper);

    /**
     * 分页查询物料信息（联查分类名称和组织名称）
     * @param page 分页对象
     * @param materialName 物料名称搜索关键词
     * @param materialCategoryName 物料分类名称搜索关键词
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    Page<BaseMaterialInfoEntity> selectPageWithCategoryName(Page<BaseMaterialInfoEntity> page,
                                                           @Param("materialName") String materialName,
                                                           @Param("materialCategoryName") String materialCategoryName,
                                                           @Param("ew") QueryWrapper<BaseMaterialInfoEntity> queryWrapper);

    /**
     * 列表查询物料信息（联查分类名称和组织名称）
     * @param materialName 物料名称搜索关键词
     * @param materialCategoryName 物料分类名称搜索关键词
     * @param queryWrapper 查询条件
     * @return 查询结果
     */
    List<BaseMaterialInfoEntity> selectListWithCategoryName(@Param("materialName") String materialName,
                                                           @Param("materialCategoryName") String materialCategoryName,
                                                           @Param("ew") QueryWrapper<BaseMaterialInfoEntity> queryWrapper);
}