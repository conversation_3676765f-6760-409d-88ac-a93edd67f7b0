package com.ylz.saas.codegen.base_service_type.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.codegen.base_service_type.entity.BaseServiceTypeEntity;
import com.ylz.saas.codegen.base_service_type.param.BaseServiceTypeParam;
import com.ylz.saas.codegen.base_service_type.service.BaseServiceTypeService;
import com.ylz.saas.codegen.base_service_type.vo.BaseServiceTypeVo;
import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 服务类型表
 *
 * <AUTHOR>
 * @date 2025-06-03 21:25:49
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/baseServiceType")
@Tag(description = "baseServiceType", name = "服务类型表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@Slf4j
public class BaseServiceTypeController {

    public static final int NO_DEL_FLAG = 0;
    public static final int DEL_FLAG = 1;
    public static final String DEL_FLAG_COLUMN = "del_flag";

    private final BaseServiceTypeService baseServiceTypeService;
    @Autowired
    private BaseServiceTypeFieldService baseServiceTypeFieldService;

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 服务类型表
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @PostMapping("/page")
    public R<Page<BaseServiceTypeEntity>> getBaseServiceTypePage(@RequestBody BaseServiceTypeParam param, Page page) {
        QueryWrapper<BaseServiceTypeEntity> queryWrapper = param.toWrapper();
        return R.ok(baseServiceTypeService.page(page, queryWrapper));
    }


    /**
     * 获取可用的服务类型数据
     *
     * @return
     */
    @Operation(summary = "获取可用的服务类型数据", description = "获取可用的服务类型数据")
    @PostMapping("/enabled")
    public R<List<BaseServiceTypeEntity>> getEnabled() {
        return R.ok(baseServiceTypeService.getEnabled());
    }


    /**
     * 通过id查询服务类型表
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{id}")
    public R<BaseServiceTypeEntity> getById(@PathVariable("id") Long id) {
        return R.ok(baseServiceTypeService.getByIdBaseServiceType(id));
    }

    /**
     * 根据服务类型id和寻源方式查询数据
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "根据服务类型id和寻源方式查询数据", description = "根据服务类型id和寻源方式查询数据")
    @GetMapping("/{id}/{buyWay}")
    public R<BaseServiceTypeEntity> getById(@PathVariable("id") Long id, @PathVariable("buyWay") BaseServiceTypeFieldService.BuyWayEnum buyWay) {
        return R.ok(baseServiceTypeService.getByIdAndBuyWay(id, buyWay));
    }


    /**
     * 新增服务类型表
     *
     * @param baseServiceTypeVo 服务类型表
     * @return R
     */
    @Operation(summary = "新增服务类型表", description = "新增服务类型表")
    @SysLog("新增服务类型表")
    @PostMapping("/save")
    public R<Boolean> save(@Valid @RequestBody BaseServiceTypeVo baseServiceTypeVo) {
        return R.ok(baseServiceTypeService.saveBaseServiceType(baseServiceTypeVo));
    }

    /**
     * 插入或更新服务类型字段子表记录
     *
     * @param baseServiceTypeVo 服务类型表
     * @return R
     */
    @Operation(summary = "插入或更新服务类型字段子表记录", description = "插入或更新服务类型字段子表记录")
    @SysLog("插入或更新服务类型字段子表记录")
    @PostMapping("/upsertTypeFields")
    public R<Boolean> upsertTypeFields(@Valid @RequestBody BaseServiceTypeVo baseServiceTypeVo) {
        return R.ok(baseServiceTypeService.upsertTypeFields(baseServiceTypeVo));
    }

    /**
     * 修改服务类型表
     *
     * @param baseServiceTypeVo 服务类型表
     * @return R
     */
    @Operation(summary = "修改服务类型表", description = "修改服务类型表")
    @SysLog("修改服务类型表")
    @PostMapping("/update")
    public R<Boolean> updateById(@Valid @RequestBody BaseServiceTypeVo baseServiceTypeVo) {
        return R.ok(baseServiceTypeService.updateBaseServiceType(baseServiceTypeVo));
    }

    /**
     * 通过id删除服务类型表
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除服务类型表", description = "通过id删除服务类型表")
    @SysLog("通过id删除服务类型表")
    @GetMapping("remove")
    public R<Boolean> removeById(@RequestParam List<Long> ids) {
        boolean remove = baseServiceTypeService.removeByIdsBaseServiceType(ids);
        return R.ok(remove);
    }


    /**
     * 返回被引用不能被删除的业务类型字段id
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "返回被引用不能被删除的业务类型字段id", description = "返回被引用不能被删除的业务类型字段id")
    @SysLog("返回被引用不能被删除的业务类型字段id")
    @GetMapping("/field/verifyRemove")
    public R<List<Long>> verifyRemoveFieldById(@RequestParam List<Long> ids) {
        List<Long> data = baseServiceTypeFieldService.verifyRemoveFieldById(ids);
        return R.ok(data);
    }
}