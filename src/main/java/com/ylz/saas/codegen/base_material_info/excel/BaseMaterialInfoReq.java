package com.ylz.saas.codegen.base_material_info.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 物料基础信息表
 *
 * <AUTHOR>
 * @date 2025-06-03 18:00:37
 */
@Data
public class BaseMaterialInfoReq implements Serializable {



    /**
  	 * 物料编码
   	 */
	@ExcelProperty(value = "物料编码")
    private String materialCode;


    /**
  	 * 物料名称
   	 */
	@ExcelProperty(value = "物料名称")
    private String materialName;


    /**
  	 * 物料简称
   	 */
	@ExcelProperty(value = "物料简称")
    private String materialSimpleName;


    /**
  	 * 规格/型号
   	 */
	@ExcelProperty(value = "规格/型号")
    private String spec;


    /**
  	 * 物料所属分类
   	 */
	@ExcelProperty(value = "物料所属分类")
    private String categoryName;


    /**
  	 * 计量单位
   	 */
	@ExcelProperty(value = "计量单位")
    private String unit;


    /**
  	 * 来源标识
   	 */
	@ExcelProperty(value = "来源标识")
    private String dataSource;


//    /**
//  	 * 状态（DISABLED-禁用、ENABLED-启用）
//   	 */
//	@ExcelProperty(value = "状态（DISABLED-禁用、ENABLED-启用）")
//    private String status;

    /**
  	 * 备注
   	 */
	@ExcelProperty(value = "备注")
    private String remark;









}