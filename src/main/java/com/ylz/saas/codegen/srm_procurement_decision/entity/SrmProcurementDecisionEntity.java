package com.ylz.saas.codegen.srm_procurement_decision.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ylz.saas.codegen.srm_procurement_decision.enums.DecisionStatus;
import com.ylz.saas.codegen.srm_procurement_decision.enums.DecisionType;
import com.ylz.saas.codegen.srm_procurement_decision.param.UpsertReq;
import com.ylz.saas.common.core.util.TenantTable;
import com.ylz.saas.enums.ApprovalType;
import com.ylz.saas.util.JsonObjectTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购决策
 *
 * <AUTHOR>
 * @date 2025/8/5 14:16
 * @since v0.0.1
 */
@Data
@TableName(value = "srm_procurement_decision", autoResultMap = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "采购决策")
@TenantTable
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SrmProcurementDecisionEntity extends Model<SrmProcurementDecisionEntity> {
    /**
  	 * 主键ID
   	 */
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;
    /**
     * 租户ID
     */
    @Schema(description="租户ID")
    private Long tenantId;
    /**
  	 * 部门ID
   	 */
    @Schema(description="部门ID")
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;
    /**
  	 * 决策code
   	 */
    @Schema(description="决策编号")
    private String code;
    /**
     * 申请标题
     */
    @Schema(description="申请标题")
    private String title;
    /**
     * 采购立项ID
     */
    @Schema(description="采购立项ID")
    private Long projectId;
    /**
     * 项目编号
     */
    @Schema(description="项目编号")
    private String projectCode;
    /**
     * 项目名称
     */
    @Schema(description="项目名称")
    private String projectName;
    /**
     * 关联计划列表[(编号、名称)]
     */
    @Schema(description = "关联计划列表[(编号、名称)]")
    @TableField(value = "`relation_plan_List`", typeHandler = JsonObjectTypeHandler.class)
    private List<RelationPlanVO> relationPlanList;
    /**
     * 决策（申请）类型
     */
    @Schema(description = "决策（申请）类型")
    private DecisionType decisionType;
    /**
     * 采购招标预算/控制价
     */
    @Schema(description="采购招标预算/控制价")
    private BigDecimal controlPriceAmount;
    /**
     * 申请人ID（列表回显）
     */
    @Schema(description="申请人ID")
    private Long applicant;
    /**
     * 申请人姓名
     */
    @Schema(description="申请人姓名")
    private String applicantName;
    /**
     * 申请部门ID（列表回显）
     */
    @Schema(description="申请部门ID")
    private Long applyDeptId;
    /**
     * 申请日期
     */
    @Schema(description="申请日期")
    private LocalDate applyDate;
    /**
     * 审批（发起）类型 (存 srm_process_instance)
     */
    @Schema(description = "审批（发起）类型")
    private ApprovalType approvalType;
    /**
     * 指定审批人 [审批人id] (存 srm_process_instance)
     */
    @Schema(description = "指定审批人 [审批人id]")
    @TableField(value = "assigned_approver", typeHandler = JsonObjectTypeHandler.class)
    private List<String> assignedApprover;
    /**
     * 申请原因
     */
    @Schema(description = "申请原因")
    private String applyReason;
    /**
     * 申请附件 [url] (存 srm_project_attachment)
     */
    @Schema(description = "申请附件")
    @TableField(value = "attachments", typeHandler = JsonObjectTypeHandler.class)
    @JsonIgnore
    private List<String> attachments;
    /**
     * 状态
     */
    @Schema(description = "状态")
    private DecisionStatus status;
    /**
     * 删除标识（0-正常:false、1-删除:true）
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标识（0-正常、1-删除）")
    private Boolean delFlag;
    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createById;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;
    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createByName;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 修改人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateById;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;
    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateByName;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    /**
     * 申请附件 [url] (存 srm_project_attachment)
     */
    @TableField(exist = false)
    private List<UpsertReq.AttachmentBO> attachmentList;
    /**
     * 申请部门名称
     */
    @TableField(exist = false)
    private String applyDeptName;
    /**
     * 关联采购计划
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RelationPlanVO implements Serializable {
        /**
         * 采购计划编号
         */
        private String planCode;
        /**
         * 采购计划名称
         */
        private String planName;
    }
}