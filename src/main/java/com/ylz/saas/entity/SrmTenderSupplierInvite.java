package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ylz.saas.common.core.util.TenantTable;
import com.ylz.saas.enums.InviteStatusEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 供应商邀请表
 * @TableName srm_tender_supplier_invite
 */
@TenantTable
@TableName(value ="srm_tender_supplier_invite")
@Data
public class SrmTenderSupplierInvite {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 采购立项ID
     */
    private Long projectId;

    /**
     * 招标公告ID
     */
    private Long noticeId;

    /**
     * 标段ID
     */
    private Long sectionId;

    /**
     * 租户供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 供应商响应ID
     */
    private Long tenderSupplierResponseId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 回执内容
     */
    private String responseContent;

    /**
     * 联系人
     */
    private String concatName;

    /**
     * 联系人电话
     */
    private String concatPhone;

    /**
     * 联系人邮箱
     */
    private String concatEmail;

    /**
     * 状态(PENDING-待确认、ACCEPTED-已接受、REJECTED-已拒绝)
     */
    private InviteStatusEnum inviteStatus;

    /**
     * 审核时间
     */
    private LocalDateTime reviewTime;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 接受或拒绝时间
     */
    private LocalDateTime responseTime;

    /**
     * 删除标识（0-正常、1-删除）
     */
    private Integer delFlag;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createById;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updateById;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 成交价格
     */
    @TableField(exist = false)
    private BigDecimal dealPrice;

    /**
     * 是否发布中标公示
     */
    @TableField(exist = false)
    private Boolean isPublicity;

    /**
     * 公示开始时间
     */
    @TableField(exist = false)
    private LocalDateTime publicityStartTime;

    /**
     * 公示结束时间
     */
    @TableField(exist = false)
    private LocalDateTime publicityEndTime;

    /**
     * 公示标题
     */
    @TableField(exist = false)
    private String publicityTitle;

    /**
     * 招标公示内容（富文本）
     */
    @TableField(exist = false)
    private String publicityContent;



    /**
     * 是否发布中标公告
     */
    @TableField(exist = false)
    private Boolean isPublishNotice;

    /**
     * 公告开始时间
     */
    @TableField(exist = false)
    private LocalDateTime publishNoticeStartTime;

    /**
     * 公告结束时间
     */
    @TableField(exist = false)
    private LocalDateTime publishNoticeEndTime;

    /**
     * 公告标题
     */
    @TableField(exist = false)
    private String noticeTitle;

    /**
     * 招标公告内容（富文本）
     */
    @TableField(exist = false)
    private String noticeContent;

    /**
     * 模板ID
     */
    @TableField(exist = false)
    private Long templateId;

    /**
     * 附件列表
     */
    @TableField(exist = false)
    private List<SrmProjectAttachment> attachmentList;

}