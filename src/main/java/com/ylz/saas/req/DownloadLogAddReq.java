package com.ylz.saas.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 下载记录信息
 */
@Data
public class DownloadLogAddReq implements Serializable {

    /**
     * 采购立项id
     */
    @NotNull(message = "采购立项ID不能为空")
    public Long projectId;

    /**
     * 招标公告id
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 租户供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 供应商编号
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 澄清公告ID
     */
    private Long clarifyNoticeId;

    /**
     * 附件下载地址
     */
    private String downloadUrl;

    /**
     * 下载人id
     */
    private String downloadUserId;

    /**
     * 下载人名称
     */
    private String downloadUserName;

}
