package com.ylz.saas.codegen.base_material_category.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 物料分类树形结构VO
 *
 * <AUTHOR>
 * @date 2025-06-03 17:25:41
 */
@Data
@Schema(description = "物料分类树形结构VO")
public class BaseMaterialCategoryTreeVo implements Serializable {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 组织ID
     */
    @Schema(description = "组织ID")
    private Long deptId;

    /**
     * 物料分类编码
     */
    @Schema(description = "物料分类编码")
    private String categoryCode;

    /**
     * 物料分类名称
     */
    @Schema(description = "物料分类名称")
    private String categoryName;

    /**
     * 上级物料分类编码
     */
    @Schema(description = "上级物料分类编码")
    private String parentCategoryCode;

    /**
     * 上级物料分类名称
     */
    @Schema(description = "上级物料分类名称")
    private String parentCategoryName;

    /**
     * 来源标识
     */
    @Schema(description = "来源标识")
    private String dataSource;

    /**
     * 状态（DISABLED-禁用、ENABLED-启用）
     */
    @Schema(description = "状态（DISABLED-禁用、ENABLED-启用）")
    private String status;

    /**
     * 创建人名称
     */
    @Schema(description = "创建人名称")
    private String createByName;

    /**
     * 修改人名称
     */
    @Schema(description = "修改人名称")
    private String updateByName;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @Schema(description = "删除标识（0-正常、1-删除）")
    private int delFlag;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    private Long createById;

    /**
     * 更新人ID
     */
    @Schema(description = "更新人ID")
    private Long updateById;

    /**
     * 子节点列表
     */
    @Schema(description = "子节点列表")
    private List<BaseMaterialCategoryTreeVo> children;

    /**
     * 是否有子节点
     */
    @Schema(description = "是否有子节点")
    private Boolean hasChildren;

    /**
     * 层级深度
     */
    @Schema(description = "层级深度")
    private Integer level;

    /**
     * 顶级分类名称
     */
    @Schema(description = "顶级分类名称")
    private String materialCategoryRoot;
}
