package com.ylz.saas.resp;

import com.ylz.saas.enums.RecruitMemberRoleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 供应商招募分页查询响应类
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@Schema(description = "供应商招募分页查询响应类")
public class SrmRecruitSupplierPageResp implements Serializable {

    /**
     * 当前登录人身份权限
     */
    private RecruitMemberRoleEnum currentRole;

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 招募编号
     */
    @Schema(description = "招募编号")
    private String recruitCode;

    /**
     * 招募公告标题
     */
    @Schema(description = "招募公告标题")
    private String recruitTitle;

    /**
     * 报名情况（统计信息，格式：报名：数量）
     */
    @Schema(description = "报名情况")
    private String signUpInfo;

    /**
     * 采购部门名称
     */
    @Schema(description = "采购部门名称")
    private String purchaseDeptName;

    /**
     * 需求部门名称
     */
    @Schema(description = "需求部门名称")
    private String demandDeptName;

    /**
     * 项目负责人
     */
    @Schema(description = "项目负责人")
    private String projectLeader;

    /**
     * 公告发布时间（创建时间）
     */
    @Schema(description = "公告发布时间")
    private LocalDateTime publishTime;

    /**
     * 报名截止时间（空则显示"长期招募"）
     */
    @Schema(description = "报名截止时间")
    private String deadlineDisplay;

    /**
     * 项目进程（枚举）
     */
    @Schema(description = "项目进程")
    private String process;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createByName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}
