<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmProjectAttachmentMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmProjectAttachment">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="businessId" column="business_id" jdbcType="BIGINT"/>
            <result property="businessType" column="business_type" jdbcType="VARCHAR"/>
            <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
            <result property="filePath" column="file_path" jdbcType="VARCHAR"/>
            <result property="fileType" column="file_type" jdbcType="VARCHAR"/>
            <result property="fileSize" column="file_size" jdbcType="BIGINT"/>
            <result property="sortNo" column="sort_no" jdbcType="INTEGER"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateById" column="update_by_id" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,
        business_id,business_type,file_name,
        file_path,file_type,file_size,
        sort_no,del_flag,create_by_id,
        create_by,create_by_name,create_time,
        update_by_id,update_by,update_by_name,
        update_time
    </sql>
</mapper>
