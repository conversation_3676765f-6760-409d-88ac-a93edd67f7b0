package com.ylz.saas.codegen.base_agent_org.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.codegen.base_agent_org.entity.BaseAgentOrgEntity;
import com.ylz.saas.codegen.base_agent_org.param.BaseAgentOrgParam;
import com.ylz.saas.codegen.base_agent_org.service.BaseAgentOrgService;
import com.ylz.saas.codegen.base_agent_org.vo.BaseAgentOrgVo;
import com.ylz.saas.codegen.base_agent_org.vo.AgentUserInnerAccountReq;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 代理机构表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:11
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/baseAgentOrg")
@Tag(description = "baseAgentOrg", name = "代理机构表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class BaseAgentOrgController {

    private final BaseAgentOrgService baseAgentOrgService;
    /**
     * 启用/禁用
     * @return R
     */
    @Operation(summary = "启用/禁用" , description = "启用/禁用" )
    @PostMapping("/switch/{id}")
    public R switchStatus(@PathVariable("id") Long id) {
        return R.ok(baseAgentOrgService.switchStatus(id));
    }
    /**
     * 新增代理机构  查询内部账号（已绑定不显示）
     * @param req req
     * @return R
     */
    @Operation(summary = "新增代理机构  查询内部账号（已绑定不显示）", description = "新增代理机构  查询内部账号（已绑定不显示）")
    @PostMapping("/getInnerAccount")
    public R getInnerAccount(@RequestBody AgentUserInnerAccountReq req) {
        return R.ok(baseAgentOrgService.getInnerAccount(req));
    }

    /**
     * 分页查询
     * @param page 分页对象
     * @param param 代理机构表
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @PostMapping("/page")
    public R getBaseAgentOrgPage(@RequestBody BaseAgentOrgParam param, Page page) {
        QueryWrapper<BaseAgentOrgEntity> queryWrapper = param.toWrapper();
        return R.ok(baseAgentOrgService.page(page, queryWrapper));
    }

    /**
     * 列表查询
     * @param param 代理机构表
     * @return
     */
    @Operation(summary = "列表查询", description = "列表查询")
    @PostMapping("/list")
    public R getBaseAgentOrgList(@RequestBody BaseAgentOrgParam param) {
        QueryWrapper<BaseAgentOrgEntity> queryWrapper = param.toWrapper();
        return R.ok(baseAgentOrgService.list(queryWrapper));
    }


    /**
     * 通过id查询代理机构表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{id}")
    public R getById(@PathVariable("id") Long id) {
        return R.ok(baseAgentOrgService.getByIdBaseAgentOrg(id));
    }

    /**
     * 新增代理机构表
     * @param baseAgentOrgVo 代理机构表
     * @return R
     */
    @Operation(summary = "新增代理机构表", description = "新增代理机构表")
    @SysLog("新增代理机构表")
    @PostMapping("/save")
    public R save(@Valid @RequestBody BaseAgentOrgVo baseAgentOrgVo) {
        return R.ok(baseAgentOrgService.saveBaseAgentOrg(baseAgentOrgVo));
    }

    /**
     * 修改代理机构表
     * @param baseAgentOrgVo 代理机构表
     * @return R
     */
    @Operation(summary = "修改代理机构表", description = "修改代理机构表")
    @SysLog("修改代理机构表")
    @PostMapping("/update")
    public R updateById(@Valid @RequestBody BaseAgentOrgVo baseAgentOrgVo) {
        return R.ok(baseAgentOrgService.updateBaseAgentOrg(baseAgentOrgVo));
    }

    /**
     * 通过id删除代理机构表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除代理机构表", description = "通过id删除代理机构表")
    @SysLog("通过id删除代理机构表")
    @GetMapping("remove")
    public R removeById(@RequestParam List<Long> ids) {
        boolean remove = baseAgentOrgService.removeByIdsBaseAgentOrg(ids);
        return R.ok(remove);
    }

}