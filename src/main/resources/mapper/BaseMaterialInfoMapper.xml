<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.codegen.base_material_info.mapper.BaseMaterialInfoMapper">

    <!-- 拼音模糊搜索分页查询 -->
    <select id="selectPageWithPinyinSearch" resultType="com.ylz.saas.codegen.base_material_info.entity.BaseMaterialInfoEntity">
        SELECT * FROM base_material_info
        <where>
            del_flag = 0
            <if test="materialName != null and materialName != ''">
                AND (
                    material_name LIKE CONCAT('%', #{materialName}, '%')

                )
            </if>
            <!-- 其他查询条件 -->
            ${ew.customSqlSegment}
        </where>
        ORDER BY id DESC
    </select>

    <!-- 拼音模糊搜索列表查询 -->
    <select id="selectListWithPinyinSearch" resultType="com.ylz.saas.codegen.base_material_info.entity.BaseMaterialInfoEntity">
        SELECT * FROM base_material_info
        <where>
            del_flag = 0
            <if test="materialName != null and materialName != ''">
                AND (
                    <!-- 1. 原始中文名称模糊搜索 -->
                    material_name LIKE CONCAT('%', #{materialName}, '%')

                )
            </if>
            <!-- 其他查询条件 -->
            ${ew.customSqlSegment}
        </where>
        ORDER BY id DESC
    </select>

    <!-- 分页查询物料信息（联查分类名称和组织名称） -->
    <select id="selectPageWithCategoryName" resultType="com.ylz.saas.codegen.base_material_info.entity.BaseMaterialInfoEntity">
        SELECT
            bmi.*,
            bmc.category_name as material_category_name,
            sd.name as dept_name
        FROM base_material_info bmi
        LEFT JOIN base_material_category bmc ON bmi.material_category_id = bmc.id AND bmc.del_flag = 0
        LEFT JOIN sys_dept sd ON bmi.dept_id = sd.dept_id
        <where>
            bmi.del_flag = 0
            <if test="materialName != null and materialName != ''">
                AND (
                    <!-- 1. 原始中文名称模糊搜索 -->
                    bmi.material_name LIKE CONCAT('%', #{materialName}, '%')

                )
            </if>
            <if test="materialCategoryName != null and materialCategoryName != ''">
                AND bmc.category_name LIKE CONCAT('%', #{materialCategoryName}, '%')
            </if>
            <!-- 其他查询条件，安全处理customSqlSegment -->
            <if test="ew != null and ew.customSqlSegment != null and ew.customSqlSegment != ''">
                <bind name="customSql" value="ew.customSqlSegment
                    .replace('base_material_info.', 'bmi.')
                    .replace('WHERE del_flag = ?', '')
                    .replace('AND del_flag = ?', '')
                    .replace('del_flag = ? AND', '')
                    .replace('del_flag = ?', '')
                    .replace('WHERE', '')
                    .replace('ORDER BY id DESC', '')
                    .replace('ORDER BY', '')
                    .trim()"/>
                <if test="customSql != null and customSql != '' and !customSql.startsWith('ORDER') and !customSql.contains('del_flag')">
                    <bind name="processedSql" value="customSql
                        .replace('data_source', 'bmi.data_source')
                        .replace('status', 'bmi.status')
                        .replace('material_name', 'bmi.material_name')
                        .replace('material_code', 'bmi.material_code')
                        .replace('material_category_id', 'bmi.material_category_id')
                        .replace('dept_id', 'bmi.dept_id')
                        .replace('create_time', 'bmi.create_time')
                        .replace('update_time', 'bmi.update_time')
                        .replace('create_by', 'bmi.create_by')
                        .replace('update_by', 'bmi.update_by')
                        .replace('tenant_id', 'bmi.tenant_id')"/>
                    AND ${processedSql}
                </if>
            </if>
        </where>
        ORDER BY bmi.id DESC
    </select>

    <!-- 列表查询物料信息（联查分类名称和组织名称） -->
    <select id="selectListWithCategoryName" resultType="com.ylz.saas.codegen.base_material_info.entity.BaseMaterialInfoEntity">
        SELECT
            bmi.*,
            bmc.category_name as material_category_name,
            sd.name as dept_name
        FROM base_material_info bmi
        LEFT JOIN base_material_category bmc ON bmi.material_category_id = bmc.id AND bmc.del_flag = 0
        LEFT JOIN sys_dept sd ON bmi.dept_id = sd.dept_id
        <where>
            bmi.del_flag = 0
            <if test="materialName != null and materialName != ''">
                AND (
                    <!-- 1. 原始中文名称模糊搜索 -->
                    bmi.material_name LIKE CONCAT('%', #{materialName}, '%')

                )
            </if>
            <if test="materialCategoryName != null and materialCategoryName != ''">
                AND bmc.category_name LIKE CONCAT('%', #{materialCategoryName}, '%')
            </if>
            <!-- 其他查询条件，安全处理customSqlSegment -->
            <if test="ew != null and ew.customSqlSegment != null and ew.customSqlSegment != ''">
                <bind name="customSql" value="ew.customSqlSegment
                    .replace('base_material_info.', 'bmi.')
                    .replace('WHERE del_flag = ?', '')
                    .replace('AND del_flag = ?', '')
                    .replace('del_flag = ? AND', '')
                    .replace('del_flag = ?', '')
                    .replace('WHERE', '')
                    .replace('ORDER BY id DESC', '')
                    .replace('ORDER BY', '')
                    .trim()"/>
                <if test="customSql != null and customSql != '' and !customSql.startsWith('ORDER') and !customSql.contains('del_flag')">
                    <bind name="processedSql" value="customSql
                        .replace('data_source', 'bmi.data_source')
                        .replace('status', 'bmi.status')
                        .replace('material_name', 'bmi.material_name')
                        .replace('material_code', 'bmi.material_code')
                        .replace('material_category_id', 'bmi.material_category_id')
                        .replace('dept_id', 'bmi.dept_id')
                        .replace('create_time', 'bmi.create_time')
                        .replace('update_time', 'bmi.update_time')
                        .replace('create_by', 'bmi.create_by')
                        .replace('update_by', 'bmi.update_by')
                        .replace('tenant_id', 'bmi.tenant_id')"/>
                    AND ${processedSql}
                </if>
            </if>
        </where>
        ORDER BY bmi.id DESC
    </select>

</mapper>
