package com.ylz.saas.service;

import com.ylz.saas.entity.SrmTenantSupplierContact;
import com.ylz.saas.vo.SupplierContactInfoVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tenant_supplier_contact(租户供应商联系人表)】的数据库操作Service
* @createDate 2025-06-10 18:40:08
*/
public interface SrmTenantSupplierContactService extends IService<SrmTenantSupplierContact> {

    void createSupplierUser(SupplierContactInfoVo supplierContactInfoVo, Long tenantId);
    /**
     * 保存或更新供应商联系人信息
     *
     * @param supplierContactInfoVo 供应商联系人信息VO
     * @return 是否保存成功
     */
    boolean saveOrUpdateSupplierContactInfo(SupplierContactInfoVo supplierContactInfoVo);

    /**
     * 保存或更新供应商联系人信息
     *
     * @param supplierContactInfoVo 联系人信息
     * @param isSubmitMode 是否为提交模式（true-提交模式，false-保存模式）
     * @return 是否保存成功
     */
    boolean saveOrUpdateSupplierContactInfo(SupplierContactInfoVo supplierContactInfoVo, boolean isSubmitMode);

    /**
     * 根据供应商ID查询联系人列表
     *
     * @param supplierId 供应商ID
     * @return 联系人列表
     */
    List<SrmTenantSupplierContact> getContactsBySupplierId(Long supplierId);



    /**
     * 删除供应商联系人信息
     *
     * @param id 联系人ID
     * @return 是否删除成功
     */
    boolean deleteSupplierContact(Long id);

    /**
     * 根据供应商ID分页查询联系人列表
     *
     * @param supplierId 供应商ID
     * @param page 分页参数
     * @return 分页联系人列表
     */
    Page<SrmTenantSupplierContact> getContactsBySupplierIdPage(Long supplierId, Page<SrmTenantSupplierContact> page);

    SrmTenantSupplierContact getByUserAccount(String account);
}
