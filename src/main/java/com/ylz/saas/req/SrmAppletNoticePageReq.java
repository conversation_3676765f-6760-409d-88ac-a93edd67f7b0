package com.ylz.saas.req;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 统一公告分页查询参数（小程序使用）
 */
@Data
public class SrmAppletNoticePageReq extends Page {

    /**
     * 项目编号/名称
     */
    private String searchContent;

    /**
     * 寻源方式
     */
    private List<BaseServiceTypeFieldService.BuyWayEnum> sourcingType;

    /**
     * 开标开始时间
     */
    private LocalDateTime bidOpenStartTime;

    /**
     * 开标结束时间
     */
    private LocalDateTime bidOpenEndTime;

    /**
     * 项目负责人
     */
    private String projectLeaderName;

    /**
     * 发布开始时间
     */
    private LocalDateTime publishStartTime;

    /**
     * 发布开始时间
     */
    private LocalDateTime publishEndTime;

    /**
     * 公告标题
     */
    private String noticeTitle;


}
