package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;
import java.util.List;

import com.ylz.saas.admin.api.handler.JsonLongListTypeHandler;
import com.ylz.saas.common.core.util.TenantTable;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.service.SrmProcessConfigService;
import lombok.Data;

/**
 * 供应商响应表
 * @TableName srm_process_instance
 */
@TableName(value ="srm_process_instance")
@Data
@TenantTable
public class SrmProcessInstance {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 业务类型
     */
    private SrmProcessConfigService.BizTypeEnum type;

    /**
     * 业务流key
     */
    private String flowKey;

    /**
     * 业务key
     */
    private String bizKey;

    /**
     * 流程实例id
     */
    private String instanceId;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT)
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String updateByName;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 业务主流程id
     */
    private Long bizId;

    /**
     * 审核状态
     */
    private ApproveStatusEnum approvalStatus;

    /**
     * 审批类型 0默认 1指定人员
     */
    private Integer approvalType;

    /**
     * 指定审批人员
     */
    @TableField(typeHandler = JsonLongListTypeHandler.class)
    private List<Long> specialProcessExecutorList;



}