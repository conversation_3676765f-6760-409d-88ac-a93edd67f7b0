package com.ylz.saas.enums;

import lombok.Getter;

/**
 * 异常处理方式枚举
 */
@Getter
public enum ExceptionTypeEnum {
    // 临时不采购，供应商报价不恰当，其他
    TEMPORARY_NOT_PURCHASE("TEMPORARY_NOT_PURCHASE","临时不采购"),
    SUPPLIER_QUOTE_NOT_APPROPRIATE("SUPPLIER_QUOTE_NOT_APPROPRIATE","供应商报价不恰当"),
    OTHER("OTHER","其他"),
    ;

    private final String code;
    private final String desc;
    ExceptionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


}
