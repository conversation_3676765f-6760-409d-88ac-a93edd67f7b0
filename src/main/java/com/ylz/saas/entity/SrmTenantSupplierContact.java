package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ylz.saas.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 租户供应商联系人表
 * @TableName srm_tenant_supplier_contact
 */
@TableName(value ="srm_tenant_supplier_contact")
@Data
@EqualsAndHashCode(callSuper = true)
@TenantTable
public class SrmTenantSupplierContact extends Model<SrmTenantSupplierContact> {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 租户供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 平台供应商ID
     */
    private Long platformSupplierId;

    /**
     * 平台供应商联系人ID
     */
    private Long platformContactId;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 联系人身份证
     */
    private String idNumber;

    /**
     * 登录账号
     */
    private String loginAccount;

    /**
     * 账号状态(ENABLED-启用、DISABLED-禁用)
     */
    private String accountStatus;

    /**
     * 备注
     */
    private String remark;



    /**
     * 身份证附件
     */
    private String idAttachment;
    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;



    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;



    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否生成账号（1-生成、0-不生成）
     */
    @Schema(description = "是否生成账号（1-生成、0-不生成）")
    private Integer generateAccount;

    /**
     * 登录密码（明文，从sys_user表联查获得）
     */
    @TableField(exist = false)
    @Schema(description = "登录密码（明文）")
    private String password;

    /**
     * 供应商名称
     */
    @TableField(exist = false)
    private String supplierName;

    /**
     * 部门名称
     */
    @TableField(exist = false)
    private String deptName;
}