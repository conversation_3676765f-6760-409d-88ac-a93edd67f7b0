package com.ylz.saas.mapper;

import com.ylz.saas.entity.SrmTenantSupplierInfo;
import com.ylz.saas.vo.SrmTenantSupplierInfoVo;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tenant_supplier_info(租户供应商信息表)】的数据库操作Mapper
* @createDate 2025-06-10 18:08:09
* @Entity com.ylz.saas.entity.SrmTenantSupplierInfo
*/
@Mapper
public interface SrmTenantSupplierInfoMapper extends SaasBaseMapper<SrmTenantSupplierInfo> {

    /**
     * 分页查询我的租户供应商信息（联查联系人）
     *
     * @param page 分页参数
     * @param supplierName 供应商名称（模糊查询）
     * @param approvalStatus 审批状态
     * @param deptId 当前用户部门ID
     * @param supplierTypes 供应商类型列表（多选）
     * @param enterpriseNatures 企业性质列表（多选）
     * @param status 启用状态
     * @return 分页供应商信息列表
     */
    IPage<SrmTenantSupplierInfoVo> getMyTenantSupplierPageWithContact(
            Page<SrmTenantSupplierInfoVo> page,
            @Param("supplierName") String supplierName,
            @Param("approvalStatus") String approvalStatus,
            @Param("deptId") Long deptId,
            @Param("supplierTypes") List<String> supplierTypes,
            @Param("enterpriseNatures") List<String> enterpriseNatures,
            @Param("status") String status
    );

    /**
     * 分页查询租户供应商信息（用于邀请功能）
     * 查询已审核通过的供应商，并标识当前用户部门的邀请状态
     *
     * @param page 分页参数
     * @param supplierName 供应商名称（模糊查询）
     * @param deptId 当前用户部门ID
     * @param supplierTypes 供应商类型列表（多选）
     * @param enterpriseNatures 企业性质列表（多选）
     * @param status 启用状态
     * @return 分页供应商信息列表（含邀请状态）
     */
    IPage<SrmTenantSupplierInfoVo> getTenantSupplierPageWithInviteStatus(
            Page<SrmTenantSupplierInfoVo> page,
            @Param("supplierName") String supplierName,
            @Param("deptId") Long deptId,
            @Param("supplierTypes") List<String> supplierTypes,
            @Param("enterpriseNatures") List<String> enterpriseNatures,
            @Param("status") String status
    );
}




