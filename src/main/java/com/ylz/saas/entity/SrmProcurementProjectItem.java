package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.ylz.saas.codegen.base_service_type_field.entity.BaseServiceTypeFieldEntity;
import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.common.core.util.TenantTable;
import lombok.Data;

/**
 * 采购立项物资明细表
 * @TableName srm_procurement_project_item
 */
@TableName(value ="srm_procurement_project_item")
@Data
@TenantTable
public class SrmProcurementProjectItem {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 采购立项ID
     */
    private Long projectId;

    /**
     * 标段ID
     */
    private Long sectionId;

    /**
     * 服务类型ID
     */
    private Long serviceTypeId;

    /**
     * 寻源方式(INQUIRY-询价、FAST_INQUIRY-快速询价、BIDDING-竞价、TENDER-招标采购、COMPETITIVE_NEGOTIATION-竞争性谈判、DIRECT-直接委托)
     */
    private BaseServiceTypeFieldService.BuyWayEnum sourcingType;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 规格型号
     */
    private String specModel;

    /**
     * 单位
     */
    private String unit;

    /**
     * 需求数量
     */
    private BigDecimal requiredQuantity;

    /**
     * 牧场id
     */
    private Long usageLocationId;

    /**
     * 标准质量参数ID
     */
    private Long qualityIndicatorId;

    /**
     * 采购计划ID
     */
    private Long planId;

    /**
     * 需求行号
     */
    private String requireNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标识（0-正常、1-删除）
     */
    private Integer delFlag;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createById;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateById;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 引用模版ID
     */
    @TableField(exist = false)
    private Long noticeTemplateId;

    /**
     * 招标公告内容（富文本）
     */
    @TableField(exist = false)
    private String noticeContent;

    /**
     * 采购项目字段值
     */
    @TableField(exist = false)
    List<SrmProcurementProjectFieldValue> projectFieldValueList;

    @TableField(exist = false)
    List<BaseServiceTypeFieldEntity> baseServiceTypeFieldEntityList;

    /**
     * 质量指标名称
     */
    @TableField(exist = false)
    private String qualityIndicatorName;

    /**
     * 牧场名称
     */
    @TableField(exist = false)
    private String usageLocationName;

    /**
     * 牧场区域
     */
    @TableField(exist = false)
    private String usageLocationRegion;

    /**
     * 牧场地址
     */
    @TableField(exist = false)
    private String usageLocationAddress;
}