package com.ylz.saas.codegen.srm_procurement_plan_detail.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 采购计划明细表
 *
 * <AUTHOR>
 * @date 2025-06-03 20:39:40
 */
@Data
public class SrmProcurementPlanDetailReq implements Serializable {


    /**
  	 * 组织ID
   	 */
	@ExcelProperty(value = "组织ID")
    private String deptId;



    /**
  	 * 服务类型ID
   	 */
	@ExcelProperty(value = "服务类型ID")
    private String serviceTypeId;


    /**
  	 * 物料编码
   	 */
	@ExcelProperty(value = "物料编码")
    private String materialCode;


    /**
  	 * 物料名称
   	 */
	@ExcelProperty(value = "物料名称")
    private String materialName;


    /**
  	 * 规格型号
   	 */
	@ExcelProperty(value = "规格型号")
    private String specModel;


    /**
  	 * 单位
   	 */
	@ExcelProperty(value = "单位")
    private String unit;


    /**
  	 * 需求数量
   	 */
	@ExcelProperty(value = "需求数量")
    private BigDecimal requiredQuantity;


    /**
  	 * 计划单价
   	 */
	@ExcelProperty(value = "计划单价")
    private BigDecimal unitPrice;


    /**
  	 * 计划总价
   	 */
	@ExcelProperty(value = "计划总价")
    private BigDecimal totalPrice;


    /**
  	 * 上单价
   	 */
	@ExcelProperty(value = "上单价")
    private BigDecimal listPrice;


    /**
  	 * 预算价
   	 */
	@ExcelProperty(value = "预算价")
    private BigDecimal budgetPrice;




    /**
  	 * 备注
   	 */
	@ExcelProperty(value = "备注")
    private String note;









}