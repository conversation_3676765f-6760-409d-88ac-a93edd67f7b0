package com.ylz.saas.codegen.base_expert.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/6/11 14:30
 * @Description
 */
@AllArgsConstructor
@Getter

public enum ExpertTypeEnum {


    BUSINESS_EXPERT("商务专家"),
    TECHNICAL_EXPERT("技术专家"),
    AUDIT_EXPERT("审计专家"),
    OTHER("其它");
//    PROCUREMENT_EXPERT("采购专家"),
//    FINANCIAL_EXPERT("财务专家"),
//    LEGAL_EXPERT("法务专家"),
//    MARKETING_EXPERT("市场专家"),
//    OPERATIONS_EXPERT("运营专家"),
//    HUMAN_RESOURCES_EXPERT("人力资源专家"),
//    INFORMATION_TECHNOLOGY_EXPERT("信息技术专家"),
//    QUALITY_EXPERT("质量专家"),
//


    private final String value;


}
