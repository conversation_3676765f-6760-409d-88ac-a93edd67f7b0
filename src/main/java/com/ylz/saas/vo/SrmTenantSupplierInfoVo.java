package com.ylz.saas.vo;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 租户供应商信息表vo
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SrmTenantSupplierInfoVo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;


//    /**
//     * 组织ID
//     */
//    @TableField(fill = FieldFill.INSERT)
//    private Long deptId;

    /**
     * 平台供应商ID
     */
    private Long platformSupplierId;

    /**
     * 供应商编号
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商简称
     */
    private String supplierShortName;

    /**
     * 供应商类型(MANUFACTURER-生产型供应商、TRADER-贸易型供应商、SERVICE-服务型供应商、LOGISTICS-物流承运商、PROJECT-工程建筑供应商、LABOR-劳务外包供应商、RENTAL-租赁服务供应商、OTHER-其他)
     */
    private String supplierType;

    /**
     * 企业性质
     */
    private String enterpriseNature;

    /**
     * 供应商分类(COMMON-普通供应商、STRATEGIC-战略供应商)
     */
    private String supplierCategory;

    /**
     * 供应商状态(QUALIFIED-合格供应商、POTENTIAL-潜在供应商、UNQUALIFIED-不合格供应商、BLACKLIST-黑名单供应商、TERMINATED-终止合作)
     */
    private String supplierStatus;

    /**
     * 供应商来源(PLATFORM_APPLY-平台申请、INVITE_JOIN-邀请加入)
     */
    private String supplierSource;

    /**
     * 审批状态(DRAFT-草稿、APPROVING-审批中、APPROVED-已审批、REJECTED-已驳回)
     */
    private String approvalStatus;

    /**
     * 合作开始时间
     */
    private LocalDate serviceStartDate;

    /**
     * 合作结束时间
     */
    private LocalDate serviceEndDate;

    /**
     * 法人代表
     */
    private String legalPerson;

    /**
     * 法人身份证号
     */
    private String legalPersonId;

    /**
     * 法人电话
     */
    private String legalPersonPhone;

    /**
     * 注册地址
     */
    private String registeredAddress;

    /**
     * 邮政编码
     */
    private String postalCode;

    /**
     * 传真
     */
    private String fax;

    /**
     * 年平均营业额
     */
    private java.math.BigDecimal annualRevenue;

    /**
     * 公司简介
     */
    private String companyProfile;

    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;



    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;



    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 注册资本
     */
    private BigDecimal registeredCapital;

    /**
     * 统一社会信用代码
     */
    private String socialCreditCode;
    /**
     * 成立日期
     */
    private LocalDateTime establishDate;
    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 是否已邀请（用于tenantSupplierPage接口）
     */
    private Boolean hasInvited;

    /**
     * 邀请状态（用于tenantSupplierPage接口）
     * NO_APPROVAL_REQUIRED-无需审核、INVITE_APPROVAL_PENDING-邀请审核中、APPROVED-审核通过、REJECTED-审核驳回
     */
    private String inviteStatus;

    /**
     * 是否显示邀请按钮（用于tenantSupplierPage接口）
     */
    private Boolean showInviteButton;

    /**
     * 启用 禁用
     */
    private String status;

    /**
     * 是否为所有组织可用（0-否、1-是）
     */
    private Boolean isAllDept;

    /**
     * 关联部门名称（逗号分隔）
     */
    private String deptNames;
}
