<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenderRequirementMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenderRequirement">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="noticeId" column="notice_id" jdbcType="BIGINT"/>
            <result property="sectionId" column="section_id" jdbcType="BIGINT"/>
            <result property="requirementType" column="requirement_type" jdbcType="VARCHAR"/>
            <result property="requirementName" column="requirement_name" jdbcType="VARCHAR"/>
            <result property="requirementContent" column="requirement_content" jdbcType="VARCHAR"/>
            <result property="needResponse" column="need_response" jdbcType="TINYINT"/>
            <result property="sortNo" column="sort_no" jdbcType="INTEGER"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateById" column="update_by_id" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>


</mapper>
