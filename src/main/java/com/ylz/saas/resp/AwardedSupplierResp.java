package com.ylz.saas.resp;

import com.ylz.saas.entity.SrmTenderAwardNotice;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.NoticeStatusEnum;
import com.ylz.saas.enums.SignatureStatusEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 中标供应商响应类
 */
@Data
public class AwardedSupplierResp {
    /**
     * 标段id
     */
    private Long sectionId;
    /**
     * 租户供应商ID
     */
    private Long tenantSupplierId;
    
    /**
     * 供应商编号
     */
    private String supplierCode;
    
    /**
     * 供应商名称
     */
    private String supplierName;
    
    /**
     * 供应商简称
     */
    private String supplierShortName;
    
    /**
     * 供应商类型
     */
    private String supplierType;
    
    /**
     * 企业性质
     */
    private String enterpriseNature;
    
    /**
     * 供应商分类
     */
    private String supplierCategory;
    
    /**
     * 供应商状态
     */
    private String supplierStatus;
    
    /**
     * 法人代表
     */
    private String legalPerson;
    
    /**
     * 法人电话
     */
    private String legalPersonPhone;
    
    /**
     * 注册地址
     */
    private String registeredAddress;
    
    /**
     * 联系人姓名
     */
    private String contactName;
    
    /**
     * 联系人电话
     */
    private String contactPhone;
    
    /**
     * 中标物料数量
     */
    private Integer awardedMaterialCount;
    
    /**
     * 中标总金额
     */
    private BigDecimal totalAwardedAmount;
    
    /**
     * 中标总数量
     */
    private BigDecimal totalAwardedQuantity;
    
    /**
     * 合作开始时间
     */
    private LocalDate serviceStartDate;
    
    /**
     * 合作结束时间
     */
    private LocalDate serviceEndDate;
    
    /**
     * 年平均营业额
     */
    private BigDecimal annualRevenue;
    
    /**
     * 公司简介
     */
    private String companyProfile;
    
    /**
     * 经营范围
     */
    private String businessScope;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 启用状态
     */
    private String status;

    /**
     * 通知状态(UNSENT-未发送、SENT-已发送)
     */
    private NoticeStatusEnum noticeStatus;


    /**
     * 签章状态(UNSIGNED-未签章、SIGNED-已签章)
     */
    private SignatureStatusEnum signatureStatus;

    /**
     * 招标通知
     */
    private SrmTenderAwardNotice awardNotice;

    /**
     * 评估结果ID
     */
    private Long resultId;

    /**
     * 定标报告状态
     * TO_APPROVE("待审批"),
     *     APPROVING("审批中"),
     *     APPROVE("审批通过"),
     *     APPROVE_REJECT("审批驳回"),
     *     APPROVE_REVOKE("审批撤销"),
     */
    private ApproveStatusEnum awardReportStatus;
}
