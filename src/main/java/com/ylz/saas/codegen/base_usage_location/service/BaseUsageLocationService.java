package com.ylz.saas.codegen.base_usage_location.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.base_usage_location.entity.BaseUsageLocationEntity;
import com.ylz.saas.codegen.base_usage_location.param.BaseUsageLocationParam;
import com.ylz.saas.codegen.base_usage_location.vo.BaseUsageLocationVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface BaseUsageLocationService extends IService<BaseUsageLocationEntity> {
	/**
     * 新增使用地点信息表
     * @param baseUsageLocationVo 使用地点信息表
     */
    boolean saveBaseUsageLocation(BaseUsageLocationVo baseUsageLocationVo);
	
	/**
     * 修改使用地点信息表
     * @param baseUsageLocationVo 使用地点信息表
     */
    boolean updateBaseUsageLocation(BaseUsageLocationVo baseUsageLocationVo);
	
	/**
     * 导入模板下载
     * @param response
     */
    void template(HttpServletResponse response);

    /**
     * 数据导入
     * @param file
     * @param response
     */
    void uploadData(MultipartFile file, HttpServletResponse response);

    /**
     * 启用/禁用
     * @param id
     * @return
     */
    Object switchStatus(Long id);

    /**
     * 查询列表（包含组织名称）
     * @param param 查询参数
     * @return 使用地点列表
     */
    List<BaseUsageLocationEntity> listWithDeptName(BaseUsageLocationParam param);

    /**
     * 物理删除使用地点信息表
     * @param ids ID列表
     * @return 删除结果
     */
    boolean removeByIdsBaseUsageLocation(List<Long> ids);
}