package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;
import lombok.Data;
import com.ylz.saas.common.core.util.TenantTable;

/**
 * 租户供应商银行账户表
 * @TableName srm_tenant_supplier_bank_account
 */
@TenantTable
@TableName(value ="srm_tenant_supplier_bank_account")
@Data
public class SrmTenantSupplierBankAccount {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 开户行名称
     */
    private String bankName;

    /**
     * 开户行行号
     */
    private String bankCode;

    /**
     * 开户行地址
     */
    private String bankAddress;

    /**
     * 开户名
     */
    private String accountName;

    /**
     * 开户行账号
     */
    private String accountNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}