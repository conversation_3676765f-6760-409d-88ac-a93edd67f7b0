package com.ylz.saas.service;

import com.ylz.saas.entity.SrmTenderAwardNotice;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.req.AwardedNoticeDetailReq;
import com.ylz.saas.req.SrmTenderAwardNoticeReq;
import com.ylz.saas.req.SrmTenderEditAwardNoticeReq;
import com.ylz.saas.req.SrmTenderEditBatchAwardNoticeReq;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tender_award_notice(中标通知书表)】的数据库操作Service
* @createDate 2025-06-17 19:17:17
*/
public interface SrmTenderAwardNoticeService extends IService<SrmTenderAwardNotice> {

    /**
     * 中标通知发送
     *
     * @param req
     */
    String awardNoticeSent(SrmTenderAwardNoticeReq req);

    /**
     * 中标通知签章
     *
     * @param req
     */
    void awardNoticeSignature(SrmTenderAwardNoticeReq req);

    /**
     * 修改中标通知
     *
     * @param req
     */
    void editAwardNotice(SrmTenderEditAwardNoticeReq req);

    /**
     * 批量修改中标通知
     * @param req
     */
    void batchAwardNotice(List<SrmTenderEditBatchAwardNoticeReq> req);

    /**
     * 中标通知详情
     *
     * @param req
     * @return
     */
    SrmTenderAwardNotice awardNoticeDetail(AwardedNoticeDetailReq req);
}
