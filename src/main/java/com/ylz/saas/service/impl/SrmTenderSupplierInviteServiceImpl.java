package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.core.util.SpringContextHolder;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.entity.SrmTenderNotice;
import com.ylz.saas.entity.SrmTenderSupplierInvite;
import com.ylz.saas.enums.*;
import com.ylz.saas.mapper.SrmTenderSupplierInviteMapper;
import com.ylz.saas.req.SrmTenderSupplierInviteReq;
import com.ylz.saas.service.SrmProcurementProjectService;
import com.ylz.saas.service.SrmTenderNoticeService;
import com.ylz.saas.service.SrmTenderSupplierInviteService;
import com.ylz.saas.service.SrmTenderSupplierResponseService;
import com.ylz.saas.vo.ValidateTenderResult;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【srm_tender_supplier_invite(供应商邀请表)】的数据库操作Service实现
* @createDate 2025-06-11 13:22:39
*/
@Service
public class SrmTenderSupplierInviteServiceImpl extends ServiceImpl<SrmTenderSupplierInviteMapper, SrmTenderSupplierInvite>
    implements SrmTenderSupplierInviteService{

    @Resource
    @Lazy
    private SrmTenderSupplierResponseService srmTenderSupplierResponseService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void supplierInviteList(SrmTenderSupplierInviteReq req) {
        Long noticeId = req.getNoticeId();
        SrmTenderNoticeService noticeService = SpringContextHolder.getBean(SrmTenderNoticeService.class);
        SrmTenderNotice notice = noticeService.getById(noticeId);
        ExceptionUtil.checkNonNull(notice, "招标公告不存在");
        ApproveStatusEnum noticeStatus = notice.getNoticeStatus();
        if(noticeStatus != ApproveStatusEnum.APPROVE){
            ExceptionUtil.checkNonNull(null, "招标公告未通过审核，不能邀请供应商！");
        }
        Long projectId = req.getProjectId();
        SrmProcurementProjectService projectService = SpringContextHolder.getBean(SrmProcurementProjectService.class);
        SrmProcurementProject project = projectService.getById(projectId);
        ExceptionUtil.checkNonNull(project, "采购立项不存在");

        // 构建并批量保存邀请数据
        Long sectionId = req.getSectionId();
        List<SrmTenderSupplierInvite> inviteList = req.getSupplierInfoList().stream()
            .map(supplier -> {
                SrmTenderSupplierInvite invite = new SrmTenderSupplierInvite();
                invite.setProjectId(projectId);
                invite.setNoticeId(noticeId);
                invite.setSectionId(sectionId);
                invite.setTenantSupplierId(supplier.getTenantSupplierId());
                invite.setSupplierName(supplier.getSupplierName());
                invite.setInviteStatus(InviteStatusEnum.PENDING);
                invite.setResponseContent("回执内容");
                return invite;
            })
            .toList();
        // 批量保存
        this.saveBatch(inviteList);
        if(project.getInviteMethod() == InviteMethodEnum.INVITE && Objects.equals(project.getInviteReceipt(), YesNoEnum.YES.getCode())){
            for (SrmTenderSupplierInvite invite : inviteList) {
                ValidateTenderResult validateTenderResult = srmTenderSupplierResponseService.validateSupplierAndProject(noticeId, invite.getTenantSupplierId());
                srmTenderSupplierResponseService.getOrCreateSupplierResponse(validateTenderResult, sectionId, TenderSupplierResponseStageEnum.INVITE);
            }
        }
    }
}




