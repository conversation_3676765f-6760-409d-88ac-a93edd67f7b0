package com.ylz.saas.service;

import com.ylz.saas.entity.SrmTenantSupplierCertificate;
import com.ylz.saas.vo.SupplierCertificateInfoVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tenant_supplier_certificate(租户供应商资质证书表)】的数据库操作Service
* @createDate 2025-06-10 18:08:09
*/
public interface SrmTenantSupplierCertificateService extends IService<SrmTenantSupplierCertificate> {


    /**
     * 保存或更新供应商资质信息
     *
     * @param supplierCertificateInfoVo 供应商资质信息VO
     * @return 是否保存成功
     */
    boolean saveOrUpdateSupplierCertificateInfo(SupplierCertificateInfoVo supplierCertificateInfoVo);

    /**
     * 根据供应商ID查询资质列表
     *
     * @param supplierId 供应商ID
     * @return 资质列表
     */
    List<SrmTenantSupplierCertificate> getCertificatesBySupplierId(Long supplierId);



    /**
     * 删除供应商资质信息
     *
     * @param id 资质ID
     * @return 是否删除成功
     */
    boolean deleteSupplierCertificate(Long id);

    /**
     * 根据供应商ID分页查询资质列表
     *
     * @param supplierId 供应商ID
     * @param page 分页参数
     * @return 分页资质列表
     */
    Page<SrmTenantSupplierCertificate> getCertificatesBySupplierIdPage(Long supplierId, Page<SrmTenantSupplierCertificate> page);

}
