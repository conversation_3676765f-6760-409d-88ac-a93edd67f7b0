package com.ylz.saas.resp;

import com.ylz.saas.enums.DepositStatusEnum;
import com.ylz.saas.enums.DownloadStatusEnum;
import com.ylz.saas.enums.InviteStatusEnum;
import com.ylz.saas.enums.QuoteStatusEnum;
import com.ylz.saas.enums.TenderFeeStatusEnum;
import com.ylz.saas.enums.TenderSupplierRegisterStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2025/6/25 16:18
 * @Description
 */
@Data
public class SrmTenderRegisterInfoListResp {

    /**
     * 采购项目id
     */
    private Long projectId;

    /**
     * 采购项目名称
     */
    private String projectName;

    /**
     * 采购项目标段id
     */
    private Long sectionId;

    /**
     * 采购项目标段名称
     */
    private String selectionName;

    /**
     * 是否已报名
     */
    private Boolean registered;

    /**
     * 报名时间
     */
    private LocalDateTime registerTime;

    /**
     * 报名审核状态
     */
    private TenderSupplierRegisterStatusEnum registerStatus;

    /**
     * 保证金要求id
     */
    private Long depositRequirementId;

    /**
     * 保证金要求
     */
    private String depositContent;

    /**
     * 标书费要求id
     */
    private Long tenderFeeRequirementId;

    /**
     * 标书费要求
     */
    private String tenderFeeContent;

    /**
     * 保证金响应内容
     */
    private String depositRespContent;

    /**
     * 保证金响应人
     */
    private String depositRespBy;

    /**
     * 保证金付时间
     */
    private LocalDateTime depositTime;

    /**
     * 保证金审核状态
     */
    private DepositStatusEnum depositStatus;

    /**
     * 标书费响应内容
     */
    private String tenderFeeRespContent;

    /**
     * 标书费响应人
     */
    private String tenderFeeRespBy;

    /**
     * 标书费支付时间
     */
    private LocalDateTime tenderFeeTime;

    /**
     * 标书费审核状态
     */
    private TenderFeeStatusEnum tenderFeeStatus;

    /**
     * 标书文件下载状态
     */
    private DownloadStatusEnum bidFileDownloadStatus;

    /**
     * 投标缴费开始时间
     */
    private LocalDateTime bidDocPayStartTime;

    /**
     * 标书费支付结束时间
     */
    private LocalDateTime bidDocPayEndTime;

    /**
     * 文件获取开始时间
     */
    private LocalDateTime fileObtainStartTime;

    /**
     * 标书文件下载结束时间
     */
    private LocalDateTime fileObtainEndTime;

    /**
     * 当前报价轮次
     */
    private Integer currentQuoteRound;

    /**
     * 报价状态
     */
    private QuoteStatusEnum quoteStatus;

    /**
     * 报价时间
     */
    private LocalDateTime quoteTime;

    /**
     * 报价Ip
     */
    private String quoteIp;

    /**
     * 邀请时间
     */
    private LocalDateTime inviteTime;

    /**
     * 邀请状态
     */
    private InviteStatusEnum inviteStatus;

    /**
     * 回执时间
     */
    private LocalDateTime inviteResponseTime;



}
