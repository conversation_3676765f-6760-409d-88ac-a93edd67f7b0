package com.ylz.saas.codegen.base_material_info.util;

import cn.hutool.core.util.StrUtil;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;

import java.util.ArrayList;
import java.util.List;

/**
 * 拼音工具类
 * 支持中文转拼音、拼音首字母提取等功能
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
public class PinyinUtil {

    private static final HanyuPinyinOutputFormat FORMAT = new HanyuPinyinOutputFormat();

    static {
        // 设置拼音输出格式
        FORMAT.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        FORMAT.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        FORMAT.setVCharType(HanyuPinyinVCharType.WITH_V);
    }

    /**
     * 获取字符串的全拼音
     *
     * @param text 输入文本
     * @return 全拼音字符串，用空格分隔
     */
    public static String getFullPinyin(String text) {
        if (StrUtil.isBlank(text)) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        for (char c : text.toCharArray()) {
            if (Character.toString(c).matches("[\\u4E00-\\u9FA5]+")) {
                // 中文字符
                try {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, FORMAT);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        result.append(pinyinArray[0]);
                    } else {
                        result.append(c);
                    }
                } catch (Exception e) {
                    result.append(c);
                }
            } else {
                // 非中文字符直接添加
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * 获取字符串的拼音首字母
     *
     * @param text 输入文本
     * @return 拼音首字母字符串
     */
    public static String getFirstLetters(String text) {
        if (StrUtil.isBlank(text)) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        for (char c : text.toCharArray()) {
            if (Character.toString(c).matches("[\\u4E00-\\u9FA5]+")) {
                // 中文字符
                try {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, FORMAT);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        result.append(pinyinArray[0].charAt(0));
                    } else {
                        result.append(c);
                    }
                } catch (Exception e) {
                    result.append(c);
                }
            } else {
                // 非中文字符直接添加
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * 获取字符串的所有可能拼音组合（处理多音字）
     *
     * @param text 输入文本
     * @return 所有可能的拼音组合列表
     */
    public static List<String> getAllPinyinCombinations(String text) {
        if (StrUtil.isBlank(text)) {
            return new ArrayList<>();
        }

        List<List<String>> allPinyinLists = new ArrayList<>();
        
        for (char c : text.toCharArray()) {
            List<String> charPinyinList = new ArrayList<>();
            
            if (Character.toString(c).matches("[\\u4E00-\\u9FA5]+")) {
                // 中文字符
                try {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c, FORMAT);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        for (String pinyin : pinyinArray) {
                            charPinyinList.add(pinyin);
                        }
                    } else {
                        charPinyinList.add(String.valueOf(c));
                    }
                } catch (Exception e) {
                    charPinyinList.add(String.valueOf(c));
                }
            } else {
                // 非中文字符直接添加
                charPinyinList.add(String.valueOf(c));
            }
            
            allPinyinLists.add(charPinyinList);
        }

        // 生成所有组合
        return generateCombinations(allPinyinLists);
    }

    /**
     * 生成拼音组合
     */
    private static List<String> generateCombinations(List<List<String>> allPinyinLists) {
        List<String> result = new ArrayList<>();
        generateCombinationsRecursive(allPinyinLists, 0, "", result);
        return result;
    }

    /**
     * 递归生成拼音组合
     */
    private static void generateCombinationsRecursive(List<List<String>> allPinyinLists, 
                                                    int index, 
                                                    String current, 
                                                    List<String> result) {
        if (index == allPinyinLists.size()) {
            result.add(current);
            return;
        }

        List<String> currentCharPinyins = allPinyinLists.get(index);
        for (String pinyin : currentCharPinyins) {
            generateCombinationsRecursive(allPinyinLists, index + 1, current + pinyin, result);
        }
    }

    /**
     * 检查搜索关键词是否匹配文本（支持拼音模糊搜索）
     *
     * @param text    要搜索的文本
     * @param keyword 搜索关键词
     * @return 是否匹配
     */
    public static boolean matches(String text, String keyword) {
        if (StrUtil.isBlank(text) || StrUtil.isBlank(keyword)) {
            return false;
        }

        String lowerKeyword = keyword.toLowerCase();
        String lowerText = text.toLowerCase();

        // 1. 直接中文匹配
        if (lowerText.contains(lowerKeyword)) {
            return true;
        }

        // 2. 全拼音匹配
        String fullPinyin = getFullPinyin(text).toLowerCase();
        if (fullPinyin.contains(lowerKeyword)) {
            return true;
        }

        // 3. 拼音首字母匹配
        String firstLetters = getFirstLetters(text).toLowerCase();
        if (firstLetters.contains(lowerKeyword)) {
            return true;
        }

        // 4. 多音字组合匹配
        List<String> allCombinations = getAllPinyinCombinations(text);
        for (String combination : allCombinations) {
            if (combination.toLowerCase().contains(lowerKeyword)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 生成用于数据库搜索的拼音字符串
     * 包含全拼音和首字母拼音，用于存储到数据库中进行搜索
     *
     * @param text 输入文本
     * @return 拼音搜索字符串
     */
    public static String generateSearchPinyin(String text) {
        if (StrUtil.isBlank(text)) {
            return "";
        }

        String fullPinyin = getFullPinyin(text);
        String firstLetters = getFirstLetters(text);
        
        // 组合全拼音和首字母，用特殊分隔符分开
        return fullPinyin + "|" + firstLetters;
    }
}
