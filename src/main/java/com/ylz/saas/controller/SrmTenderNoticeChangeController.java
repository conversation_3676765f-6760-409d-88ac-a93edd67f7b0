package com.ylz.saas.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.req.SrmTenderNoticeChangeAddReq;
import com.ylz.saas.req.SrmTenderNoticeChangeDetailReq;
import com.ylz.saas.req.SrmTenderNoticeChangePageReq;
import com.ylz.saas.resp.SrmTenderNoticeChangePageResp;
import com.ylz.saas.service.SrmTenderNoticeChangeService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 变更控制器
 */
@RestController
@RequestMapping("/noticeChange")
public class SrmTenderNoticeChangeController {

    @Resource
    private SrmTenderNoticeChangeService srmTenderNoticeChangeService;

    /**
     * 变更信息列表
     *
     * @param req 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    public R<Page<SrmTenderNoticeChangePageResp>> tenderNoticeChangePage(@RequestBody @Validated SrmTenderNoticeChangePageReq req) {
        Page<SrmTenderNoticeChangePageResp> pageResult = srmTenderNoticeChangeService.tenderNoticeChangePage(req);
        return R.ok(pageResult);
    }

    /**
     * 新增变更信息
     *
     * @param req 筛选条件
     * @return 查询结果
     */
    @PostMapping("/add")
    public R<Void> addTenderNoticeChange(@RequestBody @Validated SrmTenderNoticeChangeAddReq req) {
        srmTenderNoticeChangeService.addTenderNoticeChange(req);
        return R.ok();
    }

    /**
     * 查询变更信息
     *
     * @param req 筛选条件
     * @return 查询结果
     */
    @PostMapping("/detail")
    public R<SrmTenderNoticeChangeAddReq> tenderNoticeChangeDetail(@RequestBody @Validated SrmTenderNoticeChangeDetailReq req) {
        SrmTenderNoticeChangeAddReq webReq = srmTenderNoticeChangeService.tenderNoticeChangeDetail(req);
        return R.ok(webReq);
    }


}

