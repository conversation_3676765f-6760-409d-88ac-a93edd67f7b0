package com.ylz.saas.codegen.base_material_info.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 物料基础信息表
 *
 * <AUTHOR>
 * @date 2025-06-03 18:00:37
 */
@Data
public class BaseMaterialInfoResp implements Serializable {

//    /**
//  	 * 主键ID
//   	 */
//	@ExcelProperty(value = "主键ID")
//    private Long id;


//    /**
//  	 * 组织ID
//   	 */
//	@ExcelProperty(value = "组织ID")
//    private Long deptId;

    /**
  	 * 组织名称
   	 */
	@ExcelProperty(value = "组织名称")
    private String deptName;

    /**
     * 物料分类顶级名称
     */
	@ExcelProperty(value = "物料分类顶级名称")
    private String materialCategoryRoot;

    /**
  	 * 物料编码
   	 */
	@ExcelProperty(value = "物料编码")
    private String materialCode;

    /**
  	 * 物料名称
   	 */
	@ExcelProperty(value = "物料名称")
    private String materialName;

    /**
  	 * 物料简称
   	 */
	@ExcelProperty(value = "物料简称")
    private String materialSimpleName;

    /**
  	 * 规格/型号
   	 */
	@ExcelProperty(value = "规格/型号")
    private String spec;

//    /**
//  	 * 物料所属分类
//   	 */
//	@ExcelProperty(value = "物料所属分类")
//    private Long materialCategoryId;

    /**
  	 * 计量单位
   	 */
	@ExcelProperty(value = "计量单位")
    private String unit;

    /**
  	 * 来源标识
   	 */
	@ExcelProperty(value = "来源标识")
    private String dataSource;

    /**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
	@ExcelProperty(value = "状态（DISABLED-禁用、ENABLED-启用）")
    private String status;

    /**
  	 * 备注
   	 */
	@ExcelProperty(value = "备注")
    private String remark;



    /**
  	 * 创建人
   	 */
	@ExcelProperty(value = "创建人")
    private String createBy;


    /**
  	 * 修改人
   	 */
	@ExcelProperty(value = "修改人")
    private String updateBy;



}