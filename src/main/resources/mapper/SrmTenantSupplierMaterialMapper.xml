<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenantSupplierMaterialMapper">


    <select id="getMaterialsBySupplierId" resultType="com.ylz.saas.entity.SrmTenantSupplierMaterial">
        select stm.id,
               stm.tenant_id,
               stm.dept_id,
               stm.supplier_id,
               stm.material_id,
               stm.create_by_id,
               stm.create_by,
               stm.create_by_name,
               stm.create_time,
               stm.update_by_id,
               stm.update_by,
               stm.update_by_name,
               stm.update_time
        from srm_tenant_supplier_material stm
        where stm.supplier_id = #{supplierId}
        order by stm.create_time desc
    </select>

    <!-- 分页查询供应商主营物料列表（联查物料详细信息） -->
    <select id="getMaterialsBySupplierIdPage" resultType="com.ylz.saas.entity.SrmTenantSupplierMaterial">
        SELECT stm.id,
               stm.tenant_id,
               stm.dept_id,
               stm.supplier_id,
               stm.material_id,
               stm.create_by,
               stm.create_by_name,
               stm.create_time,
               stm.update_by,
               stm.update_by_name,
               stm.update_time,
               bmi.material_name,
               bmi.material_code,
               bmi.spec,
               bmi.unit,
               bmc.category_name,
               bmi.material_category_root
        FROM srm_tenant_supplier_material stm
                 LEFT JOIN base_material_info bmi ON stm.material_id = bmi.id AND bmi.del_flag = 0
                 LEFT JOIN base_material_category bmc ON bmi.material_category_id = bmc.id AND bmc.del_flag = 0

        WHERE stm.supplier_id = #{supplierId}
        ORDER BY stm.create_time DESC
    </select>

    <delete id="physicalDeleteById">
        DELETE FROM srm_tenant_supplier_material WHERE id = #{id}
    </delete>
</mapper>
