package com.ylz.saas.codegen.base_quality_indicator_location.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 质量指标与场地关联表
 *
 * <AUTHOR>
 * @date 2025-06-05 19:18:27
 */
@Data
public class BaseQualityIndicatorLocationResp implements Serializable {

    /**
  	 * 质量指标ID
   	 */
	@ExcelProperty(value = "质量指标ID")
    private Long indicatorId;

    /**
  	 * 使用部门ID
   	 */
	@ExcelProperty(value = "使用部门ID")
    private Long locationId;

    /**
  	 * 创建人名称
   	 */
	@ExcelProperty(value = "创建人名称")
    private String createByName;

    /**
  	 * 修改人名称
   	 */
	@ExcelProperty(value = "修改人名称")
    private String updateByName;









}