package com.ylz.saas.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 中标明细查询请求参数
 */
@Data
public class AwardedItemsQueryReq {
    
    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;
    
    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 标段id
     */
    @NotNull(message = "标段ID不能为空")
    private Long sectionId;
    
    /**
     * 物料编码（查询条件）
     */
    private String materialCode;
    
    /**
     * 物料名称（查询条件）
     */
    private String materialName;
    /**
     * 租户供应商ID
     */
    @NotNull(message = "租户供应商id不能为空")
    private Long tenantSupplierId;
}
