<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmProcurementProjectFieldValueMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmProcurementProjectFieldValue">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="projectItemId" column="project_item_id" jdbcType="BIGINT"/>
            <result property="serviceTypeId" column="service_type_id" jdbcType="BIGINT"/>
            <result property="fieldId" column="field_id" jdbcType="BIGINT"/>
            <result property="fieldCode" column="field_code" jdbcType="VARCHAR"/>
            <result property="fieldType" column="field_type" jdbcType="VARCHAR"/>
            <result property="fieldValue" column="field_value" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateById" column="update_by_id" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,
        project_item_id,service_type_id,field_id,
        field_code,field_type,field_value,
        del_flag,create_by_id,create_by,
        create_by_name,create_time,update_by_id,
        update_by,update_by_name,update_time
    </sql>
</mapper>
