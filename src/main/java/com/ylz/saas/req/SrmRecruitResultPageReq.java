package com.ylz.saas.req;

import com.ylz.saas.enums.ApproveStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 在线报名结果查询
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@Schema(description = "在线报名结果查询请求类")
public class SrmRecruitResultPageReq implements Serializable {

    /**
     * 招募公告id
     */
    @Schema(description = "招募公告id")
    @NotNull(message = "招募公告id不能为空")
    private Long recruitId;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id")
    private Long supplierId;


    /**
     * 资料审核状态 （提交入库审核的时候，只会提交审核通过的数据）
     */
    @Schema(description = "资料审核状态")
    private ApproveStatusEnum approvalStatus;

    @Serial
    private static final long serialVersionUID = 1L;
}
