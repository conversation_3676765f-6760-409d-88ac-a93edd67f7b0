package com.ylz.saas.req;

import com.ylz.saas.enums.ApproveStatusEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 审核招标公告表
 * @TableName srm_tender_notice
 */
@Data
public class SrmTenderNoticeReviewReq {

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 招标审核状态
     * TO_APPROVE("待审批"),
     *     APPROVING("审批中"),
     *     APPROVE("审批通过"),
     *     APPROVE_REJECT("审批驳回"),
     *     APPROVE_REVOKE("审批撤销"),
     */
    @NotNull(message = "招标审核状态不能为空")
    private ApproveStatusEnum noticeStatus;

    /**
     * 招标审核备注信息
     */
    private String approvedRemark;
}