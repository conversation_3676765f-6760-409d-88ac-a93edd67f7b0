package com.ylz.saas.req;

import com.ylz.saas.entity.SrmRecruitSupplier;
import com.ylz.saas.enums.ApprovalType;
import com.ylz.saas.enums.RecruitMemberRoleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.Valid;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 供应商招募新增请求类
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@Schema(description = "供应商招募新增请求类")
public class SrmRecruitSupplierSaveReq implements Serializable {

    /**
     * 招募标题
     */
    @NotBlank(message = "招募标题不能为空")
    @Schema(description = "招募标题", required = true)
    private String recruitTitle;

    /**
     * 公告模板id
     */
    @Schema(description = "公告模板id")
    @NotNull(message = "需求部门不能为空")
    private Long reportTemplateId;

    /**
     * 需求部门id
     */
//    @NotNull(message = "需求部门不能为空")
    @Schema(description = "需求部门id", required = true)
    private Long demandDeptId;

    /**
     * 采购部门
     */
    @NotNull(message = "采购部门不能为空")
    @Schema(description = "采购部门", required = true)
    private Long purchaseDeptId;

    /**
     * 招募类型 MATERIALS（物资类） - ENGINEERING （工程类）
     */
    @NotNull(message = "招募类型不能为空")
    @Schema(description = "招募类型", required = true)
    private SrmRecruitSupplier.RecruitType recruitType;

    /**
     * 招募方式 DIRECTLY（直接报名） - PREQUALIFICATION （资格预审）
     */
    @NotNull(message = "招募方式不能为空")
    @Schema(description = "招募方式", required = true)
    private SrmRecruitSupplier.RecruitWay recruitWay;

    /**
     * 招募结束时间（为空则为长期招募）
     */
    @Schema(description = "招募结束时间")
    private LocalDateTime deadline;

    /**
     * 指定审批人id
     */
    @Schema(description = "指定审批人id")
    private Long auditPersonId;

    /**
     * 审批发起类型 AUTO（系统自动发起） - CUSTOM（指定审批人）
     */
    @Schema(description = "审批发起类型")
    @NotNull(message = "审批发起类型不能为空")
    private ApprovalType auditType;

    /**
     * 省
     */
    @Schema(description = "省")
    private String province;

    /**
     * 市
     */
    @Schema(description = "市")
    private String city;

    /**
     * 区
     */
    @Schema(description = "区")
    private String district;

    /**
     * 详细地址
     */
    @Schema(description = "详细地址")
    private String address;

    /**
     * 经营模式 MANUFACTURER（生产厂家） - DISTRIBUTION（经销批发） - BUSINESS（商业服务） - AGENCY（招商代理） - OTHER（其它）
     */
    @Schema(description = "经营模式")
    private String operationMode;

    /**
     * 注册资金 （万元以上）
     */
    @Schema(description = "注册资金")
    private Long registeredCapital;

    /**
     * 补充说明
     */
    @Schema(description = "补充说明")
    private String remark;

    /**
     * 附件
     */
    @Schema(description = "附件")
    private String attachment;

    /**
     * 指定终止审批人id
     */
    @Schema(description = "指定终止审批人id")
    private Long terminationAuditPersonId;

    /**
     * 终止审批发起类型 AUTO（系统自动发起） - CUSTOM（指定审批人）
     */
    @Schema(description = "终止审批发起类型")
    private ApprovalType terminationAuditType;
    /**
     * 招募公告内容
     */
    @Schema(description = "招募公告内容")
    private ApprovalType recruitContext;

    /**
     * 招募小组成员列表
     */
    @Valid
    @Schema(description = "招募小组成员列表")
    private List<MemberInfo> memberList;

    /**
     * 物资清单列表
     */
    @Valid
    @Schema(description = "物资清单列表")
    private List<MaterialInfo> materialList;

    /**
     * 资质要求列表
     */
    @Valid
    @Schema(description = "资质要求列表")
    private List<CertificateInfo> certificateList;

    /**
     * 合作媒体列表
     */
    @Valid
    @Schema(description = "合作媒体列表")
    private List<MediaInfo> mediaList;

    /**
     * 招募小组成员信息
     */
    @Data
    @Schema(description = "招募小组成员信息")
    public static class MemberInfo implements Serializable {
        /**
         * 角色
         */
        @NotNull(message = "成员角色不能为空")
        @Schema(description = "角色", required = true)
        private RecruitMemberRoleEnum role;

        /**
         * 用户ID
         */
        @NotNull(message = "用户ID不能为空")
        @Schema(description = "用户ID", required = true)
        private Long userId;

        /**
         * 联系电话
         */
        @Schema(description = "联系电话")
        @NotNull(message = "联系电话不能为空")
        private String contactPhone;

        /**
         * 部门ID
         */
        @Schema(description = "部门ID")
        private Long deptId;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 物资清单信息
     */
    @Data
    @Schema(description = "物资清单信息")
    public static class MaterialInfo implements Serializable {
        /**
         * 招募类型
         */
//        @NotBlank(message = "招募类型不能为空")
        @Schema(description = "招募类型")
        private SrmRecruitSupplier.RecruitType recruitType;

        /**
         * 物料ID
         */
        @Schema(description = "物料ID")
        private Long materialId;

        /**
         * 物料编码
         */
        @Schema(description = "物料编码")
        private String materialCode;

        /**
         * 规格/型号
         */
        @Schema(description = "规格/型号")
        private String spec;

        /**
         * 计量单位
         */
        @Schema(description = "计量单位")
        private String unit;

        /**
         * 物料名称
         */
        @Schema(description = "物料名称")
        private String materialName;

        /**
         * 备注
         */
        @Schema(description = "备注")
        private String remark;

        /**
         * 工程服务内容
         */
        @Schema(description = "工程服务内容")
        private String engineeringContext;


        /**
         * 牧场id
         */
        @Schema(description = "牧场id")
        private Long locationId;
        /**
         * 计划采购数量
         */
        @Schema(description = "计划采购数量")
        private Long planPurchaseQuantity;
        /**
         * 计划金额
         */
        @Schema(description = "计划金额")
        private BigDecimal planAmount;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 资质要求信息
     */
    @Data
    @Schema(description = "资质要求信息")
    public static class CertificateInfo implements Serializable {
        /**
         * 文件名称
         */
        @NotBlank(message = "文件名称不能为空")
        @Schema(description = "文件名称", required = true)
        private String documentName;
        /**
         * 补充说明
         */
        @Schema(description = "补充说明")
        private String remark;
        /**
         * 枚举表ID
         */
        @NotNull(message = "证件类型不能为空")
        @Schema(description = "枚举表ID", required = true)
        private Long baseCertificateId;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 合作媒体信息
     */
    @Data
    @Schema(description = "合作媒体信息")
    public static class MediaInfo implements Serializable {
        /**
         * 媒体id
         */
        @Schema(description = "媒体id")
        private Long mediaId;
        /**
         * 媒体名称
         */
        @NotBlank(message = "媒体名称不能为空")
        @Schema(description = "媒体名称", required = true)
        private String mediaName;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
