package com.ylz.saas.codegen.base_service_type_field.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 服务类型字段表
 *
 * <AUTHOR>
 * @date 2025-06-03 21:25:49
 */
@Data
public class BaseServiceTypeFieldResp implements Serializable {

    /**
  	 * 主键ID
   	 */
	@ExcelProperty(value = "主键ID")
    private Long id;

    /**
  	 * 组织ID
   	 */
	@ExcelProperty(value = "组织ID")
    private Long deptId;

    /**
  	 * 服务类型ID
   	 */
	@ExcelProperty(value = "服务类型ID")
    private Long serviceTypeId;

    /**
  	 * 字段编码
   	 */
	@ExcelProperty(value = "字段编码")
    private String fieldCode;

    /**
  	 * 字段名称
   	 */
	@ExcelProperty(value = "字段名称")
    private String fieldName;

    /**
  	 * 字段类型（string-字符串、int-整数、decimal-小数、date-日期、datetime-日期时间、enum-枚举）
   	 */
	@ExcelProperty(value = "字段类型（string-字符串、int-整数、decimal-小数、date-日期、datetime-日期时间、enum-枚举）")
    private String fieldType;

    /**
  	 * 字段长度
   	 */
	@ExcelProperty(value = "字段长度")
    private Integer fieldLength;

    /**
  	 * 是否必填（0-否、1-是）
   	 */
	@ExcelProperty(value = "是否必填（0-否、1-是）")
    private String isRequired;

    /**
  	 * 默认值
   	 */
	@ExcelProperty(value = "默认值")
    private String defaultValue;

    /**
  	 * 验证规则（JSON格式）
   	 */
	@ExcelProperty(value = "验证规则（JSON格式）")
    private String validationRule;

    /**
  	 * 枚举值（JSON格式，当field_type为enum时使用）
   	 */
	@ExcelProperty(value = "枚举值（JSON格式，当field_type为enum时使用）")
    private String enumValues;

    /**
  	 * 占位提示文本
   	 */
	@ExcelProperty(value = "占位提示文本")
    private String placeholder;

    /**
  	 * 排序
   	 */
	@ExcelProperty(value = "排序")
    private Integer sort;

    /**
  	 * 是否应用到采购计划子表（YES-是、NO-否）
   	 */
	@ExcelProperty(value = "是否应用到采购计划子表（YES-是、NO-否）")
    private String applyToPlan;

    /**
  	 * 是否应用到采购立项子表（YES-是、NO-否）
   	 */
	@ExcelProperty(value = "是否应用到采购立项子表（YES-是、NO-否）")
    private String applyToProject;

    /**
  	 * 是否应用到采购报价子表（YES-是、NO-否）
   	 */
	@ExcelProperty(value = "是否应用到采购报价子表（YES-是、NO-否）")
    private String applyToQuote;

    /**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
	@ExcelProperty(value = "状态（DISABLED-禁用、ENABLED-启用）")
    private String status;

    /**
  	 * 创建人名称
   	 */
	@ExcelProperty(value = "创建人名称")
    private String createByName;

    /**
  	 * 修改人名称
   	 */
	@ExcelProperty(value = "修改人名称")
    private String updateByName;








}