package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ylz.saas.common.core.util.TenantTable;
import com.ylz.saas.enums.ApprovalType;
import com.ylz.saas.enums.ApproveStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 供应商招募公告表
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@TableName("srm_recruit_supplier")
@Schema(description = "供应商招募公告表")
@TenantTable
public class SrmRecruitSupplier {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "部门ID")
    private Long deptId;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "删除标识（0-正常、1-删除）")
    private Integer delFlag;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人ID")
    private Long createById;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人名称")
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人ID")
    private Long updateById;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人名称")
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 招募公告编码 规则ZMXM+年月日（yyyyMMdd）+0001
     */
    @Schema(description = "招募公告编码")
    private String recruitCode;

    /**
     * 招募标题
     */
    @Schema(description = "招募标题")
    private String recruitTitle;

    /**
     * 公告模板id
     */
    @Schema(description = "公告模板id")
    private Long reportTemplateId;

    /**
     * 需求部门id
     */
    @Schema(description = "需求部门id")
    private Long demandDeptId;

    /**
     * 采购部门
     */
    @Schema(description = "采购部门")
    private Long purchaseDeptId;

    /**
     * 招募类型 MATERIALS（物资类） - ENGINEERING （工程类）
     */
    @Schema(description = "招募类型")
    private RecruitType recruitType;

    /**
     * 招募方式 DIRECTLY（直接报名） - PREQUALIFICATION （资格预审）
     */
    @Schema(description = "招募方式")
    private RecruitWay recruitWay;

    /**
     * 招募结束时间（为空则为长期招募）
     */
    @Schema(description = "招募结束时间")
    private LocalDateTime deadline;

    /**
     * 指定审批人id
     */
    @Schema(description = "指定审批人id")
    private Long auditPersonId;

    /**
     * 审批发起类型 AUTO（系统自动发起） - CUSTOM（指定审批人）
     */
    @Schema(description = "审批发起类型")
    private ApprovalType auditType;

    /**
     * 省
     */
    @Schema(description = "省")
    private String province;

    /**
     * 市
     */
    @Schema(description = "市")
    private String city;

    /**
     * 区
     */
    @Schema(description = "区")
    private String district;

    /**
     * 详细地址
     */
    @Schema(description = "详细地址")
    private String address;

    /**
     * 经营模式 MANUFACTURER（生产厂家） - DISTRIBUTION（经销批发） - BUSINESS（商业服务） - AGENCY（招商代理） - OTHER（其它）
     */
    @Schema(description = "经营模式")
    private String operationMode;

    /**
     * 注册资金 （万元以上）
     */
    @Schema(description = "注册资金")
    private Long registeredCapital;

    /**
     * 补充说明
     */
    @Schema(description = "补充说明")
    private String remark;

    /**
     * 附件
     */
    @Schema(description = "附件")
    private String attachment;

    /**
     * 招募进程
     */
    @Schema(description = "招募进程")
    private Process process;

    /**
     * 审核状态
     */
    @Schema(description = "审核状态")
    private ApproveStatusEnum approvalStatus;

    /**
     * 指定终止审批人id
     */
    @Schema(description = "指定终止审批人id")
    private Long terminationAuditPersonId;

    /**
     * 终止审批发起类型 AUTO（系统自动发起） - CUSTOM（指定审批人）
     */
    @Schema(description = "终止审批发起类型")
    private ApprovalType terminationAuditType;

    /**
     * 入库审核状态
     */
    @Schema(description = "入库审核状态")
    private ApproveStatusEnum storeApprovalStatus;
    /**
     * 招募公告内容
     */
    @Schema(description = "招募公告内容")
    private ApprovalType recruitContext;


    @Getter
    @AllArgsConstructor
    public enum RecruitType {

        MATERIALS("物料类"),
        ENGINEERING("工程/服务类");

        private final String value;
    }

    @Getter
    @AllArgsConstructor
    public enum RecruitWay {

        DIRECTLY("直接报名"),
        PREQUALIFICATION("资格预审");

        private final String value;
    }

    @Getter
    @AllArgsConstructor
    public enum Process {

        NEW("招募新建"),
        PROCESSING("招募中"),
        FINISHED("招募完成"),
        TERMINATION("招募终止");

        private final String value;
    }
}
