package com.ylz.saas.req;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 在线报名
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@Schema(description = "在线报名请求类")
public class SrmRecruitSignUpReq implements Serializable {

    /**
     * 招募公告id
     */
    @Schema(description = "招募公告id")
    @NotNull(message = "招募公告id不能为空")
    private Long recruitId;

    /**
     * 企业名称
     */
    @Schema(description = "企业名称")
    private String enterpriseName;

    /**
     * 注册资本 （万元）
     */
    @Schema(description = "注册资本")
    private Long registerCapital;

    /**
     * 统一社会信用代码
     */
    @Schema(description = "统一社会信用代码")
    private String socialCreditCode;

    /**
     * 法人代表
     */
    @Schema(description = "法人代表")
    private String legalPerson;

    /**
     * 成立日期
     */
    @Schema(description = "成立日期")
    private LocalDate incorporateDate;

    /**
     * 注册地址
     */
    @Schema(description = "注册地址")
    private String registerAddress;

    /**
     * 联系人姓名
     */
    @Schema(description = "联系人姓名")
    private String contacts;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String contactsPhone;

    /**
     * 联系邮箱
     */
    @Schema(description = "联系邮箱")
    private String contactsEmail;

    /**
     * 说明
     */
    @Schema(description = "说明")
    private String remark;

    /**
     * 报名ip
     */
    @Schema(description = "报名ip")
    private String ip;
    /**
     * 供应商id
     */
    @Schema(description = "供应商id")
    private Long supplierId;
    /**
     * 资质材料
     */
    @Schema(description = "资质材料")
    private List<SignUpCertificate> signUpCertificates;

    @Data
    public static class SignUpCertificate{
        /**
         * 招募公告id
         */
        @Schema(description = "招募公告id")
        @JsonIgnore
        private Long recruitId;
        /**
         * 文件地址
         */
        @Schema(description = "文件地址")
        private String documentUrl;
        /**
         * 供应商招募资质要求表id
         */
        @Schema(description = "供应商招募资质要求表id")
        private Long supplierCertificateId;
//        /**
//         * 招募报名id
//         */
//        @Schema(description = "招募报名id")
//        private Long recruitSignUpId;


    }



    @Serial
    private static final long serialVersionUID = 1L;
}
