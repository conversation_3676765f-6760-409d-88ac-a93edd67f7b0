package com.ylz.saas.req;

import com.ylz.saas.enums.NoticeStatusEnum;
import com.ylz.saas.enums.SignatureStatusEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 中标通知书
 */
@Data
public class SrmTenderAwardNoticeReq {
    /**
     * 公告id
     */
    @NotNull(message = "公告id不能为空")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 中标公司信息
     */
    @Valid
    @NotNull(message = "邀请供应商不能为空")
    @Size(min = 1, message = "邀请供应商不能为空！")
    private List<BidCompany> bidCompanyList;

    /**
     * 中标公司
     */
    @Data
    public static class BidCompany {

        /**
         * 标段ID
         */
        private Long sectionId;

        /**
         * 租户供应商ID
         */
        private Long tenantSupplierId;

        /**
         * 供应商名称
         */
        private String supplierName;

        /**
         * 通知状态(UNSENT-未发送、SENT-已发送)
         */
        private NoticeStatusEnum noticeStatus;

        /**
         * 签章状态(UNSIGNED-未签章、SIGNED-已签章 签章中-SIGNING)
         */
        private SignatureStatusEnum signatureStatus;
    }

}