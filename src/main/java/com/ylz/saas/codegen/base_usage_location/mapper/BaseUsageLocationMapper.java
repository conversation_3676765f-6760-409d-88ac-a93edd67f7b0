package com.ylz.saas.codegen.base_usage_location.mapper;

import com.ylz.saas.codegen.base_usage_location.entity.BaseUsageLocationEntity;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BaseUsageLocationMapper extends SaasBaseMapper<BaseUsageLocationEntity> {


    void removeByIdsBaseUsageLocation(@Param("ids") List<Long> ids);
}