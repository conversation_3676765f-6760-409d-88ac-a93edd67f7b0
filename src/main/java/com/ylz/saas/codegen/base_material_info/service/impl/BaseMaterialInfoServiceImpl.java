package com.ylz.saas.codegen.base_material_info.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.admin.api.entity.SysDept;
import com.ylz.saas.admin.service.SysDeptService;
import com.ylz.saas.codegen.base_material_category.entity.BaseMaterialCategoryEntity;
import com.ylz.saas.codegen.base_material_category.service.BaseMaterialCategoryService;
import com.ylz.saas.codegen.base_material_info.entity.BaseMaterialInfoEntity;
import com.ylz.saas.codegen.base_material_info.enums.MaterialDateSourceEnum;
import com.ylz.saas.codegen.base_material_info_dept.entity.BaseMaterialInfoDeptEntity;
import com.ylz.saas.codegen.base_material_info_dept.service.BaseMaterialInfoDeptService;
import com.ylz.saas.codegen.base_material_info.excel.BaseMaterialInfoReq;
import com.ylz.saas.codegen.base_material_info.mapper.BaseMaterialInfoMapper;
import com.ylz.saas.codegen.base_material_info.param.BaseMaterialInfoParam;
import com.ylz.saas.codegen.base_material_info.service.BaseMaterialInfoService;
import com.ylz.saas.codegen.base_material_info.util.PinyinUtil;
import com.ylz.saas.codegen.base_material_info.vo.BaseMaterialInfoVo;
import com.ylz.saas.common.enums.CommonSwitchEnum;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.data.excel.ExcelAnalysisUtil;
import com.ylz.saas.common.data.excel.ExcelTemplateGenerator;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;
/**
 * 物料基础信息表
 *
 * <AUTHOR>
 * @date 2025-06-03 18:00:37
 */
@Service
@Slf4j
@AllArgsConstructor
public class BaseMaterialInfoServiceImpl extends ServiceImpl<BaseMaterialInfoMapper, BaseMaterialInfoEntity> implements BaseMaterialInfoService {

	private final SysDeptService sysDeptService;
	private final BaseMaterialInfoDeptService baseMaterialInfoDeptService;
	private final BaseMaterialCategoryService baseMaterialCategoryService;








	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveBaseMaterialInfo(BaseMaterialInfoVo baseMaterialInfoVo) {
		BaseMaterialInfoEntity baseMaterialInfo = new BaseMaterialInfoEntity();
        BeanUtil.copyProperties(baseMaterialInfoVo, baseMaterialInfo);
		if(StringUtils.isBlank(baseMaterialInfo.getMaterialSimpleName())){
			baseMaterialInfo.setMaterialSimpleName(baseMaterialInfo.getMaterialName());
		}
		if(StringUtils.isBlank(baseMaterialInfo.getStatus())){
			baseMaterialInfo.setStatus(CommonSwitchEnum.ENABLED.name());
		}
		String materialName = baseMaterialInfoVo.getMaterialName();
		String materialCode = baseMaterialInfoVo.getMaterialCode();
		BaseMaterialInfoEntity name = this.lambdaQuery().eq(BaseMaterialInfoEntity::getMaterialName, materialName).one();
		BaseMaterialInfoEntity code = this.lambdaQuery().eq(BaseMaterialInfoEntity::getMaterialCode, materialCode).one();

		ExceptionUtil.check(name != null, "500", "物料名称已存在，不可提交！");
		ExceptionUtil.check(code != null, "500", "物料编码已存在，不可提交！");

		// 保存物料基础信息
		boolean saveResult = save(baseMaterialInfo);

		// 保存组织关联关系
		if (saveResult && CollectionUtils.isNotEmpty(baseMaterialInfoVo.getDeptIds())) {
			baseMaterialInfoDeptService.saveMaterialDeptRelations(baseMaterialInfo.getId(), baseMaterialInfoVo.getDeptIds());
		}

		return saveResult;
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateBaseMaterialInfo(BaseMaterialInfoVo baseMaterialInfoVo) {
		BaseMaterialInfoEntity baseMaterialInfo = getById(baseMaterialInfoVo.getId());
		ExceptionUtil.check(baseMaterialInfo == null, "500", "物料基础信息表不存在");
		BeanUtil.copyProperties(baseMaterialInfoVo, baseMaterialInfo);

		// 更新物料基础信息
        boolean updateResult = updateById(baseMaterialInfo);

        // 更新组织关联关系
        if (updateResult) {
        	baseMaterialInfoDeptService.updateMaterialDeptRelations(baseMaterialInfo.getId(), baseMaterialInfoVo.getDeptIds());
        }

        return updateResult;
	}
	
	@Override
    public void template(HttpServletResponse response) {
		// 创建目录及导入模板文件
		isExcelExist();
		// 读取导入模板
		FileInputStream inputStream = null;
		ServletOutputStream outputStream = null;
		try {
			String oriFileName = "物料基础信息表导入模板";
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding("utf-8");
			String fileName = URLEncoder.encode(oriFileName, "UTF-8").replaceAll("\\+", "%20");
			response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
			Path result = ExcelTemplateGenerator.getResult();
			Path filePath = result.resolve("物料基础信息表baseMaterialInfo.xlsx");
			// 绝对路径
			File file = new File(filePath.toString());
			inputStream = new FileInputStream(file);

			// 获取输出流
			outputStream = response.getOutputStream();

			// 将输入流的内容写入到输出流中
			byte[] buffer = new byte[1024];
			int bytesRead;
			while ((bytesRead = inputStream.read(buffer)) != -1) {
				outputStream.write(buffer, 0, bytesRead);
			}

			// 确保输出流的数据完全写入
			outputStream.flush();
			log.info("模板下载成功");
		} catch (IOException e) {
			System.out.println("e = " + e.getMessage());
			log.info("模板下载失败");
		} finally {
			// 关闭流
			try {
				if (inputStream != null) {
					inputStream.close();
				}
				if (outputStream != null) {
					outputStream.close();
				}
			} catch (IOException e) {
				log.error("关闭流时发生错误: {}", e.getMessage());
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean switchStatus(Long id) {
		BaseMaterialInfoEntity baseExpert = getById(id);
		ExceptionUtil.check(baseExpert == null, "500", "物料不存在");
		if (CommonSwitchEnum.ENABLED.name().equals(baseExpert.getStatus())) {
			baseExpert.setStatus(CommonSwitchEnum.DISABLED.name());
		} else {
			baseExpert.setStatus(CommonSwitchEnum.ENABLED.name());
		}
		this.updateById(baseExpert);
		return true;
	}

	@Override
	@Transactional
	public void uploadData(MultipartFile file, HttpServletResponse response) {
		try {
			List<BaseMaterialInfoReq> importGoodsList = new ExcelAnalysisUtil<BaseMaterialInfoReq>()
					.readFile(file, BaseMaterialInfoReq.class, 1);
			if (CollectionUtils.isEmpty(importGoodsList)) {
				writeImportResponse(response, 1, "导入数据为空");
				return;
			}

			// 转换为实体对象并处理分类名称转ID
			List<BaseMaterialInfoEntity> baseMaterialInfoEntities = new ArrayList<>();
			Set<String> materialCodes = new HashSet<>(); // 用于检查导入数据中的重复编码
			
			for (BaseMaterialInfoReq req : importGoodsList) {
				BaseMaterialInfoEntity entity = new BaseMaterialInfoEntity();
				BeanUtil.copyProperties(req, entity);

				// 做字段的必传校验
				ExceptionUtil.check(StrUtil.isBlank(entity.getMaterialCode()), "500", "物料编码不能为空");
				ExceptionUtil.check(StrUtil.isBlank(entity.getMaterialName()), "500", "物料名称不能为空");
				ExceptionUtil.check(StrUtil.isBlank(req.getCategoryName()), "500", "物料所属分类名称不能为空");

				// 检查导入数据中是否有重复的物料编码
				ExceptionUtil.check(!materialCodes.add(entity.getMaterialCode()), "500", "导入数据中存在重复的物料编码：" + entity.getMaterialCode());

				// 根据分类名称查询分类ID
				BaseMaterialCategoryEntity category = baseMaterialCategoryService.getByCategoryName(req.getCategoryName());
				ExceptionUtil.check(category == null, "500", "物料分类名称[" + req.getCategoryName() + "]不存在，请检查分类名称是否正确");

				// 设置分类ID
				entity.setMaterialCategoryId(category.getId());
				entity.setDataSource(MaterialDateSourceEnum.BATCH_IMPORT.name());

				baseMaterialInfoEntities.add(entity);
			}

			// 检查数据库中是否已存在相同的物料编码
			List<String> materialCodeList = baseMaterialInfoEntities.stream()
				.map(BaseMaterialInfoEntity::getMaterialCode)
				.collect(Collectors.toList());
			
			List<BaseMaterialInfoEntity> existingMaterials = this.lambdaQuery()
				.in(BaseMaterialInfoEntity::getMaterialCode, materialCodeList)
				.list();
			
			if (CollectionUtils.isNotEmpty(existingMaterials)) {
				List<String> existingCodes = existingMaterials.stream()
					.map(BaseMaterialInfoEntity::getMaterialCode)
					.collect(Collectors.toList());
				ExceptionUtil.check(true, "500", "数据库中已存在以下物料编码，请检查：" + String.join(", ", existingCodes));
			}

			// 保存数据
			this.saveOrUpdateBatch(baseMaterialInfoEntities);
		} catch (IOException e) {
			log.info("文件解析失败");
			throw new RuntimeException(e);
		}
	}

	private void writeImportResponse(HttpServletResponse response, int code, String errorMsg) throws IOException {
		JSONObject obj = new JSONObject();
		response.setContentType("text/html;charset=utf-8");
		obj.put("code", code);
		obj.put("msg", errorMsg);
		PrintWriter writer = response.getWriter();
		writer.write(obj.toJSONString());
		writer.close();
	}

	@Override
	public Page<BaseMaterialInfoEntity> pageWithPinyinSearch(Page<BaseMaterialInfoEntity> page, BaseMaterialInfoParam param) {
		// 创建一个临时参数对象，清空物料名称和分类名称以避免重复查询
		BaseMaterialInfoParam tempParam = BeanUtil.copyProperties(param, BaseMaterialInfoParam.class);
		String originalMaterialName = tempParam.getMaterialName();
		String originalMaterialCategoryName = tempParam.getMaterialCategoryName();
		tempParam.setMaterialName(null);
		tempParam.setMaterialCategoryName(null);

		// 构建基础查询条件，确保不包含del_flag条件（XML中已处理）
		QueryWrapper<BaseMaterialInfoEntity> queryWrapper = buildSafeQueryWrapper(tempParam);

		// 使用新的联查方法
		Page<BaseMaterialInfoEntity> result = baseMapper.selectPageWithCategoryName(page, originalMaterialName, originalMaterialCategoryName, queryWrapper);

		// 如果有物料名称搜索且输入的是拼音（纯英文），进行拼音匹配
		if (StrUtil.isNotBlank(originalMaterialName) && originalMaterialName.trim().matches("^[a-zA-Z]+$")) {
			String searchKeyword = originalMaterialName.trim();
			// 获取所有数据进行拼音匹配
			QueryWrapper<BaseMaterialInfoEntity> baseWrapper = buildSafeQueryWrapper(tempParam);
			List<BaseMaterialInfoEntity> allData = baseMapper.selectListWithCategoryName(null, originalMaterialCategoryName, baseWrapper);
			List<BaseMaterialInfoEntity> pinyinMatched = allData.stream()
				.filter(entity -> {
					// 检查物料名称的拼音匹配
					boolean nameMatch = PinyinUtil.matches(entity.getMaterialName(), searchKeyword);
					// 检查物料简称的拼音匹配
					boolean simpleNameMatch = StrUtil.isNotBlank(entity.getMaterialSimpleName()) &&
						PinyinUtil.matches(entity.getMaterialSimpleName(), searchKeyword);
					return nameMatch || simpleNameMatch;
				})
				.collect(Collectors.toList());

			// 合并结果，去重
			Set<Long> existingIds = result.getRecords().stream()
				.map(BaseMaterialInfoEntity::getId)
				.collect(Collectors.toSet());

			List<BaseMaterialInfoEntity> finalRecords = new ArrayList<>(result.getRecords());
			pinyinMatched.stream()
				.filter(entity -> !existingIds.contains(entity.getId()))
				.forEach(finalRecords::add);

			// 重新分页
			int start = (int) ((page.getCurrent() - 1) * page.getSize());
			int end = Math.min(start + (int) page.getSize(), finalRecords.size());

			if (start < finalRecords.size()) {
				result.setRecords(finalRecords.subList(start, end));
			} else {
				result.setRecords(new ArrayList<>());
			}
			result.setTotal(finalRecords.size());
		}

		// 设置组织列表（分类名称和组织名称已经在SQL中联查了）
		setDeptLists(result.getRecords());
		// 设置顶级分类名称（需要根据分类名称计算顶级分类）
		setMaterialCategoryRootNames(result.getRecords());

		return result;
	}

	@Override
	public List<BaseMaterialInfoEntity> listWithDeptName(BaseMaterialInfoParam param) {
		// 创建一个临时参数对象，清空物料名称和分类名称以避免重复查询
		BaseMaterialInfoParam tempParam = BeanUtil.copyProperties(param, BaseMaterialInfoParam.class);
		String originalMaterialName = tempParam.getMaterialName();
		String originalMaterialCategoryName = tempParam.getMaterialCategoryName();
		tempParam.setMaterialName(null);
		tempParam.setMaterialCategoryName(null);

		// 构建基础查询条件，确保不包含del_flag条件（XML中已处理）
		QueryWrapper<BaseMaterialInfoEntity> queryWrapper = buildSafeQueryWrapper(tempParam);

		// 使用新的联查方法
		List<BaseMaterialInfoEntity> result = baseMapper.selectListWithCategoryName(originalMaterialName, originalMaterialCategoryName, queryWrapper);

		// 设置组织列表（分类名称和组织名称已经在SQL中联查了）
		setDeptLists(result);
		// 设置顶级分类名称
		setMaterialCategoryRootNames(result);

		// 映射 status 和 dataSource
		setStatusAndDataSourceNames(result);

		return result;
	}

	private static final Map<String, String> STATUS_MAP = Map.of(
			"ENABLED", "启用",
			"DISABLED", "禁用"
	);
	private static final Map<String, String> DATA_SOURCE_MAP = Map.of(
			"SINGLE_CREATE", "系统创建",
			"BATCH_IMPORT", "批量导入"
			// 其他映射
	);

	private void setStatusAndDataSourceNames(List<BaseMaterialInfoEntity> records) {
		for (BaseMaterialInfoEntity entity : records) {
			entity.setStatus(STATUS_MAP.getOrDefault(entity.getStatus(), entity.getStatus()));
			entity.setDataSource(DATA_SOURCE_MAP.getOrDefault(entity.getDataSource(), entity.getDataSource()));
		}
	}

	@Override
	public BaseMaterialInfoEntity getByIdWithDeptName(Long id) {
		BaseMaterialInfoEntity entity = getById(id);
		if (entity != null) {
			setDeptNames(Arrays.asList(entity));
			setDeptLists(Arrays.asList(entity));
			setMaterialCategoryRootNames(Arrays.asList(entity));
		}
		return entity;
	}

	/**
	 * 批量设置组织名称
	 * @param records 物料信息列表
	 */
	private void setDeptNames(List<BaseMaterialInfoEntity> records) {
		if (CollectionUtils.isEmpty(records)) {
			return;
		}

		// 收集所有的组织ID
		Set<Long> deptIds = records.stream()
			.map(BaseMaterialInfoEntity::getDeptId)
			.filter(Objects::nonNull)
			.collect(Collectors.toSet());

		if (CollectionUtils.isEmpty(deptIds)) {
			return;
		}

		// 批量查询组织信息
		List<SysDept> deptList = sysDeptService.listByIds(deptIds);
		Map<Long, String> deptIdToNameMap = deptList.stream()
			.collect(Collectors.toMap(SysDept::getDeptId, SysDept::getName, (existing, replacement) -> existing));

		// 设置组织名称
		records.forEach(record -> {
			if (record.getDeptId() != null) {
				String deptName = deptIdToNameMap.get(record.getDeptId());
				record.setDeptName(deptName);
			}
		});
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean newRemoveByIds(List<Long> idList) {
		if (CollectionUtils.isEmpty(idList)) {
			return true;
		}

		// 转换为Long类型的ID列表
		List<Long> ids = idList.stream()
			.map(id -> Long.valueOf(id.toString()))
			.collect(Collectors.toList());

		// 删除组织关联关系
		baseMaterialInfoDeptService.deleteByMaterialIds(ids);

		// 删除物料基础信息
		return super.removeByIds(idList);
	}

	/**
	 * 批量设置分类名称和顶级分类名称
	 * @param records 物料信息列表
	 */
	private void setMaterialCategoryRootNames(List<BaseMaterialInfoEntity> records) {
		if (CollectionUtils.isEmpty(records)) {
			return;
		}

		// 收集所有的物料分类ID
		Set<Long> categoryIds = records.stream()
			.map(BaseMaterialInfoEntity::getMaterialCategoryId)
			.filter(Objects::nonNull)
			.collect(Collectors.toSet());

		if (CollectionUtils.isEmpty(categoryIds)) {
			return;
		}

		// 批量查询所有分类信息
		List<BaseMaterialCategoryEntity> allCategories = baseMaterialCategoryService.list();
		Map<Long, BaseMaterialCategoryEntity> categoryIdMap = allCategories.stream()
			.collect(Collectors.toMap(BaseMaterialCategoryEntity::getId,
				category -> category, (existing, replacement) -> existing));

		Map<String, BaseMaterialCategoryEntity> categoryCodeMap = allCategories.stream()
			.collect(Collectors.toMap(BaseMaterialCategoryEntity::getCategoryCode,
				category -> category, (existing, replacement) -> existing));

		// 为每个物料设置分类名称和顶级分类名称
		records.forEach(record -> {
			if (record.getMaterialCategoryId() != null) {
				BaseMaterialCategoryEntity category = categoryIdMap.get(record.getMaterialCategoryId());
				if (category != null) {
					// 设置分类名称
					record.setMaterialCategoryName(category.getCategoryName());
					// 设置顶级分类名称
					String rootCategoryName = findRootCategoryNameById(category, categoryCodeMap);
					record.setMaterialCategoryRoot(rootCategoryName);
				}
			}
		});
	}

	/**
	 * 查找顶级分类名称（根据分类编码）
	 * @param categoryCode 分类编码
	 * @param categoryMap 分类映射
	 * @return 顶级分类名称
	 */
	private String findRootCategoryName(String categoryCode, Map<String, BaseMaterialCategoryEntity> categoryMap) {
		BaseMaterialCategoryEntity current = categoryMap.get(categoryCode);
		if (current == null) {
			return null;
		}

		// 向上查找直到找到根节点
		while (current != null && StrUtil.isNotBlank(current.getParentCategoryCode())) {
			BaseMaterialCategoryEntity parent = categoryMap.get(current.getParentCategoryCode());
			if (parent != null) {
				current = parent;
			} else {
				break;
			}
		}

		return current != null ? current.getCategoryName() : null;
	}

	/**
	 * 查找顶级分类名称（根据分类实体）
	 * @param category 分类实体
	 * @param categoryCodeMap 分类编码映射
	 * @return 顶级分类名称
	 */
	private String findRootCategoryNameById(BaseMaterialCategoryEntity category, Map<String, BaseMaterialCategoryEntity> categoryCodeMap) {
		if (category == null) {
			return null;
		}

		BaseMaterialCategoryEntity current = category;
		// 向上查找直到找到根节点
		while (current != null && StrUtil.isNotBlank(current.getParentCategoryCode())) {
			BaseMaterialCategoryEntity parent = categoryCodeMap.get(current.getParentCategoryCode());
			if (parent != null) {
				current = parent;
			} else {
				break;
			}
		}

		return current != null ? current.getCategoryName() : null;
	}

	/**
	 * 批量设置组织列表
	 * @param records 物料信息列表
	 */
	private void setDeptLists(List<BaseMaterialInfoEntity> records) {
		if (CollectionUtils.isEmpty(records)) {
			return;
		}

		// 收集所有的物料ID
		List<Long> materialIds = records.stream()
			.map(BaseMaterialInfoEntity::getId)
			.filter(Objects::nonNull)
			.collect(Collectors.toList());

		if (CollectionUtils.isEmpty(materialIds)) {
			return;
		}

		// 批量查询组织关联信息
		Map<Long, List<BaseMaterialInfoEntity.DeptInfo>> materialDeptMap = new HashMap<>();
		for (Long materialId : materialIds) {
			List<BaseMaterialInfoDeptEntity> deptEntities = baseMaterialInfoDeptService.getDeptsByMaterialId(materialId);
			List<BaseMaterialInfoEntity.DeptInfo> deptInfos = deptEntities.stream()
				.map(deptEntity -> {
					BaseMaterialInfoEntity.DeptInfo deptInfo = new BaseMaterialInfoEntity.DeptInfo();
					deptInfo.setDeptId(deptEntity.getDeptId());
					deptInfo.setDeptName(deptEntity.getDeptName());
					return deptInfo;
				})
				.collect(Collectors.toList());
			materialDeptMap.put(materialId, deptInfos);
		}

		// 设置组织列表
		records.forEach(record -> {
			List<BaseMaterialInfoEntity.DeptInfo> deptList = materialDeptMap.get(record.getId());
			record.setDeptList(deptList != null ? deptList : new ArrayList<>());
		});
	}

	/**
	 * 构建安全的QueryWrapper，避免与XML中的条件冲突
	 * @param param 查询参数
	 * @return 安全的QueryWrapper
	 */
	private QueryWrapper<BaseMaterialInfoEntity> buildSafeQueryWrapper(BaseMaterialInfoParam param) {
		// 处理物料分类层级查询
		processMaterialCategoryHierarchy(param);

		// 手动构建QueryWrapper，避免使用可能产生冲突的toWrapper方法
		QueryWrapper<BaseMaterialInfoEntity> queryWrapper = new QueryWrapper<>();

		// 手动添加查询条件，排除del_flag（XML中已处理）
		if (param.getId() != null) {
			queryWrapper.like("id", param.getId());
		}
		if (param.getCreateById() != null) {
			queryWrapper.eq("create_by_id", param.getCreateById());
		}
		if (CollectionUtils.isNotEmpty(param.getDeptId())) {
			queryWrapper.in("dept_id", param.getDeptId());
		}
		if (param.getUpdateById() != null) {
			queryWrapper.like("update_by_id", param.getUpdateById());
		}
		if (StrUtil.isNotBlank(param.getMaterialCode())) {
			queryWrapper.like("material_code", param.getMaterialCode());
		}
		if (StrUtil.isNotBlank(param.getMaterialSimpleName())) {
			queryWrapper.like("material_simple_name", param.getMaterialSimpleName());
		}
		if (StrUtil.isNotBlank(param.getSpec())) {
			queryWrapper.like("spec", param.getSpec());
		}
		// 使用层级查询处理后的分类ID列表
		if (CollectionUtils.isNotEmpty(param.getMaterialCategoryIds())) {
			queryWrapper.in("material_category_id", param.getMaterialCategoryIds());
		}
		if (StrUtil.isNotBlank(param.getUnit())) {
			queryWrapper.like("unit", param.getUnit());
		}
		if (StrUtil.isNotBlank(param.getDataSource())) {
			queryWrapper.like("data_source", param.getDataSource());
		}
		if (CollectionUtils.isNotEmpty(param.getStatusList())) {
			queryWrapper.in("status", param.getStatusList());
		}
		if (StrUtil.isNotBlank(param.getRemark())) {
			queryWrapper.like("remark", param.getRemark());
		}
		if (StrUtil.isNotBlank(param.getCreateByName())) {
			queryWrapper.like("create_by_name", param.getCreateByName());
		}
		if (StrUtil.isNotBlank(param.getUpdateByName())) {
			queryWrapper.like("update_by_name", param.getUpdateByName());
		}
		if (StrUtil.isNotBlank(param.getCreateBy())) {
			queryWrapper.like("create_by", param.getCreateBy());
		}
		if (param.getCreateTimeStart() != null) {
			queryWrapper.ge("create_time", param.getCreateTimeStart());
		}
		if (param.getCreateTimeEnd() != null) {
			queryWrapper.le("create_time", param.getCreateTimeEnd());
		}
		if (StrUtil.isNotBlank(param.getUpdateBy())) {
			queryWrapper.like("update_by", param.getUpdateBy());
		}
		if (param.getUpdateTimeStart() != null) {
			queryWrapper.ge("update_time", param.getUpdateTimeStart());
		}
		if (param.getUpdateTimeEnd() != null) {
			queryWrapper.le("update_time", param.getUpdateTimeEnd());
		}
		if (param.getTenantId() != null) {
			queryWrapper.like("tenant_id", param.getTenantId());
		}

		// 不添加del_flag条件，因为XML中已经处理
		// 不添加ORDER BY，因为XML中已经处理

		return queryWrapper;
	}

	/**
	 * 处理物料分类层级查询
	 * 当传入materialCategoryId时，获取该分类及其所有子分类的ID
	 * @param param 查询参数
	 */
	private void processMaterialCategoryHierarchy(BaseMaterialInfoParam param) {
		if (param.getMaterialCategoryId() == null) {
			return;
		}

		try {
			// 根据分类ID查询分类信息
			BaseMaterialCategoryEntity category = baseMaterialCategoryService.getById(param.getMaterialCategoryId());
			if (category == null) {
				// 如果分类不存在，设置为空列表，这样查询不会返回任何结果
				param.setMaterialCategoryIds(new ArrayList<>());
				return;
			}

			// 获取该分类的所有子分类编码
			List<String> childrenCodes = baseMaterialCategoryService.getAllChildrenCodes(category.getCategoryCode());

			// 将分类编码转换为分类ID
			List<Long> categoryIds = new ArrayList<>();
			categoryIds.add(param.getMaterialCategoryId()); // 包含当前分类ID

			if (CollectionUtils.isNotEmpty(childrenCodes)) {
				// 批量查询子分类的ID
				List<BaseMaterialCategoryEntity> childCategories = baseMaterialCategoryService.lambdaQuery()
					.in(BaseMaterialCategoryEntity::getCategoryCode, childrenCodes)
					.list();

				List<Long> childCategoryIds = childCategories.stream()
					.map(BaseMaterialCategoryEntity::getId)
					.collect(Collectors.toList());

				categoryIds.addAll(childCategoryIds);
			}

			// 设置分类ID列表用于IN查询
			param.setMaterialCategoryIds(categoryIds);

		} catch (Exception e) {
			log.warn("处理物料分类层级查询时发生异常，分类ID: {}, 异常信息: {}", param.getMaterialCategoryId(), e.getMessage());
			// 发生异常时，只查询当前分类ID
			param.setMaterialCategoryIds(Arrays.asList(param.getMaterialCategoryId()));
		}
	}

	private static void isExcelExist() {
		try {
			Path result = ExcelTemplateGenerator.getResult();

			File file = new File(result.toString());
			if (file.exists()) {
				log.info("excel文件夹路径存在");
			} else {
				// 创建文件夹
				if (file.mkdirs()) {
					log.info("目录已创建: {}" , file);
				} else {
					log.info("创建目录失败: {}", file);
					throw new RuntimeException("创建目录失败: " + file);
				}
			}
			// 设置 Excel 文件的完整路径
			Path filePath = result.resolve("物料基础信息表baseMaterialInfo.xlsx");
			// 生成模板文件
			ExcelTemplateGenerator.createExcelTemplate(filePath.toString(),BaseMaterialInfoReq.class);
		} catch (UnsupportedEncodingException e) {
			log.info("编码失败");
		}
	}
}