package com.ylz.saas.vo;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 平台供应商信息表vo
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SrmPlatformSupplierInfoVo {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 供应商编号
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商简称
     */
    private String supplierShortName;

    /**
     * 供应商类型(MANUFACTURER-生产型供应商、TRADER-贸易型供应商、SERVICE-服务型供应商、LOGISTICS-物流承运商、PROJECT-工程建筑供应商、LABOR-劳务外包供应商、RENTAL-租赁服务供应商、OTHER-其他)
     */
    private String supplierType;

    /**
     * 企业性质(STATE_OWNED-国有企业、FOREIGN_INVESTED-外商投资、HONGKONG_MACAO_TAIWAN-港澳台投资、COLLECTIVE-集体所有制、JOINT_VENTURE-联营、PRIVATE-私营、INDIVIDUAL-个体工商户)
     */
    private String enterpriseNature;

    /**
     * 供应商来源(SRM_SYSTEM-SRM系统、SYSTEM_CREATE-系统创建、SUPPLIER_REGISTER-供应商注册)
     */
    private String supplierSource;

    /**
     * 审批状态(DRAFT-草稿、APPROVING-审批中、APPROVED-已审批、REJECTED-已驳回)
     */
    private String approvalStatus;

    /**
     * 统一社会信用代码
     */
    private String socialCreditCode;

    /**
     * 成立日期
     */
    private LocalDateTime establishDate;

    /**
     * 注册资本
     */
    private BigDecimal registeredCapital;

    /**
     * 法人代表
     */
    private String legalPerson;

    /**
     * 法人身份证号
     */
    private String legalPersonId;

    /**
     * 法人电话
     */
    private String legalPersonPhone;

    /**
     * 注册地址
     */
    private String registeredAddress;

    /**
     * 邮政编码
     */
    private String postalCode;

    /**
     * 传真
     */
    private String fax;

    /**
     * 年平均营业额
     */
    private BigDecimal annualRevenue;

    /**
     * 公司简介
     */
    private String companyProfile;

    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 当前用户部门是否与该供应商已建立关系（用于手动建立关系功能）
     * true-已建立关系，false-未建立关系
     */
    private Boolean hasRelation;
}
