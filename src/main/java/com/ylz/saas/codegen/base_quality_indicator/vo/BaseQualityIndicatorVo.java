package com.ylz.saas.codegen.base_quality_indicator.vo;

import com.ylz.saas.codegen.base_quality_indicator_attribute_value.entity.BaseQualityIndicatorAttributeValueEntity;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 质量指标表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:00
 */
@Data
public class BaseQualityIndicatorVo implements Serializable {

    /**
  	 * 主键ID
   	 */
    private Long id;

    /**
  	 * 组织ID
   	 */
    private Long deptId;

    /**
  	 * 指标编码
   	 */
    private String indicatorCode;

    /**
  	 * 指标名称
   	 */
    @NotBlank(message = "指标名称不能为空")
    private String indicatorName;

    /**
  	 * 适用物料编码
   	 */
    @NotBlank(message = "适用物料编码不能为空")
    private String materialCode;

    /**
  	 * 指标描述
   	 */
    private String description;

    /**
  	 * 生效日期
   	 */
    private LocalDate releaseDate;

    /**
  	 * 失效日期
   	 */
    private LocalDate expiryDate;

    /**
  	 * 是否适用于所有部门（0指定部门 1所有部门)
   	 */
    private String applyAllLocations;

    /**
  	 * 状态
   	 */
    private String status;

    /**
  	 * 创建人名称
   	 */
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    private String updateByName;

    /**
  	 * 创建人
   	 */
    private String createBy;

    /**
  	 * 创建时间
   	 */
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
    private String updateBy;

    /**
  	 * 修改时间
   	 */
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
    private Long createById;

    /**
  	 * 更新人ID
   	 */
    private Long updateById;
	/**
	* 子表集合:base_quality_indicator_attribute_value
	*/
	@Valid
	private List<BaseQualityIndicatorAttributeValueEntity> baseQualityIndicatorAttributeValueList;

	/**
	 * 使用部门ID集合（当applyAllLocations为0时使用）
	 */
	private List<Long> locationIds;
}