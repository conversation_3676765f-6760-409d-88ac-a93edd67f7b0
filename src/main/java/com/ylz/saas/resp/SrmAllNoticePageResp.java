package com.ylz.saas.resp;

import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.enums.InviteMethodEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 招标公告信息
 */
@Data
public class SrmAllNoticePageResp implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 寻源方式
     */
    private BaseServiceTypeFieldService.BuyWayEnum sourcingMethod;

    /**
     * 公共标题
     */
    private String noticeTitle;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 公开类别
     */
    private InviteMethodEnum publicCategory;

    /**
     * 公告作者
     */
    private String noticeAuthor;

    /**
     * 发布人
     */
    private String publisher;

    /**
     * 发布时间
     */
    private String publishTime;

    /**
     * 报名开始时间
     */
    private String registrationStartTime;

    /**
     * 报名截止时间
     */
    private String registrationEndTime;

    /**
     * 开标时间
     */
    private String bidOpeningTime;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 附件信息
     */
    private List<SrmProjectAttachment> attachmentList;

    /**
     * 是否有变更
     */
    private Boolean isChange;

    /**
     * 公告内容
     */
    private String noticeContent;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;
}