package com.ylz.saas.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/6/26 19:30
 * @Description
 */
@Data
public class QuoteLessThanThresholdSupplierQueryReq {

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long sectionId;

    /**
     * 报价轮次
     */
    @NotNull(message = "报价轮次不能为空")
    private Integer quoteRound;


}
