package com.ylz.saas.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.houbb.heaven.annotation.reflect.Param;
import com.ylz.saas.entity.SrmTenderContract;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylz.saas.req.SrmTenderContractQueryReq;
import com.ylz.saas.resp.SrmTenderContractQueryResp;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【srm_tender_contract(合同表)】的数据库操作Mapper
* @createDate 2025-07-21 17:26:10
* @Entity com.ylz.saas.entity.SrmTenderContract
*/
@Mapper
public interface SrmTenderContractMapper extends BaseMapper<SrmTenderContract> {

    Page<SrmTenderContractQueryResp> selectContractPage(Page<SrmTenderContractQueryResp> page, SrmTenderContractQueryReq req);

    Page<SrmTenderContractQueryResp> selectContractPageBySupplier(Page<SrmTenderContractQueryResp> page, SrmTenderContractQueryReq req);
    /**
     * 查询指定日期前缀的最大合同编号序列
     * @param datePrefix 日期前缀，格式为yyyyMMdd
     * @return 最大序列号
     */
    Integer selectMaxContractSequence(@Param("datePrefix") String datePrefix);

}




