package com.ylz.saas.codegen.base_service_type_field.param;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ylz.saas.codegen.base_service_type_field.entity.BaseServiceTypeFieldEntity;
import com.ylz.saas.common.data.mybatis.helper.Operator;
import com.ylz.saas.common.data.mybatis.helper.QueryWrapperHelper;
import com.ylz.saas.common.data.mybatis.wrapper.CriteriaField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * mybatis-plus的查询帮助类
 * 
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseServiceTypeFieldParam {

	/**
  	 * 主键ID
   	 */
 	@CriteriaField(field = "id", operator = Operator.LIKE)
    private Long id;
	/**
  	 * 组织ID
   	 */
 	@CriteriaField(field = "deptId", operator = Operator.LIKE)
    private Long deptId;
	/**
  	 * 服务类型ID
   	 */
 	@CriteriaField(field = "serviceTypeId", operator = Operator.LIKE)
    private Long serviceTypeId;
	/**
  	 * 字段编码
   	 */
 	@CriteriaField(field = "fieldCode", operator = Operator.LIKE)
    private String fieldCode;
	/**
  	 * 字段名称
   	 */
 	@CriteriaField(field = "fieldName", operator = Operator.LIKE)
    private String fieldName;
	/**
  	 * 字段类型（string-字符串、int-整数、decimal-小数、date-日期、datetime-日期时间、enum-枚举）
   	 */
 	@CriteriaField(field = "fieldType", operator = Operator.LIKE)
    private String fieldType;
	/**
  	 * 字段长度
   	 */
 	@CriteriaField(field = "fieldLength", operator = Operator.LIKE)
    private Integer fieldLength;
	/**
  	 * 是否必填（0-否、1-是）
   	 */
 	@CriteriaField(field = "isRequired", operator = Operator.LIKE)
    private String isRequired;
	/**
  	 * 默认值
   	 */
 	@CriteriaField(field = "defaultValue", operator = Operator.LIKE)
    private String defaultValue;
	/**
  	 * 验证规则（JSON格式）
   	 */
 	@CriteriaField(field = "validationRule", operator = Operator.LIKE)
    private String validationRule;
	/**
  	 * 枚举值（JSON格式，当field_type为enum时使用）
   	 */
 	@CriteriaField(field = "enumValues", operator = Operator.LIKE)
    private String enumValues;
	/**
  	 * 占位提示文本
   	 */
 	@CriteriaField(field = "placeholder", operator = Operator.LIKE)
    private String placeholder;
	/**
  	 * 排序
   	 */
 	@CriteriaField(field = "sort", operator = Operator.LIKE)
    private Integer sort;
	/**
  	 * 是否应用到采购计划子表（YES-是、NO-否）
   	 */
 	@CriteriaField(field = "applyToPlan", operator = Operator.LIKE)
    private String applyToPlan;
	/**
  	 * 是否应用到采购立项子表（YES-是、NO-否）
   	 */
 	@CriteriaField(field = "applyToProject", operator = Operator.LIKE)
    private String applyToProject;
	/**
  	 * 是否应用到采购报价子表（YES-是、NO-否）
   	 */
 	@CriteriaField(field = "applyToQuote", operator = Operator.LIKE)
    private String applyToQuote;
	/**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
 	@CriteriaField(field = "status", operator = Operator.LIKE)
    private String status;
	/**
  	 * 创建人名称
   	 */
 	@CriteriaField(field = "createByName", operator = Operator.LIKE)
    private String createByName;
	/**
  	 * 修改人名称
   	 */
 	@CriteriaField(field = "updateByName", operator = Operator.LIKE)
    private String updateByName;
	/**
  	 * 创建人
   	 */
 	@CriteriaField(field = "createBy", operator = Operator.LIKE)
    private String createBy;
	/**
  	 * 创建时间_开始
   	 */
	@CriteriaField(field = "createTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime createTimeStart;
	/**
  	 * 创建时间_结束
   	 */
    @CriteriaField(field = "createTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime createTimeEnd;
	/**
  	 * 修改人
   	 */
 	@CriteriaField(field = "updateBy", operator = Operator.LIKE)
    private String updateBy;
	/**
  	 * 修改时间_开始
   	 */
	@CriteriaField(field = "updateTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime updateTimeStart;
	/**
  	 * 修改时间_结束
   	 */
    @CriteriaField(field = "updateTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime updateTimeEnd;
	/**
  	 * 删除标识（0-正常、1-删除）
   	 */
	@CriteriaField(field = "delFlag", operator = Operator.EQ)
	private int delFlag;
	/**
  	 * 租户ID
   	 */
 	@CriteriaField(field = "tenantId", operator = Operator.LIKE)
    private Long tenantId;
	/**
  	 * 创建人ID
   	 */
 	@CriteriaField(field = "createById", operator = Operator.LIKE)
    private Long createById;
	/**
  	 * 更新人ID
   	 */
 	@CriteriaField(field = "updateById", operator = Operator.LIKE)
    private Long updateById;

    /**
    * 构建查询
    */
    public QueryWrapper<BaseServiceTypeFieldEntity> toWrapper() {
        QueryWrapper<BaseServiceTypeFieldEntity> queryWrapper = QueryWrapperHelper.fromBean(this, BaseServiceTypeFieldEntity.class)
            .orderByDesc("id");
       return queryWrapper;
    }
}