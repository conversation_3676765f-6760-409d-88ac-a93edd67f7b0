package com.ylz.saas.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylz.saas.entity.SrmTenderBidEvaluationScoring;
import com.ylz.saas.req.SrmTenderBidEvaluationScoringQueryReq;

import com.ylz.saas.resp.SrmTenderBidEvaluationScoringQueryResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 针对表【srm_tender_bid_evaluation_scoring(评标专家打分)】的数据库操作Mapper
 * <AUTHOR>
 * @createDate 2025-07-09
 */
@Mapper
public interface SrmTenderBidEvaluationScoringMapper extends BaseMapper<SrmTenderBidEvaluationScoring> {

    /**
     * 查询专家对某节点的项目打分情况
     * @param req 查询条件，userId必传
     * @return 打分情况列表
     */
    List<SrmTenderBidEvaluationScoringQueryResp> getExpertScoringByNode(@Param("req") SrmTenderBidEvaluationScoringQueryReq req);

    /**
     * 根据节点名称撤回专家评分结果
     * @param req 查询条件，包含userId、projectId、noticeId、sectionId、nodeName
     * @return 影响的行数
     */
    int revokeScoringResultByNode(@Param("req") SrmTenderBidEvaluationScoringQueryReq req);

    /**
     * 查询节点下所有专家对各个供应商的评审/评分详情
     * @param req 查询条件，包含projectId、noticeId、sectionId、nodeName
     * @return 专家评审/评分详情列表
     */
    List<SrmTenderBidEvaluationScoringQueryResp> getNodeEvaluationDetails(@Param("req") SrmTenderBidEvaluationScoringQueryReq req);


    int countExpertEvaluationCompleted(@Param("noticeId") Long noticeId);


    /**
     * 检查指定公告下的标段是否配置了评标标准
     * @param noticeId 招标公告ID
     * @return 是否有评标标准配置
     */
    boolean hasEvaluationStandards(@Param("noticeId") Long noticeId);

    /**
     * 检查指定评标委员会是否配置了评标标准
     * @param evaluationId 评标委员会ID
     * @return 是否有评标标准配置
     */
    boolean hasEvaluationStandardsByEvaluationId(@Param("evaluationId") Long evaluationId);

    /**
     * 检查指定标段是否已完成评标
     * @param sectionId 标段ID
     * @return 是否已完成评标
     */
    boolean isSectionEvaluationCompleted(@Param("sectionId") Long sectionId);

}
