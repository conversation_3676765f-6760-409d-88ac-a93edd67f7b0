package com.ylz.saas.codegen.base_payment_method_period.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.base_payment_method_period.entity.BasePaymentMethodPeriodEntity;
import com.ylz.saas.codegen.base_payment_method_period.vo.BasePaymentMethodPeriodVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

public interface BasePaymentMethodPeriodService extends IService<BasePaymentMethodPeriodEntity> {
	/**
     * 新增支付方式账期时间表
     * @param basePaymentMethodPeriodVo 支付方式账期时间表
     */
    boolean saveBasePaymentMethodPeriod(BasePaymentMethodPeriodVo basePaymentMethodPeriodVo);
	
	/**
     * 修改支付方式账期时间表
     * @param basePaymentMethodPeriodVo 支付方式账期时间表
     */
    boolean updateBasePaymentMethodPeriod(BasePaymentMethodPeriodVo basePaymentMethodPeriodVo);
	
	/**
     * 导入模板下载
     * @param response
     */
    void template(HttpServletResponse response);

    /**
     * 数据导入
     * @param file
     * @param response
     */
    void uploadData(MultipartFile file, HttpServletResponse response);
}