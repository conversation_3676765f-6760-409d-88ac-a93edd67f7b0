<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenderAwardNoticeMapper">


    <select id="getAwardNoticeList" resultType="com.ylz.saas.entity.SrmTenderAwardNotice">
        select
        stan.*,
        ssi.contact_phone as contactPhone,
        ssi.contact_person as contactPerson
        from srm_tender_award_notice stan
        left join srm_tender_supplier_response ssi on stan.tenant_supplier_id = ssi.tenant_supplier_id and
        stan.project_id = ssi.project_id
        and stan.notice_id = ssi.notice_id and stan.section_id = ssi.section_id
        where stan.notice_id = #{noticeId}
        and stan.project_id = #{projectId}
        and stan.notice_status = 'UNSENT'
        <if test="supplierIdList !=null and supplierIdList.size() > 0">
            and stan.tenant_supplier_id in
            <foreach item="item" collection="supplierIdList" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="sectionIdList !=null and sectionIdList.size() > 0">
            and stan.section_id in
            <foreach item="item" collection="sectionIdList" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

    </select>
</mapper>
