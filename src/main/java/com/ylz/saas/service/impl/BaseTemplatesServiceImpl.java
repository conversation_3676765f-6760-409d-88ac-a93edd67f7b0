package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ylz.saas.entity.BaseTemplates;
import com.ylz.saas.req.BaseAnnouncementTemplateAddReq;
import com.ylz.saas.req.BaseAnnouncementTemplateUpdateReq;
import com.ylz.saas.resp.BaseAnnouncementTemplateQueryResp;
import com.ylz.saas.service.BaseTemplatesHistoryService;
import com.ylz.saas.service.BaseTemplatesService;
import com.ylz.saas.mapper.BaseTemplatesMapper;
import org.anyline.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ylz.saas.enums.TemplateStatusEnum;

/**
* <AUTHOR>
* @description 针对表【base_templates(模板表)】的数据库操作Service实现
* @createDate 2025-06-11 13:07:56
*/
@Service
public class BaseTemplatesServiceImpl extends ServiceImpl<BaseTemplatesMapper, BaseTemplates>
    implements BaseTemplatesService{
    @Autowired
    private ObjectMapper objectMapper;

    private final BaseTemplatesHistoryService templatesHistoryService;

    @Autowired
    public BaseTemplatesServiceImpl(BaseTemplatesHistoryService templatesHistoryService) {
        this.templatesHistoryService = templatesHistoryService;
    }
    @Override
    public Page<BaseAnnouncementTemplateQueryResp> query(Page<BaseAnnouncementTemplateQueryResp> page, String searchKey, String type) {

        return baseMapper.selectByType(page, searchKey, type);
    }

    @Override
    public boolean logicDeleteTemplate(Long id) {
        return baseMapper.update(null,
                new LambdaUpdateWrapper<BaseTemplates>()
                        .eq(BaseTemplates::getId, id)
                        .set(BaseTemplates::getDelFlag, 1)
        ) > 0;
    }

    @Override
    public boolean addTemplate(BaseAnnouncementTemplateAddReq req) {
        // 1. 校验模板名称是否已存在
        LambdaQueryWrapper<BaseTemplates> nameWrapper = new LambdaQueryWrapper<>();
        nameWrapper.eq(BaseTemplates::getTemplateName, req.getTemplateName())
                .eq(BaseTemplates::getTemplateSceneId, req.getTemplateSceneId());

        if (baseMapper.selectCount(nameWrapper) > 0) {
            throw new RuntimeException("模板名称已存在");
        }

        // 2. 构建 Entity 对象
        BaseTemplates template = new BaseTemplates();
        template.setTemplateName(req.getTemplateName());
        template.setTemplateSceneId(req.getTemplateSceneId());
        template.setType(req.getType());
        template.setDescription(req.getDescription());
        template.setIconUrl(req.getIconUrl());
        template.setIsDefault(req.getIsDefault());
        template.setContent(req.getContent());
        template.setStatus(TemplateStatusEnum.ENABLE.getValue()); // 默认启用

        template.setDelFlag(0);

        // 3. 生成编号code（年月日+3位流水号） 示例：20250101001
        String code = generateCode(req.getTemplateSceneId());
        template.setCode(code);

        // 4. 如果是默认模板，则取消同类型下的其他默认模板
        if (template.getIsDefault() == 1) {
            LambdaUpdateWrapper<BaseTemplates> unsetDefaultWrapper = new LambdaUpdateWrapper<>();
            unsetDefaultWrapper
                    .eq(BaseTemplates::getType, req.getType()) // 按 type 匹配
                    .set(BaseTemplates::getIsDefault, 0);      // 设置为非默认
            baseMapper.update(null, unsetDefaultWrapper);
        }

        return baseMapper.insert(template) > 0;
    }

    /**
     * 生成编号code（年月日+3位流水号） 示例：20250101001
     */
    private String generateCode(Long templateSceneId) {
        String prefix = java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.BASIC_ISO_DATE);

        // 查询当天最大编号
        LambdaQueryWrapper<BaseTemplates> codeWrapper = new LambdaQueryWrapper<>();
        codeWrapper.likeRight(BaseTemplates::getCode, prefix) // 查找以 prefix 开头的编号
                .eq(BaseTemplates::getTemplateSceneId, templateSceneId) // 按模板场景过滤
                .orderByDesc(BaseTemplates::getCode)//倒序
                .last("LIMIT 1");//取第一条

        BaseTemplates latest = baseMapper.selectOne(codeWrapper);
        int nextNum = 1;

        if (latest != null && latest.getCode().startsWith(prefix)) {
            String lastCode = latest.getCode();
            try {
                nextNum = Integer.parseInt(lastCode.substring(prefix.length())) + 1;
            } catch (NumberFormatException e) {
                nextNum = 1;
            }
        }

        return prefix + String.format("%03d", nextNum);
    }

    @Override
    public boolean updateTemplateWithHistory(BaseAnnouncementTemplateUpdateReq req) {
        // 1.校验 status 是否合法
        try {
            TemplateStatusEnum.fromValue(req.getStatus());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("无效的状态值: " + req.getStatus(), e);
        }
        // 2. 校验模板是否存在
        Long id = req.getId();
        if (id == null) {
            throw new IllegalArgumentException("模板ID不能为空");
        }

        BaseTemplates existing = baseMapper.selectById(id);
        if (existing == null) {
            throw new RuntimeException("模板不存在");
        }
        // 3. 校验模板名称是否重复（排除自己）
        LambdaQueryWrapper<BaseTemplates> nameWrapper = new LambdaQueryWrapper<>();
        nameWrapper.eq(BaseTemplates::getTemplateName, req.getTemplateName())
                .eq(BaseTemplates::getTemplateSceneId, existing.getTemplateSceneId())
                .ne(BaseTemplates::getId, id);

        if (baseMapper.selectCount(nameWrapper) > 0) {
            throw new RuntimeException("模板名称已存在");
        }

        // 4. 如果版本号不同，保存历史记录
        if (!req.getVersion().equals(existing.getVersion())) {
            templatesHistoryService.saveToHistory(existing);
        }
        // 5. 更新对象
        UpdateWrapper<BaseTemplates> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", req.getId())
                .set("template_name", req.getTemplateName())
                .set("type", req.getType())
                .set("description", req.getDescription())
                .set("icon_url", req.getIconUrl())
                .set("content", req.getContent())
                .set("status", req.getStatus())
                .set("version", req.getVersion())
                .set("is_default", req.getIsDefault());

        // 6. 如果是默认模板，则取消同类型下的其他默认模板
        if (req.getIsDefault() == 1) {
            LambdaUpdateWrapper<BaseTemplates> unsetDefaultWrapper = new LambdaUpdateWrapper<>();
            unsetDefaultWrapper
                    .eq(BaseTemplates::getType, req.getType()) // 按 type 匹配
                    .set(BaseTemplates::getIsDefault, 0);      // 设置为非默认
            baseMapper.update(null, unsetDefaultWrapper);
        }
        // 7. 执行更新
        return baseMapper.update(null, updateWrapper) > 0;

    }

    @Override
    public boolean changeTemplateStatus(Long id, String status) {
        // 校验 status 是否在枚举中
        try {
            TemplateStatusEnum.fromValue(status);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("无效的状态值: " + status, e);
        }
        // 查询模板是否存在
        BaseTemplates template = baseMapper.selectById(id);
        if (template == null) {
            throw new RuntimeException("模板不存在");
        }

        template.setStatus(status);
        return baseMapper.updateById(template) > 0;



    }



}




