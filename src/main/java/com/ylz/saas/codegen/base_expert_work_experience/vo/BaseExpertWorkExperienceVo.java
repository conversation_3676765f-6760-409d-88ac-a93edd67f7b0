package com.ylz.saas.codegen.base_expert_work_experience.vo;

import cn.hutool.json.JSONArray;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 专家职业履历表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:42:08
 */
@Data
public class BaseExpertWorkExperienceVo implements Serializable {

    /**
  	 * 主键ID
   	 */
    private Long id;

    /**
  	 * 组织ID
   	 */
    private Long deptId;

    /**
  	 * 附件
   	 */
    private JSONArray attachment;

    /**
  	 * 专家ID
   	 */
    private Long expertId;

    /**
  	 * 开始时间
   	 */
    private LocalDate startDate;

    /**
  	 * 截止时间
   	 */
    private LocalDate endDate;

    /**
  	 * 工作单位
   	 */
    private String company;

    /**
  	 * 职位
   	 */
    private String position;

    /**
  	 * 备注
   	 */
    private String remarks;

    /**
  	 * 创建人名称
   	 */
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    private String updateByName;

    /**
  	 * 创建人
   	 */
    private String createBy;

    /**
  	 * 创建时间
   	 */
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
    private String updateBy;

    /**
  	 * 修改时间
   	 */
    private LocalDateTime updateTime;

    /**
  	 * 租户ID
   	 */
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
    private Long createById;

    /**
  	 * 更新人ID
   	 */
    private Long updateById;
}