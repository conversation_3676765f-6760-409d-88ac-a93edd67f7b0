<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.BaseTemplatesHistoryMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.BaseTemplatesHistory">
            <id property="id" column="id" />
            <result property="tenantId" column="tenant_id" />
            <result property="deptId" column="dept_id" />
            <result property="templateId" column="template_id" />
            <result property="version" column="version" />
            <result property="createBy" column="create_by" />
            <result property="createByName" column="create_by_name" />
            <result property="createTime" column="create_time" />
            <result property="createById" column="create_by_id" />
            <result property="updateById" column="update_by_id" />
            <result property="updateBy" column="update_by" />
            <result property="updateByName" column="update_by_name" />
            <result property="updateTime" column="update_time" />
            <result property="content" column="content" />
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,template_id,version,create_by,create_by_name,
        create_time,create_by_id,update_by_id,update_by,update_by_name,
        update_time,content
    </sql>
</mapper>
