package com.ylz.saas.listener;

import com.alibaba.fastjson.JSON;
import com.ylz.saas.common.data.event.FlowEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.context.event.EventListener;

@Slf4j
@Component
public class AsyncEventListener {

    @EventListener
    public void handleFlowEvent(FlowEvent event) {
        log.info("AsyncEventListener--->handleFlowEvent:{}", JSON.toJSONString(event));
    }

}
