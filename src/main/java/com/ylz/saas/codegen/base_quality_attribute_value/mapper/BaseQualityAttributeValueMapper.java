package com.ylz.saas.codegen.base_quality_attribute_value.mapper;

import com.ylz.saas.codegen.base_quality_attribute_value.entity.BaseQualityAttributeValueEntity;
import com.ylz.saas.codegen.base_quality_attribute_value.vo.BaseQualityAttributeValueWithAttributeVo;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BaseQualityAttributeValueMapper extends SaasBaseMapper<BaseQualityAttributeValueEntity> {

    /**
     * 根据物料编码查询质量属性值列表（联查质量属性信息）
     * @param materialCode 物料编码
     * @param attributeName 质量属性名称（模糊查询）
     * @return 质量属性值列表（包含质量属性信息）
     */
    List<BaseQualityAttributeValueWithAttributeVo> selectQualityAttributeValueListByMaterialCode(
            @Param("materialCode") String materialCode,
            @Param("attributeName") String attributeName);

}