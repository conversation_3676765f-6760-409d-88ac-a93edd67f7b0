# 专家分类功能说明

## 功能概述

专家分类分为内部专家和外部专家，根据专家分类的不同，系统会采用不同的用户账号处理策略：

- **内部专家（INTERNAL）**：新增时关联已有的用户账号，来源于SysUser表
- **外部专家（EXTERNAL）**：新增时会新增一个用户账号

## 专家分类常量

```java
public class ExpertCategoryConstants {
    public static final String INTERNAL = "INTERNAL"; // 内部专家
    public static final String EXTERNAL = "EXTERNAL"; // 外部专家
}
```

## 接口变更

### 1. BaseExpertVo新增字段

为了支持外部专家新建用户账号，在`BaseExpertVo`中新增了以下字段：

```java
/**
 * 用户密码（外部专家新建账号时使用，如果不提供则使用默认密码123456）
 */
private String password;

/**
 * 用户昵称（外部专家新建账号时使用，如果不提供则使用姓名）
 */
private String nickname;

/**
 * 锁定标记（外部专家新建账号时使用，0-正常，9-锁定，默认为0）
 */
private String lockFlag;
```

### 2. 专家新增接口逻辑变更

`POST /baseExpert/save` 接口根据`expertCategory`字段进行不同处理：

#### 内部专家（expertCategory = "INTERNAL"）
- 根据`loginAccount`查询已有的SysUser账号
- 如果找不到对应账号，抛出异常
- 将专家信息与已有用户账号关联

#### 外部专家（expertCategory = "EXTERNAL"）
- 检查`loginAccount`是否已存在，如果存在则抛出异常
- 创建新的SysUser账号
- 设置用户信息（用户名、密码、姓名、昵称、手机号、邮箱等）
- 将专家信息与新创建的用户账号关联

## 请求示例

### 内部专家新增示例

```json
{
  "name": "张三",
  "phone": "***********",
  "expertCategory": "INTERNAL",
  "loginAccount": "zhangsan",
  "address": "北京市朝阳区",
  "email": "<EMAIL>",
  "expertType": "技术专家"
}
```

### 外部专家新增示例

```json
{
  "name": "李四",
  "phone": "***********",
  "expertCategory": "EXTERNAL",
  "loginAccount": "lisi",
  "address": "上海市浦东新区",
  "email": "<EMAIL>",
  "expertType": "业务专家",
  "password": "mypassword123",
  "nickname": "李专家",
  "lockFlag": "0"
}
```

## 注意事项

1. **expertCategory字段必填**：必须指定为"INTERNAL"或"EXTERNAL"
2. **内部专家**：loginAccount必须对应已存在的用户账号
3. **外部专家**：loginAccount不能与现有用户重复
4. **外部专家密码**：如果不提供password字段，系统将使用默认密码"123456"
5. **外部专家昵称**：如果不提供nickname字段，系统将使用name字段作为昵称
6. **锁定标记**：如果不提供lockFlag字段，默认为"0"（正常状态）

## 错误处理

- 不支持的专家分类：抛出异常提示支持的分类
- 内部专家找不到对应账号：抛出异常提示未找到用户
- 外部专家用户名重复：抛出异常提示用户名已存在
- 创建用户失败：抛出异常提示创建失败

## 日志记录

系统会记录外部专家用户账号的创建日志，包括用户名和用户ID，便于问题排查。
