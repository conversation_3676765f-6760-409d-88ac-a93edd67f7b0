<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenderNoticeChangeMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenderNoticeChange">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="noticeId" column="notice_id" jdbcType="BIGINT"/>
            <result property="changeType" column="change_type" jdbcType="VARCHAR"/>
            <result property="changeDigest" column="change_digest" jdbcType="VARCHAR"/>
            <result property="changeContent" column="change_content" jdbcType="VARCHAR"/>
            <result property="oldParams" column="old_params" jdbcType="VARCHAR"/>
            <result property="newParams" column="new_params" jdbcType="VARCHAR"/>
            <result property="changeTime" column="change_time" jdbcType="TIMESTAMP"/>
            <result property="changeBy" column="change_by" jdbcType="VARCHAR"/>
            <result property="changeByName" column="change_by_name" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateById" column="update_by_id" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>


</mapper>
