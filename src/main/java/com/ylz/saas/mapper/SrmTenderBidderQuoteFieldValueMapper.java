package com.ylz.saas.mapper;

import com.ylz.saas.common.data.datascope.annotation.DataPermission;
import com.ylz.saas.entity.SrmTenderBidderQuoteFieldValue;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【srm_tender_bidder_quote_field_value(投标报价字段值表)】的数据库操作Mapper
* @createDate 2025-06-13 14:57:59
* @Entity com.ylz.saas.entity.SrmTenderBidderQuoteFieldValue
*/
@Mapper
//@DataPermission(userEnabled = true, dataPermissionRuleList = "[{\"dataTypeCode\":\"PROJECT\",\"targetField\":\"project_id\",\"dataSourceField\":\"ID\"}]")
public interface SrmTenderBidderQuoteFieldValueMapper extends BaseMapper<SrmTenderBidderQuoteFieldValue> {

}




