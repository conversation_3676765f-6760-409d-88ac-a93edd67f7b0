package com.ylz.saas.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.codegen.base_agent_user.entity.BaseAgentUserEntity;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.common.data.mybatis.helper.QueryWrapperHelper;
import com.ylz.saas.entity.SrmPlatformSupplierInfo;
import com.ylz.saas.entity.SrmTenantSupplierInfo;
import com.ylz.saas.enums.SupplierApprovalStatus;
import com.ylz.saas.enums.SupplierSourceEnum;
import com.ylz.saas.service.*;
import com.ylz.saas.vo.*;
import com.ylz.saas.common.security.util.SecurityUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

import java.util.List;

/**
 * 供应商信息表
 *
 * <AUTHOR>
 * @Date 2025/6/11 11:45
 * @Description
 */
@RestController
@RequestMapping("/supplier")
@AllArgsConstructor
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
@Tag(name = "供应商管理", description = "供应商信息管理相关接口")
public class SrmPlatformSupplierController {
    private final SrmTenantSupplierInfoService srmTenantSupplierInfoService;
    private final SrmPlatformSupplierInfoService srmPlatformSupplierInfoService;
    private final SrmTenantSupplierContactService srmTenantSupplierContactService;
    private final SrmTenantSupplierCertificateService srmTenantSupplierCertificateService;
    private final SrmTenantSupplierBankAccountService srmTenantSupplierBankAccountService;
    private final SrmTenantSupplierMaterialService srmTenantSupplierMaterialService;

    /**
     * 启用/禁用供应商状态
     *
     * @param id 供应商ID
     * @return R<Boolean> 操作结果，true表示状态切换成功，false表示失败
     *
     * @apiNote
     * 1. 切换供应商的启用/禁用状态
     * 2. 如果当前状态为启用，则切换为禁用
     * 3. 如果当前状态为禁用，则切换为启用
     * 4. 只有已审批通过的供应商才能进行状态切换
     * 5. 状态切换会影响供应商在系统中的可用性
     * 6. 禁用的供应商无法参与采购流程
     */
    @Operation(summary = "启用/禁用", description = "启用/禁用")
    @PostMapping("/switch/{id}")
    public R switchStatus(@PathVariable("id") Long id) {
        return R.ok(srmTenantSupplierInfoService.switchStatus(id));
    }

    /**
     * 保存供应商完整信息（包含所有子表信息）
     *
     * @param supplierCompleteInfoVo 供应商完整信息VO，包含基本信息和所有子表信息
     * @return R<Boolean> 操作结果，true表示保存成功，false表示保存失败
     *
     * @apiNote
     * 1. 支持一次性保存供应商的所有信息（基本信息、联系人、资质、银行账户、主营物料）
     * 2. 支持新增、更新、删除操作的一体化处理
     * 3. 根据ID字段判断是新增还是更新操作
     * 4. 通过删除ID列表实现子表数据的删除
     * 5. 整个操作在事务中执行，保证数据一致性
     */
    @PostMapping("/saveCompleteInfo")
    @Operation(summary = "保存供应商完整信息", description = "一次性保存供应商的所有信息，支持新增、更新、删除操作")
    public R saveCompleteInfo(@Valid @RequestBody SupplierCompleteInfoVo supplierCompleteInfoVo) {
        supplierCompleteInfoVo.setSupplierSource(SupplierSourceEnum.SRM_SYSTEM.name());
        SrmTenantSupplierInfo srmTenantSupplierInfo = srmPlatformSupplierInfoService.saveSupplierCompleteInfo(supplierCompleteInfoVo);
        return R.ok(srmTenantSupplierInfo, "供应商完整信息保存成功");
    }


    /**
     * 查询供应商基础信息详情
     *
     * @param code 租户供应商ID
     * @return R<SupplierBasicInfoDetailVo> 供应商基础信息详情
     *
     * @apiNote
     * 1. 根据租户供应商ID查询供应商基础信息详情
     * 2. 用于编辑和回显saveBasicInfo接口保存的信息
     * 3. 包含平台供应商信息和租户供应商信息
     * 4. 包含关联部门信息列表
     * 5. 返回完整的供应商基础信息用于前端回显
     */
    @GetMapping("/getByCode")
    @Operation(summary = "查询供应商基础信息详情", description = "根据租户供应商ID查询供应商基础信息详情，用于编辑回显")
    public R<SupplierBasicInfoDetailVo> getByCode(@RequestParam("code") String code) {
        return R.ok(srmPlatformSupplierInfoService.getSupplierBasicInfoDetail(code));
    }


    /**
     * 查询供应商基础信息详情
     *
     * @param code 租户供应商ID
     * @return R<SupplierBasicInfoDetailVo> 供应商基础信息详情
     *
     * @apiNote
     * 1. 根据租户供应商ID查询供应商基础信息详情
     * 2. 用于编辑和回显saveBasicInfo接口保存的信息
     * 3. 包含平台供应商信息和租户供应商信息
     * 4. 包含关联部门信息列表
     * 5. 返回完整的供应商基础信息用于前端回显
     */
    @GetMapping("/getBasicInfo/{tenantSupplierCode}")
    @Operation(summary = "查询供应商基础信息详情", description = "根据租户供应商ID查询供应商基础信息详情，用于编辑回显")
    public R<SupplierBasicInfoDetailVo> getBasicInfo(@PathVariable("tenantSupplierCode") String code) {
        SupplierBasicInfoDetailVo result = srmPlatformSupplierInfoService.getSupplierBasicInfoDetail(code);
        return R.ok(result, "供应商基础信息查询成功");
    }

    /**
     * 根据当前登录人查询其为联系人的供应商
     * @return
     */
    @GetMapping("/getOwnSupplier")
    @Operation(summary = "根据当前登录人查询其为联系人的供应商", description = "根据当前登录人查询其为联系人的供应商，用于编辑回显")
    public R<SupplierBasicInfoDetailVo> getOwnSupplier() {
        SupplierBasicInfoDetailVo result = srmPlatformSupplierInfoService.getOwnSupplier();
        return R.ok(result, "根据当前登录人查询其为联系人的供应商");
    }
    // ========== 子表分页查询接口 ==========

    /**
     * 根据供应商ID分页查询联系人列表
     *
     * @param supplierId 供应商ID
     * @param page 分页参数，包含页码、页大小等信息
     * @return R<Page<SrmTenantSupplierContact>> 分页联系人列表
     *
     * @apiNote
     * 1. 分页查询指定供应商的联系人信息
     * 2. 支持自定义页码和页大小
     * 3. 按创建时间倒序排列
     * 4. 返回分页结果，包含总记录数、当前页数据等
     * 5. 包含联系人姓名、电话、身份证等完整信息
     */
    @PostMapping("/getContactsBySupplierIdPage/{supplierId}")
    @Operation(summary = "根据供应商ID分页查询联系人列表", description = "分页查询指定供应商的联系人信息")
    public R<Page> getContactsBySupplierIdPage(@PathVariable("supplierId") Long supplierId, @RequestBody Page page) {
        return R.ok(srmTenantSupplierContactService.getContactsBySupplierIdPage(supplierId, page));
    }

    /**
     * 根据供应商ID分页查询资质列表
     *
     * @param supplierId 供应商ID
     * @param page 分页参数，包含页码、页大小等信息
     * @return R<Page<SrmTenantSupplierCertificate>> 分页资质列表
     *
     * @apiNote
     * 1. 分页查询指定供应商的资质证书信息
     * 2. 支持自定义页码和页大小
     * 3. 按创建时间倒序排列
     * 4. 返回完整的证书信息，包括证书类型、名称、有效期等
     * 5. 只返回未删除的资质记录
     */
    @PostMapping("/getCertificatesBySupplierIdPage/{supplierId}")
    @Operation(summary = "根据供应商ID分页查询资质列表", description = "分页查询指定供应商的资质证书信息")
    public R<Page> getCertificatesBySupplierIdPage(@PathVariable("supplierId") Long supplierId, @RequestBody Page page) {
        return R.ok(srmTenantSupplierCertificateService.getCertificatesBySupplierIdPage(supplierId, page));
    }

    /**
     * 根据供应商ID分页查询银行账户列表
     *
     * @param supplierId 供应商ID
     * @param page 分页参数，包含页码、页大小等信息
     * @return R<Page<SrmTenantSupplierBankAccount>> 分页银行账户列表
     *
     * @apiNote
     * 1. 分页查询指定供应商的银行账户信息
     * 2. 支持自定义页码和页大小
     * 3. 按创建时间倒序排列
     * 4. 返回完整的银行账户信息，包括开户行、账号等
     * 5. 只返回未删除的银行账户记录
     */
    @PostMapping("/getBankAccountsBySupplierIdPage/{supplierId}")
    @Operation(summary = "根据供应商ID分页查询银行账户列表", description = "分页查询指定供应商的银行账户信息")
    public R<Page> getBankAccountsBySupplierIdPage(@PathVariable("supplierId") Long supplierId, @RequestBody Page page) {
        return R.ok(srmTenantSupplierBankAccountService.getBankAccountsBySupplierIdPage(supplierId, page));
    }

    /**
     * 根据供应商ID分页查询主营物料列表
     *
     * @param supplierId 供应商ID
     * @param page 分页参数，包含页码、页大小等信息
     * @return R<Page<SrmTenantSupplierMaterial>> 分页主营物料列表
     *
     * @apiNote
     * 1. 分页查询指定供应商的主营物料信息
     * 2. 支持自定义页码和页大小
     * 3. 按创建时间倒序排列
     * 4. 返回物料关联信息，包含物料名称、编码、规格等
     * 5. 通过关联查询获取物料的详细信息
     */
    @PostMapping("/getMaterialsBySupplierIdPage/{supplierId}")
    @Operation(summary = "根据供应商ID分页查询主营物料列表", description = "分页查询指定供应商的主营物料信息")
    public R<Page> getMaterialsBySupplierIdPage(@PathVariable("supplierId") Long supplierId, @RequestBody Page page) {
        return R.ok(srmTenantSupplierMaterialService.getMaterialsBySupplierIdPage(supplierId, page));
    }



    /**
     * 查询供应商信息分页（部门隔离）
     *
     * @param queryVo 查询条件，包含供应商名称、类型、企业性质、启用状态等筛选条件
     * @param pageParams 分页参数，包含页码、页大小等信息
     * @return R<Page<SrmTenantSupplierInfoVo>> 分页供应商信息列表
     *
     * @apiNote
     * 1. 查询当前租户下已审批通过的供应商信息
     * 2. 支持按供应商名称模糊查询
     * 3. 支持按供应商类型多选查询
     * 4. 支持按企业性质多选查询
     * 5. 支持按启用状态查询
     * 6. 按ID倒序排列
     * 7. 只返回审批状态为APPROVED的供应商
     * 8. 返回VO对象，包含供应商的基本信息
     * 9. 用于供应商管理页面的数据展示
     * 10. 根据当前用户的部门ID筛选有权限查看的供应商
     */
    @PostMapping("/supplierInfo/page" )
    @Operation(summary = "分页查询-我的租户供应商信息表", description = "分页查询供我的租户供应商信息表")
    public R<Page<SrmTenantSupplierInfoVo>>supplierInfoPage(@RequestBody SupplierQueryVo queryVo, Page<SrmTenantSupplierInfo> pageParams) {
        IPage<SrmTenantSupplierInfoVo> result = srmTenantSupplierInfoService.getMyTenantSupplierPage(queryVo, pageParams);
        return R.ok((Page<SrmTenantSupplierInfoVo>) result);
    }


    /**
     * 提交并保存供应商管理信息
     * @param supplierCompleteInfoVo
     * @return
     */
    @PostMapping("/submitSupplierInfo")
    @Operation(summary = "提交并保存供应商管理信息", description = "提交并保存供应商管理信息")
    public R<Boolean> submitSupplierInfo(@Valid @RequestBody SupplierCompleteInfoVo supplierCompleteInfoVo) {
        boolean result = srmPlatformSupplierInfoService.submitSupplierInfo(supplierCompleteInfoVo);
        return result ? R.ok(true, "供应商信息提交成功") : R.failed("供应商信息提交失败");
    }


    /**
     * 提交并保存注册供应商信息
     * @param supplierCompleteInfoVo
     * @return
     */
    @PostMapping("/submitRegisterSupplier")
    @Operation(summary = "提交并保存注册供应商信息", description = "提交并保存注册供应商信息")
    public R<Boolean> submitRegisterSupplier(@Valid @RequestBody SupplierCompleteInfoVo supplierCompleteInfoVo) {
        boolean result = srmPlatformSupplierInfoService.submitRegisterSupplier(supplierCompleteInfoVo);
        return result ? R.ok(true, "注册供应商信息提交成功") : R.failed("注册供应商信息提交失败");
    }

    /**
     * 查询注册来源的供应商
     *
     * @param queryVo 查询条件，包含供应商名称、类型、企业性质、启用状态等筛选条件
     * @param pageParams 分页参数，包含页码、页大小等信息
     * @return R<Page<SrmTenantSupplierInfoVo>> 分页供应商信息列表
     *
     * @apiNote
     * 1. 查询当前租户下所有状态的供应商信息
     * 2. 支持按供应商名称模糊查询
     * 3. 支持按供应商类型多选查询
     * 4. 支持按企业性质多选查询
     * 5. 支持按启用状态查询
     * 6. 按ID倒序排列
     * 7. 包含所有审批状态的供应商（草稿、审批中、已审批、已驳回）
     * 8. 返回VO对象，包含供应商的基本信息
     * 9. 用于供应商注册管理页面的数据展示
     */
    @PostMapping("/registerTenantSupplier/page" )
    @Operation(summary = "分页查询-注册租户供应商信息表", description = "分页查询-注册租户供应商信息表")
    public R<Page<SrmTenantSupplierInfoVo>> registerTenantSupplierPage(@RequestBody SupplierQueryVo queryVo, Page<SrmTenantSupplierInfo> pageParams) {
        IPage<SrmTenantSupplierInfoVo> result = srmTenantSupplierInfoService.getRegisterTenantSupplierPage(queryVo, pageParams);
        return R.ok((Page<SrmTenantSupplierInfoVo>) result);
    }


    /**
     * 分页查询租户下全部供应商（审核通过的）
     * 根据当前登录人部门ID，判断是否邀请过供应商，并回显供应商邀请状态
     * 只有未邀请的供应商显示邀请按钮（标识）
     *
     * @param queryVo 查询条件，包含供应商名称、类型、企业性质、启用状态等筛选条件
     * @param pageParams 分页参数
     * @return 分页供应商信息列表（含邀请状态）
     */
    @PostMapping("/tenantSupplier/page" )
    @Operation(summary = "分页查询-平台供应商信息表", description = "分页查询-平台供应商信息表，只返回审核通过的供应商，并标识邀请状态和是否显示邀请按钮")
    public R<Page<SrmTenantSupplierInfoVo>> tenantSupplierPage(@RequestBody SupplierQueryVo queryVo, Page<SrmTenantSupplierInfo> pageParams) {
        IPage<SrmTenantSupplierInfoVo> result = srmTenantSupplierInfoService.getTenantSupplierPageWithInviteStatus(queryVo, pageParams);
        return R.ok((Page<SrmTenantSupplierInfoVo>) result);
    }

//    /**
//     * 邀请供应商建立部门关联关系
//     *
//     * @param supplierInviteRequestVo 邀请供应商请求VO，包含供应商ID和部门关联信息
//     * @return R<Boolean> 操作结果，true表示邀请成功，false表示邀请失败
//     *
//     * @apiNote
//     * 1. 主动与tenantSupplierPage接口展示的供应商建立部门关联关系
//     * 2. 只能邀请已审核通过的供应商
//     * 3. 建立的关联关系初始状态为"邀请审核中"
//     * 4. 需要后续审核这个关联关系
//     * 5. 支持批量建立多个部门的关联关系
//     */
//    @PostMapping("/inviteSupplier")
//    @Operation(summary = "邀请供应商建立部门关联关系", description = "主动与审核通过的供应商建立部门关联关系，关联关系需要审核")
//    public R<Boolean> inviteSupplier(@Valid @RequestBody SupplierCompleteInfoVo supplierInviteRequestVo) {
//        boolean result = srmTenantSupplierInfoService.inviteSupplier(supplierInviteRequestVo);
//        return result ? R.ok(true, "供应商邀请成功") : R.failed("供应商邀请失败");
//    }

    /**
     * 邀请供应商并修改信息
     *
     * @param supplierCompleteInfoVo 供应商完整信息VO，包含基本信息、联系人、资质、银行账户、物料等
     * @return R<Boolean> 操作结果，true表示邀请并修改成功，false表示失败
     *
     * @apiNote
     * 1. 建立部门关联关系的同时允许修改供应商信息
     * 2. 只能邀请已审核通过的供应商
     * 3. 支持修改供应商的基本信息和子表信息（联系人、资质、银行账户、物料）
     * 4. 建立的关联关系初始状态为"邀请审核中"
     * 5. 预留审核流程，支持后续扩展
     * 6. 类似submitRegisterSupplier接口，但用于邀请场景
     */
    @PostMapping("/inviteSupplier")
    @Operation(summary = "邀请供应商并修改信息", description = "建立部门关联关系的同时允许修改供应商完整信息，并提交审核")
    public R<Boolean> inviteAndUpdateSupplier(@Valid @RequestBody SupplierCompleteInfoVo supplierCompleteInfoVo) {
        boolean result = srmPlatformSupplierInfoService.inviteAndUpdateSupplier(supplierCompleteInfoVo);
        return result ? R.ok(true, "邀请供应商并修改信息成功") : R.failed("邀请供应商并修改信息失败");
    }


}
