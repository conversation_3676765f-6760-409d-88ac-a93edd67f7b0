package com.ylz.saas.codegen.base_agent_org.param;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ylz.saas.codegen.base_agent_org.entity.BaseAgentOrgEntity;
import com.ylz.saas.common.data.mybatis.helper.Operator;
import com.ylz.saas.common.data.mybatis.helper.QueryWrapperHelper;
import com.ylz.saas.common.data.mybatis.wrapper.CriteriaField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * mybatis-plus的查询帮助类
 * 
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseAgentOrgParam {

	/**
  	 * 主键ID
   	 */
 	@CriteriaField(field = "id", operator = Operator.LIKE)
    private Long id;
	/**
  	 * 组织ID
   	 */
 	@CriteriaField(field = "deptId", operator = Operator.LIKE)
    private Long deptId;
	/**
	 * 社会统一信用代码
	 */
	@Schema(description="社会统一信用代码")
	private String socialCreditCode;
	/**
  	 * 代理机构编码
   	 */
 	@CriteriaField(field = "agentCode", operator = Operator.LIKE)
    private String agentCode;
	/**
  	 * 代理机构名称
   	 */
 	@CriteriaField(field = "agentName", operator = Operator.LIKE)
    private String agentName;
	/**
  	 * 省
   	 */
 	@CriteriaField(field = "province", operator = Operator.LIKE)
    private String province;
	/**
  	 * 市
   	 */
 	@CriteriaField(field = "city", operator = Operator.LIKE)
    private String city;
	/**
  	 * 区
   	 */
 	@CriteriaField(field = "district", operator = Operator.LIKE)
    private String district;
	/**
  	 * 详细地址
   	 */
 	@CriteriaField(field = "address", operator = Operator.LIKE)
    private String address;
	/**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
 	@CriteriaField(field = "status", operator = Operator.IN)
    private List<String> statusList;
	/**
  	 * 创建人名称
   	 */
 	@CriteriaField(field = "createByName", operator = Operator.LIKE)
    private String createByName;
	/**
  	 * 修改人名称
   	 */
 	@CriteriaField(field = "updateByName", operator = Operator.LIKE)
    private String updateByName;
	/**
  	 * 创建人
   	 */
 	@CriteriaField(field = "createBy", operator = Operator.LIKE)
    private String createBy;
	/**
  	 * 创建时间_开始
   	 */
	@CriteriaField(field = "createTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime createTimeStart;
	/**
  	 * 创建时间_结束
   	 */
    @CriteriaField(field = "createTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime createTimeEnd;
	/**
  	 * 修改人
   	 */
 	@CriteriaField(field = "updateBy", operator = Operator.LIKE)
    private String updateBy;
	/**
  	 * 修改时间_开始
   	 */
	@CriteriaField(field = "updateTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime updateTimeStart;
	/**
  	 * 修改时间_结束
   	 */
    @CriteriaField(field = "updateTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime updateTimeEnd;
	/**
  	 * 删除标识（0-正常、1-删除）
   	 */
	@CriteriaField(field = "delFlag", operator = Operator.EQ)
	private int delFlag;
	/**
  	 * 租户ID
   	 */
 	@CriteriaField(field = "tenantId", operator = Operator.LIKE)
    private Long tenantId;
	/**
  	 * 创建人ID
   	 */
 	@CriteriaField(field = "createById", operator = Operator.LIKE)
    private Long createById;
	/**
  	 * 更新人ID
   	 */
 	@CriteriaField(field = "updateById", operator = Operator.LIKE)
    private Long updateById;

    /**
    * 构建查询
    */
    public QueryWrapper<BaseAgentOrgEntity> toWrapper() {
        QueryWrapper<BaseAgentOrgEntity> queryWrapper = QueryWrapperHelper.fromBean(this, BaseAgentOrgEntity.class)
            .orderByDesc("id");
       return queryWrapper;
    }
}