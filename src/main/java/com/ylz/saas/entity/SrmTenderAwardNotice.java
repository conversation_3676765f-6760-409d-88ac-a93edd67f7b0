package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ylz.saas.common.core.util.TenantTable;
import com.ylz.saas.enums.NoticeStatusEnum;
import com.ylz.saas.enums.SignatureStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 中标通知书表
 * @TableName srm_tender_award_notice
 */
@TenantTable
@TableName(value ="srm_tender_award_notice")
@Data
public class SrmTenderAwardNotice {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 采购立项ID
     */
    private Long projectId;

    /**
     * 招标公告ID
     */
    private Long noticeId;

    /**
     * 标段ID
     */
    private Long sectionId;

    /**
     * 评标结果ID
     */
    private Long evaluationResultId;

    /**
     * 租户供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 通知书内容
     */
    private String noticeContent;

    /**
     * 通知时间
     */
    private LocalDateTime noticeTime;

    /**
     * 通知状态(UNSENT-未发送、SENT-已发送)
     */
    private NoticeStatusEnum noticeStatus;

    /**
     * 签章时间
     */
    private LocalDateTime signatureTime;
    /**
     * 签章状态(UNSIGNED-未签章、SIGNED-已签章)
     */
    private SignatureStatusEnum signatureStatus;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    private Integer delFlag;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createById;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updateById;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    /**
     * 供应商联系人电话
     */
    @TableField(exist = false)
    private String contactPhone;
    /**
     * 供应商联系人
     */
    @TableField(exist = false)
    private String contactPerson;
}