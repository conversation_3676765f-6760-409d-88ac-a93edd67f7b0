package com.ylz.saas.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 供应商物料信息VO
 *
 * <AUTHOR>
 * @Date 2025/6/11 11:45
 * @Description
 */
@Data
@Schema(description = "供应商物料信息VO")
public class SupplierMaterialInfoVo {

    /**
     * 物料关联ID（更新时必填，新增时为空）
     */
    @Schema(description = "物料关联ID（更新时必填，新增时为空）")
    private Long id;

    /**
     * 供应商ID（必填）
     */
    @Schema(description = "供应商ID", required = true)
    private Long supplierId;

    /**
     * 物料ID（必填）
     */
    @NotNull(message = "物料ID不能为空")
    @Schema(description = "物料ID", required = true)
    private Long materialId;
}
