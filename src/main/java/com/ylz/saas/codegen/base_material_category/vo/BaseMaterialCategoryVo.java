package com.ylz.saas.codegen.base_material_category.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 物料分类表
 *
 * <AUTHOR>
 * @date 2025-06-03 17:25:41
 */
@Data
public class BaseMaterialCategoryVo implements Serializable {

    /**
  	 * 创建人ID
   	 */
    private Long createById;

    /**
  	 * 主键ID
   	 */
    private Long id;

    /**
  	 * 组织ID
   	 */
    private Long deptId;

    /**
  	 * 更新人ID
   	 */
    private Long updateById;

    /**
  	 * 物料分类编码
   	 */
    private String categoryCode;

    /**
  	 * 物料分类名称
   	 */
    private String categoryName;

    /**
  	 * 上级物料分类编码
   	 */
    private String parentCategoryCode;

    /**
  	 * 上级物料分类名称
   	 */
    private String parentCategoryName;

    /**
  	 * 来源标识
   	 */
    private String dataSource;

    /**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
    private String status;

    /**
  	 * 创建人名称
   	 */
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    private String updateByName;

    /**
  	 * 创建人
   	 */
    private String createBy;

    /**
  	 * 创建时间
   	 */
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
    private String updateBy;

    /**
  	 * 修改时间
   	 */
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    private Long tenantId;
}