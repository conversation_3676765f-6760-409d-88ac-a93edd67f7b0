package com.ylz.saas.enums;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/6/11 14:51
 * @Description
 */
@AllArgsConstructor
@Getter
public enum SupplierApprovalStatus {

    WAIT_APPROVAL("待审核"),
    APPROVING("审核中"),
    APPROVED("审核通过"),
    REJECTED("审核不通过"),
    INVITE_APPROVING("邀请审核中"),
    INVITE_REJECTED("邀请审核不通过"),
    INVITE_APPROVED("邀请审核通过"),
NO_NEED_APPROVING("无需审核")
    ;

    private final String value;


    }
