package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmTenderBidderSupplierQuoteAgain;
import com.ylz.saas.service.SrmTenderBidderSupplierQuoteAgainService;
import com.ylz.saas.mapper.SrmTenderBidderSupplierQuoteAgainMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【srm_tender_bidder_supplier_quote_again(供应商重新报价表)】的数据库操作Service实现
* @createDate 2025-07-09 18:43:54
*/
@Service
public class SrmTenderBidderSupplierQuoteAgainServiceImpl extends ServiceImpl<SrmTenderBidderSupplierQuoteAgainMapper, SrmTenderBidderSupplierQuoteAgain>
    implements SrmTenderBidderSupplierQuoteAgainService{

}




