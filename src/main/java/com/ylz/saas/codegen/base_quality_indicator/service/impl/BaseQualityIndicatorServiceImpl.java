package com.ylz.saas.codegen.base_quality_indicator.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.codegen.base_quality_indicator.entity.BaseQualityIndicatorEntity;
import com.ylz.saas.codegen.base_quality_indicator.mapper.BaseQualityIndicatorMapper;
import com.ylz.saas.codegen.base_quality_indicator.param.BaseQualityIndicatorParam;
import com.ylz.saas.codegen.base_quality_indicator.service.BaseQualityIndicatorService;
import com.ylz.saas.codegen.base_quality_indicator.vo.BaseQualityIndicatorVo;
import com.ylz.saas.codegen.base_quality_indicator_attribute_value.entity.BaseQualityIndicatorAttributeValueEntity;
import com.ylz.saas.codegen.base_quality_indicator_attribute_value.service.BaseQualityIndicatorAttributeValueService;
import com.ylz.saas.codegen.base_quality_indicator_location.entity.BaseQualityIndicatorLocationEntity;
import com.ylz.saas.codegen.base_quality_indicator_location.service.BaseQualityIndicatorLocationService;
import com.ylz.saas.codegen.base_usage_location.entity.BaseUsageLocationEntity;
import com.ylz.saas.codegen.base_usage_location.service.BaseUsageLocationService;
import com.ylz.saas.codegen.base_quality_attribute_value.service.BaseQualityAttributeValueService;
import com.ylz.saas.codegen.base_quality_attribute_value.vo.BaseQualityAttributeValueVo;
import com.ylz.saas.codegen.base_material_info.entity.BaseMaterialInfoEntity;
import com.ylz.saas.codegen.base_material_info.service.BaseMaterialInfoService;
import com.ylz.saas.common.enums.CommonSwitchEnum;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.util.StrUtil;
import com.ylz.saas.admin.api.entity.SysDept;
import com.ylz.saas.admin.service.SysDeptService;

import java.util.*;
import java.util.stream.Collectors;
/**
 * 质量指标表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:00
 */
@Service
@Slf4j
public class BaseQualityIndicatorServiceImpl extends ServiceImpl<BaseQualityIndicatorMapper, BaseQualityIndicatorEntity> implements BaseQualityIndicatorService {

	@Autowired
	private BaseQualityIndicatorAttributeValueService baseQualityIndicatorAttributeValueService;

	@Autowired
	private BaseQualityIndicatorLocationService baseQualityIndicatorLocationService;

	@Autowired
	private BaseUsageLocationService baseUsageLocationService;
	
	@Autowired
	private BaseQualityAttributeValueService baseQualityAttributeValueService;
	
	@Autowired
	private BaseMaterialInfoService baseMaterialInfoService;
	
	@Autowired
	private SysDeptService sysDeptService;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveBaseQualityIndicator(BaseQualityIndicatorVo baseQualityIndicatorVo) {
		// 校验唯一性：同一个物料、牧场、组织只能存在一条质量指标
		validateUniqueQualityIndicator(baseQualityIndicatorVo.getMaterialCode(), 
				baseQualityIndicatorVo.getDeptId(), 
				baseQualityIndicatorVo.getTenantId(), 
				null);
		// 校验指标名称唯一性
		validateUniqueIndicatorName(baseQualityIndicatorVo.getIndicatorName(),
				baseQualityIndicatorVo.getDeptId(),
				baseQualityIndicatorVo.getTenantId(),
				null);
		
		BaseQualityIndicatorEntity baseQualityIndicator = new BaseQualityIndicatorEntity();
        BeanUtil.copyProperties(baseQualityIndicatorVo, baseQualityIndicator);
		save(baseQualityIndicator);
		if (CollectionUtils.isNotEmpty(baseQualityIndicatorVo.getBaseQualityIndicatorAttributeValueList())) {
			baseQualityIndicatorVo.getBaseQualityIndicatorAttributeValueList().forEach(baseQualityIndicatorAttributeValue -> {
				baseQualityIndicatorAttributeValue.setQualityIndicatorId(baseQualityIndicator.getId());
				
				// 如果attributeValueId为空，说明需要新增BaseQualityAttributeValueEntity
				if (StrUtil.isBlank(baseQualityIndicatorAttributeValue.getAttributeValueId())) {
					BaseQualityAttributeValueVo newAttributeValueVo = new BaseQualityAttributeValueVo();
					newAttributeValueVo.setAttributeId(Long.valueOf(baseQualityIndicatorAttributeValue.getAttributeId()));
					newAttributeValueVo.setValueContent(baseQualityIndicatorAttributeValue.getValueContent());
//					newAttributeValueVo.setMaterialCode(baseQualityIndicator.getMaterialCode());
					newAttributeValueVo.setRejectionStandard(baseQualityIndicatorAttributeValue.getRejectionStandard());
					newAttributeValueVo.setPenaltyStandard(baseQualityIndicatorAttributeValue.getPenaltyStandard());

					Long attributeValueId = baseQualityAttributeValueService.saveBaseQualityAttributeValue(newAttributeValueVo);

					// 更新attributeValueId为新插入记录的ID
					baseQualityIndicatorAttributeValue.setAttributeValueId(String.valueOf(attributeValueId));
				}
			});
			baseQualityIndicatorAttributeValueService.saveBatch(baseQualityIndicatorVo.getBaseQualityIndicatorAttributeValueList());
		}

		// 处理使用部门关联：当applyAllLocations为0时，保存使用部门关联
		if ("0".equals(baseQualityIndicatorVo.getApplyAllLocations()) && CollectionUtils.isNotEmpty(baseQualityIndicatorVo.getLocationIds())) {
			// 校验唯一索引：indicator_id + location_id + tenant_id
			validateUniqueConstraint(baseQualityIndicator.getId(), baseQualityIndicatorVo.getLocationIds(), baseQualityIndicator.getTenantId());

			List<BaseQualityIndicatorLocationEntity> locationEntities = baseQualityIndicatorVo.getLocationIds().stream()
					.map(locationId -> {
						BaseQualityIndicatorLocationEntity entity = new BaseQualityIndicatorLocationEntity();
						entity.setIndicatorId(baseQualityIndicator.getId());
						entity.setLocationId(locationId);
						return entity;
					})
					.toList();
			baseQualityIndicatorLocationService.saveBatch(locationEntities);
		}
        return Boolean.TRUE;
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateBaseQualityIndicator(BaseQualityIndicatorVo baseQualityIndicatorVo) {
		BaseQualityIndicatorEntity baseQualityIndicator = getById(baseQualityIndicatorVo.getId());
		ExceptionUtil.check(baseQualityIndicator == null, "500", "质量指标表不存在");
		
		// 校验唯一性：同一个物料、牧场、组织只能存在一条质量指标（更新时排除当前记录）
		validateUniqueQualityIndicator(baseQualityIndicatorVo.getMaterialCode(), 
				baseQualityIndicatorVo.getDeptId(), 
				baseQualityIndicatorVo.getTenantId(), 
				baseQualityIndicatorVo.getId());
		// 校验指标名称唯一性（排除自己）
		validateUniqueIndicatorName(baseQualityIndicatorVo.getIndicatorName(),
				baseQualityIndicatorVo.getDeptId(),
				baseQualityIndicatorVo.getTenantId(),
				baseQualityIndicatorVo.getId());
		
		List<BaseQualityIndicatorAttributeValueEntity> oldBaseQualityIndicatorAttributeValueList = Optional.ofNullable(baseQualityIndicatorAttributeValueService.list(
						Wrappers.<BaseQualityIndicatorAttributeValueEntity>lambdaQuery()
								.eq(BaseQualityIndicatorAttributeValueEntity::getQualityIndicatorId, baseQualityIndicator.getId())
				))
				.orElse(Collections.emptyList());

		List<BaseQualityIndicatorAttributeValueEntity> newBaseQualityIndicatorAttributeValueList = Optional.ofNullable(baseQualityIndicatorVo.getBaseQualityIndicatorAttributeValueList())
				.orElse(Collections.emptyList());

		Set<Long> newBaseQualityIndicatorAttributeValueIds = newBaseQualityIndicatorAttributeValueList.stream()
				.map(BaseQualityIndicatorAttributeValueEntity::getId)
				.filter(Objects::nonNull)
				.collect(Collectors.toSet());

		List<Long> idsBaseQualityIndicatorAttributeValueToDelete = oldBaseQualityIndicatorAttributeValueList.stream()
				.filter(old -> !newBaseQualityIndicatorAttributeValueIds.contains(old.getId()))
				.map(BaseQualityIndicatorAttributeValueEntity::getId)
				.collect(Collectors.toList());

		if (!idsBaseQualityIndicatorAttributeValueToDelete.isEmpty()) {
			baseQualityIndicatorAttributeValueService.removeByIds(idsBaseQualityIndicatorAttributeValueToDelete);
		}
		
		// 处理新的属性值列表，检查是否需要新增BaseQualityAttributeValueEntity
		for (BaseQualityIndicatorAttributeValueEntity baseQualityIndicatorAttributeValue : newBaseQualityIndicatorAttributeValueList) {
			baseQualityIndicatorAttributeValue.setQualityIndicatorId(baseQualityIndicator.getId());
			
			// 如果attributeValueId为空，说明需要新增BaseQualityAttributeValueEntity
			if (StrUtil.isBlank(baseQualityIndicatorAttributeValue.getAttributeValueId())) {
				BaseQualityAttributeValueVo newAttributeValueVo = new BaseQualityAttributeValueVo();
				newAttributeValueVo.setAttributeId(Long.valueOf(baseQualityIndicatorAttributeValue.getAttributeId()));
				newAttributeValueVo.setValueContent(baseQualityIndicatorAttributeValue.getValueContent());
//				newAttributeValueVo.setMaterialCode(baseQualityIndicator.getMaterialCode());
				newAttributeValueVo.setRejectionStandard(baseQualityIndicatorAttributeValue.getRejectionStandard());
				newAttributeValueVo.setPenaltyStandard(baseQualityIndicatorAttributeValue.getPenaltyStandard());

				Long attributeValueId = baseQualityAttributeValueService.saveBaseQualityAttributeValue(newAttributeValueVo);

				// 更新attributeValueId为新插入记录的ID
				baseQualityIndicatorAttributeValue.setAttributeValueId(String.valueOf(attributeValueId));
			}
		}
		
		baseQualityIndicatorAttributeValueService.saveOrUpdateBatch(baseQualityIndicatorVo.getBaseQualityIndicatorAttributeValueList());

		// 处理使用部门关联的更新
		// 先删除原有的使用部门关联
		baseQualityIndicatorLocationService.remove(Wrappers.<BaseQualityIndicatorLocationEntity>lambdaQuery()
				.eq(BaseQualityIndicatorLocationEntity::getIndicatorId, baseQualityIndicator.getId()));

		// 当applyAllLocations为0时，重新保存使用部门关联
		if ("0".equals(baseQualityIndicatorVo.getApplyAllLocations()) && CollectionUtils.isNotEmpty(baseQualityIndicatorVo.getLocationIds())) {
			// 校验唯一索引：indicator_id + location_id + tenant_id（更新时排除当前指标的关联）
			validateUniqueConstraintForUpdate(baseQualityIndicator.getId(), baseQualityIndicatorVo.getLocationIds(), baseQualityIndicator.getTenantId());

			List<BaseQualityIndicatorLocationEntity> locationEntities = baseQualityIndicatorVo.getLocationIds().stream()
					.map(locationId -> {
						BaseQualityIndicatorLocationEntity entity = new BaseQualityIndicatorLocationEntity();
						entity.setIndicatorId(baseQualityIndicator.getId());
						entity.setLocationId(locationId);
						return entity;
					})
					.toList();
			baseQualityIndicatorLocationService.saveBatch(locationEntities);
		}

		BeanUtil.copyProperties(baseQualityIndicatorVo, baseQualityIndicator);
        return updateById(baseQualityIndicator);
	}
	
	@Override
	public BaseQualityIndicatorEntity getByIdBaseQualityIndicator(Long id) {
		BaseQualityIndicatorEntity baseQualityIndicator = getById(id);
		if (baseQualityIndicator != null) {
			// 查询质量指标属性值
			baseQualityIndicator.setBaseQualityIndicatorAttributeValueList(baseQualityIndicatorAttributeValueService.list(Wrappers.<BaseQualityIndicatorAttributeValueEntity>lambdaQuery().eq(BaseQualityIndicatorAttributeValueEntity::getQualityIndicatorId, baseQualityIndicator.getId())));

			// 查询关联的部门名称列表
			if ("0".equals(baseQualityIndicator.getApplyAllLocations())) {
				List<BaseQualityIndicatorLocationEntity> locationRelations = baseQualityIndicatorLocationService.list(
						Wrappers.<BaseQualityIndicatorLocationEntity>lambdaQuery()
							.eq(BaseQualityIndicatorLocationEntity::getIndicatorId, baseQualityIndicator.getId())
				);
				if (CollectionUtils.isNotEmpty(locationRelations)) {
					List<Long> locationIds = locationRelations.stream()
							.map(BaseQualityIndicatorLocationEntity::getLocationId)
							.toList();
					List<SysDept> deptList = sysDeptService.lambdaQuery()
						.in(SysDept::getDeptId, locationIds)
						.list();
					List<String> deptNameList = deptList.stream()
						.map(SysDept::getName)
						.toList();
					baseQualityIndicator.setLocationNameList(deptNameList);
				}
			}
		}
		return baseQualityIndicator;
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean removeByIdsBaseQualityIndicator(List<Long> ids) {
		List<BaseQualityIndicatorEntity> baseQualityIndicator = lambdaQuery()
				.in(BaseQualityIndicatorEntity::getId, ids)
				.list();
		if (CollectionUtils.isNotEmpty(baseQualityIndicator)) {
			removeByIds(ids);
			List<Long> baseQualityIndicatorAttributeValueBaseQualityIndicatorId = baseQualityIndicator.stream().map(BaseQualityIndicatorEntity::getId).toList();
			// 删除质量指标属性值
			baseQualityIndicatorAttributeValueService.remove(Wrappers.<BaseQualityIndicatorAttributeValueEntity>lambdaQuery().in(BaseQualityIndicatorAttributeValueEntity::getQualityIndicatorId, baseQualityIndicatorAttributeValueBaseQualityIndicatorId));
			// 删除质量指标与使用部门的关联
			baseQualityIndicatorLocationService.remove(Wrappers.<BaseQualityIndicatorLocationEntity>lambdaQuery().in(BaseQualityIndicatorLocationEntity::getIndicatorId, baseQualityIndicatorAttributeValueBaseQualityIndicatorId));
		}
		return true;
	}

	/**
	 * 校验唯一索引：indicator_id + location_id + tenant_id
	 * @param indicatorId 质量指标ID
	 * @param locationIds 使用部门ID集合
	 * @param tenantId 租户ID
	 */
	private void validateUniqueConstraint(Long indicatorId, List<Long> locationIds, Long tenantId) {
		for (Long locationId : locationIds) {
			long count = baseQualityIndicatorLocationService.count(
					Wrappers.<BaseQualityIndicatorLocationEntity>lambdaQuery()
							.eq(BaseQualityIndicatorLocationEntity::getIndicatorId, indicatorId)
							.eq(BaseQualityIndicatorLocationEntity::getLocationId, locationId)
							.eq(BaseQualityIndicatorLocationEntity::getTenantId, tenantId)
			);
			if (count > 0) {
				ExceptionUtil.check(true, "500", "该质量指标已关联此使用部门，不能重复添加");
			}
		}
	}

	/**
	 * 校验唯一索引：indicator_id + location_id + tenant_id（更新时使用）
	 * 更新时由于已经删除了原有关联，所以直接校验是否存在相同的组合即可
	 * @param indicatorId 质量指标ID
	 * @param locationIds 使用部门ID集合
	 * @param tenantId 租户ID
	 */
	private void validateUniqueConstraintForUpdate(Long indicatorId, List<Long> locationIds, Long tenantId) {
		for (Long locationId : locationIds) {
			long count = baseQualityIndicatorLocationService.count(
					Wrappers.<BaseQualityIndicatorLocationEntity>lambdaQuery()
							.eq(BaseQualityIndicatorLocationEntity::getIndicatorId, indicatorId)
							.eq(BaseQualityIndicatorLocationEntity::getLocationId, locationId)
							.eq(BaseQualityIndicatorLocationEntity::getTenantId, tenantId)
			);
			if (count > 0) {
				ExceptionUtil.check(true, "500", "该质量指标已关联此使用部门，不能重复添加");
			}
		}
	}
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean switchStatus(Long id) {
		BaseQualityIndicatorEntity original = getById(id);
		ExceptionUtil.check(original == null, "500", "指标不存在");
		if (CommonSwitchEnum.ENABLED.name().equals(original.getStatus())) {
			original.setStatus(CommonSwitchEnum.DISABLED.name());
		} else {
			original.setStatus(CommonSwitchEnum.ENABLED.name());
		}
		this.updateById(original);
		return true;
	}

    @Override
    public Page pageInfo(Page page1, BaseQualityIndicatorParam param) {
        QueryWrapper<BaseQualityIndicatorEntity> queryWrapper = param.toWrapper();

        // 物料名称模糊查询逻辑（保留）
        if (StrUtil.isNotBlank(param.getMaterialName())) {
            List<String> materialCodes = baseMaterialInfoService.lambdaQuery()
                    .like(BaseMaterialInfoEntity::getMaterialName, param.getMaterialName())
                    .list()
                    .stream()
                    .map(BaseMaterialInfoEntity::getMaterialCode)
                    .filter(StrUtil::isNotBlank)
                    .toList();
            if (CollectionUtils.isEmpty(materialCodes)) {
                Page<BaseQualityIndicatorEntity> emptyPage = new Page<>(page1.getCurrent(), page1.getSize());
                emptyPage.setRecords(new ArrayList<>());
                emptyPage.setTotal(0);
                return emptyPage;
            }
            queryWrapper.in("material_code", materialCodes);
        }

        // 部门名称模糊查询逻辑
        if (StrUtil.isNotBlank(param.getLocationName())) {
            // 1. 查出所有匹配的部门ID
            List<SysDept> deptList = sysDeptService.lambdaQuery()
                    .like(SysDept::getName, param.getLocationName())
                    .list();
            List<Long> deptIds = deptList.stream().map(SysDept::getDeptId).toList();
            if (CollectionUtils.isEmpty(deptIds)) {
                // 没有匹配的部门，直接返回空
                Page<BaseQualityIndicatorEntity> emptyPage = new Page<>(page1.getCurrent(), page1.getSize());
                emptyPage.setRecords(new ArrayList<>());
                emptyPage.setTotal(0);
                return emptyPage;
            }

            // 2. 查出applyAllLocations=1的所有质量指标
            QueryWrapper<BaseQualityIndicatorEntity> allLocWrapper = param.toWrapper();
            allLocWrapper.eq("apply_all_locations", "1");
            List<BaseQualityIndicatorEntity> allLocList = list(allLocWrapper);

            // 3. 查出applyAllLocations=0且有关联的质量指标
            // 先查出所有匹配的indicatorId
            List<Long> indicatorIds = baseQualityIndicatorLocationService.lambdaQuery()
                    .in(BaseQualityIndicatorLocationEntity::getLocationId, deptIds)
                    .list()
                    .stream()
                    .map(BaseQualityIndicatorLocationEntity::getIndicatorId)
                    .distinct()
                    .toList();
            List<BaseQualityIndicatorEntity> partLocList = new ArrayList<>();
            if (!indicatorIds.isEmpty()) {
                QueryWrapper<BaseQualityIndicatorEntity> partLocWrapper = param.toWrapper();
                partLocWrapper.eq("apply_all_locations", "0");
                partLocWrapper.in("id", indicatorIds);
                partLocList = list(partLocWrapper);
            }

            // 4. 合并、去重
            Set<Long> idSet = new HashSet<>();
            List<BaseQualityIndicatorEntity> allList = new ArrayList<>();
            for (BaseQualityIndicatorEntity e : allLocList) {
                if (idSet.add(e.getId())) allList.add(e);
            }
            for (BaseQualityIndicatorEntity e : partLocList) {
                if (idSet.add(e.getId())) allList.add(e);
            }

            // 5. 分页
            int fromIndex = (int) ((page1.getCurrent() - 1) * page1.getSize());
            int toIndex = Math.min(fromIndex + (int) page1.getSize(), allList.size());
            List<BaseQualityIndicatorEntity> pageRecords = fromIndex < toIndex ? allList.subList(fromIndex, toIndex) : new ArrayList<>();
            Page<BaseQualityIndicatorEntity> resultPage = new Page<>(page1.getCurrent(), page1.getSize(), allList.size());
            resultPage.setRecords(pageRecords);

            // 批量设置物料名称和部门名称
            setMaterialNames(pageRecords);
            setDeptNames(pageRecords);
            setDeptNameList(pageRecords);
            return resultPage;
        }

        // 没有部门名称模糊查询时，走原有分页
        Page<BaseQualityIndicatorEntity> pageInfo = page(page1, queryWrapper);
        List<BaseQualityIndicatorEntity> records = pageInfo.getRecords();
        setMaterialNames(records);
        setDeptNames(records);
        setDeptNameList(records);
        return pageInfo;
    }
    
    /**
     * 批量设置物料名称
     * @param records 质量指标列表
     */
    private void setMaterialNames(List<BaseQualityIndicatorEntity> records) {
		if (CollectionUtils.isEmpty(records)) {
			return;
		}
		
		// 收集所有的物料编码
		List<String> materialCodes = records.stream()
				.map(BaseQualityIndicatorEntity::getMaterialCode)
				.filter(StrUtil::isNotBlank)
				.distinct()
				.toList();
		
		if (CollectionUtils.isEmpty(materialCodes)) {
			return;
		}
		
		// 批量查询物料信息
		List<BaseMaterialInfoEntity> materialInfoList = baseMaterialInfoService.lambdaQuery()
				.in(BaseMaterialInfoEntity::getMaterialCode, materialCodes)
				.list();
		
		// 构建物料编码到物料名称的映射
		Map<String, String> materialCodeToNameMap = materialInfoList.stream()
				.collect(Collectors.toMap(
						BaseMaterialInfoEntity::getMaterialCode,
						BaseMaterialInfoEntity::getMaterialName,
						(existing, replacement) -> existing
				));
		
		// 设置物料名称
		records.forEach(record -> {
			if (StrUtil.isNotBlank(record.getMaterialCode())) {
				String materialName = materialCodeToNameMap.get(record.getMaterialCode());
				record.setMaterialName(materialName);
			}
		});
	}

	/**
	 * 批量设置组织名称
	 * @param records 质量指标列表
	 */
	private void setDeptNames(List<BaseQualityIndicatorEntity> records) {
		if (CollectionUtils.isEmpty(records)) {
			return;
		}
		
		// 收集所有的组织ID
		List<Long> deptIds = records.stream()
				.map(BaseQualityIndicatorEntity::getDeptId)
				.filter(Objects::nonNull)
				.distinct()
				.toList();
		
		if (CollectionUtils.isEmpty(deptIds)) {
			return;
		}
		
		// 批量查询组织信息
		List<SysDept> deptList = sysDeptService.lambdaQuery()
				.in(SysDept::getDeptId, deptIds)
				.list();
		
		// 构建组织ID到组织名称的映射
		Map<Long, String> deptIdToNameMap = deptList.stream()
				.collect(Collectors.toMap(
						SysDept::getDeptId,
						SysDept::getName,
						(existing, replacement) -> existing
				));
		
		// 设置组织名称
		records.forEach(record -> {
			if (record.getDeptId() != null) {
				String deptName = deptIdToNameMap.get(record.getDeptId());
				record.setDeptName(deptName);
			}
		});
	}

	/**
	 * 校验质量指标唯一性：同一个物料、牧场、组织只能存在一条质量指标
	 * @param materialCode 物料编码
	 * @param deptId 组织ID
	 * @param tenantId 租户ID
	 * @param excludeId 排除的记录ID（更新时使用）
	 */
	private void validateUniqueQualityIndicator(String materialCode, Long deptId, Long tenantId, Long excludeId) {
		if (StrUtil.isBlank(materialCode)) {
			ExceptionUtil.check(true, "500", "物料编码不能为空");
		}
		
		// 构建查询条件
		LambdaQueryWrapper<BaseQualityIndicatorEntity> queryWrapper = Wrappers.<BaseQualityIndicatorEntity>lambdaQuery()
				.eq(BaseQualityIndicatorEntity::getMaterialCode, materialCode)
				.eq(BaseQualityIndicatorEntity::getDeptId, deptId)
				.eq(BaseQualityIndicatorEntity::getTenantId, tenantId);
		
		// 更新时排除当前记录
		if (excludeId != null) {
			queryWrapper.ne(BaseQualityIndicatorEntity::getId, excludeId);
		}
		
		// 查询是否存在重复记录
		long count = count(queryWrapper);
		if (count > 0) {
			ExceptionUtil.check(true, "500", "质量标准已存在，不可重复提交！");
		}
	}

	/**
	 * 校验指标名称唯一性：同一组织、租户下不能重复
	 */
	private void validateUniqueIndicatorName(String indicatorName, Long deptId, Long tenantId, Long excludeId) {
		if (StrUtil.isBlank(indicatorName)) {
			ExceptionUtil.check(true, "500", "指标名称不能为空");
		}
		LambdaQueryWrapper<BaseQualityIndicatorEntity> queryWrapper = Wrappers.<BaseQualityIndicatorEntity>lambdaQuery()
				.eq(BaseQualityIndicatorEntity::getIndicatorName, indicatorName);
		if (excludeId != null) {
			queryWrapper.ne(BaseQualityIndicatorEntity::getId, excludeId);
		}
		long count = count(queryWrapper);
		if (count > 0) {
			ExceptionUtil.check(true, "500", "指标名称已存在，不可重复提交！");
		}
	}

	/**
	 * 批量设置deptNameList
	 */
	private void setDeptNameList(List<BaseQualityIndicatorEntity> records) {
		if (CollectionUtils.isEmpty(records)) {
			return;
		}
		// 只处理applyAllLocations=0的
		List<Long> indicatorIds = records.stream()
				.filter(e -> "0".equals(e.getApplyAllLocations()))
				.map(BaseQualityIndicatorEntity::getId)
				.toList();
		if (CollectionUtils.isEmpty(indicatorIds)) {
			return;
		}
		// 查询所有关联关系
		List<BaseQualityIndicatorLocationEntity> locationList = baseQualityIndicatorLocationService.lambdaQuery()
				.in(BaseQualityIndicatorLocationEntity::getIndicatorId, indicatorIds)
				.list();
		if (CollectionUtils.isEmpty(locationList)) {
			return;
		}
		// indicatorId -> List<locationId>
		Map<Long, List<Long>> indicatorIdToLocIds = locationList.stream()
				.collect(Collectors.groupingBy(
						BaseQualityIndicatorLocationEntity::getIndicatorId,
						Collectors.mapping(BaseQualityIndicatorLocationEntity::getLocationId, Collectors.toList())
				));
		// 批量查所有部门
		Set<Long> allLocIds = locationList.stream().map(BaseQualityIndicatorLocationEntity::getLocationId).collect(Collectors.toSet());
		List<SysDept> deptList = sysDeptService.lambdaQuery().in(SysDept::getDeptId, allLocIds).list();
		Map<Long, String> locIdToDeptName = deptList.stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getName));
		// 设置到每条记录
		for (BaseQualityIndicatorEntity record : records) {
			if ("0".equals(record.getApplyAllLocations())) {
				List<Long> locIds = indicatorIdToLocIds.get(record.getId());
				if (CollectionUtils.isNotEmpty(locIds)) {
					List<String> deptNameList = locIds.stream().map(locIdToDeptName::get).filter(Objects::nonNull).toList();
					record.setLocationNameList(deptNameList);
				} else {
					record.setLocationNameList(new ArrayList<>());
				}
			} else {
				record.setLocationNameList(null);
			}
		}
	}

}