package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.time.LocalDateTime;

import com.ylz.saas.enums.BidsSegmentTypeEnum;
import lombok.Data;
import com.ylz.saas.common.core.util.TenantTable;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;

/**
 * 投标响应表
 * @TableName srm_tender_supplier_bidder_response
 */
@TenantTable
@TableName(value ="srm_tender_supplier_bidder_response")
@Data
public class SrmTenderSupplierBidderResponse {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 采购立项ID
     */
    private Long projectId;

    /**
     * 招标公告ID
     */
    private Long noticeId;

    /**
     * 标段ID
     */
    private Long sectionId;

    /**
     * 租户供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 供应商响应表ID
     */
    private Long tenderSupplierResponseId;

    /**
     * 招标要求ID
     */
    private Long requirementId;

    /**
     * 要求类型(QUALIFICATION-资质要求、CONDITION-响应条件、FEE-费用)
     */
    private BidsSegmentTypeEnum requirementType;

    /**
     * 响应名称
     */
    private String responseName;

    /**
     * 响应内容
     */
    private String responseContent;

    /**
     * 序号
     */
    private Integer sortNo;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 招标要求
     */
    @TableField(exist = false)
    private SrmTenderRequirement srmTenderRequirement;
}