package com.ylz.saas.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.common.security.service.SaasUser;
import com.ylz.saas.common.security.util.SecurityUtils;
import com.ylz.saas.entity.SrmRecruitSupplier;
import com.ylz.saas.req.*;
import com.ylz.saas.resp.*;
import com.ylz.saas.service.SrmRecruitSupplierChangeService;
import com.ylz.saas.service.SrmRecruitSupplierService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;


import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 供应商招募管理
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/srmRecruitSupplier")
@Tag(description = "srmRecruitSupplier", name = "供应商招募管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SrmRecruitSupplierController {

    private final SrmRecruitSupplierService srmRecruitSupplierService;
    private final SrmRecruitSupplierChangeService srmRecruitSupplierChangeService;


//    招募公告相关接口
    /**
     * 分页查询供应商招募信息
     *
     * @param req 查询条件
     * @return 分页结果
     */
    @Operation(summary = "分页查询供应商招募信息", description = "分页查询供应商招募信息")
    @PostMapping("/page")
    public R<IPage<SrmRecruitSupplierPageResp>> pageRecruitSupplier(@RequestBody SrmRecruitSupplierPageReq req, Page<SrmRecruitSupplierPageResp> page) {
        SaasUser user = SecurityUtils.getUser();
        req.setMemberId(user.getId());
        req.setPurchaseDeptId(user.getDeptId());

        return R.ok(srmRecruitSupplierService.pageRecruitSupplier(req,page));
    }

    /**
     * 查询供应商招募详情
     *
     * @param idOrCode 主键ID或招募编码
     * @return 详情信息
     */
    @Operation(summary = "查询供应商招募详情", description = "根据ID或编码查询供应商招募详情信息")
    @GetMapping("/detail/{idOrCode}")
    public R<SrmRecruitSupplierDetailResp> getRecruitSupplierDetail(@PathVariable("idOrCode") String idOrCode) {
        return R.ok(srmRecruitSupplierService.getRecruitSupplierDetail(idOrCode));
    }

    /**
     * 新增并提交供应商招募公告
     *
     * @param req 新增请求参数
     * @return 新增后的详情信息
     */
    @Operation(summary = "新增并提交供应商招募公告", description = "新增并提交供应商招募公告")
    @PostMapping("/saveAndSubmit")
    public R<SrmRecruitSupplierDetailResp> saveRecruitSupplier(@Valid @RequestBody SrmRecruitSupplierSaveReq req) {
        SrmRecruitSupplier savedEntity = srmRecruitSupplierService.saveRecruitSupplier(req);
        // 返回详情信息，包含所有子表数据
        SrmRecruitSupplierDetailResp detailResp = srmRecruitSupplierService.getRecruitSupplierDetail(savedEntity.getId().toString());
        return R.ok(detailResp);
    }



    /**
     * 分页查询供方报名信息
     *
     * @param req 查询条件
     * @return 分页结果
     */
    @Operation(summary = "分页查询供方报名信息", description = "分页查询供方报名信息")
    @PostMapping("/pageSignUp")
    public R<IPage<SrmRecruitResultPageResp>> pageSignUp(@RequestBody SrmRecruitResultPageReq req, Page<SrmRecruitResultPageResp> page) {
        return R.ok(srmRecruitSupplierService.pageSignUp(req,page));
    }

    /**
     * 审核供方提供的报名信息
     *
     * @param req 查询条件
     * @return 分页结果
     */
    @Operation(summary = "审核供方提供的报名信息", description = "审核供方提供的报名信息")
    @PostMapping("/auditSignUp")
    public R auditSignUp(@RequestBody SrmRecruitSupplierSignUpApproveReq req) {
        return R.ok(srmRecruitSupplierService.auditSignUp(req));
    }
    /**
     * 提交入库结果
     *
     * @param req 入库结果提交请求
     * @return 提交结果
     */
    @Operation(summary = "提交入库结果", description = "对于招募中或招募结束状态的招募公告进行入库结果提交")
    @PostMapping("/submitStoreResult")
    public R<Void> submitStoreResult(@Valid @RequestBody SrmRecruitSupplierStoreSubmitReq req) {
        srmRecruitSupplierService.submitStoreResult(req);
        return R.ok();
    }

    /**
     * 终止招募
     *
     * @param req 终止招募请求
     * @return 操作结果
     */
    @Operation(summary = "终止招募", description = "将招募中状态的招募公告提交终止审核")
    @PostMapping("/terminateRecruit")
    public R<Void> terminateRecruit(@Valid @RequestBody SrmRecruitSupplierTerminateReq req) {
        srmRecruitSupplierService.terminateRecruit(req);
        return R.ok();
    }


//    变更相关接口

    /**
     * 创建招募公告变更记录
     *
     * @param req 变更创建请求
     * @return 变更记录ID
     */
    @Operation(summary = "创建招募公告变更记录", description = "根据原招募公告创建变更记录")
    @PostMapping("/createChange")
    public R<Long> createChange(@Valid @RequestBody SrmRecruitSupplierChangeCreateReq req) {
        Long changeId = srmRecruitSupplierChangeService.createChange(req);
        return R.ok(changeId);
    }

    /**
     * 分页查询变更记录
     *
     * @param req  查询条件
     * @param page 分页参数
     * @return 分页结果
     */
    @Operation(summary = "分页查询变更记录", description = "分页查询招募公告变更记录")
    @PostMapping("/pageChange")
    public R<IPage<SrmRecruitSupplierChangePageResp>> pageChange(@RequestBody SrmRecruitSupplierChangePageReq req, Page<SrmRecruitSupplierChangePageResp> page) {
        return R.ok(srmRecruitSupplierChangeService.pageChange(req, page));
    }

    /**
     * 查询变更详情
     *
     * @param changeId 变更记录ID
     * @return 变更详情
     */
    @Operation(summary = "查询变更详情", description = "查询招募公告变更详情，包含变更前后的完整对象")
    @GetMapping("/changeDetail/{changeId}")
    public R<SrmRecruitSupplierChangeDetailResp> getChangeDetail(@PathVariable("changeId") Long changeId) {
        return R.ok(srmRecruitSupplierChangeService.getChangeDetail(changeId));
    }

    /**
     * 审核变更记录
     *
     * @param req 审核请求
     * @return 审核结果
     */
    @Operation(summary = "审核变更记录", description = "审核招募公告变更记录，通过后自动更新招募公告并发送通知")
    @PostMapping("/approveChange")
    public R<Void> approveChange(@Valid @RequestBody SrmRecruitSupplierChangeApproveReq req) {
        srmRecruitSupplierChangeService.approveChange(req);
        return R.ok();
    }




}
