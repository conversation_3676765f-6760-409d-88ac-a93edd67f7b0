package com.ylz.saas.codegen.srm_procurement_plan_detail.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.ylz.saas.codegen.base_quality_indicator.entity.BaseQualityIndicatorEntity;
import com.ylz.saas.codegen.base_quality_indicator.service.BaseQualityIndicatorService;
import com.ylz.saas.codegen.base_service_type_field.entity.BaseServiceTypeFieldEntity;
import com.ylz.saas.codegen.base_service_type_field.mapper.BaseServiceTypeFieldMapper;
import com.ylz.saas.codegen.base_usage_location.entity.BaseUsageLocationEntity;
import com.ylz.saas.codegen.base_usage_location.service.BaseUsageLocationService;
import com.ylz.saas.codegen.srm_procurement_plan.entity.SrmProcurementPlanEntity;
import com.ylz.saas.codegen.srm_procurement_plan.mapper.SrmProcurementPlanMapper;
import com.ylz.saas.codegen.srm_procurement_plan_detail.entity.SrmProcurementPlanDetailEntity;
import com.ylz.saas.codegen.srm_procurement_plan_detail.mapper.SrmProcurementPlanDetailMapper;
import com.ylz.saas.codegen.srm_procurement_plan_detail.param.SrmProcurementPlanDetailQueryParam;
import com.ylz.saas.codegen.srm_procurement_plan_detail.service.SrmProcurementPlanDetailService;
import com.ylz.saas.codegen.srm_procurement_plan_detail.vo.SrmProcurementPlanDetailVo;
import com.ylz.saas.codegen.srm_procurement_plan_field_value.entity.SrmProcurementPlanFieldValueEntity;
import com.ylz.saas.codegen.srm_procurement_plan_field_value.service.SrmProcurementPlanFieldValueService;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.enums.CommonSwitchEnum;
import com.ylz.saas.enums.YesNoEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 采购计划明细表
 *
 * <AUTHOR>
 * @date 2025-06-03 21:00:11
 */
@Service
@Slf4j
public class SrmProcurementPlanDetailServiceImpl extends ServiceImpl<SrmProcurementPlanDetailMapper, SrmProcurementPlanDetailEntity> implements SrmProcurementPlanDetailService {

    @Autowired
    private SrmProcurementPlanFieldValueService srmProcurementPlanFieldValueService;
    @Autowired
    private SrmProcurementPlanDetailMapper srmProcurementPlanDetailMapper;
    @Autowired
    private SrmProcurementPlanMapper srmProcurementPlanMapper;
    @Autowired
    private BaseServiceTypeFieldMapper baseServiceTypeFieldMapper;
    @Autowired
    private BaseUsageLocationService baseUsageLocationService;
    @Autowired
    private BaseQualityIndicatorService baseQualityIndicatorService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveSrmProcurementPlanDetail(SrmProcurementPlanDetailVo srmProcurementPlanDetailVo) {
        SrmProcurementPlanDetailEntity srmProcurementPlanDetail = new SrmProcurementPlanDetailEntity();
        BeanUtil.copyProperties(srmProcurementPlanDetailVo, srmProcurementPlanDetail);
        save(srmProcurementPlanDetail);
        if (CollectionUtils.isNotEmpty(srmProcurementPlanDetailVo.getSrmProcurementPlanFieldValueList())) {
            srmProcurementPlanDetailVo.getSrmProcurementPlanFieldValueList().forEach(srmProcurementPlanFieldValue -> {
                srmProcurementPlanFieldValue.setPlanDetailId(srmProcurementPlanDetail.getId());
            });
            srmProcurementPlanFieldValueService.saveBatch(srmProcurementPlanDetailVo.getSrmProcurementPlanFieldValueList());
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSrmProcurementPlanDetail(SrmProcurementPlanDetailVo srmProcurementPlanDetailVo) {
        SrmProcurementPlanDetailEntity srmProcurementPlanDetail = getById(srmProcurementPlanDetailVo.getId());
        ExceptionUtil.check(srmProcurementPlanDetail == null, "500", "采购计划明细表不存在");
        List<SrmProcurementPlanFieldValueEntity> oldSrmProcurementPlanFieldValueList = Optional.ofNullable(srmProcurementPlanFieldValueService.list(Wrappers.<SrmProcurementPlanFieldValueEntity>lambdaQuery().eq(SrmProcurementPlanFieldValueEntity::getPlanDetailId, srmProcurementPlanDetail.getId()))).orElse(Collections.emptyList());

        List<SrmProcurementPlanFieldValueEntity> newSrmProcurementPlanFieldValueList = Optional.ofNullable(srmProcurementPlanDetailVo.getSrmProcurementPlanFieldValueList()).orElse(Collections.emptyList());

        Set<Long> newSrmProcurementPlanFieldValueIds = newSrmProcurementPlanFieldValueList.stream().map(SrmProcurementPlanFieldValueEntity::getId).filter(Objects::nonNull).collect(Collectors.toSet());

        List<Long> idsSrmProcurementPlanFieldValueToDelete = oldSrmProcurementPlanFieldValueList.stream().filter(old -> !newSrmProcurementPlanFieldValueIds.contains(old.getId())).map(SrmProcurementPlanFieldValueEntity::getId).collect(Collectors.toList());

        if (!idsSrmProcurementPlanFieldValueToDelete.isEmpty()) {
            srmProcurementPlanFieldValueService.removeByIds(idsSrmProcurementPlanFieldValueToDelete);
        }
        for (SrmProcurementPlanFieldValueEntity srmProcurementPlanFieldValue : newSrmProcurementPlanFieldValueList) {
            srmProcurementPlanFieldValue.setPlanDetailId(srmProcurementPlanDetail.getId());
        }
        srmProcurementPlanFieldValueService.saveOrUpdateBatch(srmProcurementPlanDetailVo.getSrmProcurementPlanFieldValueList());
        BeanUtil.copyProperties(srmProcurementPlanDetailVo, srmProcurementPlanDetail);
        return updateById(srmProcurementPlanDetail);
    }

    @Override
    public boolean batchUpdatePlanFieldByPlanDetail(List<SrmProcurementPlanDetailEntity> srmProcurementPlanDetailVoList) {
        List<Long> planFieldValueIdListToDelete = new ArrayList<>();
        List<SrmProcurementPlanFieldValueEntity> planFieldValueListToUpsert = new ArrayList<>();

        srmProcurementPlanDetailVoList.forEach(e -> {
            // 根据采购计划明细id获取所有采购计划字段值
            List<SrmProcurementPlanFieldValueEntity> oldSrmProcurementPlanFieldValueList = Optional.ofNullable(srmProcurementPlanFieldValueService.list(Wrappers.<SrmProcurementPlanFieldValueEntity>lambdaQuery().eq(SrmProcurementPlanFieldValueEntity::getPlanDetailId, e.getId()))).orElse(Collections.emptyList());
            // 获取需要更新的采购计划字段值
            List<SrmProcurementPlanFieldValueEntity> newSrmProcurementPlanFieldValueList = Optional.ofNullable(e.getSrmProcurementPlanFieldValueList()).orElse(Collections.emptyList());
            // 获取需要删除的字段记录
            Set<Long> newSrmProcurementPlanFieldValueIds = newSrmProcurementPlanFieldValueList.stream().map(SrmProcurementPlanFieldValueEntity::getId).filter(Objects::nonNull).collect(Collectors.toSet());
            List<Long> idsSrmProcurementPlanFieldValueToDelete = oldSrmProcurementPlanFieldValueList.stream().filter(old -> !newSrmProcurementPlanFieldValueIds.contains(old.getId())).map(SrmProcurementPlanFieldValueEntity::getId).collect(Collectors.toList());
            if (!idsSrmProcurementPlanFieldValueToDelete.isEmpty()) {
                planFieldValueIdListToDelete.addAll(planFieldValueIdListToDelete);
            }
            // 获取需要upsert的字段记录
            for (SrmProcurementPlanFieldValueEntity srmProcurementPlanFieldValue : newSrmProcurementPlanFieldValueList) {
                srmProcurementPlanFieldValue.setPlanDetailId(e.getId());
            }
            planFieldValueListToUpsert.addAll(newSrmProcurementPlanFieldValueList);

        });

        return srmProcurementPlanFieldValueService.removeByIds(planFieldValueIdListToDelete)
                && srmProcurementPlanFieldValueService.saveOrUpdateBatch(planFieldValueListToUpsert);
    }

    @Override
    public SrmProcurementPlanDetailEntity getByIdSrmProcurementPlanDetail(Long id) {
        SrmProcurementPlanDetailEntity srmProcurementPlanDetail = getById(id);
        if (srmProcurementPlanDetail != null) {
            srmProcurementPlanDetail.setSrmProcurementPlanFieldValueList(srmProcurementPlanFieldValueService.list(Wrappers.<SrmProcurementPlanFieldValueEntity>lambdaQuery().eq(SrmProcurementPlanFieldValueEntity::getPlanDetailId, srmProcurementPlanDetail.getId())));
        }
        return srmProcurementPlanDetail;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIdsSrmProcurementPlanDetail(List<Long> ids) {
        List<SrmProcurementPlanDetailEntity> srmProcurementPlanDetailList = lambdaQuery().in(SrmProcurementPlanDetailEntity::getId, ids).list();
        if (CollectionUtils.isNotEmpty(srmProcurementPlanDetailList)) {
            // 逻辑删除计划明细数据
            removeBatchByIds(srmProcurementPlanDetailList);
            // 逻辑删除采购计划字段值数据
            final List<SrmProcurementPlanFieldValueEntity> fieldValueEntityList = srmProcurementPlanFieldValueService.list(Wrappers.<SrmProcurementPlanFieldValueEntity>lambdaQuery().in(SrmProcurementPlanFieldValueEntity::getPlanDetailId, ids));
            srmProcurementPlanFieldValueService.removeBatchByIds(fieldValueEntityList);
        }
        return true;
    }

    @Override
    public boolean physicsRemoveByPlanId(Long planId, Long tenantId) {
        return srmProcurementPlanDetailMapper.physicsRemoveByPlanId(planId,tenantId);
    }


    @Override
    public List<SrmProcurementPlanDetailEntity> getBatchByPlanIds(SrmProcurementPlanDetailQueryParam params) {
        ExceptionUtil.checkNotEmpty(params.getIds(), "ids不能为空");
        List<Long> planIds = params.getIds();
        if (StringUtils.isNotBlank(params.getSearchContent())) {
            List<SrmProcurementPlanEntity> planEntities = srmProcurementPlanMapper.selectList(Wrappers.<SrmProcurementPlanEntity>lambdaQuery()
                    .in(SrmProcurementPlanEntity::getId, params.getIds()).and(sw ->
                            sw.like(SrmProcurementPlanEntity::getPlanName, params.getSearchContent())
                                    .or()
                                    .like(SrmProcurementPlanEntity::getPlanCode, params.getSearchContent())));
            if (CollectionUtils.isEmpty(planEntities)) {
                return Lists.newArrayList();
            }
            planIds = planEntities.stream().map(SrmProcurementPlanEntity::getId).toList();
        }
        List<SrmProcurementPlanDetailEntity> planDetailEntities = this.lambdaQuery()
                .in(SrmProcurementPlanDetailEntity::getPlanId, planIds)
                .like(StringUtils.isNotBlank(params.getRequireNo()), SrmProcurementPlanDetailEntity::getRequireNo, params.getRequireNo())
                .and(StringUtils.isNotBlank(params.getMaterialSearchContent()), sw ->
                        sw.like(SrmProcurementPlanDetailEntity::getMaterialCode, params.getMaterialSearchContent())
                                .or()
                                .like(SrmProcurementPlanDetailEntity::getMaterialName, params.getMaterialSearchContent()))
                .list();
        if (CollectionUtils.isEmpty(planDetailEntities)) {
            return Lists.newArrayList();
        }

        List<Long> resultPlanIds = planDetailEntities.stream().map(SrmProcurementPlanDetailEntity::getPlanId).toList();
        // 采购计划
        List<SrmProcurementPlanEntity> plans = srmProcurementPlanMapper.selectByIds(resultPlanIds);
        Map<Long, SrmProcurementPlanEntity> planIdMap = plans.stream().collect(Collectors.toMap(SrmProcurementPlanEntity::getId, Function.identity()));
        // 动态字段
        List<Long> detailIds = planDetailEntities.stream().map(SrmProcurementPlanDetailEntity::getId).toList();
        List<SrmProcurementPlanFieldValueEntity> fieldValues = srmProcurementPlanFieldValueService.lambdaQuery().in(SrmProcurementPlanFieldValueEntity::getPlanDetailId, detailIds).list();
        Map<Long, List<SrmProcurementPlanFieldValueEntity>> detailIdGroup = fieldValues.stream().collect(Collectors.groupingBy(SrmProcurementPlanFieldValueEntity::getPlanDetailId));
        // 服务类型字段
        List<Long> serviceTypeIds = plans.stream().map(SrmProcurementPlanEntity::getServiceTypeId).distinct().toList();
        List<BaseServiceTypeFieldEntity> serviceTypeFieldEntities = baseServiceTypeFieldMapper.selectList(Wrappers.<BaseServiceTypeFieldEntity>lambdaQuery()
                .in(BaseServiceTypeFieldEntity::getServiceTypeId, serviceTypeIds)
                .eq(BaseServiceTypeFieldEntity::getDelFlag, YesNoEnum.NO.getCode()));

        // 牧场
        List<Long> locationIds = planDetailEntities.stream().map(SrmProcurementPlanDetailEntity::getUsageLocationId).distinct().toList();
        Map<Long, BaseUsageLocationEntity> locationEntityMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(locationIds)) {
            List<BaseUsageLocationEntity> baseUsageLocationEntities = baseUsageLocationService.listByIds(locationIds);
            locationEntityMap = Optional.ofNullable(baseUsageLocationEntities).stream().flatMap(Collection::stream)
                    .collect(Collectors.toMap(BaseUsageLocationEntity::getId, Function.identity()));
        }

        // 质量指标
        List<Long> qualityIds = planDetailEntities.stream().map(SrmProcurementPlanDetailEntity::getQualityIndicatorId).distinct().toList();
        Map<Long, String> qualityIndicatorEntityMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(qualityIds)) {
            List<BaseQualityIndicatorEntity> baseQualityIndicatorEntities = baseQualityIndicatorService.listByIds(qualityIds);
            qualityIndicatorEntityMap = Optional.ofNullable(baseQualityIndicatorEntities).stream().flatMap(Collection::stream)
                    .collect(Collectors.toMap(BaseQualityIndicatorEntity::getId, BaseQualityIndicatorEntity::getIndicatorName));
        }

        for (SrmProcurementPlanDetailEntity detail : planDetailEntities) {
            SrmProcurementPlanEntity plan = planIdMap.get(detail.getPlanId());
            detail.setPlanCode(plan.getPlanCode());
            detail.setPlanName(plan.getPlanName());
            detail.setApplyDate(plan.getApplyDate());
            detail.setBaseServiceTypeFieldList(serviceTypeFieldEntities.stream().filter(field ->
                            field.getServiceTypeId().equals(plan.getServiceTypeId()) && field.getBuyWay().equals(plan.getPlanRecruitMethod()))
                    .sorted(Comparator.comparingInt(BaseServiceTypeFieldEntity::getSort)).toList());
            detail.setSrmProcurementPlanFieldValueList(detailIdGroup.get(detail.getId()));

            BaseUsageLocationEntity baseUsageLocationEntity = locationEntityMap.get(detail.getUsageLocationId());
            if (Objects.nonNull(baseUsageLocationEntity)) {
                detail.setUsageLocationName(baseUsageLocationEntity.getLocationName());
                detail.setUsageLocationRegion(baseUsageLocationEntity.getProvince() + "/" + baseUsageLocationEntity.getCity() + "/" + baseUsageLocationEntity.getDistrict());
                detail.setUsageLocationAddress(baseUsageLocationEntity.getAddress());
            }
            detail.setQualityIndicatorName(qualityIndicatorEntityMap.get(detail.getQualityIndicatorId()));
        }
        return planDetailEntities;
    }
}