package com.ylz.saas.mapper;

import com.ylz.saas.entity.SrmPlatformSupplierInfo;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import com.ylz.saas.vo.SupplierBasicInfoDetailVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【srm_platform_supplier_info(供应商基础信息表)】的数据库操作Mapper
* @createDate 2025-06-10 18:08:09
* @Entity com.ylz.saas.entity.SrmPlatformSupplierInfo
*/
@Mapper
public interface SrmPlatformSupplierInfoMapper extends SaasBaseMapper<SrmPlatformSupplierInfo> {

    /**
     *
     * @param username
     * @return
     */
    SupplierBasicInfoDetailVo getOwnSupplier(@Param("username") String username);
}




