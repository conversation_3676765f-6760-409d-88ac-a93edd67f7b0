<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.BaseSceneFieldsMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.BaseSceneFields">
            <id property="id" column="id" />
            <result property="templateSceneId" column="template_scene_id" />
            <result property="fieldName" column="field_name" />
            <result property="fieldDisplayName" column="field_display_name" />
            <result property="fieldType" column="field_type" />
            <result property="fieldLength" column="field_length" />
            <result property="isRequired" column="is_required" />
            <result property="defaultValue" column="default_value" />
            <result property="description" column="description" />
            <result property="createBy" column="create_by" />
            <result property="createByName" column="create_by_name" />
            <result property="createTime" column="create_time" />
            <result property="createById" column="create_by_id" />
            <result property="updateById" column="update_by_id" />
            <result property="updateBy" column="update_by" />
            <result property="updateByName" column="update_by_name" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,template_scene_id,field_name,field_display_name,field_type,field_length,is_required,defualt_value,description,create_by,
        create_by_name,create_time,create_by_id,update_by_id,update_by,
        update_by_name,update_time
    </sql>
</mapper>
