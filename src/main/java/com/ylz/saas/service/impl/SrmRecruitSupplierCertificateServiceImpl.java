package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmRecruitSupplierCertificate;
import com.ylz.saas.mapper.SrmRecruitSupplierCertificateMapper;
import com.ylz.saas.service.SrmRecruitSupplierCertificateService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 供应商招募资质要求表Service实现类
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
@Slf4j
@AllArgsConstructor
public class SrmRecruitSupplierCertificateServiceImpl extends ServiceImpl<SrmRecruitSupplierCertificateMapper, SrmRecruitSupplierCertificate> implements SrmRecruitSupplierCertificateService {

}
