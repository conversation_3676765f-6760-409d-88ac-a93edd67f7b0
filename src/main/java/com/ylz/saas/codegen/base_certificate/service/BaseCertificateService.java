package com.ylz.saas.codegen.base_certificate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.base_certificate.entity.BaseCertificateEntity;
import com.ylz.saas.codegen.base_certificate.vo.BaseCertificateVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

public interface BaseCertificateService extends IService<BaseCertificateEntity> {
	/**
     * 新增证件信息表
     * @param baseCertificateVo 证件信息表
     */
    boolean saveBaseCertificate(BaseCertificateVo baseCertificateVo);
	
	/**
     * 修改证件信息表
     * @param baseCertificateVo 证件信息表
     */
    boolean updateBaseCertificate(BaseCertificateVo baseCertificateVo);
	
	/**
     * 导入模板下载
     * @param response
     */
    void template(HttpServletResponse response);

    /**
     * 数据导入
     * @param file
     * @param response
     */
    void uploadData(MultipartFile file, HttpServletResponse response);
}