package com.ylz.saas.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.entity.SrmRecruitSupplier;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.req.*;
import com.ylz.saas.resp.SrmRecruitResultPageResp;
import com.ylz.saas.resp.SrmRecruitSignUpResp;
import com.ylz.saas.resp.SrmRecruitSupplierDetailResp;
import com.ylz.saas.resp.SrmRecruitSupplierPageResp;

/**
 * 供应商招募公告表Service
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface SrmRecruitSupplierService extends IService<SrmRecruitSupplier> {

    /**
     * 分页查询供应商招募信息
     *
     * @param req 查询条件
     * @return 分页结果
     */
    IPage<SrmRecruitSupplierPageResp> pageRecruitSupplier(SrmRecruitSupplierPageReq req,Page<SrmRecruitSupplierPageResp> pagev);

    /**
     * 查询供应商招募详情
     *
     * @param idOrCode 主键ID或招募编码
     * @return 详情信息
     */
    SrmRecruitSupplierDetailResp getRecruitSupplierDetail(String idOrCode);

    /**
     * 新增供应商招募公告
     *
     * @param req 新增请求参数
     * @return 新增后的实体对象
     */
    SrmRecruitSupplier saveRecruitSupplier(SrmRecruitSupplierSaveReq req);
    /**
     * 审核招募公告

     */
    void auditRecruitSupplier(Long id, ApproveStatusEnum statusEnum);
    /**
     * 新增供应商招募公告
     */
    void stopRecruitSupplier(Long id);

    /**
     * 在线报名
     * @param req
     * @return
     */
    Object signUp(SrmRecruitSignUpReq req);

    /**
     * 招募结果
     * @param req
     * @param page
     * @return
     */
    IPage<SrmRecruitResultPageResp> pageSignUp(SrmRecruitResultPageReq req, Page<SrmRecruitResultPageResp> page);

    /**
     * 招募报名详情
     * @param signUpId
     * @return
     */
    SrmRecruitSignUpResp getSignUpDetail(Long signUpId);

    /**
     * 更新招募公告子表数据
     *
     * @param recruitId 招募公告ID
     * @param req 更新数据
     */
    void updateRecruitSupplierSubData(Long recruitId, SrmRecruitSupplierSaveReq req);

    /**
     *
     * @param req
     * @return
     */
    Object auditSignUp(SrmRecruitSupplierSignUpApproveReq req);

}
