package com.ylz.saas.codegen.base_agent_user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.base_agent_user.entity.BaseAgentUserEntity;
import com.ylz.saas.codegen.base_agent_user.vo.BaseAgentUserVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

public interface BaseAgentUserService extends IService<BaseAgentUserEntity> {
	/**
     * 新增代理机构用户表
     * @param baseAgentUserVo 代理机构用户表
     */
    boolean saveBaseAgentUser(BaseAgentUserVo baseAgentUserVo);
	
	/**
     * 修改代理机构用户表
     * @param baseAgentUserVo 代理机构用户表
     */
    boolean updateBaseAgentUser(BaseAgentUserVo baseAgentUserVo);
	
	/**
     * 导入模板下载
     * @param response
     */
    void template(HttpServletResponse response);

    /**
     * 数据导入
     * @param file
     * @param response
     */
    void uploadData(MultipartFile file, HttpServletResponse response);
}