package com.ylz.saas.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/6/26 13:52
 * @Description
 */
@Data
public class NoticeAndSectionIdReq {

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    private Long sectionId;


    /**
     * 供应商ID， 供方不用传，会查自己所属供应商
     */
    private Long tenantSupplierId;

}
