<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenderSupplierBidderResponseMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenderSupplierBidderResponse">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="projectId" column="project_id" jdbcType="BIGINT"/>
            <result property="noticeId" column="notice_id" jdbcType="BIGINT"/>
            <result property="sectionId" column="section_id" jdbcType="BIGINT"/>
            <result property="tenantSupplierId" column="tenant_supplier_id" jdbcType="BIGINT"/>
            <result property="tenderSupplierResponseId" column="tender_supplier_response_id" jdbcType="BIGINT"/>
            <result property="requirementId" column="requirement_id" jdbcType="BIGINT"/>
            <result property="requirementType" column="requirement_type" jdbcType="VARCHAR"/>
            <result property="responseName" column="response_name" jdbcType="VARCHAR"/>
            <result property="responseContent" column="response_content" jdbcType="VARCHAR"/>
            <result property="sortNo" column="sort_no" jdbcType="INTEGER"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,
        project_id,notice_id,section_id,
        tenant_supplier_id,tender_supplier_response_id,requirement_id,
        requirement_type,response_name,response_content,
        sort_no,del_flag,create_by,
        create_by_name,create_time,update_by,
        update_by_name,update_time
    </sql>
</mapper>
