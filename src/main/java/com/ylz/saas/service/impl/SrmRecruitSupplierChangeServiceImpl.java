package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.security.service.SaasUser;
import com.ylz.saas.common.security.util.SecurityUtils;
import com.ylz.saas.entity.SrmRecruitSupplier;
import com.ylz.saas.entity.SrmRecruitSupplierChange;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.mapper.SrmRecruitSupplierChangeMapper;
import com.ylz.saas.req.SrmRecruitSupplierChangeApproveReq;
import com.ylz.saas.req.SrmRecruitSupplierChangeCreateReq;
import com.ylz.saas.req.SrmRecruitSupplierChangePageReq;
import com.ylz.saas.req.SrmRecruitSupplierSaveReq;
import com.ylz.saas.resp.SrmRecruitSupplierChangeDetailResp;
import com.ylz.saas.resp.SrmRecruitSupplierChangePageResp;
import com.ylz.saas.resp.SrmRecruitSupplierDetailResp;
import com.ylz.saas.service.SrmRecruitSupplierChangeService;
import com.ylz.saas.service.SrmRecruitSupplierService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 供应商招募公告变更记录Service实现
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SrmRecruitSupplierChangeServiceImpl extends ServiceImpl<SrmRecruitSupplierChangeMapper, SrmRecruitSupplierChange> implements SrmRecruitSupplierChangeService {

    private final SrmRecruitSupplierService srmRecruitSupplierService;
    private final ObjectMapper objectMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createChange(SrmRecruitSupplierChangeCreateReq req) {
        SaasUser user = SecurityUtils.getUser();
        SrmRecruitSupplierSaveReq afterChangeData = req.getAfterChangeData();
        SrmRecruitSupplier.RecruitType recruitType = afterChangeData.getRecruitType();
        afterChangeData.getMaterialList().forEach(e->e.setRecruitType(recruitType));

        // 1. 验证招募公告是否存在且状态为招募中
        SrmRecruitSupplier recruitSupplier = srmRecruitSupplierService.getById(req.getRecruitId());
        if (recruitSupplier == null) {
            throw new RuntimeException("招募公告不存在");
        }
        if (recruitSupplier.getProcess() != SrmRecruitSupplier.Process.PROCESSING) {
            throw new RuntimeException("只有招募中的公告才能进行变更");
        }
        Long count = this.lambdaQuery()
                .eq(SrmRecruitSupplierChange::getRecruitId, req.getRecruitId())
                .eq(SrmRecruitSupplierChange::getApprovalStatus, ApproveStatusEnum.APPROVING)
                .count();
        ExceptionUtil.check(count!=null&&count!=0,"500","当前招募公告存在审核中的变更，暂时无法再次变更");


        // 2. 获取变更前的完整数据
        SrmRecruitSupplierDetailResp beforeDetail = srmRecruitSupplierService.getRecruitSupplierDetail(req.getRecruitId().toString());
        SrmRecruitSupplier.Process process = beforeDetail.getProcess();
        ExceptionUtil.check(!SrmRecruitSupplier.Process.PROCESSING.equals(process),"500","只有招募中状态下的招募公告可以被修改");

        // 3. 序列化变更前后的数据为JSON
        String beforeChangeJson;
        String afterChangeJson;
        try {
            beforeChangeJson = objectMapper.writeValueAsString(beforeDetail);
            afterChangeJson = objectMapper.writeValueAsString(afterChangeData);
        } catch (JsonProcessingException e) {
            log.error("序列化变更数据失败", e);
            throw new RuntimeException("序列化变更数据失败");
        }

        // 4. 创建变更记录
        SrmRecruitSupplierChange change = new SrmRecruitSupplierChange();
        change.setRecruitId(req.getRecruitId());
        change.setRecruitCode(recruitSupplier.getRecruitCode());
        change.setChangeById(user.getId());
        change.setChangeBy(user.getUsername());
        change.setChangeByName(user.getName());
        change.setChangeTime(LocalDateTime.now());
        change.setBeforeChangeJson(beforeChangeJson);
        change.setAfterChangeJson(afterChangeJson);
        change.setApprovalStatus(ApproveStatusEnum.TO_APPROVE);
        change.setChangeRemark(req.getChangeRemark());
        change.setDelFlag(0);

        // 5. 保存变更记录
        save(change);

        log.info("创建招募公告变更记录成功，变更ID：{}", change.getId());
        return change.getId();
    }

    @Override
    public IPage<SrmRecruitSupplierChangePageResp> pageChange(SrmRecruitSupplierChangePageReq req, Page<SrmRecruitSupplierChangePageResp> page) {
        return baseMapper.selectChangePage(page, req);
    }

    @Override
    public SrmRecruitSupplierChangeDetailResp getChangeDetail(Long changeId) {
        SrmRecruitSupplierChange change = getById(changeId);
        if (change == null) {
            throw new RuntimeException("变更记录不存在");
        }

        SrmRecruitSupplierChangeDetailResp resp = new SrmRecruitSupplierChangeDetailResp();
        BeanUtils.copyProperties(change, resp);

        // 反序列化JSON为详情对象
        try {
            // 变更前的详情直接反序列化
            SrmRecruitSupplierDetailResp beforeDetail = objectMapper.readValue(change.getBeforeChangeJson(), SrmRecruitSupplierDetailResp.class);
            resp.setBeforeChangeDetail(beforeDetail);

            // 变更后的详情需要构建完整对象
            SrmRecruitSupplierSaveReq afterChangeReq = objectMapper.readValue(change.getAfterChangeJson(), SrmRecruitSupplierSaveReq.class);
            SrmRecruitSupplierDetailResp afterDetail = buildDetailFromSaveReq(afterChangeReq, beforeDetail);
            resp.setAfterChangeDetail(afterDetail);
        } catch (JsonProcessingException e) {
            log.error("反序列化变更数据失败", e);
            throw new RuntimeException("反序列化变更数据失败");
        }

        return resp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approveChange(SrmRecruitSupplierChangeApproveReq req) {
        
        SrmRecruitSupplierChange change = getById(req.getChangeId());
        if (change == null) {
            throw new RuntimeException("变更记录不存在");
        }

        if (change.getApprovalStatus() != ApproveStatusEnum.TO_APPROVE) {
            throw new RuntimeException("该变更记录已审核，无法重复审核");
        }

        // 更新审核信息
        change.setApprovalStatus(req.getApprovalStatus());


        updateById(change);

        // 如果审核通过，则应用变更到招募公告
        if (req.getApprovalStatus() == ApproveStatusEnum.APPROVE) {
            applyChangeToRecruitSupplier(change);
            // 发送通知给已报名的供应商
            sendChangeNotificationToSuppliers(change.getRecruitId());
        }

        log.info("变更记录审核完成，变更ID：{}，审核结果：{}", req.getChangeId(), req.getApprovalStatus());
    }

    @Override
    public void sendChangeNotificationToSuppliers(Long recruitId) {
        // TODO: 实现向已报名供应商发送变更通知的逻辑
        log.info("向招募公告{}的已报名供应商发送变更通知", recruitId);
    }

    /**
     * 应用变更到招募公告
     */
    private void applyChangeToRecruitSupplier(SrmRecruitSupplierChange change) {
        try {
            log.info("应用变更到招募公告，招募公告ID：{}", change.getRecruitId());

            // 解析变更后的数据
            SrmRecruitSupplierSaveReq afterChangeData = objectMapper.readValue(
                change.getAfterChangeJson(),
                SrmRecruitSupplierSaveReq.class
            );

            // 获取原招募公告
            SrmRecruitSupplier originalRecruit = srmRecruitSupplierService.getById(change.getRecruitId());
            if (originalRecruit == null) {
                throw new RuntimeException("原招募公告不存在");
            }

            // 更新主表数据
            updateRecruitSupplierMainData(originalRecruit, afterChangeData);

            // 更新子表数据
            updateRecruitSupplierSubData(change.getRecruitId(), afterChangeData);

            log.info("成功应用变更到招募公告，招募公告ID：{}", change.getRecruitId());

        } catch (Exception e) {
            log.error("应用变更到招募公告失败", e);
            throw new RuntimeException("应用变更失败: " + e.getMessage());
        }
    }

    /**
     * 更新招募公告主表数据
     */
    private void updateRecruitSupplierMainData(SrmRecruitSupplier originalRecruit, SrmRecruitSupplierSaveReq afterChangeData) {
        // 复制变更后的数据到原实体
        originalRecruit.setRecruitTitle(afterChangeData.getRecruitTitle());
        originalRecruit.setReportTemplateId(afterChangeData.getReportTemplateId());
        originalRecruit.setDemandDeptId(afterChangeData.getDemandDeptId());
        originalRecruit.setPurchaseDeptId(afterChangeData.getPurchaseDeptId());
        originalRecruit.setRecruitType(afterChangeData.getRecruitType());
        originalRecruit.setRecruitWay(afterChangeData.getRecruitWay());
        originalRecruit.setDeadline(afterChangeData.getDeadline());
        originalRecruit.setAuditPersonId(afterChangeData.getAuditPersonId());
        originalRecruit.setAuditType(afterChangeData.getAuditType());
        originalRecruit.setProvince(afterChangeData.getProvince());
        originalRecruit.setCity(afterChangeData.getCity());
        originalRecruit.setDistrict(afterChangeData.getDistrict());
        originalRecruit.setAddress(afterChangeData.getAddress());
        originalRecruit.setOperationMode(afterChangeData.getOperationMode());
        originalRecruit.setRegisteredCapital(afterChangeData.getRegisteredCapital());
        originalRecruit.setRemark(afterChangeData.getRemark());
        originalRecruit.setAttachment(afterChangeData.getAttachment());
        originalRecruit.setTerminationAuditPersonId(afterChangeData.getTerminationAuditPersonId());
        originalRecruit.setTerminationAuditType(afterChangeData.getTerminationAuditType());
        originalRecruit.setRecruitContext(afterChangeData.getRecruitContext());

        // 更新主表
        srmRecruitSupplierService.updateById(originalRecruit);
    }

    /**
     * 更新招募公告子表数据
     */
    private void updateRecruitSupplierSubData(Long recruitId, SrmRecruitSupplierSaveReq afterChangeData) {
        try {
            // 调用SrmRecruitSupplierService中的公共子表更新方法
            srmRecruitSupplierService.updateRecruitSupplierSubData(recruitId, afterChangeData);
            log.info("子表数据更新完成，招募公告ID：{}", recruitId);
        } catch (Exception e) {
            log.error("更新招募公告子表数据失败，招募公告ID：{}", recruitId, e);
            throw new RuntimeException("更新子表数据失败: " + e.getMessage());
        }
    }

    /**
     * 根据SrmRecruitSupplierSaveReq构建详情响应对象
     */
    private SrmRecruitSupplierDetailResp buildDetailFromSaveReq(SrmRecruitSupplierSaveReq saveReq, SrmRecruitSupplierDetailResp baseDetail) {
        SrmRecruitSupplierDetailResp detail = new SrmRecruitSupplierDetailResp();

        // 复制基础信息（保留ID、编码等不变的字段）
        detail.setId(baseDetail.getId());
        detail.setTenantId(baseDetail.getTenantId());
        detail.setDeptId(baseDetail.getDeptId());
        detail.setRecruitCode(baseDetail.getRecruitCode());
        detail.setCreateById(baseDetail.getCreateById());
        detail.setCreateBy(baseDetail.getCreateBy());
        detail.setCreateByName(baseDetail.getCreateByName());
        detail.setCreateTime(baseDetail.getCreateTime());
        detail.setProcess(baseDetail.getProcess());
        detail.setApprovalStatus(baseDetail.getApprovalStatus());
        detail.setStoreApprovalStatus(baseDetail.getStoreApprovalStatus());

        // 复制变更后的数据
        detail.setRecruitTitle(saveReq.getRecruitTitle());
        detail.setReportTemplateId(saveReq.getReportTemplateId());
        detail.setDemandDeptId(saveReq.getDemandDeptId());
        detail.setPurchaseDeptId(saveReq.getPurchaseDeptId());
        detail.setRecruitType(saveReq.getRecruitType());
        detail.setRecruitWay(saveReq.getRecruitWay());
        detail.setDeadline(saveReq.getDeadline());
        detail.setAuditPersonId(saveReq.getAuditPersonId());
        detail.setAuditType(saveReq.getAuditType());
        detail.setProvince(saveReq.getProvince());
        detail.setCity(saveReq.getCity());
        detail.setDistrict(saveReq.getDistrict());
        detail.setAddress(saveReq.getAddress());
        detail.setOperationMode(saveReq.getOperationMode());
        detail.setRegisteredCapital(saveReq.getRegisteredCapital());
        detail.setRemark(saveReq.getRemark());
        detail.setAttachment(saveReq.getAttachment());
        detail.setTerminationAuditPersonId(saveReq.getTerminationAuditPersonId());
        detail.setTerminationAuditType(saveReq.getTerminationAuditType());
        detail.setRecruitContext(saveReq.getRecruitContext());

        // 转换子表数据（这里简化处理，实际可能需要更复杂的转换逻辑）
        // 注意：这里的转换可能不完整，因为SaveReq中的子表数据结构可能与DetailResp中的不完全一致
        // 实际使用中可能需要根据具体需求进行调整

        return detail;
    }
}
