package com.ylz.saas.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.admin.api.dto.MessageSmsDTO;
import com.ylz.saas.admin.api.feign.RemoteMessageService;
import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.security.service.SaasUser;
import com.ylz.saas.common.security.util.SecurityUtils;
import com.ylz.saas.entity.*;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.entity.SrmProcurementProjectItem;
import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.entity.SrmTenderAwardNotice;
import com.ylz.saas.entity.SrmTenderBidderQuoteItem;
import com.ylz.saas.entity.SrmTenderContract;
import com.ylz.saas.entity.SrmTenderEvaluationResult;
import com.ylz.saas.entity.SrmTenderNotice;
import com.ylz.saas.entity.SrmTenderNoticeChange;
import com.ylz.saas.entity.SrmTenderOpen;
import com.ylz.saas.entity.SrmTenderRequirement;
import com.ylz.saas.entity.SrmTenderSupplierInvite;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.AttachmentTypeEnum;
import com.ylz.saas.enums.ChangeTypeEnum;
import com.ylz.saas.enums.InviteMethodEnum;
import com.ylz.saas.enums.NoticeStatusEnum;
import com.ylz.saas.enums.PublicNoticeStatusEnum;
import com.ylz.saas.enums.PublicityStatusEnum;
import com.ylz.saas.mapper.SrmTenderNoticeChangeMapper;
import com.ylz.saas.req.AttachmentInfoReq;
import com.ylz.saas.req.InviteSupplierReq;
import com.ylz.saas.req.ProcessInstanceStartReq;
import com.ylz.saas.req.SrmEnsilageAddReq;
import com.ylz.saas.req.SrmNegotiationAddReq;
import com.ylz.saas.req.SrmTenderContractSaveReq;
import com.ylz.saas.req.SrmTenderNoticeChangeAddReq;
import com.ylz.saas.req.SrmTenderNoticeChangeDetailReq;
import com.ylz.saas.req.SrmTenderNoticeChangePageReq;
import com.ylz.saas.req.SrmTenderNoticeChangeReviewReq;
import com.ylz.saas.resp.SrmTenderContractDetailResp;
import com.ylz.saas.resp.SrmTenderNoticeChangePageResp;
import com.ylz.saas.service.ApproveRejectHookService;
import com.ylz.saas.service.SrmNegotiationBidDocService;
import com.ylz.saas.service.SrmProcessConfigService;
import com.ylz.saas.service.SrmProcessInstanceService;
import com.ylz.saas.service.SrmProcurementProjectItemService;
import com.ylz.saas.service.SrmProcurementProjectService;
import com.ylz.saas.service.SrmProjectAttachmentService;
import com.ylz.saas.service.SrmTenderAwardNoticeService;
import com.ylz.saas.service.SrmTenderBidderQuoteItemService;
import com.ylz.saas.service.SrmTenderContractService;
import com.ylz.saas.service.SrmTenderEnsilageService;
import com.ylz.saas.service.SrmTenderEvaluationResultService;
import com.ylz.saas.service.SrmTenderNoticeChangeService;
import com.ylz.saas.service.SrmTenderNoticeService;
import com.ylz.saas.service.SrmTenderOpenService;
import com.ylz.saas.service.SrmTenderRequirementService;
import com.ylz.saas.service.SrmTenderSupplierInviteService;
import com.ylz.saas.service.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【srm_tender_notice_change(招标公告变更记录表)】的数据库操作Service实现
* @createDate 2025-06-10 14:21:21
*/
@Slf4j
@Service
public class SrmTenderNoticeChangeServiceImpl extends ServiceImpl<SrmTenderNoticeChangeMapper, SrmTenderNoticeChange>
    implements SrmTenderNoticeChangeService, ApproveRejectHookService {

    @Resource
    private SrmProcurementProjectService projectService;

    @Resource
    private SrmTenderNoticeService noticeService;

    @Resource
    private SrmTenderRequirementService requirementService;

    @Resource
    private SrmProjectAttachmentService attachmentService;

    @Resource
    private SrmProcurementProjectItemService itemService;

    @Resource
    private SrmTenderEvaluationResultService evaluationResultService;

    @Resource
    private SrmTenderAwardNoticeService awardNoticeService;

    @Resource
    private SrmTenderSupplierInviteService supplierInviteService;

    @Resource
    private SrmProcessInstanceService srmProcessInstanceService;

    @Resource
    private SrmTenderOpenService tenderOpenService;

    @Resource
    private SrmTenderBidderQuoteItemService quoteItemService;

    @Resource
    private SrmNegotiationBidDocService negotiationBidDocService;

    @Resource
    private SrmTenderEnsilageService ensilageService;

    @Resource
    private SrmTenderContractService srmTenderContractService;

    @Resource
    private SrmTenantSupplierInfoService srmTenantSupplierInfoService;

    @Resource
    private AsyncSmsService asyncSmsService;

    @Value("${app.api-url:https://ayn.canpanscp.com}")
    private String appApiUrl;

    @Override
    public Page<SrmTenderNoticeChangePageResp> tenderNoticeChangePage(SrmTenderNoticeChangePageReq req) {
        LambdaQueryWrapper<SrmTenderNoticeChange> queryWrapper = Wrappers.<SrmTenderNoticeChange>lambdaQuery()
                .eq(SrmTenderNoticeChange::getProjectId, req.getProjectId())
                .like(StringUtils.isNotBlank(req.getChangeBy()), SrmTenderNoticeChange::getChangeByName, req.getChangeBy())
                .in(CollectionUtils.isNotEmpty(req.getChangeTypeEnums()), SrmTenderNoticeChange::getChangeType, req.getChangeTypeEnums())
                .in(CollectionUtils.isNotEmpty(req.getApprovedStatusList()), SrmTenderNoticeChange::getApprovedStatus, req.getApprovedStatusList())
                .orderByDesc(SrmTenderNoticeChange::getChangeTime);
        Page<SrmTenderNoticeChange> pageList = this.page(req, queryWrapper);
        Page<SrmTenderNoticeChangePageResp> lastPage = new Page<>();
        BeanUtils.copyProperties(pageList, lastPage);
        List<SrmTenderNoticeChange> records = pageList.getRecords();
        if(CollectionUtils.isEmpty(records)){
            return lastPage;
        }
        List<Long> projectIds = records.stream().map(SrmTenderNoticeChange::getProjectId).toList();
        List<SrmProcurementProject> procurementProjects = projectService.listByIds(projectIds);
        Map<Long, SrmProcurementProject> idMapVo = procurementProjects.stream().collect(Collectors.toMap(
                SrmProcurementProject::getId, Function.identity(), (x1, x2) -> x1));
        lastPage.setRecords(records.stream().map(item -> {
            SrmTenderNoticeChangePageResp resp = new SrmTenderNoticeChangePageResp();
            BeanUtils.copyProperties(item, resp);
            SrmProcurementProject srmProcurementProject = idMapVo.get(item.getProjectId());
            if(srmProcurementProject != null){
                resp.setProjectName(srmProcurementProject.getProjectName());
            }
            return resp;
        }).toList());
        return lastPage;
    }

    @Override
    public void addTenderNoticeChange(SrmTenderNoticeChangeAddReq req) {
        SaasUser user = SecurityUtils.getUser();
        ExceptionUtil.checkNonNull(user, "请重新登录！");
        Long noticeId = req.getNoticeId();
        Long projectId = req.getProjectId();
        ChangeTypeEnum changeTypeEnum = req.getChangeTypeEnum();
        SrmProcurementProject project = projectService.getById(projectId);
        SrmTenderNotice oldTenderNotice = noticeService.getById(noticeId);
        // 中标通知书表
        List<SrmTenderAwardNotice> awardNoticeList = awardNoticeService.lambdaQuery().eq(SrmTenderAwardNotice::getNoticeId, noticeId).list();
        oldTenderNotice.setAwardNoticeList(awardNoticeList);
        // 招标结果信息
        List<SrmTenderEvaluationResult> evaluationResultList = evaluationResultService.lambdaQuery().eq(SrmTenderEvaluationResult::getNoticeId, noticeId).list();
        oldTenderNotice.setEvaluationResultList(evaluationResultList);
        // 物料信息
        List<SrmProcurementProjectItem> oldProjectItemList = itemService.lambdaQuery().eq(SrmProcurementProjectItem::getProjectId, projectId).list();
        oldTenderNotice.setProjectItemList(oldProjectItemList);
        // 招标要求信息
        List<SrmTenderRequirement> requirementList = requirementService.lambdaQuery().eq(SrmTenderRequirement::getNoticeId, noticeId).list();
        oldTenderNotice.setRequirementList(requirementList);
        // 附件信息
        List<SrmProjectAttachment> attachmentList = attachmentService.lambdaQuery().eq(SrmProjectAttachment::getBusinessId, noticeId).list();
        oldTenderNotice.setAttachmentList(attachmentList);
        // 供应商邀请信息
        List<SrmTenderSupplierInvite> supplierInviteList = supplierInviteService.lambdaQuery().eq(SrmTenderSupplierInvite::getNoticeId, noticeId).list();
        oldTenderNotice.setSupplierInviteList(supplierInviteList);
        // 报价明细信息
        List<SrmTenderBidderQuoteItem> quoteItemList = quoteItemService.lambdaQuery().eq(SrmTenderBidderQuoteItem::getNoticeId, noticeId)
                .eq(SrmTenderBidderQuoteItem::getProjectId, projectId)
                .list();
        oldTenderNotice.setQuoteItemList(quoteItemList);
        // 校验数据
        checkNoticeChangeValidate(project, oldTenderNotice, changeTypeEnum, req);
        // 添加数据
        addChangeInfo(project, changeTypeEnum, req, oldTenderNotice, user);

    }

    private synchronized void addChangeInfo(SrmProcurementProject project, ChangeTypeEnum changeTypeEnum, SrmTenderNoticeChangeAddReq req, SrmTenderNotice oldTenderNotice, SaasUser user) {
        Long noticeId = oldTenderNotice.getId();
        Long projectId = oldTenderNotice.getProjectId();
        SrmTenderNoticeChange srmTenderNoticeChange = new SrmTenderNoticeChange();
        switch (changeTypeEnum) {
            case CHANGE_NOTICE:
                // 变更招标公告信息
                handlerChangeNotice(srmTenderNoticeChange, req, oldTenderNotice);
                break;
            case CHANGE_INVITE:
                // 变更邀请函信息
                handlerChangeInvite(srmTenderNoticeChange, req, oldTenderNotice);
                break;
            case CHANGE_PROJECT_TIME:
                // 招标项目时间变更
                handlerChangeProjectTime(srmTenderNoticeChange, req, oldTenderNotice);
                break;
            case CHANGE_OPENER:
                // 招标项目 opener变更
                handlerChangeOpener(srmTenderNoticeChange, req, oldTenderNotice);
                break;
            case CHANGE_BID:
                // 招标项目 定标变更
                handlerChangeBid(srmTenderNoticeChange, req, oldTenderNotice);
                break;
            case CHANGE_BID_PUBLICITY:
                // 招标项目 变更中标公示
                handlerChangeBidPublicity(srmTenderNoticeChange, req, oldTenderNotice);
                break;
            case CHANGE_BID_NOTICE:
                // 变更项目 变更中标公告
                handlerChangeBidNotice(srmTenderNoticeChange, req, oldTenderNotice);
                break;
            case CHANGE_BID_DOC:
                // 变更竞谈文件
                handlerChangeBidDoc(srmTenderNoticeChange, req, oldTenderNotice);
                break;
            case CHANGE_ENSILAGE:
                // 变更青贮信息
                handlerChangeEnsilage(srmTenderNoticeChange, req, oldTenderNotice);
                break;
            case CHANGE_CONTRACT:
                // 变更合同信息
                handlerChangeContract(srmTenderNoticeChange, req, oldTenderNotice);
                break;
            default:
                ExceptionUtil.checkNonNull(null, "变更类型错误！");
                break;
        }
        // 设置变更提交人信息
        srmTenderNoticeChange.setChangeBy(String.valueOf(user.getId()));
        srmTenderNoticeChange.setChangeByName(user.getName());
        // 设置审核状态
        srmTenderNoticeChange.setApprovedStatus(ApproveStatusEnum.APPROVING);
        srmTenderNoticeChange.setChangeType(changeTypeEnum);
        srmTenderNoticeChange.setProjectId(projectId);
        srmTenderNoticeChange.setNoticeId(noticeId);
        // 设置请求参数，为了查询回填信息
        srmTenderNoticeChange.setWebReq(JSONObject.toJSONString(req));
        SrmTenderNoticeChange tenderNoticeChange = lambdaQuery().eq(SrmTenderNoticeChange::getNoticeId, noticeId)
                .eq(SrmTenderNoticeChange::getProjectId, projectId)
                .eq(SrmTenderNoticeChange::getChangeType, changeTypeEnum)
                .one();
        if(tenderNoticeChange != null){
            // 如果存在设置id
            srmTenderNoticeChange.setId(tenderNoticeChange.getId());
        }
        saveOrUpdate(srmTenderNoticeChange);
        Long changeId = srmTenderNoticeChange.getId();
        // 业务流程发起
        ProcessInstanceStartReq startReq = new ProcessInstanceStartReq();
        startReq.setBizKey(String.valueOf(changeId));
        startReq.setBizId(projectId);
        String projectCode = project.getProjectCode();
        if (BaseServiceTypeFieldService.BuyWayEnum.QZXJ == project.getSourcingType()) {
            projectCode = projectCode + "&isSilage=true&noticeId="+noticeId+"&projectId="+projectId;
        } else {
            projectCode = projectCode + "&isSilage=false";
        }
        startReq.setArgs(List.of(projectCode,changeTypeEnum,changeId));
        startReq.setBizType(SrmProcessConfigService.BizTypeEnum.SRM_CHANGE_AUDIT);
        srmProcessInstanceService.startProcessInstance(startReq);
        log.info("变更业务流程发起完成！");
    }

    private void handlerChangeContract(SrmTenderNoticeChange srmTenderNoticeChange, SrmTenderNoticeChangeAddReq req, SrmTenderNotice oldTenderNotice) {
        SrmTenderContractSaveReq contractSaveReq = req.getContractSaveReq();
        // 变更原因
        srmTenderNoticeChange.setChangeDigest(req.getChangeDigest());

        // 设置bizId
        srmTenderNoticeChange.setBizId(contractSaveReq.getId());

        // 设置新旧参数
        SrmTenderContract contract = srmTenderContractService.getById(contractSaveReq.getId());
        if(contract != null){
            SrmTenderContractDetailResp resp = srmTenderContractService.queryDetail(contract.getContractCode());
            srmTenderNoticeChange.setOldParams(JSONObject.toJSONString(resp));
        }
        srmTenderNoticeChange.setNewParams(JSONObject.toJSONString(contractSaveReq));
    }

    private void handlerChangeEnsilage(SrmTenderNoticeChange srmTenderNoticeChange, SrmTenderNoticeChangeAddReq req, SrmTenderNotice oldTenderNotice) {
        SrmEnsilageAddReq upsertReq = new SrmEnsilageAddReq();
        BeanUtils.copyProperties(req, upsertReq);

        // 变更原因
        srmTenderNoticeChange.setChangeDigest(req.getChangeDigest());

        // 设置新旧参数
        srmTenderNoticeChange.setOldParams(JSONObject.toJSONString(oldTenderNotice));
        srmTenderNoticeChange.setNewParams(JSONObject.toJSONString(upsertReq));
    }

    private void handlerChangeBidDoc(SrmTenderNoticeChange srmTenderNoticeChange, SrmTenderNoticeChangeAddReq req, SrmTenderNotice oldTenderNotice) {
        SrmNegotiationAddReq addReq = new SrmNegotiationAddReq();
        BeanUtils.copyProperties(req, addReq);
        List<SrmNegotiationAddReq.BidsSegment> bidsSegments = req.getBidsSegments().stream().map(info -> {
            SrmNegotiationAddReq.BidsSegment segment = new SrmNegotiationAddReq.BidsSegment();
            segment.setRequirementType(info.getRequirementType());
            segment.setSectionId(info.getSectionId());
            segment.setRequirementName(info.getRequirementName());
            segment.setRequirementContent(info.getRequirementContent());
            segment.setBidFeeInfo(info.getBidFeeInfo());
            segment.setAttachmentInfos(info.getAttachmentInfos());
            return segment;
        }).toList();
        addReq.setBidsSegments(bidsSegments);

        // 变更原因
        srmTenderNoticeChange.setChangeDigest(req.getChangeDigest());

        // 设置新旧参数
        srmTenderNoticeChange.setOldParams(JSONObject.toJSONString(oldTenderNotice));
        srmTenderNoticeChange.setNewParams(JSONObject.toJSONString(addReq));
    }

    /**
     * 处理变更项目 变更中标公告
     *
     * @param srmTenderNoticeChange
     * @param req
     * @param oldTenderNotice
     */
    private void handlerChangeBidNotice(SrmTenderNoticeChange srmTenderNoticeChange, SrmTenderNoticeChangeAddReq req
            , SrmTenderNotice oldTenderNotice) {
        // 旧供应商信息
        List<SrmTenderSupplierInvite> oldSupplierInviteList = oldTenderNotice.getSupplierInviteList();
        oldSupplierInviteList.forEach(info->{
            // 是否发布公告
            info.setIsPublishNotice(oldTenderNotice.getStatus() == PublicNoticeStatusEnum.PUBLISHED);
            info.setPublishNoticeStartTime(oldTenderNotice.getPublishNoticeStartTime());
            info.setPublishNoticeEndTime(oldTenderNotice.getPublishNoticeEndTime());
            info.setNoticeTitle(oldTenderNotice.getNoticeTitle());
            info.setTemplateId(oldTenderNotice.getNoticeTemplateId());
            info.setNoticeContent(oldTenderNotice.getNoticeContent());
            info.setAttachmentList(oldTenderNotice.getAttachmentList());
        });

        // 新附件信息
        List<SrmProjectAttachment> newProjectAttachments = req.getAttachmentInfos().stream().map(item -> {
            SrmProjectAttachment attachment = new SrmProjectAttachment();
            BeanUtils.copyProperties(item, attachment);
            return attachment;
        }).toList();
        // 新供应商信息
        List<InviteSupplierReq> inviteSupplierList = req.getInviteSupplierList();
        List<SrmTenderSupplierInvite> newSupplierInviteList = inviteSupplierList.stream().map(
                        supplier -> {
                            SrmTenderSupplierInvite invite = new SrmTenderSupplierInvite();
                            BeanUtils.copyProperties(supplier, invite);
                            // 是否公告信息
                            invite.setIsPublishNotice(req.getIsPublishNotice());
                            invite.setPublishNoticeStartTime(req.getPublishNoticeStartTime());
                            invite.setPublishNoticeEndTime(req.getPublishNoticeEndTime());
                            invite.setNoticeTitle(req.getNoticeTitle());
                            invite.setTemplateId(req.getNoticeTemplateId());
                            invite.setNoticeContent(req.getNoticeContent());
                            invite.setAttachmentList(newProjectAttachments);
                            return invite;
                        }).toList();
        // 变更原因
        srmTenderNoticeChange.setChangeDigest(req.getChangeDigest());

        // 封装新旧信息
        srmTenderNoticeChange.setNewParams(JSONObject.toJSONString(newSupplierInviteList));
        srmTenderNoticeChange.setOldParams(JSONObject.toJSONString(oldSupplierInviteList));
    }

    /**
     * 处理变更项目 变更中标公示
     *
     * @param srmTenderNoticeChange
     * @param req
     * @param oldTenderNotice
     */
    private void handlerChangeBidPublicity(SrmTenderNoticeChange srmTenderNoticeChange, SrmTenderNoticeChangeAddReq req
            , SrmTenderNotice oldTenderNotice) {
        List<SrmTenderEvaluationResult> evaluationResultList = oldTenderNotice.getEvaluationResultList();
        SrmTenderEvaluationResult evaluationResult = evaluationResultList.get(0);
        // 旧供应商信息
        List<SrmTenderSupplierInvite> oldSupplierInviteList = oldTenderNotice.getSupplierInviteList();
        oldSupplierInviteList.forEach(info->{
            info.setIsPublicity(evaluationResult.getPublicityStatus() == PublicityStatusEnum.PUBLISHED);
            info.setPublicityStartTime(evaluationResult.getPublicityStartTime());
            info.setPublicityEndTime(evaluationResult.getPublicityEndTime());
            info.setPublicityTitle(evaluationResult.getPublicityTitle());
            info.setTemplateId(evaluationResult.getPublicityTemplateId());
            info.setPublicityContent(evaluationResult.getNoticeContent());
            info.setAttachmentList(oldTenderNotice.getAttachmentList());
        });

        // 新附件信息
        List<SrmProjectAttachment> newProjectAttachments = req.getAttachmentInfos().stream().map(item -> {
            SrmProjectAttachment attachment = new SrmProjectAttachment();
            BeanUtils.copyProperties(item, attachment);
            return attachment;
        }).toList();
        // 新供应商信息
        List<InviteSupplierReq> inviteSupplierList = req.getInviteSupplierList();
        List<SrmTenderSupplierInvite> newSupplierInviteList = inviteSupplierList.stream().map(
                        supplier -> {
                            SrmTenderSupplierInvite invite = new SrmTenderSupplierInvite();
                            BeanUtils.copyProperties(supplier, invite);
                            invite.setIsPublicity(req.getIsPublicity());
                            invite.setPublicityStartTime(req.getPublicityStartTime());
                            invite.setPublicityEndTime(req.getPublicityEndTime());
                            invite.setPublicityTitle(req.getPublicityTitle());
                            invite.setTemplateId(req.getNoticeTemplateId());
                            invite.setPublicityContent(req.getNoticeContent());
                            invite.setAttachmentList(newProjectAttachments);
                            return invite;
                        }).toList();
        // 变更原因
        srmTenderNoticeChange.setChangeDigest(req.getChangeDigest());

        // 封装新旧信息
        srmTenderNoticeChange.setNewParams(JSONObject.toJSONString(newSupplierInviteList));
        srmTenderNoticeChange.setOldParams(JSONObject.toJSONString(oldSupplierInviteList));
    }

    /**
     * 处理变更项目 定标结果
     *
     * @param srmTenderNoticeChange
     * @param req
     * @param oldTenderNotice
     */
    private void handlerChangeBid(SrmTenderNoticeChange srmTenderNoticeChange, SrmTenderNoticeChangeAddReq req
            , SrmTenderNotice oldTenderNotice) {
        Long noticeId = oldTenderNotice.getId();
        // 设置旧信息
        List<SrmTenderBidderQuoteItem> oldQuoteItemList = oldTenderNotice.getQuoteItemList();
        // 设置新信息
        List<SrmTenderNoticeChangeAddReq.ProjectItem> webProjectItemList = req.getProjectItemList();
        List<SrmTenderBidderQuoteItem> newQuoteItemList = webProjectItemList.stream().map(item -> {
            SrmTenderBidderQuoteItem quoteItem = new SrmTenderBidderQuoteItem();
            BeanUtils.copyProperties(item, quoteItem);
            quoteItem.setAwardTemplateId(req.getAwardTemplateId());
            quoteItem.setAwardReportContent(req.getAwardReportContent());
            quoteItem.setAwardReportTime(req.getAwardReportTime());
            return quoteItem;
        }).toList();
        // 变更原因
        srmTenderNoticeChange.setChangeDigest(req.getChangeDigest());

        // 封装新旧信息
        srmTenderNoticeChange.setNewParams(JSONObject.toJSONString(newQuoteItemList));
        srmTenderNoticeChange.setOldParams(JSONObject.toJSONString(oldQuoteItemList));
    }

    /**
     * 处理变更项目 opener信息
     *
     * @param srmTenderNoticeChange
     * @param req
     * @param oldTenderNotice
     */
    private void handlerChangeOpener(SrmTenderNoticeChange srmTenderNoticeChange, SrmTenderNoticeChangeAddReq req
            , SrmTenderNotice oldTenderNotice) {
        SrmTenderNotice newTenderNotice = new SrmTenderNotice();
        BeanUtils.copyProperties(oldTenderNotice, newTenderNotice);
        // 开标人
        Long bidOpener = req.getBidOpener();
        newTenderNotice.setBidOpener(bidOpener);

        // 变更原因
        srmTenderNoticeChange.setChangeDigest(req.getChangeDigest());

        // 封装新旧信息
        srmTenderNoticeChange.setNewParams(JSONObject.toJSONString(newTenderNotice));
        srmTenderNoticeChange.setOldParams(JSONObject.toJSONString(oldTenderNotice));
    }

    /**
     * 处理变更招标项目时间信息
     *
     * @param srmTenderNoticeChange
     * @param req
     * @param oldTenderNotice
     */
    private void handlerChangeProjectTime(SrmTenderNoticeChange srmTenderNoticeChange, SrmTenderNoticeChangeAddReq req
            , SrmTenderNotice oldTenderNotice) {
        SrmTenderNotice newTenderNotice = new SrmTenderNotice();
        BeanUtils.copyProperties(oldTenderNotice, newTenderNotice);
        // 采购日期要求
        SrmTenderNoticeChangeAddReq.PurchaseDateDemand purchaseDateDemand = req.getPurchaseDateDemand();
        newTenderNotice.setRegisterStartTime(purchaseDateDemand.getRegisterStartTime());
        newTenderNotice.setRegisterEndTime(purchaseDateDemand.getRegisterEndTime());
        newTenderNotice.setAuditStartTime(purchaseDateDemand.getAuditStartTime());
        newTenderNotice.setAuditEndTime(purchaseDateDemand.getAuditEndTime());
        newTenderNotice.setQuoteStartTime(purchaseDateDemand.getQuoteStartTime());
        newTenderNotice.setQuoteEndTime(purchaseDateDemand.getQuoteEndTime());
        newTenderNotice.setBidOpenTime(purchaseDateDemand.getBidOpenTime());
        // 开标地点和标书时间
        newTenderNotice.setBidOpener(purchaseDateDemand.getBidOpener());
        newTenderNotice.setBidDocPayStartTime(purchaseDateDemand.getBidDocPayStartTime());
        newTenderNotice.setBidDocPayEndTime(purchaseDateDemand.getBidDocPayEndTime());
        newTenderNotice.setFileObtainStartTime(purchaseDateDemand.getFileObtainStartTime());
        newTenderNotice.setFileObtainEndTime(purchaseDateDemand.getFileObtainEndTime());
        // 变更原因
        srmTenderNoticeChange.setChangeDigest(req.getChangeDigest());
        // 封装新旧信息
        srmTenderNoticeChange.setNewParams(JSONObject.toJSONString(newTenderNotice));
        srmTenderNoticeChange.setOldParams(JSONObject.toJSONString(oldTenderNotice));
    }

    /**
     * 处理变更招标邀请信息
     *
     * @param srmTenderNoticeChange
     * @param req
     * @param oldTenderNotice
     */
    private void handlerChangeInvite(SrmTenderNoticeChange srmTenderNoticeChange, SrmTenderNoticeChangeAddReq req
            , SrmTenderNotice oldTenderNotice) {
        Long noticeId = oldTenderNotice.getId();
        SrmTenderNotice newTenderNotice = new SrmTenderNotice();
        BeanUtils.copyProperties(oldTenderNotice, newTenderNotice);
        // 报价要求
        SrmTenderNoticeChangeAddReq.QuotationDemand quotationDemand = req.getQuotationDemand();
        newTenderNotice.setProvince(quotationDemand.getProvince());
        newTenderNotice.setCity(quotationDemand.getCity());
        newTenderNotice.setDistrict(quotationDemand.getDistrict());
        newTenderNotice.setAddress(quotationDemand.getAddress());
        newTenderNotice.setIncludeTax(quotationDemand.getIncludeTax());
        newTenderNotice.setCertificateType(quotationDemand.getCertificateType());
        // 评审规则
        String evaluationMethod = req.getEvaluationMethod();
        newTenderNotice.setEvaluationMethod(evaluationMethod);
        // 供应商报价须知
        String quotationNotice = req.getQuotationNotice();
        newTenderNotice.setQuotationNotice(quotationNotice);
        // 联系方式
        SrmTenderNoticeChangeAddReq.ContactInfo contactInfo = req.getContactInfo();
        newTenderNotice.setContactPerson(contactInfo.getContactPerson());
        newTenderNotice.setContactPhone(contactInfo.getContactPhone());
        newTenderNotice.setContactFixedPhone(contactInfo.getContactFixedPhone());
        newTenderNotice.setContactEmail(contactInfo.getContactEmail());
        // 更新附件信息
        List<AttachmentInfoReq> attachmentInfos = req.getAttachmentInfos();
        List<SrmProjectAttachment> attachments = attachmentInfos.stream().map(info -> {
            SrmProjectAttachment attachment = new SrmProjectAttachment();
            BeanUtils.copyProperties(info, attachment);
            attachment.setBusinessType(AttachmentTypeEnum.NOTICE);
            attachment.setProjectId(srmTenderNoticeChange.getProjectId());
            attachment.setBusinessId(noticeId);
            return attachment;
        }).toList();
        newTenderNotice.setAttachmentList(attachments);
        // 富文本
        String noticeContent = req.getNoticeContent();
        newTenderNotice.setNoticeContent(noticeContent);

        // 招标邀请函标题
        String inviteTitle = req.getInviteTitle();
        newTenderNotice.setInviteTitle(inviteTitle);

        // 封装新旧信息
        srmTenderNoticeChange.setNewParams(JSONObject.toJSONString(newTenderNotice));
        srmTenderNoticeChange.setOldParams(JSONObject.toJSONString(oldTenderNotice));
    }

    /**
     * 处理变更招标公告信息
     *
     * @param srmTenderNoticeChange
     * @param req
     * @param oldTenderNotice
     */
    private void handlerChangeNotice(SrmTenderNoticeChange srmTenderNoticeChange, SrmTenderNoticeChangeAddReq req
            , SrmTenderNotice oldTenderNotice) {
        Long noticeId = oldTenderNotice.getId();
        SrmTenderNotice newTenderNotice = new SrmTenderNotice();
        BeanUtils.copyProperties(oldTenderNotice, newTenderNotice);
        // 报价要求
        SrmTenderNoticeChangeAddReq.QuotationDemand quotationDemand = req.getQuotationDemand();
        newTenderNotice.setProvince(quotationDemand.getProvince());
        newTenderNotice.setCity(quotationDemand.getCity());
        newTenderNotice.setDistrict(quotationDemand.getDistrict());
        newTenderNotice.setAddress(quotationDemand.getAddress());
        newTenderNotice.setIncludeTax(quotationDemand.getIncludeTax());
        newTenderNotice.setCertificateType(quotationDemand.getCertificateType());
        // 招标要求（包括资质要求、报名响应条件、保证金设置等）
        SrmTenderNoticeChangeAddReq.PurchaseDateDemand purchaseDateDemand = req.getPurchaseDateDemand();
        newTenderNotice.setFileSubmissionAddress(purchaseDateDemand.getFileSubmissionAddress());
        newTenderNotice.setBidOpeningAddress(req.getBidOpeningAddress());
        newTenderNotice.setFileObtainStartTime(purchaseDateDemand.getFileObtainStartTime());
        newTenderNotice.setFileObtainEndTime(purchaseDateDemand.getFileObtainEndTime());
        newTenderNotice.setBidDocPayStartTime(purchaseDateDemand.getBidDocPayStartTime());
        newTenderNotice.setBidDocPayEndTime(purchaseDateDemand.getBidDocPayEndTime());

        // 评审规则
        String evaluationMethod = req.getEvaluationMethod();
        newTenderNotice.setEvaluationMethod(evaluationMethod);
        // 供应商报价须知
        String quotationNotice = req.getQuotationNotice();
        newTenderNotice.setQuotationNotice(quotationNotice);
        // 联系方式
        SrmTenderNoticeChangeAddReq.ContactInfo contactInfo = req.getContactInfo();
        newTenderNotice.setContactPerson(contactInfo.getContactPerson());
        newTenderNotice.setContactPhone(contactInfo.getContactPhone());
        newTenderNotice.setContactFixedPhone(contactInfo.getContactFixedPhone());
        newTenderNotice.setContactEmail(contactInfo.getContactEmail());
        // 更新附件信息
        List<AttachmentInfoReq> attachmentInfos = req.getAttachmentInfos();
        List<SrmProjectAttachment> attachments = attachmentInfos.stream().map(info -> {
            SrmProjectAttachment attachment = new SrmProjectAttachment();
            BeanUtils.copyProperties(info, attachment);
            attachment.setBusinessType(AttachmentTypeEnum.NOTICE);
            attachment.setProjectId(srmTenderNoticeChange.getProjectId());
            attachment.setBusinessId(noticeId);
            return attachment;
        }).toList();
        newTenderNotice.setAttachmentList(attachments);

        // 招标公告标题
        String noticeTitle = req.getNoticeTitle();
        newTenderNotice.setNoticeTitle(noticeTitle);

        // 富文本
        String noticeContent = req.getNoticeContent();
        newTenderNotice.setNoticeContent(noticeContent);

        // 封装新旧信息
        srmTenderNoticeChange.setNewParams(JSONObject.toJSONString(newTenderNotice));
        srmTenderNoticeChange.setOldParams(JSONObject.toJSONString(oldTenderNotice));
    }

    private synchronized void checkNoticeChangeValidate(SrmProcurementProject procurementProject, SrmTenderNotice oldTenderNotice
            , ChangeTypeEnum changeTypeEnum, SrmTenderNoticeChangeAddReq req) {
        ExceptionUtil.checkNonNull(procurementProject, "采购立项不存在！");
        ExceptionUtil.checkNonNull(oldTenderNotice, "招标公告不存在！");
        LocalDateTime currentTime = LocalDateTime.now();
        log.info("开始校验变更请求，变更类型: {}", changeTypeEnum);
        Long projectId = procurementProject.getId();
        Long noticeId = oldTenderNotice.getId();
        SrmTenderNoticeChange tenderNoticeChange = lambdaQuery().eq(SrmTenderNoticeChange::getNoticeId, noticeId)
                .eq(SrmTenderNoticeChange::getProjectId, projectId)
                .eq(SrmTenderNoticeChange::getChangeType, changeTypeEnum)
                .eq(SrmTenderNoticeChange::getApprovedStatus, ApproveStatusEnum.APPROVING)
                .one();
        if(tenderNoticeChange != null){
            ExceptionUtil.checkNonNull(null, "该采购立项的变更"+tenderNoticeChange.getApprovedStatus().getDesc());
        }
        switch (changeTypeEnum) {
            case CHANGE_NOTICE:
                // 变更公告校验
                log.debug("执行变更公告校验，采购立项ID: {}, 招标公告ID: {}", projectId, noticeId);
                checkChangeNotice(procurementProject, oldTenderNotice, req, currentTime);
                break;
            case CHANGE_INVITE:
                // 变更邀请函校验
                log.debug("执行变更邀请函校验，采购立项ID: {}, 招标公告ID: {}", projectId, noticeId);
                checkChangeInvite(procurementProject, oldTenderNotice, req, currentTime);
                break;
            case CHANGE_PROJECT_TIME:
                // 变更项目时间校验
                log.debug("执行变更项目时间校验，采购立项ID: {}, 招标公告ID: {}", projectId, noticeId);
                checkChangeProjectTime(procurementProject, oldTenderNotice, req, currentTime);
                break;
            case CHANGE_OPENER:
                // 变更采购人校验
                log.debug("执行变更开标人校验，采购立项ID: {}, 招标公告ID: {}", projectId, noticeId);
                checkChangeOpener(procurementProject, oldTenderNotice, req, currentTime);
                break;
            case CHANGE_BID:
                // 变更中标校验
                log.debug("执行变更定标校验，采购立项ID: {}, 招标公告ID: {}", projectId, noticeId);
                checkChangeBid(procurementProject, oldTenderNotice, req, currentTime);
                break;
            case CHANGE_BID_PUBLICITY:
                // 变更中标公示校验
                log.debug("执行变更中标公示校验，采购立项ID: {}, 招标公告ID: {}", projectId, noticeId);
                checkChangeBidPublicity(procurementProject, oldTenderNotice, req, currentTime);
                break;
            case CHANGE_BID_NOTICE:
                // 变更中标公告校验
                log.debug("执行变更中标公告校验，采购立项ID: {}, 招标公告ID: {}", projectId, noticeId);
                checkChangeBidNotice(procurementProject, oldTenderNotice, req, currentTime);
                break;
            case CHANGE_BID_DOC:
                // 变更竞谈文件校验
                log.debug("执行变更竞谈文件校验，采购立项ID: {}, 招标公告ID: {}", projectId, noticeId);
                checkChangeBidDoc(procurementProject, oldTenderNotice, req, currentTime);
                break;
            case CHANGE_ENSILAGE:
                // 变更青贮信息校验
                log.debug("执行变更青贮信息校验，采购立项ID: {}, 招标公告ID: {}", projectId, noticeId);
                checkChangeEnsilage(procurementProject, oldTenderNotice, req, currentTime);
                break;
            case CHANGE_CONTRACT:
                // 变更合同信息校验
                log.debug("执行变更合同信息校验，采购立项ID: {}, 招标公告ID: {}", projectId, noticeId);
                checkChangeContract(procurementProject, oldTenderNotice, req, currentTime);
                break;
            default:
                log.warn("不支持的变更类型: {}", changeTypeEnum);
                ExceptionUtil.checkNonNull(null, "变更类型错误！");
                break;
        }
        log.info("变更校验完成，变更类型: {}", changeTypeEnum);    }

    private void checkChangeContract(SrmProcurementProject procurementProject, SrmTenderNotice oldTenderNotice
            , SrmTenderNoticeChangeAddReq req, LocalDateTime currentTime) {
        SrmTenderContractSaveReq contractSaveReq = req.getContractSaveReq();
        Long id = contractSaveReq.getId();
        ExceptionUtil.checkNonNull(id, "合同ID不能为空！");
        // 7. 更新合同清单和投标报价明细
        if (contractSaveReq.getContractItems() != null && !contractSaveReq.getContractItems().isEmpty()) {
            // 7.1 先将该标段下所有投标报价明细的 contractQuantity 和 contractAmount 设置为 null
            // 获取标段ID
            Long sectionId = contractSaveReq.getSectionId();
            if (sectionId == null) {
                throw new RuntimeException("标段ID不能为空");
            }
        }

    }

    private void checkChangeEnsilage(SrmProcurementProject procurementProject, SrmTenderNotice oldTenderNotice
            , SrmTenderNoticeChangeAddReq req, LocalDateTime currentTime) {
        String demandName = req.getDemandName();
        Long noticeId = req.getNoticeId();
        // 需求名称不能重复
        Long count;
        if(noticeId != null){
            count = noticeService.lambdaQuery().eq(SrmTenderNotice::getNoticeTitle, demandName).ne(SrmTenderNotice::getId, noticeId).count();
        } else {
            count = noticeService.lambdaQuery().eq(SrmTenderNotice::getNoticeTitle, demandName).count();
        }
        if (count > 0L){
            ExceptionUtil.checkNonNull(null, "需求名称已存在!");
        }
        // 是否收取保证金
        Boolean needDeposit = req.getNeedDeposit();
        if (needDeposit) {
            // 获取保证金金额
            BigDecimal guaranteeAmount = req.getGuaranteeAmount();
            String payAccount = req.getPayAccount();
            if(guaranteeAmount.compareTo(BigDecimal.ZERO) == 0 || StringUtils.isBlank(payAccount)){
                ExceptionUtil.checkNonNull(null, "保证金设置-保证金金额不能为空！");
            }
        }
        // 联系人信息校验
        String contactPerson = req.getContactPerson();
        if(StringUtils.isBlank(contactPerson)){
            ExceptionUtil.checkNonNull(null, "联系人信息-联系人人不能为空！");
        }
        String contactPhone = req.getContactPhone();
        if(StringUtils.isBlank(contactPhone)){
            ExceptionUtil.checkNonNull(null, "联系人信息-联系电话不能为空！");
        }
    }

    private void checkChangeBidDoc(SrmProcurementProject procurementProject, SrmTenderNotice oldTenderNotice
            , SrmTenderNoticeChangeAddReq req, LocalDateTime currentTime) {
        List<SrmTenderNoticeChangeAddReq.BidsSegment> bidsSegments = req.getBidsSegments();
        if (req.getBidFeeCollection()) {
            ExceptionUtil.checkNotEmpty(bidsSegments, "标书费收取时，账号信息不能为空");
            bidsSegments.forEach(bidsSegment -> {
                ExceptionUtil.checkNonNull(bidsSegment.getSectionId(), "标书费&标书文件设置--标段ID不能为空");
                ExceptionUtil.checkNonNull(bidsSegment.getRequirementType(), "标书费&标书文件设置--要求类型不能为空");
                ExceptionUtil.checkNonNull(bidsSegment.getRequirementName(), "标书费&标书文件设置--要求名称不能为空");
                ExceptionUtil.checkNonNull(bidsSegment.getBidFeeInfo(), "标书费&标书文件设置--要求内容不能为空");
            });
        }

        if (req.getFileObtainStartTime().isAfter(req.getFileObtainEndTime())) {
            throw new IllegalArgumentException("文件获取开始时间不能晚于结束时间");
        }
        if (req.getBidDocPayStartTime().isAfter(req.getBidDocPayEndTime())) {
            throw new IllegalArgumentException("标书费缴纳开始时间不能晚于结束时间");
        }
        if (req.getQuoteStartTime().isAfter(req.getQuoteEndTime())) {
            throw new IllegalArgumentException("报价开始时间不能晚于结束时间");
        }
    }

    /**
     * 变更中标公告
     * @param procurementProject
     * @param oldTenderNotice
     * @param req
     * @param currentTime
     */
    private void checkChangeBidNotice(SrmProcurementProject procurementProject, SrmTenderNotice oldTenderNotice
            , SrmTenderNoticeChangeAddReq req, LocalDateTime currentTime) {
        // 中标公告后，中标通知书前可变更
        List<SrmTenderEvaluationResult> evaluationResultList = oldTenderNotice.getEvaluationResultList();
        boolean hadUnPublished = evaluationResultList.stream().anyMatch(
                info -> info.getNoticeStatus() != PublicNoticeStatusEnum.PUBLISHED);
        List<SrmTenderAwardNotice> awardNoticeList = oldTenderNotice.getAwardNoticeList();
        boolean hadSent = awardNoticeList.stream().anyMatch(info -> info.getNoticeStatus() != NoticeStatusEnum.UNSENT);
        if(hadUnPublished || hadSent || CollectionUtils.isEmpty(evaluationResultList)){
            ExceptionUtil.checkNonNull(null, "公告后，中标通知前才能变更！");
        }
        // 是否发布公告
        Boolean isPublicityNotice = req.getIsPublishNotice();
        ExceptionUtil.checkNonNull(isPublicityNotice, "请选择是否公告！");
        if(isPublicityNotice){
            // 公告标题
            String noticeTitle = req.getNoticeTitle();
            if(StringUtils.isBlank(noticeTitle)){
                ExceptionUtil.checkNonNull(noticeTitle, "公告标题不能为空！");
            }
            // 模板不能为空
            Long noticeTemplateId = req.getNoticeTemplateId();
            ExceptionUtil.checkNonNull(noticeTemplateId, "模板不能为空！");
        }
        // 供应商信息不能为空
        List<InviteSupplierReq> inviteSupplierList = req.getInviteSupplierList();
        ExceptionUtil.checkNotEmpty(inviteSupplierList, "供应商信息不能为空！");
    }

    /**
     * 变更中标公示
     * @param procurementProject
     * @param oldTenderNotice
     * @param req
     * @param currentTime
     */
    private void checkChangeBidPublicity(SrmProcurementProject procurementProject, SrmTenderNotice oldTenderNotice
            , SrmTenderNoticeChangeAddReq req, LocalDateTime currentTime) {
        // 公示后，公告前才能变更
        List<SrmTenderEvaluationResult> evaluationResultList = oldTenderNotice.getEvaluationResultList();
        boolean anyMatch = evaluationResultList.stream().anyMatch(
                info -> info.getPublicityStatus() != PublicityStatusEnum.PUBLISHED
                        || info.getNoticeStatus() != PublicNoticeStatusEnum.UNPUBLISHED);
        if(anyMatch || CollectionUtils.isEmpty(evaluationResultList)){
            ExceptionUtil.checkNonNull(null, "公示后，公告前才能变更！");
        }
        // 是否公示公布
        Boolean isPublicity = req.getIsPublicity();
        ExceptionUtil.checkNonNull(isPublicity, "请选择是否公示！");
        if(isPublicity){
            // 公示起止时间
            LocalDateTime publicityStartTime = req.getPublicityStartTime();
            LocalDateTime publicityEndTime = req.getPublicityEndTime();
            if(publicityStartTime == null || publicityEndTime == null){
                ExceptionUtil.checkNonNull(null, "公示起止时间不能为空！");
            }
            if(publicityStartTime.isAfter(publicityEndTime)){
                ExceptionUtil.checkNonNull(null, "公示开始时间不能大于结束时间！");
            }
            // 公示标题
            String publicityTitle = req.getPublicityTitle();
            if(StringUtils.isBlank(publicityTitle)){
                ExceptionUtil.checkNonNull(publicityTitle, "公示标题不能为空！");
            }
            // 模板不能为空
            Long publicityTemplateId = req.getPublicityTemplateId();
            ExceptionUtil.checkNonNull(publicityTemplateId, "模板不能为空！");
        }
        // 供应商信息不能为空
        List<InviteSupplierReq> inviteSupplierList = req.getInviteSupplierList();
        ExceptionUtil.checkNotEmpty(inviteSupplierList, "供应商信息为空！");
    }

    /**
     * 变更定标
     * @param procurementProject
     * @param oldTenderNotice
     * @param req
     * @param currentTime
     */
    private void checkChangeBid(SrmProcurementProject procurementProject, SrmTenderNotice oldTenderNotice
            , SrmTenderNoticeChangeAddReq req, LocalDateTime currentTime) {
        // 定标后，公示前才能变更
        List<SrmTenderEvaluationResult> evaluationResultList = oldTenderNotice.getEvaluationResultList();
        boolean anyMatch = evaluationResultList.stream().anyMatch(
                info ->info.getAwardReportStatus() != ApproveStatusEnum.APPROVE
                        || info.getPublicityStatus() != PublicityStatusEnum.UNPUBLISHED);
        if(anyMatch || CollectionUtils.isEmpty(evaluationResultList)){
            ExceptionUtil.checkNonNull(null, "定标后，公示前才能变更！");
        }
        // 模板不能为空
        Long awardTemplateId = req.getAwardTemplateId();
        ExceptionUtil.checkNonNull(awardTemplateId, "模板不能为空！");
        // 采购项目物料信息不能为空
        List<SrmTenderNoticeChangeAddReq.ProjectItem> projectItemList = req.getProjectItemList();
        ExceptionUtil.checkNotEmpty(projectItemList, "采购物料信息不能为空！");
    }

    /**
     * 变更开标人
     *
     * @param procurementProject
     * @param oldTenderNotice
     * @param req
     * @param currentTime
     */
    private void checkChangeOpener(SrmProcurementProject procurementProject, SrmTenderNotice oldTenderNotice
            , SrmTenderNoticeChangeAddReq req, LocalDateTime currentTime) {
        LocalDateTime bidOpenTime = oldTenderNotice.getBidOpenTime();
        if(currentTime.isAfter(bidOpenTime)){
            ExceptionUtil.checkNonNull(null, "开标前才能变更变更开标人！");
        }
        Long bidOpener = req.getBidOpener();
        ExceptionUtil.checkNonNull(bidOpener, "开标人不能为空！");
    }

    /**
     * 变更项目时间
     *
     * @param procurementProject
     * @param oldTenderNotice
     * @param req
     * @param currentTime
     */
    private void checkChangeProjectTime(SrmProcurementProject procurementProject, SrmTenderNotice oldTenderNotice
            , SrmTenderNoticeChangeAddReq req, LocalDateTime currentTime) {
        LocalDateTime bidOpenTime = oldTenderNotice.getBidOpenTime();
        if(currentTime.isAfter(bidOpenTime)){
            ExceptionUtil.checkNonNull(null, "开标前才能变更项目时间！");
        }
        SrmTenderNoticeChangeAddReq.PurchaseDateDemand purchaseDateDemand = req.getPurchaseDateDemand();
        ExceptionUtil.checkNonNull(purchaseDateDemand, "采购时间要求不能为空！");
        // 是否启用资格预审
        Integer preQualification = procurementProject.getPreQualification();
        if (Objects.equals(preQualification, 1)) {
            ExceptionUtil.checkNonNull(purchaseDateDemand.getRegisterStartTime(), "报名截止时间不能为空！");
            ExceptionUtil.checkNonNull(purchaseDateDemand.getRegisterEndTime(), "报名截止时间不能为空！");
            ExceptionUtil.checkNonNull(purchaseDateDemand.getAuditStartTime(), "资质预审截止时间不能为空！");
            ExceptionUtil.checkNonNull(purchaseDateDemand.getAuditEndTime(), "资质预审截止时间不能为空！");
        }
    }

    /**
     * 变更邀请函
     *
     * @param procurementProject
     * @param oldTenderNotice
     * @param req
     * @param currentTime
     */
    private void checkChangeInvite(SrmProcurementProject procurementProject, SrmTenderNotice oldTenderNotice
            , SrmTenderNoticeChangeAddReq req, LocalDateTime currentTime) {
        if(procurementProject.getInviteMethod() != InviteMethodEnum.INVITE){
            ExceptionUtil.checkNonNull(null, "只有邀请询价时才能变更公告");
        }
        LocalDateTime bidOpenTime = oldTenderNotice.getBidOpenTime();
        if(currentTime.isAfter(bidOpenTime)){
            ExceptionUtil.checkNonNull(null, "开标前才能变更邀请函！");
        }
        // 报价要求
        SrmTenderNoticeChangeAddReq.QuotationDemand quotationDemand = req.getQuotationDemand();
        ExceptionUtil.checkNonNull(quotationDemand, "报价要求不能为空！");
        ExceptionUtil.checkNonNull(quotationDemand.getIncludeTax(),  "报价要求-报价是否含税不能为空！");
        String certificateType = quotationDemand.getCertificateType();
        if(StringUtils.isBlank(certificateType)){
            ExceptionUtil.checkNonNull(null, "报价要求-发票要求不能为空！");
        }
        // 评审规则校验
        String evaluationMethod = req.getEvaluationMethod();
        if(StringUtils.isBlank(evaluationMethod)){
            ExceptionUtil.checkNonNull(null, "评审规则不能为空！");
        }
        // 联系人信息校验
        SrmTenderNoticeChangeAddReq.ContactInfo contactInfo = req.getContactInfo();
        ExceptionUtil.checkNonNull(contactInfo, "联系人信息不能为空！");
        String contactPerson = contactInfo.getContactPerson();
        if(StringUtils.isBlank(contactPerson)){
            ExceptionUtil.checkNonNull(null, "联系人信息-联系人人不能为空！");
        }
        String contactPhone = contactInfo.getContactPhone();
        if(StringUtils.isBlank(contactPhone)){
            ExceptionUtil.checkNonNull(null, "联系人信息-联系电话不能为空！");
        }
    }

    /**
     * 变更公告
     *
     * @param procurementProject
     * @param oldTenderNotice
     * @param req
     * @param currentTime
     */
    private static void checkChangeNotice(SrmProcurementProject procurementProject, SrmTenderNotice oldTenderNotice
            , SrmTenderNoticeChangeAddReq req, LocalDateTime currentTime) {
        if(procurementProject.getInviteMethod() != InviteMethodEnum.PUBLICITY){
            ExceptionUtil.checkNonNull(null, "只有公开询价时才能变更公告");
        }
        LocalDateTime bidOpenTime = oldTenderNotice.getBidOpenTime();
        if(currentTime.isAfter(bidOpenTime)){
            ExceptionUtil.checkNonNull(null, "开标前才能变更公告内容！");
        }
        // 报价要求
        SrmTenderNoticeChangeAddReq.QuotationDemand quotationDemand = req.getQuotationDemand();
        ExceptionUtil.checkNonNull(quotationDemand, "报价要求不能为空！");
        ExceptionUtil.checkNonNull(quotationDemand.getIncludeTax(),  "报价要求-报价是否含税不能为空！");
        String certificateType = quotationDemand.getCertificateType();
        if(StringUtils.isBlank(certificateType)){
            ExceptionUtil.checkNonNull(null, "报价要求-发票要求不能为空！");
        }
        // 评审规则校验
        String evaluationMethod = req.getEvaluationMethod();
        if(StringUtils.isBlank(evaluationMethod)){
            ExceptionUtil.checkNonNull(null, "评审规则不能为空！");
        }
        // 联系人信息校验
        SrmTenderNoticeChangeAddReq.ContactInfo contactInfo = req.getContactInfo();
        ExceptionUtil.checkNonNull(contactInfo, "联系人信息不能为空！");
        String contactPerson = contactInfo.getContactPerson();
        if(StringUtils.isBlank(contactPerson)){
            ExceptionUtil.checkNonNull(null, "联系人信息-联系人人不能为空！");
        }
        String contactPhone = contactInfo.getContactPhone();
        if(StringUtils.isBlank(contactPhone)){
            ExceptionUtil.checkNonNull(null, "联系人信息-联系电话不能为空！");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void tenderNoticeChangeReview(SrmTenderNoticeChangeReviewReq req) {
        Long noticeId = req.getNoticeId();
        Long projectId = req.getProjectId();
        SrmProcurementProject procurementProject = projectService.getById(projectId);
        ExceptionUtil.checkNonNull(procurementProject, "采购立项不存在！");
        SrmTenderNotice oldTenderNotice = noticeService.getById(noticeId);
        ExceptionUtil.checkNonNull(oldTenderNotice, "招标公告不存在！");

        // 更新变更记录状态
        Long changeId = req.getChangeId();
        SrmTenderNoticeChange latestChangeRecord = getById(changeId);
        ExceptionUtil.checkNonNull(latestChangeRecord, "未找到变更记录！");
        ChangeTypeEnum changeTypeEnum = latestChangeRecord.getChangeType();
        LocalDateTime currentTime = LocalDateTime.now();
        // 如果审批通过，则执行变更操作
        if (Objects.equals(req.getApprovedStatus(), ApproveStatusEnum.APPROVE)) {
            String newParams = latestChangeRecord.getNewParams();
            switch (changeTypeEnum) {
                case CHANGE_NOTICE:
                    // 处理变更招标公告信息
                    handlerTenderNotice(newParams, noticeId, projectId);
                    break;
                case CHANGE_INVITE:
                    // 处理变更邀请函信息
                    handlerSupplierInviteInfo(newParams, noticeId);
                    break;
                case CHANGE_PROJECT_TIME:
                    // 处理变更项目时间信息
                    handlerPurchaseTime(newParams, noticeId);
                    break;
                case CHANGE_OPENER:
                    // 处理变更开标人信息
                    handlerBidOpener(newParams, noticeId);
                    break;
                case CHANGE_BID:
                    // 处理变更定标结果
                    handlerBidResult(newParams, noticeId, projectId);
                    break;
                case CHANGE_BID_PUBLICITY:
                    // 处理变更中标公示
                    handlerBidPublicity(newParams, noticeId, projectId);
                    break;
                case CHANGE_BID_NOTICE:
                    // 处理变更中标公告
                    handlerBidNotice(newParams, noticeId, projectId);
                    break;
                case CHANGE_BID_DOC:
                    // 处理变更竞谈文件
                    handlerBidDoc(newParams, noticeId, projectId);
                    break;
                case CHANGE_ENSILAGE:
                    // 处理变更青贮
                    handlerEnsilage(newParams, noticeId, projectId);
                    break;
                case CHANGE_CONTRACT:
                    // 处理变更合同
                    handlerContract(newParams, noticeId, projectId);
                    break;
                default:
                    ExceptionUtil.checkNonNull(null, "变更类型错误！");
                    break;
            }

            // 更新变更记录为已处理
            lambdaUpdate().set(SrmTenderNoticeChange::getApprovedStatus, ApproveStatusEnum.APPROVE)
                    .set(SrmTenderNoticeChange::getApprovedRemark, req.getApprovedRemark())
                    .set(SrmTenderNoticeChange::getChangeTime, currentTime)
                    .eq(SrmTenderNoticeChange::getId, changeId)
                    .update();
        } else {
            // 审批拒绝，仅更新审批状态和审批人信息
            lambdaUpdate().set(SrmTenderNoticeChange::getApprovedStatus, ApproveStatusEnum.APPROVE_REJECT)
                    .set(SrmTenderNoticeChange::getApprovedRemark, req.getApprovedRemark())
                    .set(SrmTenderNoticeChange::getChangeTime, currentTime)
                    .eq(SrmTenderNoticeChange::getId, changeId)
                    .update();
        }
        srmProcessInstanceService.handlerInstanceStatus(req.getProjectId(),SrmProcessConfigService.BizTypeEnum.SRM_CHANGE_AUDIT,req.getApprovedStatus());
    }

    private void handlerContract(String newParams, Long noticeId, Long projectId) {
        SrmTenderContractSaveReq req = JSONObject.parseObject(newParams, SrmTenderContractSaveReq.class);
        srmTenderContractService.handlerUpdateContract(req);
    }

    private void handlerEnsilage(String newParams, Long noticeId, Long projectId) {
        SrmEnsilageAddReq req = JSONObject.parseObject(newParams, SrmEnsilageAddReq.class);
        req.setNoticeStatus(ApproveStatusEnum.APPROVE);
        ensilageService.handlerEnsilageNotice(req);
    }

    private void handlerBidDoc(String newParams, Long noticeId, Long projectId) {
        SrmNegotiationAddReq addReq = JSONObject.parseObject(newParams, SrmNegotiationAddReq.class);
        SrmTenderNotice tenderNotice = noticeService.getById(noticeId);
        addReq.setDeptId(tenderNotice.getDeptId());
        addReq.setTenantId(tenderNotice.getTenantId());
        negotiationBidDocService.negotiationCoreHandler(addReq, true);
    }

    private void handlerBidNotice(String newParams, Long noticeId, Long projectId) {
        List<SrmTenderSupplierInvite> noticeSuppliers = JSONObject.parseArray(newParams, SrmTenderSupplierInvite.class);
        // 获取现有的中标通知书信息
        List<SrmTenderEvaluationResult> evaluationResults = evaluationResultService.lambdaQuery()
                .eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                .list();
        ExceptionUtil.checkNonNull(evaluationResults, "未找到对应的评标结果信息");

        // 遍历供应商公告信息并更新中标通知书表中的相关字段
        for (SrmTenderSupplierInvite supplierInvite : noticeSuppliers) {
            SrmTenderEvaluationResult evaluationResult = evaluationResults.stream()
                    .filter(notice -> notice.getSectionId().equals(supplierInvite.getSectionId()))
                    .findFirst()
                    .orElse(null);

            if (evaluationResult != null) {
                // 更新公告相关信息
                evaluationResult.setNoticeTitle(supplierInvite.getNoticeTitle());
                evaluationResult.setNoticeTemplateId(supplierInvite.getTemplateId());
                evaluationResult.setNoticeContent(supplierInvite.getNoticeContent());
                evaluationResult.setNoticeStatus(supplierInvite.getIsPublishNotice() ? PublicNoticeStatusEnum.PUBLISHED : PublicNoticeStatusEnum.UNPUBLISHED);

                // 更新附件信息
                List<SrmProjectAttachment> attachments = supplierInvite.getAttachmentList();
                if (CollectionUtils.isNotEmpty(attachments)) {
                    attachmentService.saveOrUpdateBatch(attachments);
                }

                // 执行更新操作
                evaluationResultService.updateById(evaluationResult);
            }
        }
    }

    private void handlerBidPublicity(String newParams, Long noticeId, Long projectId) {
        List<SrmTenderSupplierInvite> publicitySuppliers = JSONObject.parseArray(newParams, SrmTenderSupplierInvite.class);
        // 获取现有的评标结果信息
        List<SrmTenderEvaluationResult> evaluationResults = evaluationResultService.lambdaQuery()
                .eq(SrmTenderEvaluationResult::getNoticeId, noticeId)
                .list();
        ExceptionUtil.checkNonNull(evaluationResults, "未找到对应的评标结果信息");

        // 遍历公示供应商信息并更新评标结果表中的相关字段
        for (SrmTenderSupplierInvite supplierInvite : publicitySuppliers) {
            SrmTenderEvaluationResult evaluationResult = evaluationResults.stream()
                    .filter(result -> result.getSectionId().equals(supplierInvite.getSectionId()))
                    .findFirst()
                    .orElse(null);

            if (evaluationResult != null) {
                // 更新公示相关信息
                evaluationResult.setPublicityTitle(supplierInvite.getPublicityTitle());
                evaluationResult.setPublicityTemplateId(supplierInvite.getTemplateId());
                evaluationResult.setPublicityContent(supplierInvite.getPublicityContent());
                evaluationResult.setPublicityStatus(supplierInvite.getIsPublicity() ? PublicityStatusEnum.PUBLISHED : PublicityStatusEnum.UNPUBLISHED);
                evaluationResult.setPublicityStartTime(supplierInvite.getPublicityStartTime());
                evaluationResult.setPublicityEndTime(supplierInvite.getPublicityEndTime());

                // 更新附件信息
                List<SrmProjectAttachment> attachments = supplierInvite.getAttachmentList();
                if (CollectionUtils.isNotEmpty(attachments)) {
                    attachmentService.saveOrUpdateBatch(attachments);
                }

                // 执行更新操作
                evaluationResultService.updateById(evaluationResult);
            }
        }
    }

    private void handlerBidResult(String newParams, Long noticeId, Long projectId) {
        List<SrmTenderBidderQuoteItem> newQuoteItems = JSONObject.parseArray(newParams, SrmTenderBidderQuoteItem.class);
        SrmProcurementProject procurementProject = projectService.getById(projectId);
        ExceptionUtil.checkNonNull(procurementProject, "采购立项信息不存在，ID：" + projectId);
        Map<String, List<SrmTenderBidderQuoteItem>> keyMapList = newQuoteItems.stream().collect(Collectors.groupingBy(
                info -> info.getSectionId() + "" + info.getNoticeId() + info.getTenantSupplierId()));
        // 先删除报价信息
        quoteItemService.lambdaUpdate().set(SrmTenderBidderQuoteItem::getAwarded, false)
                .set(SrmTenderBidderQuoteItem::getAwardedQuantity, null)
                .set(SrmTenderBidderQuoteItem::getResultId, null)
                .set(SrmTenderBidderQuoteItem::getProcurementProjectPaymentId, null)
                .eq(SrmTenderBidderQuoteItem::getNoticeId, noticeId)
                .update();
        List<SrmTenderEvaluationResult> executionResultList = new ArrayList<>();
        keyMapList.forEach((key,values)->{
            SrmTenderBidderQuoteItem srmTenderBidderQuoteItem = values.get(0);
            // 删除定标信息
            evaluationResultService.lambdaUpdate()
                    .eq(SrmTenderEvaluationResult::getNoticeId, srmTenderBidderQuoteItem.getNoticeId())
                    .eq(SrmTenderEvaluationResult::getSectionId, srmTenderBidderQuoteItem.getSectionId())
                    .eq(SrmTenderEvaluationResult::getTenantSupplierId, srmTenderBidderQuoteItem.getTenantSupplierId())
                    .remove();
            SrmTenderEvaluationResult evaluationResult = new SrmTenderEvaluationResult();
            evaluationResult.setProjectId(srmTenderBidderQuoteItem.getId());
            evaluationResult.setNoticeId(srmTenderBidderQuoteItem.getNoticeId());
            evaluationResult.setSectionId(srmTenderBidderQuoteItem.getSectionId());
            evaluationResult.setTenantSupplierId(srmTenderBidderQuoteItem.getTenantSupplierId());
            evaluationResult.setAwardReportStatus(ApproveStatusEnum.APPROVE);
            evaluationResult.setAwardTemplateId(srmTenderBidderQuoteItem.getAwardTemplateId());
            evaluationResult.setAwardReportContent(srmTenderBidderQuoteItem.getAwardReportContent());
            evaluationResult.setAwardReportTime(LocalDateTime.now());
            evaluationResultService.save(evaluationResult);
            values.forEach(info->{
                quoteItemService.lambdaUpdate().set(SrmTenderBidderQuoteItem::getAwarded, true)
                        .set(SrmTenderBidderQuoteItem::getAwardedQuantity, info.getAwardedQuantity())
                        .set(SrmTenderBidderQuoteItem::getResultId, evaluationResult.getId())
                        .set(SrmTenderBidderQuoteItem::getProcurementProjectPaymentId, info.getProcurementProjectPaymentId())
                        .eq(SrmTenderBidderQuoteItem::getNoticeId, info.getNoticeId())
                        .eq(SrmTenderBidderQuoteItem::getSectionId, info.getSectionId())
                        .eq(SrmTenderBidderQuoteItem::getTenantSupplierId, info.getTenantSupplierId())
                        .eq(SrmTenderBidderQuoteItem::getMaterialCode, info.getMaterialCode())
                        .update();
            });


        });
    }

    private void handlerBidOpener(String newParams, Long noticeId) {
        SrmTenderNotice openerInfo = JSONObject.parseObject(newParams, SrmTenderNotice.class);
        // 获取现有招标公告用于更新开标相关信息
        SrmTenderNotice oldNotice = noticeService.lambdaQuery()
                .eq(SrmTenderNotice::getId, noticeId)
                .one();
        ExceptionUtil.checkNonNull(oldNotice, "招标公告不存在");

        // 更新开标人信息
        Long bidOpener = openerInfo.getBidOpener();
        if (bidOpener != null) {
            oldNotice.setBidOpener(bidOpener);
            SrmProcurementProject project = projectService.getById(oldNotice.getProjectId());
            tenderOpenService.handlerProjectUserIdentityDataPermission(noticeId, bidOpener, new HashSet<>(), project);
        }

        // 执行更新操作
        noticeService.updateById(oldNotice);
    }

    private void handlerPurchaseTime(String newParams, Long noticeId) {
        SrmTenderNotice noticeInfo = JSONObject.parseObject(newParams, SrmTenderNotice.class);
        // 获取现有招标公告用于更新时间相关信息
        SrmTenderNotice oldNotice = noticeService.lambdaQuery()
                .eq(SrmTenderNotice::getId, noticeId)
                .one();
        ExceptionUtil.checkNonNull(oldNotice, "招标公告不存在");

        // 更新采购日期要求相关字段
        oldNotice.setRegisterStartTime(noticeInfo.getRegisterStartTime());
        oldNotice.setRegisterEndTime(noticeInfo.getRegisterEndTime());
        oldNotice.setAuditStartTime(noticeInfo.getAuditStartTime());
        oldNotice.setAuditEndTime(noticeInfo.getAuditEndTime());
        oldNotice.setQuoteStartTime(noticeInfo.getQuoteStartTime());
        oldNotice.setQuoteEndTime(noticeInfo.getQuoteEndTime());
        oldNotice.setBidOpenTime(noticeInfo.getBidOpenTime());
        oldNotice.setBidOpener(noticeInfo.getBidOpener());
        oldNotice.setFileObtainStartTime(noticeInfo.getFileObtainStartTime());
        oldNotice.setFileObtainEndTime(noticeInfo.getFileObtainEndTime());
        oldNotice.setBidDocPayStartTime(noticeInfo.getBidDocPayStartTime());
        oldNotice.setBidDocPayEndTime(noticeInfo.getBidDocPayEndTime());

        // 执行最终的更新操作
        noticeService.updateById(oldNotice);

        // 项目上开标时间修改
        projectService.lambdaUpdate().set(SrmProcurementProject::getBidOpenTime, noticeInfo.getBidOpenTime())
                .eq(SrmProcurementProject::getId, oldNotice.getProjectId())
                .update();

        // 开标表里报价截止时间修改
        tenderOpenService.lambdaUpdate().set(SrmTenderOpen::getLatestQuoteStartTime, noticeInfo.getQuoteStartTime())
                .set(SrmTenderOpen::getLatestQuoteEndTime, noticeInfo.getQuoteEndTime())
                .set(SrmTenderOpen::getOpenTime, noticeInfo.getBidOpenTime())
                .eq(SrmTenderOpen::getNoticeId, noticeId)
                .update();

    }

    private void handlerSupplierInviteInfo(String newParams, Long noticeId) {
        SrmTenderNotice inviteInfo = JSONObject.parseObject(newParams, SrmTenderNotice.class);
        // 获取现有招标公告信息用于部分更新
        SrmTenderNotice oldNotice = noticeService.lambdaQuery()
                .eq(SrmTenderNotice::getId, noticeId)
                .one();
        ExceptionUtil.checkNonNull(oldNotice, "招标公告不存在");
        // 报价要求修改
        oldNotice.setProvince(inviteInfo.getProvince());
        oldNotice.setCity(inviteInfo.getCity());
        oldNotice.setDistrict(inviteInfo.getDistrict());
        oldNotice.setAddress(inviteInfo.getAddress());
        oldNotice.setIncludeTax(inviteInfo.getIncludeTax());
        oldNotice.setCertificateType(inviteInfo.getCertificateType());

        // 评审规则修改
        if (StringUtils.isNotBlank(inviteInfo.getEvaluationMethod())) {
            oldNotice.setEvaluationMethod(inviteInfo.getEvaluationMethod());
        }
        // 供应商报价须知修改
        if (StringUtils.isNotBlank(inviteInfo.getQuotationNotice())) {
            oldNotice.setQuotationNotice(inviteInfo.getQuotationNotice());
        }
        // 联系人信息修改
        if (inviteInfo.getContactPerson() != null && StringUtils.isNotBlank(inviteInfo.getContactPerson())) {
            oldNotice.setContactPerson(inviteInfo.getContactPerson());
        }

        if (StringUtils.isNotBlank(inviteInfo.getContactPhone())) {
            oldNotice.setContactPhone(inviteInfo.getContactPhone());
        }

        if (StringUtils.isNotBlank(inviteInfo.getContactFixedPhone())) {
            oldNotice.setContactFixedPhone(inviteInfo.getContactFixedPhone());
        }

        if (StringUtils.isNotBlank(inviteInfo.getContactEmail())) {
            oldNotice.setContactEmail(inviteInfo.getContactEmail());
        }

        // 公告标题修改
        oldNotice.setNoticeTitle(inviteInfo.getNoticeTitle());
        // 富文本修改
        oldNotice.setNoticeContent(inviteInfo.getNoticeContent());

        // 更新附件信息（如果有的话）
        List<SrmProjectAttachment> attachmentList = inviteInfo.getAttachmentList();
        if (CollectionUtils.isNotEmpty(attachmentList)) {
            for (SrmProjectAttachment attachment : attachmentList) {
                attachment.setDeptId(oldNotice.getDeptId());
                attachment.setTenantId(oldNotice.getTenantId());
            }
            // 保存新的附件信息
            attachmentService.saveOrUpdateBatch(attachmentList);
        }

        // 执行最终更新
        noticeService.updateById(oldNotice);

        // 邀请函变更发送短信通知
        // 获取项目信息
        SrmProcurementProject project = projectService.lambdaQuery()
                .eq(SrmProcurementProject::getId, oldNotice.getProjectId())
                .one();

        // 获取所有受邀供应商
        List<SrmTenderSupplierInvite> invitedSuppliers = supplierInviteService.lambdaQuery()
                .eq(SrmTenderSupplierInvite::getNoticeId, noticeId)
                .list();

        // 向每个受邀供应商发送短信通知
        for (SrmTenderSupplierInvite invitedSupplier : invitedSuppliers) {
            if (StringUtils.isNotBlank(invitedSupplier.getConcatPhone())) {
                MessageSmsDTO messageSmsDTO = MessageSmsDTO.builder()
                        .mobile(invitedSupplier.getConcatPhone())
                        .biz("AYN_NOTICE_UPDATE")
                        .param("company_name", invitedSupplier.getSupplierName() != null ?
                                invitedSupplier.getSupplierName() : "供应商")
                        .param("project_name", project != null ? project.getProjectName() : "")
                        .param("project_code", project != null ? project.getProjectCode() : "")
                        .param("login_url", appApiUrl != null ? appApiUrl : "https://ayn.canpanscp.com")
                        .build();

                asyncSmsService.sendSmsAsync(messageSmsDTO);
            }
        }
    }

    private void handlerTenderNotice(String newParams,Long noticeId, Long projectId) {
        // 获取旧的招标公告信息用于对比更新内容
        SrmTenderNotice oldNotice = noticeService.lambdaQuery().eq(SrmTenderNotice::getId, noticeId)
                .eq(SrmTenderNotice::getProjectId, projectId)
                .one();
        ExceptionUtil.checkNonNull(oldNotice, "招标公告不存在");

        SrmTenderNotice noticeInfo = JSONObject.parseObject(newParams, SrmTenderNotice.class);
        // 报价要求修改 - 更新地址、含税状态和发票类型
        oldNotice.setProvince(noticeInfo.getProvince());
        oldNotice.setCity(noticeInfo.getCity());
        oldNotice.setDistrict(noticeInfo.getDistrict());
        oldNotice.setAddress(noticeInfo.getAddress());
        oldNotice.setIncludeTax(noticeInfo.getIncludeTax());
        oldNotice.setCertificateType(noticeInfo.getCertificateType());
        // 评审规则修改
        oldNotice.setEvaluationMethod(noticeInfo.getEvaluationMethod());
        // 供应商须知修改
        oldNotice.setQuotationNotice(noticeInfo.getQuotationNotice());
        // 联系方式修改
        oldNotice.setContactPerson(noticeInfo.getContactPerson());
        oldNotice.setContactPhone(noticeInfo.getContactPhone());
        oldNotice.setContactFixedPhone(noticeInfo.getContactFixedPhone());
        oldNotice.setContactEmail(noticeInfo.getContactEmail());
        // 公告标题修改
        oldNotice.setNoticeTitle(noticeInfo.getNoticeTitle());
        // 富文本修改
        oldNotice.setNoticeContent(noticeInfo.getNoticeContent());
        // 标书信息
        oldNotice.setFileSubmissionAddress(noticeInfo.getFileSubmissionAddress());
        oldNotice.setBidOpeningAddress(noticeInfo.getBidOpeningAddress());
        oldNotice.setFileObtainStartTime(noticeInfo.getFileObtainStartTime());
        oldNotice.setFileObtainEndTime(noticeInfo.getFileObtainEndTime());
        oldNotice.setBidDocPayStartTime(noticeInfo.getBidDocPayStartTime());
        oldNotice.setBidDocPayEndTime(noticeInfo.getBidDocPayEndTime());


        // 更新附件信息（如果有的话）
        List<SrmProjectAttachment> attachmentList = noticeInfo.getAttachmentList();
        if (CollectionUtils.isNotEmpty(attachmentList)) {
            attachmentList.forEach(attachment -> {
                attachment.setDeptId(oldNotice.getDeptId());
                attachment.setTenantId(oldNotice.getTenantId());
            });
            // 保存新的附件信息
            attachmentService.saveOrUpdateBatch(attachmentList);
        }

        // 执行最终更新
        noticeService.updateById(oldNotice);
    }

    @Override
    public SrmTenderNoticeChangeAddReq tenderNoticeChangeDetail(SrmTenderNoticeChangeDetailReq req) {
        Long bizId = req.getBizId();
        Long noticeId = req.getNoticeId();
        if(bizId == null && noticeId == null){
            ExceptionUtil.checkNonNull(null, "bizId和noticeId至少有一个不能为空");
        }

        ChangeTypeEnum changeTypeEnum = req.getChangeTypeEnum();
        // 获取招标变更信息
        SrmTenderNoticeChange tenderNoticeChange = lambdaQuery().eq(noticeId != null, SrmTenderNoticeChange::getNoticeId, noticeId)
                .eq(SrmTenderNoticeChange::getChangeType, changeTypeEnum)
                .eq(bizId != null, SrmTenderNoticeChange::getBizId, bizId)
                .one();
        ExceptionUtil.checkNonNull(tenderNoticeChange, "变更信息不存在");
        SrmTenderNoticeChangeAddReq changeAddReq = JSONObject.parseObject(tenderNoticeChange.getWebReq(), SrmTenderNoticeChangeAddReq.class);
        if (changeAddReq == null){
            return new SrmTenderNoticeChangeAddReq();
        }
        // 变更节点审批信息
        changeAddReq.setApprovedStatus(tenderNoticeChange.getApprovedStatus());
        changeAddReq.setId(tenderNoticeChange.getId());
        return changeAddReq;
    }

    @Override
    public SrmTenderNoticeChangeAddReq tenderNoticeChangeDetailForBase(String changeId) {
        if(StringUtils.isBlank(changeId)){
            ExceptionUtil.checkNonNull(null, "changeId参数不能为空");
        }
        // 获取招标变更信息
        SrmTenderNoticeChange tenderNoticeChange = getById(changeId);
        ExceptionUtil.checkNonNull(tenderNoticeChange, "变更信息不存在");
        SrmTenderNoticeChangeAddReq changeAddReq = JSONObject.parseObject(tenderNoticeChange.getWebReq(), SrmTenderNoticeChangeAddReq.class);
        if (changeAddReq == null){
            return new SrmTenderNoticeChangeAddReq();
        }
        // 变更节点审批信息
        changeAddReq.setApprovedStatus(tenderNoticeChange.getApprovedStatus());
        changeAddReq.setId(tenderNoticeChange.getId());
        return changeAddReq;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void tenderNoticeChangeReviewPass(Long changeId, Long projectId, Long noticeId) {
        SrmTenderNoticeChangeReviewReq req = new SrmTenderNoticeChangeReviewReq();
        req.setChangeId(changeId);
        req.setProjectId(projectId);
        req.setNoticeId(noticeId);
        req.setApprovedStatus(ApproveStatusEnum.APPROVE);
        this.tenderNoticeChangeReview(req);

        // 发送公告更改短信
        LambdaQueryWrapper<SrmTenantSupplierInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SrmTenantSupplierInfo::getDelFlag, 0) // 未删除
                .eq(SrmTenantSupplierInfo::getApprovalStatus, "APPROVED") // 审批状态为已审批
                .isNotNull(SrmTenantSupplierInfo::getLegalPersonPhone); // 确保法人电话不为空

        List<SrmTenantSupplierInfo> suppliers = srmTenantSupplierInfoService.list(queryWrapper);

        // 获取项目信息用于短信内容变量
        SrmProcurementProject project = projectService.getById(projectId);

        // 为每个供应商发送单独的短信
        for (SrmTenantSupplierInfo tenantSupplier : suppliers) {
            if (tenantSupplier.getLegalPersonPhone() != null) {
                MessageSmsDTO messageSmsDTO = MessageSmsDTO.builder()
                        .mobile(tenantSupplier.getLegalPersonPhone())
                        .biz("AYN_NOTICE_UPDATE")
                        .param("company_name", tenantSupplier.getSupplierName() != null ?
                                tenantSupplier.getSupplierName() : "供应商")
                        .param("project_name", project != null ? project.getProjectName() : "")
                        .param("project_code", project != null ? project.getProjectCode() : "")
                        .param("login_url", appApiUrl != null ? appApiUrl : "https://ayn.canpanscp.com")
                        .build();

                // 调用发送短信的服务方法
                asyncSmsService.sendSmsAsync(messageSmsDTO);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void tenderNoticeChangeReviewReject(Long changeId, Long projectId, Long noticeId) {
        SrmTenderNoticeChangeReviewReq req = new SrmTenderNoticeChangeReviewReq();
        req.setChangeId(changeId);
        req.setProjectId(projectId);
        req.setNoticeId(noticeId);
        req.setApprovedStatus(ApproveStatusEnum.APPROVE_REJECT);
        this.tenderNoticeChangeReview(req);
    }

    @Override
    public boolean match(SrmProcessConfigService.BizTypeEnum bizType) {
        return bizType == SrmProcessConfigService.BizTypeEnum.SRM_CHANGE_AUDIT;
    }

    @Override
    public void approveRejectHook(String bizKey) {
        this.lambdaUpdate().set(SrmTenderNoticeChange::getApprovedStatus, ApproveStatusEnum.APPROVE_REVOKE)
                .eq(SrmTenderNoticeChange::getId, bizKey).update();
    }

    @Override
    public void changeApprovingHook(String changeId) {
        this.lambdaUpdate().set(SrmTenderNoticeChange::getApprovedStatus, ApproveStatusEnum.APPROVING)
                .eq(SrmTenderNoticeChange::getId, changeId).update();
    }
}


