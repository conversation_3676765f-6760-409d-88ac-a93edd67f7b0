//package com.ylz.saas.test.service.impl;
//
//import cn.hutool.core.bean.BeanUtil;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.ylz.saas.test.entity.SingleTableEntity;
//import com.ylz.saas.test.vo.SingleTableVo;
//import com.ylz.saas.test.mapper.SingleTableMapper;
//import com.ylz.saas.test.service.SingleTableService;
//import org.springframework.stereotype.Service;
//import com.ylz.saas.common.core.exception.ExceptionUtil;
//import org.springframework.transaction.annotation.Transactional;
//import com.alibaba.fastjson.JSONObject;
//import com.ylz.saas.test.excel.SingleTableReq;
//import com.ylz.saas.common.data.excel.ExcelAnalysisUtil;
//import com.ylz.saas.common.data.excel.ExcelTemplateGenerator;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.springframework.web.multipart.MultipartFile;
//
//import jakarta.servlet.ServletOutputStream;
//import jakarta.servlet.http.HttpServletResponse;
//import java.io.*;
//import java.net.URLEncoder;
//import java.nio.file.Path;
//import java.util.List;
///**
// * 单表测试页面
// *
// * <AUTHOR>
// * @date 2025-05-19 15:35:40
// */
//@Service
//@Slf4j
//public class SingleTableServiceImpl extends ServiceImpl<SingleTableMapper, SingleTableEntity> implements SingleTableService {
//	@Override
//	@Transactional(rollbackFor = Exception.class)
//	public boolean saveSingleTable(SingleTableVo singleTableVo) {
//		SingleTableEntity singleTable = new SingleTableEntity();
//        BeanUtil.copyProperties(singleTableVo, singleTable);
//        return save(singleTable);
//	}
//
//	@Override
//	@Transactional(rollbackFor = Exception.class)
//	public boolean updateSingleTable(SingleTableVo singleTableVo) {
//		SingleTableEntity singleTable = getById(singleTableVo.getId());
//		ExceptionUtil.check(singleTable == null, "500", "单表测试页面不存在");
//		BeanUtil.copyProperties(singleTableVo, singleTable);
//        return updateById(singleTable);
//	}
//
//	@Override
//    public void template(HttpServletResponse response) {
//		// 创建目录及导入模板文件
//		isExcelExist();
//		// 读取导入模板
//		FileInputStream inputStream = null;
//		ServletOutputStream outputStream = null;
//		try {
//			String oriFileName = "单表测试页面导入模板";
//			response.setContentType("application/vnd.ms-excel");
//			response.setCharacterEncoding("utf-8");
//			String fileName = URLEncoder.encode(oriFileName, "UTF-8").replaceAll("\\+", "%20");
//			response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
//			Path result = ExcelTemplateGenerator.getResult();
//			Path filePath = result.resolve("单表测试页面singleTable.xlsx");
//			// 绝对路径
//			File file = new File(filePath.toString());
//			inputStream = new FileInputStream(file);
//
//			// 获取输出流
//			outputStream = response.getOutputStream();
//
//			// 将输入流的内容写入到输出流中
//			byte[] buffer = new byte[1024];
//			int bytesRead;
//			while ((bytesRead = inputStream.read(buffer)) != -1) {
//				outputStream.write(buffer, 0, bytesRead);
//			}
//
//			// 确保输出流的数据完全写入
//			outputStream.flush();
//			log.info("模板下载成功");
//		} catch (IOException e) {
//			System.out.println("e = " + e.getMessage());
//			log.info("模板下载失败");
//		} finally {
//			// 关闭流
//			try {
//				if (inputStream != null) {
//					inputStream.close();
//				}
//				if (outputStream != null) {
//					outputStream.close();
//				}
//			} catch (IOException e) {
//				log.error("关闭流时发生错误: {}", e.getMessage());
//			}
//		}
//	}
//
//	@Override
//	@Transactional
//	public void uploadData(MultipartFile file, HttpServletResponse response) {
//		try {
//			List<SingleTableReq> importGoodsList = new ExcelAnalysisUtil<SingleTableReq>()
//					.readFile(file, SingleTableReq.class, 1);
//			if (CollectionUtils.isEmpty(importGoodsList)) {
//				writeImportResponse(response, 1, "导入数据为空");
//				return;
//			}
//			List<SingleTableEntity> singleTableEntities = BeanUtil.copyToList(importGoodsList, SingleTableEntity.class);
//			// 做字段的必传校验
//			for (SingleTableEntity singleTableEntity : singleTableEntities) {
//			}
//			// 保存数据
//			this.saveOrUpdateBatch(singleTableEntities);
//		} catch (IOException e) {
//			log.info("文件解析失败");
//			throw new RuntimeException(e);
//		}
//	}
//
//	private void writeImportResponse(HttpServletResponse response, int code, String errorMsg) throws IOException {
//		JSONObject obj = new JSONObject();
//		response.setContentType("text/html;charset=utf-8");
//		obj.put("code", code);
//		obj.put("msg", errorMsg);
//		PrintWriter writer = response.getWriter();
//		writer.write(obj.toJSONString());
//		writer.close();
//	}
//
//	private static void isExcelExist() {
//		try {
//			Path result = ExcelTemplateGenerator.getResult();
//
//			File file = new File(result.toString());
//			if (file.exists()) {
//				log.info("excel文件夹路径存在");
//			} else {
//				// 创建文件夹
//				if (file.mkdirs()) {
//					log.info("目录已创建: {}" , file);
//				} else {
//					log.info("创建目录失败: {}", file);
//					throw new RuntimeException("创建目录失败: " + file);
//				}
//			}
//			// 设置 Excel 文件的完整路径
//			Path filePath = result.resolve("单表测试页面singleTable.xlsx");
//			// 生成模板文件
//			ExcelTemplateGenerator.createExcelTemplate(filePath.toString(),SingleTableReq.class);
//		} catch (UnsupportedEncodingException e) {
//			log.info("编码失败");
//		}
//	}
//}