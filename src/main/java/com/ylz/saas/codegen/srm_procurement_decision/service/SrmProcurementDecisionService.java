package com.ylz.saas.codegen.srm_procurement_decision.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.srm_procurement_decision.entity.SrmProcurementDecisionEntity;
import com.ylz.saas.codegen.srm_procurement_decision.param.PageReq;
import com.ylz.saas.codegen.srm_procurement_decision.param.UpsertReq;

/**
 * SrmProcurementDecisionService
 *
 * <AUTHOR>
 * @date 2025/8/5 16:32
 * @since v0.0.1
 */
public interface SrmProcurementDecisionService extends IService<SrmProcurementDecisionEntity> {

    void upsert(UpsertReq req);

    SrmProcurementDecisionEntity detail(Long id);

    Page<SrmProcurementDecisionEntity> page(Page page, PageReq req);

    void logicDelete(Long id);

    void decisionInvalid(Long id);

    void approveRevoke(Long id);
}