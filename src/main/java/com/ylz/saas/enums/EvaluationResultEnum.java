package com.ylz.saas.enums;

import lombok.Getter;

/**
 * 评价结果枚举
 */
@Getter
public enum EvaluationResultEnum {
    // DRAFT-草稿、PROCESSING-处理中、COMPLETED-已完成
    DRAFT("DRAFT", "草稿"),
    PROCESSING("PROCESSING", "处理中"),
    COMPLETED("COMPLETED", "已完成");

    private final String code;
    private final String desc;

    EvaluationResultEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
