package com.ylz.saas.resp;

import com.ylz.saas.enums.ApproveStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 定标列表
 */
@Data
public class BidSectionInfoResp {

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 标段id
     */
    private Long sectionId;

    /**
     * 标段名称
     */
    private String sectionName;

    /**
     * 定标报告状态
     * TO_APPROVE("待审批"),
     *     APPROVING("审批中"),
     *     APPROVE("审批通过"),
     *     APPROVE_REJECT("审批驳回"),
     *     APPROVE_REVOKE("审批撤销"),
     */
    private ApproveStatusEnum awardReportStatus;

    /**
     * 定标人
     */
    private Long userId;

    /**
     * 定标人名称
     */
    private String userName;

    /**
     * 定标报告时间
     */
    private LocalDateTime awardReportTime;

    /**
     * 定标报告
     */
    private String awardReportContent;

}
