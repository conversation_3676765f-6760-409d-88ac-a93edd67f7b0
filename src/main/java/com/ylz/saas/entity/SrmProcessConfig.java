package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ylz.saas.common.core.util.TenantTable;
import com.ylz.saas.service.SrmProcessConfigService;
import lombok.Data;

/**
 * 供应商响应表
 * @TableName srm_process_config
 */
@TableName(value ="srm_process_config")
@Data
@TenantTable
public class SrmProcessConfig {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 业务类型
     */
    private SrmProcessConfigService.BizTypeEnum bizType;

    /**
     * 流程key
     */
    private String flowKey;

    /**
     * 参数
     */
    private String variables;
}