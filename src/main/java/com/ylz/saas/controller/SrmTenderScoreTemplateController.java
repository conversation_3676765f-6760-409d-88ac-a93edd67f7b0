package com.ylz.saas.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.entity.SrmTenderScoreTemplate;
import com.ylz.saas.req.BaseAnnouncementSceneQueryReq;
import com.ylz.saas.req.BaseAnnouncementTemplateQueryReq;
import com.ylz.saas.req.SrmTenderScoreTemplateQueryReq;
import com.ylz.saas.req.SrmTenderScoreTemplateSaveReq;
import com.ylz.saas.resp.BaseAnnouncementTemplateQueryResp;
import com.ylz.saas.resp.BaseAnnouncementTemplateSceneQueryResp;
import com.ylz.saas.resp.SrmTenderNoticeAddResp;
import com.ylz.saas.resp.SrmTenderScoreTemplateDetailResp;
import com.ylz.saas.service.SrmTenderScoreTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 打分模板管理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequestMapping("/srmTenderScoreTemplate")
@Tag(name = "评分模板管理", description = "评分模板的增删改查操作")
@RequiredArgsConstructor
public class SrmTenderScoreTemplateController {
    private final SrmTenderScoreTemplateService srmTenderScoreTemplateService;
    /**
     * 分页查询模板（模板编号、名称模糊搜索）
     */
    @GetMapping("/query/page")
    @Operation(summary = "分页查询模板（模板编号、名称模糊搜索）", description = "分页查询模板（模板编号、名称模糊搜索）")
    public R<Page<SrmTenderScoreTemplate>> query(SrmTenderScoreTemplateQueryReq
                                                                        req) {
        Page<SrmTenderScoreTemplate> page = new Page<>(req.getCurrent(), req.getSize());
        return R.ok(srmTenderScoreTemplateService.query(page, req.getTemplateName(), req.getTemplateCode()));
    }
    /**
     * 模板详情
     */
    @GetMapping("/detail/{id}")
    @Operation(summary = "查询模板详情", description = "模板信息，评审项、评分项")
    public R<SrmTenderScoreTemplateDetailResp> queryDetail(@PathVariable("id") Long id) {
        SrmTenderScoreTemplateDetailResp resp = srmTenderScoreTemplateService.queryDetail(id);
        return R.ok(resp);
    }
    /**
     * 保存模板（有id修改，无id保存）
     */
    @PostMapping("/save")
    @Operation(summary = "保存模板", description = "新增或修改模板，并保存其评审项和评分项")
    public R<Boolean> saveOrUpdateTemplate(@RequestBody SrmTenderScoreTemplateSaveReq req) {
        if (req.getId() == null) {
            srmTenderScoreTemplateService.saveTemplate(req);
        } else {
            srmTenderScoreTemplateService.updateTemplate(req);
        }
        return R.ok(true);
    }
    /**
     * 删除模板
     */
    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除模板", description = "根据ID删除模板及其子项")
    public R<Boolean> deleteTemplate(@PathVariable("id") Long id) {
        srmTenderScoreTemplateService.deleteTemplate(id);
        return R.ok(true);
    }

}
