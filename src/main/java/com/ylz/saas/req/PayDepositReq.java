package com.ylz.saas.req;

import com.ylz.saas.vo.BidderResponseVo;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/11 16:13
 * @Description
 */
@Data
public class PayDepositReq {

    /**
     * 公告id
     */
    @NotNull(message = "公告id不能为空")
    private Long noticeId;

    @NotNull(message = "保证金缴纳信息不能为空")
    private List<DepositResponse> depositResponseList;


    @Data
    @Valid
    public static class DepositResponse {


        /**
         * 标段id
         */
        @NotNull(message = "标段id不能为空")
        private Long sectionId;

        @Valid
        private BidderResponseVo bidderResponse;
    }

}
