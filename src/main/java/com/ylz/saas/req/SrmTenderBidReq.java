package com.ylz.saas.req;

import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.EvaluationResultEnum;
import com.ylz.saas.enums.PublicNoticeStatusEnum;
import com.ylz.saas.enums.PublicityStatusEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 定标参数
 */
@Data
public class SrmTenderBidReq {
    /**
     * 公告id
     */
    @NotNull(message = "公告id不能为空")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 引用模版ID
     */
    @NotNull(message = "引用模版ID不能为空")
    private Long awardTemplateId;

    /**
     * 定标报告内容
     */
    @NotBlank(message = "定标报告内容不能为空")
    private String awardReportContent;

    /**
     * 附件信息
     */
    private List<AttachmentInfoReq> attachmentInfos;

    /**
     * 定标备注
     */
    private String awardRemark;

    /**
     * 采购项目物料信息
     */
    @Valid
    @NotNull(message = "采购项目物料信息不能为空")
    @Size(min = 1, message = "采购项目物料信息不能为空！")
    private List<BidProjectItem> projectItemList;

    /**
     * 中标通知书参数
     */
    @Valid
    private List<SrmTenderEditBatchAwardNoticeReq> awardNoticeReqList;

    /**
     * 是否竞谈定标
     */
    private Boolean isCompetitive;

    /**
     * 定标物料信息
     */
    @Data
    public static class BidProjectItem {
        /**
         * 采购立项ID
         */
        private Long projectId;

        /**
         * 标段ID
         */
        @NotNull(message = "标段ID不能为空")
        private Long sectionId;

        /**
         * 租户供应商ID
         */
        private Long tenantSupplierId;

        /**
         * 供应商名称
         */
        private String supplierName;

        /**
         * 服务类型ID
         */
        private Long serviceTypeId;

        /**
         * 寻源方式(INQUIRY-询价、FAST_INQUIRY-快速询价、BIDDING-竞价、TENDER-招标采购、COMPETITIVE_NEGOTIATION-竞争性谈判、DIRECT-直接委托)
         */
        private String sourcingType;

        /**
         * 物料编码
         */
        private String materialCode;

        /**
         * 物料名称
         */
        private String materialName;

        /**
         * 规格型号
         */
        private String specModel;

        /**
         * 单位
         */
        private String unit;

        /**
         * 需求数量
         */
        private BigDecimal requiredQuantity;

        /**
         * 采购计划ID
         */
        private Long planId;

        /**
         * 需求行号
         */
        private String requireNo;

        /**
         * 备注
         */
        private String remark;

        /**
         * 定标备注
         */
        private String awardRemark;

        /**
         * 中标数量
         */
        private BigDecimal awardedQuantity;

        /**
         * 是否中标
         */
        private Boolean isBid;

        /**
         * 支付方式id
         */
        private Long projectPaymentId;

        /**
         * 中标金额
         */
        private BigDecimal bidAmount;

        /**
         *  评价结果ID
         */
        private transient Long evaluationResultId;

        /**
         * 定标报告状态
         * TO_APPROVE("待审批"),
         *     APPROVING("审批中"),
         *     APPROVE("审批通过"),
         *     APPROVE_REJECT("审批驳回"),
         *     APPROVE_REVOKE("审批撤销"),
         */
        private ApproveStatusEnum awardReportStatus;

        /**
         * 审批备注
         */
        private String examineRemark;

        /**
         * 定标报告内容
         */
        private String awardReportContent;

        /**
         * 定标报告时间
         */
        private LocalDateTime awardReportTime;

        /**
         * 定标报告模板ID
         */
        private Long awardTemplateId;


        /**
         * 公示状态(UNPUBLISHED-未公示、PUBLISHED-已公示、EXPIRED-已过期)
         */
        private PublicityStatusEnum publicityStatus;

        /**
         * 公示标题
         */
        private String publicityTitle;

        /**
         * 公示引用模版ID
         */
        private Long publicityTemplateId;

        /**
         * 公示内容
         */
        private String publicityContent;

        /**
         * 公示开始时间
         */
        private LocalDateTime publicityStartTime;

        /**
         * 公示结束时间
         */
        private LocalDateTime publicityEndTime;

        /**
         * 公告状态(UNPUBLISHED-未公告、PUBLISHED-已公告)
         */
        private PublicNoticeStatusEnum noticeStatus;

        /**
         * 公告标题
         */
        private String noticeTitle;

        /**
         * 公示引用模版ID
         */
        private Long noticeTemplateId;

        /**
         * 公告内容
         */
        private String noticeContent;

        /**
         * 公告时间
         */
        private LocalDateTime noticeTime;

        /**
         * 整体状态(DRAFT-草稿、PROCESSING-处理中、COMPLETED-已完成)
         */
        private EvaluationResultEnum resultStatus;

        /**
         * 定标附件
         */
        private List<SrmProjectAttachment> awardAttachments;

        /**
         * 中标公示附件列表
         */
        private List<SrmProjectAttachment> publicityAttachments;

        /**
         * 中标公告附件列表
         */
        private List<SrmProjectAttachment> noticeAttachments;
    }


}