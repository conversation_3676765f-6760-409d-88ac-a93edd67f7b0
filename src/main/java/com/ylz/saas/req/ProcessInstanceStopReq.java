package com.ylz.saas.req;

import com.ylz.saas.service.SrmProcessConfigService;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/6/16 16:22
 * @Description
 */
@Data
public class ProcessInstanceStopReq {

    /**
     * 业务类型
     */
    @NotNull(message = "业务类型不能为空")
    private SrmProcessConfigService.BizTypeEnum bizType;

    /**
     * 业务key
     */
    @NotBlank(message = "业务key不能为空")
    private String bizKey;

    /**
     * 终止原因
     */
    private String reason;
}
