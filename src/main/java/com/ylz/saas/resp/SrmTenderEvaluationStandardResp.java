package com.ylz.saas.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 评标标准响应类
 */
@Data
@Schema(description = "评标标准响应")
public class SrmTenderEvaluationStandardResp implements Serializable {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 采购立项ID
     */
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 标段ID
     */
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 评审类型（评审项、评分项）
     */
    @Schema(description = "评审类型（评审项、评分项）")
    private String type;

    /**
     * 节点名称（评审类型下的子分类）
     */
    @Schema(description = "节点名称（评审类型下的子分类）")
    private String nodeName;

    /**
     * 评审项名称
     */
    @Schema(description = "评审项名称")
    private String itemName;

    /**
     * 评审项描述
     */
    @Schema(description = "评审项描述")
    private String itemDescription;

    /**
     * 满分分值（仅评分项有效）
     */
    @Schema(description = "满分分值（仅评分项有效）")
    private Integer maxScore;

    /**
     * 权重（仅评分项有效）
     */
    @Schema(description = "权重（仅评分项有效）")
    private Double weight;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortOrder;

    /**
     * 是否必填
     */
    @Schema(description = "是否必填")
    private Integer isRequired;

    /**
     * 创建人名称
     */
    @Schema(description = "创建人名称")
    private String createByName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人名称
     */
    @Schema(description = "修改人名称")
    private String updateByName;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}
