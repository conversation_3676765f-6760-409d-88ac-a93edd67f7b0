package com.ylz.saas.codegen.base_expert.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ylz.saas.codegen.base_expert_achievement.entity.BaseExpertAchievementEntity;
import com.ylz.saas.codegen.base_expert_education.entity.BaseExpertEducationEntity;
import com.ylz.saas.codegen.base_expert_work_experience.entity.BaseExpertWorkExperienceEntity;
import com.ylz.saas.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 专家信息表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:42:08
 */
@Data
@TableName("base_expert")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "专家信息表")
@TenantTable
public class BaseExpertEntity extends Model<BaseExpertEntity> {


    /**
  	 * 主键ID
   	 */
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

    /**
  	 * 组织ID
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="组织ID")
    private Long deptId;

    /**
  	 * 专家编码
   	 */
    @Schema(description="专家编码")
    private String expertCode;

    /**
  	 * 姓名
   	 */
    @Schema(description="姓名")
    private String name;

    /**
  	 * 手机号
   	 */
    @Schema(description="手机号")
    private String phone;

    /**
  	 * 专家分类
   	 */
    @Schema(description="专家分类")
    private String expertCategory;

    /**
  	 * 登录账号
   	 */
    @Schema(description="登录账号")
    private String loginAccount;

    /**
  	 * 所在地
   	 */
    @Schema(description="所在地")
    private String address;

    /**
  	 * 身份证号码
   	 */
    @Schema(description="身份证号码")
    private String idNumber;

    /**
  	 * 专家类型  @ExpertTypeEnum
   	 */
    @Schema(description="专家类型")
    private String expertType;

    /**
  	 * 关联用户ID
   	 */
    @Schema(description="关联用户ID")
    private Long userId;

    /**
  	 * 邮箱
   	 */
    @Schema(description="邮箱")
    private String email;

    /**
  	 * 状态
   	 */
    @Schema(description="状态")
    private String status;

    /**
  	 * 创建人名称
   	 */
    @Schema(description="创建人名称")
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    @Schema(description="修改人名称")
    private String updateByName;

    /**
  	 * 创建人
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
  	 * 创建时间
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

    /**
  	 * 修改时间
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标识（0-正常、1-删除）")
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    @Schema(description="租户ID")
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人ID")
    private Long createById;

    /**
  	 * 更新人ID
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人ID")
    private Long updateById;
	/**
	* 子表集合:base_expert_achievement
	*/
	@ExcelIgnore
	@TableField(exist = false)
	private List<BaseExpertAchievementEntity> baseExpertAchievementList;
	/**
	* 子表集合:base_expert_education
	*/
	@ExcelIgnore
	@TableField(exist = false)
	private List<BaseExpertEducationEntity> baseExpertEducationList;
	/**
	* 子表集合:base_expert_work_experience
	*/
	@ExcelIgnore
	@TableField(exist = false)
	private List<BaseExpertWorkExperienceEntity> baseExpertWorkExperienceList;
}