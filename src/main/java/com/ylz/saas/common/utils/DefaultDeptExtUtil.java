package com.ylz.saas.common.utils;

import com.ylz.saas.admin.api.entity.SysDept;
import com.ylz.saas.admin.api.entity.SysPost;
import com.ylz.saas.admin.api.entity.SysRole;
import com.ylz.saas.admin.service.SysDeptService;
import com.ylz.saas.admin.service.SysPostService;
import com.ylz.saas.admin.service.SysRoleService;
import com.ylz.saas.common.security.util.SecurityUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2025/6/13 10:16
 * @Description
 */
@Component
@AllArgsConstructor
public class DefaultDeptExtUtil {

    private final SysDeptService sysDeptService;

    private final SysRoleService sysRoleService;

    private final SysPostService sysPostService;






    public SysRole getRoleByCode(String roleCode) {
        Long tenantId = SecurityUtils.getUser().getTenantId();
        return sysRoleService.findRoleCode(roleCode, tenantId);
    }

    public SysDept getDeptByName(String deptName) {
        return sysDeptService.lambdaQuery().eq(SysDept::getName, deptName).one();
    }

    public SysPost getPostByCode(String postCode) {
        return sysPostService.lambdaQuery().eq(SysPost::getPostCode, postCode).one();
    }
}
