package com.ylz.saas.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 采购立项物资明细表
 */
@Data
public class SrmOpenTenderMaterialResp {
/*************srm_procurement_project_item**********/
    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 规格型号
     */
    private String specModel;

    /**
     * 单位
     */
    private String unit;

    /**
     * 需求数量
     */
    private BigDecimal requiredQuantity;
/*****************srm_tenant_supplier_info************************/
    /**
     * 平台供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 供应商编号
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商简称
     */
    private String supplierShortName;

    /**********srm_tender_bidder_quote_item*************/

    /**
     * 报价数量
     */
    private BigDecimal quoteQuantity;

    /**
     * 报价单价
     */
    private BigDecimal quotePrice;

    /**
     * 报价金额
     */
    private BigDecimal quoteAmount;

    /**
     * 报价时间
     */
    private LocalDateTime quoteTime;

    /**
     * 报价IP
     */
    private String quoteIp;

    /**
     * 中标数量
     */
    private BigDecimal awardedQuantity;

    /*******************字段*******************/

    /**
     * 出厂价
     */
    private BigDecimal factoryPrice;

    /**
     * 运费
     */
    private BigDecimal freight;

    /**
     * 到厂价
     */
    private BigDecimal arrivalPrice;

}