package com.ylz.saas.resp;

import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.InviteMethodEnum;
import com.ylz.saas.enums.PublicNoticeStatusEnum;
import com.ylz.saas.enums.PublicityStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 中标公示信息
 */
@Data
public class SrmAllPublicityPageResp implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 公示id
     */
    private Long noticeId;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 寻源方式
     */
    private BaseServiceTypeFieldService.BuyWayEnum sourcingMethod;


    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 公开类别
     */
    private InviteMethodEnum publicCategory;

    /**
     * 公告作者
     */
    private String noticeAuthor;

    /**
     * 发布人
     */
    private String publisher;

    /**
     * 发布时间
     */
    private String publishTime;

    /**
     * 公示开始时间
     */
    private LocalDateTime publicityStartTime;

    /**
     * 公示截止时间
     */
    private LocalDateTime publicityEndTime;

    /**
     * 公告状态
     */
    private PublicNoticeStatusEnum noticeStatus;

    // ========== 来自 SrmTenderEvaluationResult 的相关字段 ==========

    /**
     * 评标结果ID
     */
    private Long evaluationResultId;

    /**
     * 公示标题
     */
    private String publicityTitle;

    /**
     * 公示内容
     */
    private String publicityContent;

    /**
     * 中标总金额
     */
    private BigDecimal awardedAmountTotal;

    /**
     * 供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 公示状态
     */
    private PublicityStatusEnum publicityStatus;

    /**
     * 公示审核状态
     */
    private ApproveStatusEnum publicityAuditStatus;

    /**
     * 附件信息
     */
    private List<SrmProjectAttachment> attachmentList;

    /**
     * 公告内容
     */
    private String noticeContent;

}