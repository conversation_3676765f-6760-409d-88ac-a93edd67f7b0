package com.ylz.saas.codegen.base_quality_attribute.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ylz.saas.codegen.base_quality_attribute_value.entity.BaseQualityAttributeValueEntity;
import com.ylz.saas.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 质量属性表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:42:43
 */
@Data
@TableName("base_quality_attribute")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "质量属性表")
@TenantTable
public class BaseQualityAttributeEntity extends Model<BaseQualityAttributeEntity> {


    /**
  	 * 主键ID
   	 */
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

    /**
  	 * 组织ID
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="组织ID")
    private Long deptId;

    /**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
    @Schema(description="状态（DISABLED-禁用、ENABLED-启用）")
    private String status;

    /**
  	 * 属性编码
   	 */
    @Schema(description="属性编码")
    private String attributeCode;

    /**
  	 * 属性名称
   	 */
    @Schema(description="属性名称")
    private String attributeName;

    /**
  	 * 排序
   	 */
    @Schema(description="排序")
    private Integer sortOrder;

    /**
  	 * 属性描述
   	 */
    @Schema(description="属性描述")
    private String description;

    /**
  	 * 创建人名称
   	 */
    @Schema(description="创建人名称")
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    @Schema(description="修改人名称")
    private String updateByName;

    /**
  	 * 创建人
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
  	 * 创建时间
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

    /**
  	 * 修改时间
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标识（0-正常、1-删除）")
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    @Schema(description="租户ID")
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人ID")
    private Long createById;

    /**
  	 * 更新人ID
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人ID")
    private Long updateById;
	/**
	* 子表集合:base_quality_attribute_value
	*/
	@ExcelIgnore
	@TableField(exist = false)
	private List<BaseQualityAttributeValueEntity> baseQualityAttributeValueList;
}