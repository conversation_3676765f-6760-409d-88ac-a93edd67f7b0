package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ylz.saas.common.core.util.TenantTable;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.EvaluationResultEnum;
import com.ylz.saas.enums.PublicNoticeStatusEnum;
import com.ylz.saas.enums.PublicityStatusEnum;
import com.ylz.saas.req.SrmTenderBidReq;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 评标结果表
 * @TableName srm_tender_evaluation_result
 */
@TableName(value = "srm_tender_evaluation_result")
@Data
@TenantTable
public class SrmTenderEvaluationResult implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 采购立项ID
     */
    private Long projectId;

    /**
     * 招标公告ID
     */
    private Long noticeId;

    /**
     * 标段ID
     */
    private Long sectionId;

    /**
     * 中标总金额
     */
    private BigDecimal awardedAmountTotal;

    /**
     * 供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 定标备注
     */
    private String awardRemark;

    /**
     * 定标报告状态
     * TO_APPROVE("待审批"),
     *     APPROVING("审批中"),
     *     APPROVE("审批通过"),
     *     APPROVE_REJECT("审批驳回"),
     *     APPROVE_REVOKE("审批撤销"),
     */
    private ApproveStatusEnum awardReportStatus;

    /**
     * 定标报告模板ID
     */
    private Long awardTemplateId;

    /**
     * 审批备注
     */
    private String examineRemark;

    /**
     * 定标报告内容
     */
    private String awardReportContent;

    /**
     * 定标报告时间
     */
    private LocalDateTime awardReportTime;

    /**
     * 公示状态(UNPUBLISHED-未公示、PUBLISHED-已公示、EXPIRED-已过期)
     */
    private PublicityStatusEnum publicityStatus;

    /**
     * 公示标题
     */
    private String publicityTitle;

    /**
     * 公示引用模版ID
     */
    private Long publicityTemplateId;

    /**
     * 公示内容
     */
    private String publicityContent;

    /**
     * 公示审核状态
     * TO_APPROVE("待审批"),
     *     APPROVING("审批中"),
     *     APPROVE("审批通过"),
     *     APPROVE_REJECT("审批驳回"),
     *     APPROVE_REVOKE("审批撤销"),
     */
    private ApproveStatusEnum publicityAuditStatus;
    /**
     * 公告审核状态
     * TO_APPROVE("待审批"),
     *     APPROVING("审批中"),
     *     APPROVE("审批通过"),
     *     APPROVE_REJECT("审批驳回"),
     *     APPROVE_REVOKE("审批撤销"),
     */
    private ApproveStatusEnum noticeAuditStatus;
    /**
     * 公示开始时间
     */
    private LocalDateTime publicityStartTime;

    /**
     * 公示结束时间
     */
    private LocalDateTime publicityEndTime;

    /**
     * 公告状态(UNPUBLISHED-未公告、PUBLISHED-已公告)
     */
    private PublicNoticeStatusEnum noticeStatus;

    /**
     * 公告标题
     */
    private String noticeTitle;

    /**
     * 公示引用模版ID
     */
    private Long noticeTemplateId;

    /**
     * 公告内容
     */
    private String noticeContent;

    /**
     * 公告时间
     */
    private LocalDateTime noticeTime;

    /**
     * 整体状态(DRAFT-草稿、PROCESSING-处理中、COMPLETED-已完成)
     */
    private EvaluationResultEnum resultStatus;

    /**
     * 删除标识（0-正常、1-删除）
     */
    private Integer delFlag;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createById;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updateById;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 招标公告标题
     */
    @TableField(exist = false)
    private String tenderNoticeTitle;

    /**
     * 招标项目物料信息
     */
    @TableField(exist = false)
    private List<SrmTenderBidReq.BidProjectItem> projectItemList;


    /**
     * 定标附件列表
     */
    @TableField(exist = false)
    private List<SrmProjectAttachment> awardAttachmentList;


    /**
     * 合同标题
     */
    @TableField(exist = false)
    private String contractTitle;

    /**
     * 合同内容
     */
    @TableField(exist = false)
    private String contractContent;

    /**
     * 合同附件列表
     */
    @TableField(exist = false)
    private List<SrmProjectAttachment> contractAttachmentList;

}