package com.ylz.saas.mapper;

import com.ylz.saas.common.data.datascope.annotation.DataPermission;
import com.ylz.saas.entity.SrmTenderSupplierInvite;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【srm_tender_supplier_invite(供应商邀请表)】的数据库操作Mapper
* @createDate 2025-06-11 13:22:39
* @Entity com.ylz.saas.entity.SrmTenderSupplierInvite
*/
@Mapper
//@DataPermission(userEnabled = true, dataPermissionRuleList = "[{\"dataTypeCode\":\"PROJECT\",\"targetField\":\"project_id\",\"dataSourceField\":\"ID\"}]")
public interface SrmTenderSupplierInviteMapper extends SaasBaseMapper<SrmTenderSupplierInvite> {

}




