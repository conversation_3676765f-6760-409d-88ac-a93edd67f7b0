/*
 Navicat Premium Data Transfer

 Source Server         : A参盘-绿色动力
 Source Server Type    : MySQL
 Source Server Version : 50742
 Source Host           : qa-dc-saas-rds-zjk-01.dc-qa.yunlizhi.net:3306
 Source Schema         : saas_ayn_17

 Target Server Type    : MySQL
 Target Server Version : 50742
 File Encoding         : 65001

 Date: 09/07/2025 16:31:50
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for srm_tender_bid_evaluation_scoring
-- ----------------------------
DROP TABLE IF EXISTS `srm_tender_bid_evaluation_scoring`;
CREATE TABLE `srm_tender_bid_evaluation_scoring`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `dept_id` bigint(20) NOT NULL COMMENT '部门ID',
  `project_id` bigint(20) NOT NULL COMMENT '采购立项ID',
  `notice_id` bigint(20) NOT NULL COMMENT '招标公告ID',
  `section_id` bigint(20) NOT NULL COMMENT '标段ID',
  `del_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标识（0-正常、1-删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_by_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人名称',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `update_by_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人名称',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `user_id` bigint(20) NOT NULL COMMENT '专家用户id',
  `scoring_detail_id` bigint(20) NOT NULL COMMENT '评分详情id',
  `response_id` bigint(20) NULL DEFAULT NULL COMMENT '投标响应表ID',
  `tenant_supplier_id` bigint(20) NOT NULL COMMENT '供应商id',
  `score` int(11) NULL DEFAULT NULL COMMENT '分值（评分项）',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '待提交 已提交（评审完成） 已审核（已汇总）',
  `is_conform` tinyint(2) NULL DEFAULT NULL COMMENT '是否符合（评审项）',
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型（评审项、评分项）',
  `scoring_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'EXPERT_SCORING' COMMENT '评分类型（EXPERT_SCORING-专家评分，LEADER_SUMMARY-组长汇总）',
  `conclusion` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评审结论',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 129 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '评标专家打分' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
