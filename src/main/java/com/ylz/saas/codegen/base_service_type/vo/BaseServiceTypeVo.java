package com.ylz.saas.codegen.base_service_type.vo;

import com.ylz.saas.codegen.base_service_type.service.BaseServiceTypeService;
import com.ylz.saas.codegen.base_service_type_field.entity.BaseServiceTypeFieldEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 服务类型表
 *
 * <AUTHOR>
 * @date 2025-06-03 21:25:49
 */
@Data
public class BaseServiceTypeVo implements Serializable {

    /**
  	 * 主键ID
   	 */
    private Long id;

    /**
  	 * 组织ID
   	 */
	@NotNull(message = "组织ID不能为空")
    private Long deptId;

    /**
  	 * 类型编码
   	 */
    private String typeCode;

    /**
  	 * 类型名称
   	 */
    @NotBlank(message = "类型名称不能为空")
    private String typeName;

    /**
  	 * 描述
   	 */
    private String description;

    /**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
    private BaseServiceTypeService.StatusEnum status;

    /**
  	 * 排序
   	 */
    private Integer sort;

    /**
  	 * 创建人名称
   	 */
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    private String updateByName;

    /**
  	 * 创建人
   	 */
    private String createBy;

    /**
  	 * 创建时间
   	 */
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
    private String updateBy;

    /**
  	 * 修改时间
   	 */
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
    private Long createById;

    /**
  	 * 更新人ID
   	 */
    private Long updateById;
	/**
	* 子表集合:base_service_type_field
	*/
	private List<BaseServiceTypeFieldEntity> baseServiceTypeFieldList;
}