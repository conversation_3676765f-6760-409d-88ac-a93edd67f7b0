package com.ylz.saas.job;

import com.xxl.job.core.handler.annotation.XxlJob;
import com.ylz.saas.service.SrmProcurementProjectService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2025/7/1 13:40
 * @Description
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SrmProcurementProjectProgressStatusJob {

    private final SrmProcurementProjectService srmProcurementProjectService;


    @XxlJob("srmProcurementProjectProgressStatusHandler")
    public void srmProcurementProjectProgressStatusHandler(String s) {
        long startTime = System.currentTimeMillis();
        log.info("处理项目状态开始！");
        try {
            srmProcurementProjectService.autoUpdateProgressStatus();
        } catch (Exception e) {
            log.error("处理项目状态异常：",e);
        }
        log.info("处理项目状态完成！时间：{}", System.currentTimeMillis()-startTime);
    }


}
