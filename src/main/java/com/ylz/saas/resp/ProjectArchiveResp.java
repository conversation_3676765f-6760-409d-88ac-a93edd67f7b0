package com.ylz.saas.resp;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.entity.SrmTenderEvaluationResult;
import com.ylz.saas.entity.SrmTenderSupplierResponse;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/18 13:39
 * @Description
 */
@Data
public class ProjectArchiveResp {

    /**
     * 附件列表
     */
    private List<SrmProjectAttachment> projectAttachmentList;

    /**
     * 招标公告标题
     */
    private String noticeTitle;

    /**
     * 招标公告内容
     */
    private String noticeContent;

    /**
     * 招标公告附件列表
     */
    private List<SrmProjectAttachment> noticeAttachmentList;

    /**
     * 招标结果列表
     */
    private List<SrmTenderEvaluationResult> evaluationResultList;

    /**
     * 定标公示标题
     */
    private String awardPublicityTitle;

    /**
     * 定标公示内容
     */
    private String awardPublicityContent;

    /**
     * 定标公示附件列表
     */
    @TableField(exist = false)
    private List<SrmProjectAttachment> awardPublicityAttachmentList;

    /**
     * 定标公告标题
     */
    private String awardNoticeTitle;

    /**
     * 定标公告内容
     */
    private String awardNoticeContent;

    /**
     * 定标公告附件列表
     */
    @TableField(exist = false)
    private List<SrmProjectAttachment> awardNoticeAttachmentList;


    /**
     * 标段供应商附件列表
     */
    private List<SectionSupplierAttachment> sectionSupplierAttachmentList;


    @Data
    public static class SectionSupplierAttachment {

        /**
         * 标段id
         */
        private Long sectionId;

        /**
         * 标段名称
         */
        private String sectionName;

        /**
         * 供应商响应列表
         */
        private List<SrmTenderSupplierResponse> srmTenderSupplierResponseList;

    }




}
