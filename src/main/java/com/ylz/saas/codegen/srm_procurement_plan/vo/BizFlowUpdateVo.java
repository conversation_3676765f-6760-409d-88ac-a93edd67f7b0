package com.ylz.saas.codegen.srm_procurement_plan.vo;

import com.ylz.saas.codegen.srm_procurement_plan.service.SrmProcurementPlanService;
import com.ylz.saas.enums.ApproveStatusEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 业务审批流执行后的更新方法实体
 *
 * <AUTHOR>
 * @date 2025-06-03 20:39:40
 */
@Data
public class BizFlowUpdateVo implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 单据状态
     */
    private SrmProcurementPlanService.DocumentStatus status;

    /**
     * 当前审批人
     */
    private String currentApprover;

    /**
     * 审批状态
     */
    private ApproveStatusEnum approveStatus;
}