//package com.ylz.saas.test.controller;
//
//import cn.hutool.core.bean.BeanUtil;
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import com.ylz.saas.common.core.util.R;
//import com.ylz.saas.common.log.annotation.SysLog;
//import com.ylz.saas.test.vo.SingleTableVo;
//import com.ylz.saas.test.excel.SingleTableResp;
//import com.ylz.saas.test.param.SingleTableParam;
//import com.ylz.saas.test.entity.SingleTableEntity;
//import com.ylz.saas.test.service.SingleTableService;
//import com.ylz.saas.common.excel.annotation.ResponseExcel;
//import io.swagger.v3.oas.annotations.security.SecurityRequirement;
//import org.springframework.http.HttpHeaders;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Operation;
//import lombok.RequiredArgsConstructor;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.multipart.MultipartFile;
//import jakarta.servlet.http.HttpServletResponse;
//import jakarta.validation.Valid;
//
//import java.util.List;
//
//
///**
// * 单表测试页面
// *
// * <AUTHOR>
// * @date 2025-05-19 15:35:40
// */
//@RestController
//@RequiredArgsConstructor
//@RequestMapping("/singleTable" )
//@Tag(description = "singleTable" , name = "单表测试页面管理" )
//@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
//public class SingleTableController {
//
//    private final SingleTableService singleTableService;
//
//    /**
//     * 分页查询
//     * @param page 分页对象
//     * @param param 单表测试页面
//     * @return
//     */
//    @Operation(summary = "分页查询" , description = "分页查询" )
//    @PostMapping("/page" )
//    public R getSingleTablePage(@RequestBody SingleTableParam param, Page page) {
//        QueryWrapper<SingleTableEntity> queryWrapper = param.toWrapper();
//        return R.ok(singleTableService.page(page, queryWrapper));
//    }
//
//
//    /**
//     * 通过id查询单表测试页面
//     * @param id id
//     * @return R
//     */
//    @Operation(summary = "通过id查询" , description = "通过id查询" )
//    @GetMapping("/{id}" )
//    public R getById(@PathVariable("id" ) Long id) {
//        return R.ok(singleTableService.getById(id));
//    }
//
//    /**
//     * 新增单表测试页面
//     * @param singleTableVo 单表测试页面
//     * @return R
//     */
//    @Operation(summary = "新增单表测试页面" , description = "新增单表测试页面" )
//    @SysLog("新增单表测试页面" )
//    @PostMapping("/save")
//    public R save(@Valid @RequestBody SingleTableVo singleTableVo) {
//        return R.ok(singleTableService.saveSingleTable(singleTableVo));
//    }
//
//    /**
//     * 修改单表测试页面
//     * @param singleTableVo 单表测试页面
//     * @return R
//     */
//    @Operation(summary = "修改单表测试页面" , description = "修改单表测试页面" )
//    @SysLog("修改单表测试页面" )
//    @PostMapping("/update")
//    public R updateById(@Valid @RequestBody SingleTableVo singleTableVo) {
//        return R.ok(singleTableService.updateSingleTable(singleTableVo));
//    }
//
//    /**
//     * 通过id删除单表测试页面
//     * @param ids id列表
//     * @return R
//     */
//    @Operation(summary = "通过id删除单表测试页面" , description = "通过id删除单表测试页面" )
//    @SysLog("通过id删除单表测试页面" )
//    @GetMapping("remove")
//    public R removeById(@RequestParam List<Long> ids) {
//		boolean remove = singleTableService.removeByIds(ids);
//		return R.ok(remove);
//    }
//
//
//    /**
//     * 导出excel 表格
//     * @param param 查询条件
//     * @return excel 文件流
//     */
//    @ResponseExcel
//    @PostMapping("/export")
//	@Operation(summary = "导出excel" , description = "导出excel" )
//    public List<SingleTableResp> export(@RequestBody SingleTableParam param) {
//		QueryWrapper<SingleTableEntity> queryWrapper = param.toWrapper();
//		List<SingleTableEntity> entityList = singleTableService.list(queryWrapper);
//        return BeanUtil.copyToList(entityList, SingleTableResp.class);
//    }
//
//	/**
//     * 导入excel 表格
//     * @param file
//     * @param response
//     * @return
//     */
//    @PostMapping("/import")
//    @Operation(summary = "导入excel" , description = "导入excel" )
//    public R uploadData(@RequestParam("file") MultipartFile file,HttpServletResponse response) {
//        singleTableService.uploadData(file,response);
//        return R.ok();
//    }
//
//    /**
//     * excel模板下载
//     * @param response
//     */
//    @GetMapping("/template")
//    @Operation(summary = "excel模板下载" , description = "excel模板下载" )
//    public void template(HttpServletResponse response) {
//        singleTableService.template(response);
//    }
//}