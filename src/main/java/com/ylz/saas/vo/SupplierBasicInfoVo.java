package com.ylz.saas.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 供应商基本信息VO
 *
 * <AUTHOR>
 * @Date 2025/6/11 11:45
 * @Description
 */
@Data
@Schema(description = "供应商基本信息VO")
public class SupplierBasicInfoVo {

    /**
     * 租户供应商id（更新时必填，新增时为空）
     */
    @Schema(description = "租户供应商id（更新时必填，新增时为空）")
    private Long id;

//    /**
//     * 供应商编号（必填）
//     */
//    @NotBlank(message = "供应商编号不能为空")
//    @Schema(description = "供应商编号", required = true)
//    private String supplierCode;

    /**
     * 供应商名称（必填）
     */
    @NotBlank(message = "供应商名称不能为空")
    @Schema(description = "供应商名称", required = true)
    private String supplierName;

    /**
     * 供应商简称
     */
    @Schema(description = "供应商简称")
    private String supplierShortName;

    /**
     * 供应商类型(MANUFACTURER-生产型供应商、TRADER-贸易型供应商、SERVICE-服务型供应商、LOGISTICS-物流承运商、PROJECT-工程建筑供应商、LABOR-劳务外包供应商、RENTAL-租赁服务供应商、OTHER-其他)
     */
    @Schema(description = "供应商类型")
    private String supplierType;

    /**
     * 企业性质(STATE_OWNED-国有企业、PRIVATE-民营企业、FOREIGN-外资企业、JOINT_VENTURE-合资企业、LISTED-上市公司、OTHER-其他)
     */
    @Schema(description = "企业性质")
    private String enterpriseNature;

    /**
     * 供应商来源(PLATFORM_APPLY-平台申请、INVITE_JOIN-邀请加入)
     */
    @Schema(description = "供应商来源")
    private String supplierSource;

    /**
     * 审批状态(DRAFT-草稿、APPROVING-审批中、APPROVED-已审批、REJECTED-已驳回)
     */
    @Schema(description = "审批状态")
    private String approvalStatus;

    /**
     * 统一社会信用代码
     */
    @Schema(description = "统一社会信用代码")
    private String socialCreditCode;

    /**
     * 成立日期
     */
    @Schema(description = "成立日期")
    private Date establishDate;

    /**
     * 注册资本
     */
    @Schema(description = "注册资本")
    private BigDecimal registeredCapital;

    /**
     * 法人代表
     */
    @Schema(description = "法人代表")
    private String legalPerson;

    /**
     * 法人身份证号
     */
    @Schema(description = "法人身份证号")
    private String legalPersonId;

    /**
     * 法人联系电话
     */
    @Schema(description = "法人联系电话")
    private String legalPersonPhone;

    /**
     * 注册地址
     */
    @Schema(description = "注册地址")
    private String registeredAddress;

    /**
     * 邮政编码
     */
    @Schema(description = "邮政编码")
    private String postalCode;

    /**
     * 传真
     */
    @Schema(description = "传真")
    private String fax;

    /**
     * 年平均营业额
     */
    @Schema(description = "年平均营业额")
    private BigDecimal annualRevenue;

    /**
     * 公司简介
     */
    @Schema(description = "公司简介")
    private String companyProfile;

    /**
     * 经营范围
     */
    @Schema(description = "经营范围")
    private String businessScope;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    // 租户供应商信息相关字段

    /**
     * 供应商分类(COMMON-普通供应商、STRATEGIC-战略供应商)（必填）
     */
    @NotBlank(message = "供应商分类不能为空")
    @Schema(description = "供应商分类", required = true)
    private String supplierCategory;

    /**
     * 供应商状态(QUALIFIED-合格供应商、POTENTIAL-潜在供应商、UNQUALIFIED-不合格供应商、BLACKLIST-黑名单供应商、TERMINATED-终止合作)（必填）
     */
    @Schema(description = "供应商状态", required = true)
    private String supplierStatus;

    /**
     * 服务开始日期
     */
    @Schema(description = "服务开始日期")
    private LocalDate serviceStartDate;

    /**
     * 服务结束日期
     */
    @Schema(description = "服务结束日期")
    private LocalDate serviceEndDate;

    /**
     * 租户供应商备注
     */
    @Schema(description = "租户供应商备注")
    private String tenantRemark;

    /**
     * 关联部门ID集合
     */
    @Schema(description = "关联部门ID集合")
    private List<Long> deptIds;
    /**
     * 法人委托书
     */
    private String letterOfAuthorization;

    /**
     * 是否为所有组织可用（0-否、1-是）
     */
    @Schema(description = "是否为所有组织可用")
    private Boolean isAllDept;
}
