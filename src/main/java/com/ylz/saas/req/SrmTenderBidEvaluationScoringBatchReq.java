package com.ylz.saas.req;

import com.ylz.saas.entity.SrmTenderBidEvaluationScoring;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 专家打分批量保存请求类
 */
@Data
@Schema(description = "专家打分批量保存请求类")
public class SrmTenderBidEvaluationScoringBatchReq implements Serializable {
    /**
     * 当前报价轮次
     */
    @Schema(description = "当前报价轮次")
    private Integer currentRound;
    /**
     * 打分记录列表
     */
    @NotEmpty(message = "打分记录列表不能为空")
    @Valid
    @Schema(description = "打分记录列表")
    private List<SrmTenderBidEvaluationScoringReq> scoringList;

    /**
     * 保存还是提交
     */
    @NotNull(message = "状态不能为空")
    private SrmTenderBidEvaluationScoring.SubmitStatus status;

    private static final long serialVersionUID = 1L;
}
