package com.ylz.saas.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 供应商银行信息VO
 *
 * <AUTHOR>
 * @Date 2025/6/11 11:45
 * @Description
 */
@Data
@Schema(description = "供应商银行信息VO")
public class SupplierBankAccountInfoVo {

    /**
     * 银行账户ID（更新时必填，新增时为空）
     */
    @Schema(description = "银行账户ID（更新时必填，新增时为空）")
    private Long id;

    /**
     * 供应商ID（必填）
     */
//    @NotNull(message = "供应商ID不能为空")
    @Schema(description = "供应商ID", required = true)
    private Long supplierId;

    /**
     * 开户行名称（必填）
     */
    @NotBlank(message = "开户行名称不能为空")
    @Schema(description = "开户行名称", required = true)
    private String bankName;

    /**
     * 开户行行号
     */
    @Schema(description = "开户行行号")
    private String bankCode;

    /**
     * 开户行地址
     */
    @Schema(description = "开户行地址")
    private String bankAddress;

    /**
     * 开户名（必填）
     */
    @NotBlank(message = "开户名不能为空")
    @Schema(description = "开户名", required = true)
    private String accountName;

    /**
     * 开户行账号（必填）
     */
    @NotBlank(message = "开户行账号不能为空")
    @Schema(description = "开户行账号", required = true)
    private String accountNo;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}
