package com.ylz.saas.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 评标汇总查询响应类
 * <AUTHOR>
 * @createDate 2025-07-11
 */
@Data
@Schema(description = "评标汇总查询响应类")
public class SrmTenderEvaluationSummaryQueryResp implements Serializable {

    /**
     * 评标委员会id
     */
    private Long evaluationId;
    /**
     * 供应商汇总列表
     */
    @Schema(description = "供应商汇总列表")
    private List<SrmTenderEvaluationSummarySupplierResp> supplierList;

    /**
     * 评标报告内容
     */
    @Schema(description = "评标报告内容")
    private String reportContent;

    /**
     * 报告模板ID
     */
    @Schema(description = "报告模板ID")
    private Long reportTemplateId;

    /**
     * 评标节点列表（用于动态列显示）
     */
    @Schema(description = "评标节点列表")
    private List<String> evaluationNodes;

    /**
     * 汇总状态
     */
    @Schema(description = "汇总状态")
    private String summaryStatus;

    /**
     * 汇总人
     */
    @Schema(description = "汇总人")
    private String summaryBy;

    /**
     * 汇总人名称
     */
    @Schema(description = "汇总人名称")
    private String summaryByName;

    /**
     * 汇总时间
     */
    @Schema(description = "汇总时间")
    private LocalDateTime summaryTime;

    private static final long serialVersionUID = 1L;
}
