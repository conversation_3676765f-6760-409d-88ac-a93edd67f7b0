package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.entity.SrmProcurementProjectSection;
import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.entity.SrmTenderFailedLog;
import com.ylz.saas.entity.SrmTenderNotice;
import com.ylz.saas.entity.SrmTenderRequirement;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.ExceptionHandleTypeEnum;
import com.ylz.saas.enums.ProjectProgressStatusEnum;
import com.ylz.saas.enums.PublicNoticeStatusEnum;
import com.ylz.saas.mapper.SrmTenderFailedLogMapper;
import com.ylz.saas.service.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【srm_tender_failed_log(流标记录表)】的数据库操作Service实现
 * @createDate 2025-06-24 20:17:56
 */
@Slf4j
@Service
public class SrmTenderFailedLogServiceImpl extends ServiceImpl<SrmTenderFailedLogMapper, SrmTenderFailedLog>
        implements SrmTenderFailedLogService, ApproveRejectHookService {

    @Resource
    private SrmProjectAttachmentService attachmentInfoService;

    @Resource
    private SrmTenderNoticeService tenderNoticeService;

    @Resource
    private SrmTenderRequirementService requirementService;

    @Resource
    private SrmProcurementProjectService projectService;
    @Autowired
    private SrmProcessInstanceService srmProcessInstanceService;

    @Override
    public List<SrmTenderFailedLog> tenderFailedLogDetail(String projectAndNoticeId) {
        String[] split = projectAndNoticeId.split("#");
        String projectId = split[0];
        String noticeId = split[1];
        return lambdaQuery().eq(SrmTenderFailedLog::getProjectId, projectId)
                .eq(SrmTenderFailedLog::getNoticeId, noticeId)
                .list();
    }

    @Override
    public void tenderFailedLogPass(String projectId, String noticeId) {
        Long oldProjectId = Long.parseLong(projectId);
        Long oldNoticeId = Long.parseLong(noticeId);
        handlerFailedLogInfo(oldNoticeId, oldProjectId, ApproveStatusEnum.APPROVE);
    }

    @Override
    public void tenderFailedLogReject(String projectId, String noticeId) {
        Long oldProjectId = Long.parseLong(projectId);
        Long oldNoticeId = Long.parseLong(noticeId);
        log.info("流标审批拒绝开始，projectId: {}, noticeId: {}", oldProjectId, oldNoticeId);
        handlerFailedLogInfo(oldNoticeId, oldProjectId, ApproveStatusEnum.APPROVE_REJECT);
        log.info("流标审批拒绝完成，projectId: {}, noticeId: {}", oldProjectId, oldNoticeId);
    }

    private void handlerFailedLogInfo(Long oldNoticeId, Long oldProjectId, ApproveStatusEnum approved) {
        log.info("处理流标信息开始，noticeId: {}, projectId: {}, approved: {}", oldNoticeId, oldProjectId, approved);
        SrmTenderNotice oldNoticeNotice = tenderNoticeService.getById(oldNoticeId);
        ExceptionUtil.checkNonNull(oldNoticeNotice, "公告不存在");
        SrmProcurementProject procurementProject = projectService.getById(oldProjectId);
        ExceptionUtil.checkNonNull(procurementProject, "采购项目不存在");
        SrmProcurementProject srmProcurementProject = projectService.detail(procurementProject.getProjectCode(), false);
        // 校验流标信息
        List<SrmTenderFailedLog> failedLogs = lambdaQuery().eq(SrmTenderFailedLog::getNoticeId, oldNoticeId)
                .eq(SrmTenderFailedLog::getProjectId, oldProjectId)
                .notIn(SrmTenderFailedLog::getApprovalStatus, ApproveStatusEnum.APPROVE, ApproveStatusEnum.APPROVE_REJECT)
                .list();
        ExceptionUtil.checkNotEmpty(failedLogs, "此流标信息已审批！");
        ExceptionHandleTypeEnum exceptionHandleType = failedLogs.get(0).getExceptionHandleType();
        log.info("流标异常处理类型: {}, 审批状态: {}", exceptionHandleType, approved);
        if(approved == ApproveStatusEnum.APPROVE){
            log.info("审批通过，开始处理异常逻辑，异常处理类型: {}", exceptionHandleType);
            // 处理异常逻辑
            handlerExceptionType(exceptionHandleType, oldNoticeId, oldProjectId, srmProcurementProject);
            // 修改采购项目状态为流标
            projectService.updateProgressStatus(procurementProject.getProjectCode(), ProjectProgressStatusEnum.FAILED, true);
        } else {
            log.info("审批拒绝或其他状态，不执行异常处理逻辑");
        }
        // 修改流程状态为通过
        lambdaUpdate()
                .set(SrmTenderFailedLog::getApprovalStatus, approved)
                .eq(SrmTenderFailedLog::getNoticeId, oldNoticeId)
                .eq(SrmTenderFailedLog::getProjectId, oldProjectId)
                .update();
        // 修改流程状态
        srmProcessInstanceService.handlerInstanceStatus(oldProjectId, SrmProcessConfigService.BizTypeEnum.SRM_TENDER_FAILED_AUDIT, approved);

    }

    private void handlerExceptionType(ExceptionHandleTypeEnum exceptionHandleType, Long oldNoticeId, Long oldProjectId, SrmProcurementProject srmProcurementProject) {
        log.info("开始处理异常类型: {}, noticeId: {}, projectId: {}", exceptionHandleType, oldNoticeId, oldProjectId);
        switch (exceptionHandleType) {
            case RE_PURCHASE:
                log.info("处理重新采购逻辑，将作废原公告");
                // 沿用当前项目名称，重新创建一条询价数据，并自动带入本次询价公告的数据，可修改采购公告等，重新走采购流程，生成新的采购执行编号，重新走流程
                // 复制招标信息，设置新项目id
                SrmTenderNotice oldNotice = tenderNoticeService.getById(oldNoticeId);
                SrmTenderNotice newNotice = new SrmTenderNotice();
                BeanUtils.copyProperties(oldNotice, newNotice, "id");
                newNotice.setProjectId(oldProjectId);
                tenderNoticeService.save(newNotice);
                Long newNoticeId = newNotice.getId();

                // 复制招标要求信息，设置新的项目id，公告id
                List<SrmProcurementProjectSection> projectSectionList = srmProcurementProject.getProjectSectionList();
                List<SrmTenderRequirement> requirementList = requirementService.lambdaQuery()
                        .eq(SrmTenderRequirement::getNoticeId, oldNoticeId).list();
                // 复制并更新招标要求中的标段ID
                for (int i = 0; i < projectSectionList.size(); i++) {
                    SrmProcurementProjectSection projectSection = projectSectionList.get(i);
                    SrmTenderRequirement requirement = requirementList.get(i);
                    requirement.setId(null);
                    requirement.setSectionId(projectSection.getId());
                    requirement.setNoticeId(newNoticeId);
                    requirementService.save(requirement);
                }
                // 复制附件信息，设置新公告id
                List<SrmProjectAttachment> attachmentList = attachmentInfoService.lambdaQuery().eq(SrmProjectAttachment::getBusinessId, oldNoticeId).list();
                attachmentList.forEach(item -> {
                    item.setId(null);
                    item.setBusinessId(newNoticeId);
                    attachmentInfoService.save(item);
                });
                // 旧公告作废
                log.info("重新采购：将原公告状态设置为DISCARD，noticeId: {}", oldNoticeId);
                tenderNoticeService.lambdaUpdate().set(SrmTenderNotice::getStatus, PublicNoticeStatusEnum.DISCARD)
                        .eq(SrmTenderNotice::getId, oldNoticeId)
                        .update();
                break;
            case TERMINATE_PROJECT:
                log.info("项目终止：将公告状态设置为DISCARD，noticeId: {}", oldNoticeId);
                // 修改公告状态为作废
                tenderNoticeService.lambdaUpdate().set(SrmTenderNotice::getStatus, PublicNoticeStatusEnum.DISCARD)
                        .eq(SrmTenderNotice::getId, oldNoticeId)
                        .update();
                break;
            default:
                ExceptionUtil.checkNonNull(null, "变更类型错误！");
                break;
        }
    }

    @Override
    public boolean match(SrmProcessConfigService.BizTypeEnum bizType) {
        return bizType == SrmProcessConfigService.BizTypeEnum.SRM_TENDER_FAILED_AUDIT;
    }

    @Override
    public void approveRejectHook(String bizKey) {
        if (!bizKey.contains("#")) {
            ExceptionUtil.checkNonNull(null, "参数格式应为：projectId#noticeId，例如：123#23");
        }
        String[] split = bizKey.split("#");
        String projectId = split[0];
        String noticeId = split[1];
        this.lambdaUpdate().set(SrmTenderFailedLog::getApprovalStatus, ApproveStatusEnum.APPROVE_REVOKE)
                .eq(SrmTenderFailedLog::getProjectId, projectId)
                .eq(SrmTenderFailedLog::getNoticeId, noticeId)
                .update();
    }

    @Override
    public void failedBidApprovingHook(String noticeId) {
        this.lambdaUpdate().set(SrmTenderFailedLog::getApprovalStatus, ApproveStatusEnum.APPROVING)
                .eq(SrmTenderFailedLog::getNoticeId, noticeId)
                .update();
    }
}




