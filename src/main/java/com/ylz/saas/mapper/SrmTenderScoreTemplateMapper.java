package com.ylz.saas.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.entity.SrmTenderScoreTemplate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylz.saas.entity.SrmTenderScoreTemplateItem;
import com.ylz.saas.resp.SrmTenderScoreTemplateDetailResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tender_score_template(评分模板表)】的数据库操作Mapper
* @createDate 2025-07-09 16:30:35
* @Entity com.ylz.saas.entity.SrmTenderScoreTemplate
*/
@Mapper
public interface SrmTenderScoreTemplateMapper extends BaseMapper<SrmTenderScoreTemplate> {

    Page<SrmTenderScoreTemplate> selectPage(Page<SrmTenderScoreTemplate> page, String templateName, String templateCode);

    @Select("SELECT * FROM srm_tender_score_template_item WHERE template_id = #{templateId} AND type = #{type}")
    <T> List<T> selectItemsByType(@Param("templateId") Long templateId, @Param("type") String type, Class<T> clazz);


    String getMaxTemplateCodeForUpdate();



}




