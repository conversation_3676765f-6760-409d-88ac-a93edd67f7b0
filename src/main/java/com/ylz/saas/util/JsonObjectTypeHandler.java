/*
 * Copyright (c) 2011-2020, baomidou (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * https://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package com.ylz.saas.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.springframework.util.StringUtils;

/**
 * from com.baomidou.mybatisplus.extension.handlers.FastJsonObjectTypeHandler
 * 增加对内存数据库h2的支持
 * Fastjson 实现 JSON 字段类型处理器 List<Object> 和 Object
 *
 * <AUTHOR>
 * @date 2025/8/5 18:23
 * @since v0.0.1
 */
@Slf4j
@MappedTypes({Object.class})
@MappedJdbcTypes(JdbcType.VARCHAR)
public class JsonObjectTypeHandler<T> extends AbstractJsonTypeHandler<T> {
    private Class<T> type;

    public JsonObjectTypeHandler(Class<T> type) {
        super(type);
        if (log.isTraceEnabled()) {
            log.trace("FastJsonObjectTypeHandler(" + type + ")");
        }
        Assert.notNull(type, "Type argument cannot be null");
        this.type = type;
    }

    @Override
    public T parse(String json) {
        // 支持解析测试时 h2 数据库 json 类型字段
        if (StringUtils.hasText(json) && json.startsWith("\"")) {
            json = json.substring(1, json.length() - 1).replace("\\\"", "\"");
        }
        return JSON.parseObject(json, type);
    }

    @Override
    public String toJson(T obj) {
        return JSON.toJSONString(obj, SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteNullListAsEmpty, SerializerFeature.WriteNullStringAsEmpty);
    }
}