package com.ylz.saas.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 供应商联系人信息VO
 *
 * <AUTHOR>
 * @Date 2025/6/20 11:45
 * @Description
 */
@Data
@Schema(description = "供应商联系人信息VO")
public class SupplierContactInfoVo {

    /**
     * 联系人ID（更新时必填，新增时为空）
     */
    @Schema(description = "联系人ID（更新时必填，新增时为空）")
    private Long id;

    /**
     * 供应商ID（必填）
     */
//    @NotNull(message = "供应商ID不能为空")
    @Schema(description = "供应商ID", required = true)
    private Long supplierId;

    /**
     * 联系人姓名（必填）
     */
    @NotBlank(message = "联系人姓名不能为空")
    @Schema(description = "联系人姓名", required = true)
    private String contactName;

    /**
     * 联系人电话（必填）
     */
    @NotBlank(message = "联系人电话不能为空")
    @Schema(description = "联系人电话", required = true)
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    @Schema(description = "联系人邮箱")
    private String contactEmail;

    /**
     * 联系人身份证
     */
    @Schema(description = "联系人身份证")
    private String idNumber;

    /**
     * 职位
     */
    @Schema(description = "职位")
    private String position;

    /**
     * 登录账号（必填）
     */
//    @NotBlank(message = "登录账号不能为空")
    @Schema(description = "登录账号", required = true)
    private String loginAccount;

    /**
     * 是否生成账号（1-生成、0-不生成）
     */
    @Schema(description = "是否生成账号（1-生成、0-不生成）")
    private Integer generateAccount;

    /**
     * 账号状态(ENABLED-启用、DISABLED-禁用)
     */
    @Schema(description = "账号状态")
    private String accountStatus;

    /**
     * 密码（生成账号时使用，不传则使用默认密码123456）
     */
    @Schema(description = "密码（生成账号时使用，不传则使用默认密码123456）")
    private String password;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
    /**
     * 身份证附件
     */
    private String idAttachment;

    /**
     * 部门id
     *
     */
    private Long deptId;
}
