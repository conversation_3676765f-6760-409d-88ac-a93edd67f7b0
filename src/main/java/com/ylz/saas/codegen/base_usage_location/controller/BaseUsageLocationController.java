package com.ylz.saas.codegen.base_usage_location.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.codegen.base_usage_location.entity.BaseUsageLocationEntity;
import com.ylz.saas.codegen.base_usage_location.excel.BaseUsageLocationResp;
import com.ylz.saas.codegen.base_usage_location.param.BaseUsageLocationParam;
import com.ylz.saas.codegen.base_usage_location.service.BaseUsageLocationService;
import com.ylz.saas.codegen.base_usage_location.vo.BaseUsageLocationVo;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.common.excel.annotation.ResponseExcel;
import com.ylz.saas.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * 使用地点信息表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:41:24
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/baseUsageLocation" )
@Tag(description = "baseUsageLocation" , name = "使用地点信息表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class BaseUsageLocationController {

    private final BaseUsageLocationService baseUsageLocationService;
    /**
     * 启用/禁用
     * @return R
     */
    @Operation(summary = "启用/禁用" , description = "启用/禁用" )
    @PostMapping("/switch/{id}")
    public R switchStatus(@PathVariable("id") Long id) {
        return R.ok(baseUsageLocationService.switchStatus(id));
    }

    /**
     * 分页查询
     * @param page 分页对象
     * @param param 使用地点信息表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @PostMapping("/page" )
    public R getBaseUsageLocationPage(@RequestBody BaseUsageLocationParam param, Page page) {
        QueryWrapper<BaseUsageLocationEntity> queryWrapper = param.toWrapper(); 
        return R.ok(baseUsageLocationService.page(page, queryWrapper));
    }

    /**
     * 查询列表
     * @param param 使用地点信息表
     * @return
     */
    @Operation(summary = "查询列表" , description = "查询列表" )
    @PostMapping("/list" )
    public R getBaseUsageLocationList(@RequestBody BaseUsageLocationParam param) {
        QueryWrapper<BaseUsageLocationEntity> queryWrapper = param.toWrapper();
        return R.ok(baseUsageLocationService.list(queryWrapper));
    }
    /**
     * 通过id查询使用地点信息表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(baseUsageLocationService.getById(id));
    }

    /**
     * 新增使用地点信息表
     * @param baseUsageLocationVo 使用地点信息表
     * @return R
     */
    @Operation(summary = "新增使用地点信息表" , description = "新增使用地点信息表" )
    @SysLog("新增使用地点信息表" )
    @PostMapping("/save")
    public R save(@Valid @RequestBody BaseUsageLocationVo baseUsageLocationVo) {
        return R.ok(baseUsageLocationService.saveBaseUsageLocation(baseUsageLocationVo));
    }

    /**
     * 修改使用地点信息表
     * @param baseUsageLocationVo 使用地点信息表
     * @return R
     */
    @Operation(summary = "修改使用地点信息表" , description = "修改使用地点信息表" )
    @SysLog("修改使用地点信息表" )
    @PostMapping("/update")
    public R updateById(@Valid @RequestBody BaseUsageLocationVo baseUsageLocationVo) {
        return R.ok(baseUsageLocationService.updateBaseUsageLocation(baseUsageLocationVo));
    }

    /**
     * 通过id删除使用地点信息表（物理删除）
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除使用地点信息表（物理删除）" , description = "通过id删除使用地点信息表（物理删除）" )
    @SysLog("通过id删除使用地点信息表" )
    @GetMapping("remove")
    public R removeById(@RequestParam List<Long> ids) {
		boolean remove = baseUsageLocationService.removeByIdsBaseUsageLocation(ids);
		return R.ok(remove);
    }


    /**
     * 导出excel 表格（包含组织名称）
     * @param param 查询条件
     * @return excel 文件流
     */
    @ResponseExcel
    @PostMapping("/export")
	@Operation(summary = "导出excel（包含组织名称）" , description = "导出excel，包含组织名称信息" )
    public List<BaseUsageLocationResp> export(@RequestBody BaseUsageLocationParam param) {
		List<BaseUsageLocationEntity> entityList = baseUsageLocationService.listWithDeptName(param);
        return BeanUtil.copyToList(entityList, BaseUsageLocationResp.class);
    }
	
	/**
     * 导入excel 表格
     * @param file
     * @param response
     * @return
     */
    @PostMapping("/import")
    @Operation(summary = "导入excel" , description = "导入excel" )
    public R uploadData(@RequestParam("file") MultipartFile file,HttpServletResponse response) {
        baseUsageLocationService.uploadData(file,response);
        return R.ok();
    }

    /**
     * excel模板下载
     * @param response
     */
    @GetMapping("/template")
    @Operation(summary = "excel模板下载" , description = "excel模板下载" )
    public void template(HttpServletResponse response) {
        baseUsageLocationService.template(response);
    }
}