package com.ylz.saas.resp;

import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.ContractStatusEnum;
import com.ylz.saas.enums.ContractTypeEnum;
import com.ylz.saas.req.SrmTenderContractSaveReq;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class SrmTenderContractDetailResp implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 评标结果ID
     */
    private Long evaluationResultId;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 采购立项ID
     */
    private Long projectId;

    /**
     * 采购立项名称
     */
    private String projectName;

    /**
     * 标段ID
     */
    private Long sectionId;

    /**
     * 标段名称
     */
    private String sectionName;

    /**
     * 采购方ID
     */
    private String purchaserDeptId;

    /**
     * 采购方名称
     */
    private String purchaserDeptName;

    /**
     * 采购联系人ID
     */
    private Long purchaseContactId;

    /**
     * 采购联系电话
     */
    private String purchaseContactPhone;

    /**
     * 合同有效期起始时间
     */
    private LocalDateTime effectStartDate;

    /**
     * 合同有效期终止时间
     */
    private LocalDateTime effectEndDate;

    /**
     * 签约时间
     */
    private LocalDateTime signDate;

    /**
     * 签约地点
     */
    private String signLocation;

//    /**
//     * 支付方式ID
//     */
//    private Long paymentMethodId;
//
//    /**
//     * 支付方式名称
//     */
//    private String paymentMethodName;
//
//    /**
//     * 账期ID
//     */
//    private Long paymentPeriodId;

    /**
     * 账期名称
     */
    private String paymentPeriodName;

    /**
     * 履约保证金
     */
    private BigDecimal performanceBond;

    /**
     * 合同含税总金额
     */
    private BigDecimal totalAmount;

    /**
     * 是否为电子章(0-否、1-是)
     */
    private Integer electronicSeal;

    /**
     * 供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 销售负责人
     */
    private String supplierSalesPrincipal;

    /**
     * 销售联系电话
     */
    private String supplierSalesPrincipalPhone;

    /**
     * 收款银行卡ID
     */
    private Long supplierBankCardId;

    /**
     * 收款银行
     */
    private String supplierBankName;

    /**
     * 收款银行账号
     */
    private String supplierBankAccount;

    /**
     * 合同清单
     */
    private List<ContractItem> contractItems;

    /**
     * 合同附件
     */
    private List<SrmProjectAttachment> contractAttachment;

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 合同正文
     */
    private String contractBody;

    /**
     * 合同状态（NEW-新建、PENDING_SUPPLIER-待供应商确认、EFFECTIVE-合同生效、INVALID-已失效、REVOKED-已作废、SUPPLIER_REJECTED-供应商退回）
     */
    private ContractStatusEnum contractStatus;

    /**
     * 审批状态（APPROVING-审批中、APPROVE_REJECT-审批驳回、APPROVE-审批通过）
     */
    private ApproveStatusEnum approvalStatus;

    /**
     * 供方退回且采方修改后保存待确认时合同信息
     */
    private String supplierRejectedContractInfo;

    @Data
    public static class ContractItem implements Serializable {

        /**
         * 投标报价明细ID
         */
        private Long id;

        /**
         * 需求行号
         */
        private String requireNo;

        /**
         * 物料编码
         */
        private String materialCode;

        /**
         * 物料名称
         */
        private String materialName;

        /**
         * 规格型号
         */
        private String specModel;

        /**
         * 单位
         */
        private String unit;

        /**
         * 需求牧场ID
         */
        private Long usageLocationId;

        /**
         * 需求牧场
         */
        private String locationName;

        /**
         * 牧场省
         */
        private String province;
        /**
         * 牧场市
         */
        private String city;
        /**
         * 牧场区
         */
        private String district;

        /**
         * 详细地址
         */
        private String address;

        /**
         * 合同数量
         */
        private BigDecimal contractQuantity;

        /**
         * 合同总价
         */
        private BigDecimal contractAmount;

        /**
         * 支付方式ID
         */
        private Long paymentMethodId;

        /**
         * 支付方式名称
         */
        private String paymentMethodName;

        @Serial
        private static final long serialVersionUID = 1L;
    }

    @Serial
    private static final long serialVersionUID = 1L;
}
