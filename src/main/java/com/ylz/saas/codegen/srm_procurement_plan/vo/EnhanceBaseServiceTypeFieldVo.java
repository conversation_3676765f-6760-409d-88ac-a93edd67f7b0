package com.ylz.saas.codegen.srm_procurement_plan.vo;

import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.codegen.base_service_type_field.vo.BaseServiceTypeFieldVo;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 服务类型字段表
 *
 * <AUTHOR>
 * @date 2025-06-03 21:25:49
 */
@Data
public class EnhanceBaseServiceTypeFieldVo extends BaseServiceTypeFieldVo implements Serializable {

    /**
     * 是否固定字段
     */
    private Boolean ifFixed;

    /**
     * 前端字段代码
     */
    private String fieldCodeMock;
}