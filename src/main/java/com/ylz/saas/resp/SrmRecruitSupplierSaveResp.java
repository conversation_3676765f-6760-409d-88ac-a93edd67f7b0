package com.ylz.saas.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 供应商招募新增响应类
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@Schema(description = "供应商招募新增响应类")
public class SrmRecruitSupplierSaveResp implements Serializable {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 招募编码
     */
    @Schema(description = "招募编码")
    private String recruitCode;

    /**
     * 招募标题
     */
    @Schema(description = "招募标题")
    private String recruitTitle;

    /**
     * 招募进程
     */
    @Schema(description = "招募进程")
    private String process;

    /**
     * 审核状态
     */
    @Schema(description = "审核状态")
    private String approvalStatus;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    private static final long serialVersionUID = 1L;
}
