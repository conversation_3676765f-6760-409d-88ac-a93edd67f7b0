package com.ylz.saas.codegen.base_expert_work_experience.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.base_expert_work_experience.entity.BaseExpertWorkExperienceEntity;
import com.ylz.saas.codegen.base_expert_work_experience.vo.BaseExpertWorkExperienceVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

public interface BaseExpertWorkExperienceService extends IService<BaseExpertWorkExperienceEntity> {
	/**
     * 新增专家职业履历表
     * @param baseExpertWorkExperienceVo 专家职业履历表
     */
    boolean saveBaseExpertWorkExperience(BaseExpertWorkExperienceVo baseExpertWorkExperienceVo);
	
	/**
     * 修改专家职业履历表
     * @param baseExpertWorkExperienceVo 专家职业履历表
     */
    boolean updateBaseExpertWorkExperience(BaseExpertWorkExperienceVo baseExpertWorkExperienceVo);
	
	/**
     * 导入模板下载
     * @param response
     */
    void template(HttpServletResponse response);

    /**
     * 数据导入
     * @param file
     * @param response
     */
    void uploadData(MultipartFile file, HttpServletResponse response);
}