package com.ylz.saas.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.entity.SrmTenderEvaluationStandard;
import com.ylz.saas.req.SrmTenderEvaluationProgressReq;
import com.ylz.saas.req.SrmTenderEvaluationStandardReq;
import com.ylz.saas.resp.SrmTenderEvaluationProgressResp;
import com.ylz.saas.resp.SrmTenderEvaluationStandardResp;

import java.util.List;

/**
 * 针对表【srm_tender_evaluation_standard(评标标准)】的数据库操作Service
 * <AUTHOR>
 * @createDate 2025-07-10
 */
public interface SrmTenderEvaluationStandardService extends IService<SrmTenderEvaluationStandard> {

    /**
     * 新增评标标准
     * @param req 评标标准信息
     * @return 是否成功
     */
    boolean saveEvaluationStandard(SrmTenderEvaluationStandardReq req);

    /**
     * 修改评标标准
     * @param req 评标标准信息
     * @return 是否成功
     */
    boolean updateEvaluationStandard(SrmTenderEvaluationStandardReq req);

    /**
     * 根据ID查询评标标准详情
     * @param id 主键ID
     * @return 评标标准详情
     */
    SrmTenderEvaluationStandardResp getByIdEvaluationStandard(Long id);

    /**
     * 根据标段ID查询评标标准列表
     * @param sectionId 标段ID
     * @return 评标标准列表
     */
    List<SrmTenderEvaluationStandardResp> listBySectionId(Long sectionId);

    /**
     * 根据标段ID和招标公告ID查询评标标准列表
     * @param sectionId 标段ID
     * @param noticeId 招标公告ID
     * @return 评标标准列表
     */
    List<SrmTenderEvaluationStandardResp> listBySectionIdAndNoticeId(Long sectionId, Long noticeId);

    /**
     * 批量保存评标标准
     * @param standardList 评标标准列表
     * @return 是否成功
     */
    boolean batchSaveEvaluationStandards(List<SrmTenderEvaluationStandardReq> standardList);

    /**
     * 删除评标标准
     * @param ids 主键ID列表
     * @return 是否成功
     */
    boolean removeByIdsEvaluationStandard(List<Long> ids);

    /**
     * 查询评标进度
     * @param req 查询条件
     * @return 评标进度信息
     */
    SrmTenderEvaluationProgressResp getEvaluationProgress(SrmTenderEvaluationProgressReq req);
}
