package com.ylz.saas.codegen.srm_procurement_plan_detail.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.srm_procurement_plan_detail.entity.SrmProcurementPlanDetailEntity;
import com.ylz.saas.codegen.srm_procurement_plan_detail.param.SrmProcurementPlanDetailQueryParam;
import com.ylz.saas.codegen.srm_procurement_plan_detail.vo.SrmProcurementPlanDetailVo;

import java.util.List;

public interface SrmProcurementPlanDetailService extends IService<SrmProcurementPlanDetailEntity> {
    /**
     * 新增采购计划明细表
     *
     * @param srmProcurementPlanDetailVo 采购计划明细表
     */
    boolean saveSrmProcurementPlanDetail(SrmProcurementPlanDetailVo srmProcurementPlanDetailVo);

    /**
     * 修改采购计划明细表
     *
     * @param srmProcurementPlanDetailVo 采购计划明细表
     */
    boolean updateSrmProcurementPlanDetail(SrmProcurementPlanDetailVo srmProcurementPlanDetailVo);

    /**
     * 根据修改采购计划明细表
     *
     * @param srmProcurementPlanDetailVoList 采购计划明细列表
     */
    boolean batchUpdatePlanFieldByPlanDetail(List<SrmProcurementPlanDetailEntity> srmProcurementPlanDetailVoList);

    /**
     * 查询采购计划明细表详情
     *
     * @param id
     */
    SrmProcurementPlanDetailEntity getByIdSrmProcurementPlanDetail(Long id);

    /**
     * 通过id删除采购计划明细表
     */
    boolean removeByIdsSrmProcurementPlanDetail(List<Long> ids);

    /**
     * 通过采购计划ID和租户ID物理删除采购明细数据
     */
    boolean physicsRemoveByPlanId(Long planId, Long tenantId);

    List<SrmProcurementPlanDetailEntity> getBatchByPlanIds(SrmProcurementPlanDetailQueryParam params);

}