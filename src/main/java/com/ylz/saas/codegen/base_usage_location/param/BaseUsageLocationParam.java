package com.ylz.saas.codegen.base_usage_location.param;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ylz.saas.codegen.base_usage_location.entity.BaseUsageLocationEntity;
import com.ylz.saas.common.data.mybatis.helper.Operator;
import com.ylz.saas.common.data.mybatis.helper.QueryWrapperHelper;
import com.ylz.saas.common.data.mybatis.wrapper.CriteriaField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * mybatis-plus的查询帮助类
 * 
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseUsageLocationParam {

	/**
  	 * 主键ID
   	 */
 	@CriteriaField(field = "id", operator = Operator.LIKE)
    private Long id;
	/**
  	 * 组织ID
   	 */
 	@CriteriaField(field = "deptId", operator = Operator.LIKE)
    private Long deptId;
	/**
  	 * 使用地点名称
   	 */
 	@CriteriaField(field = "locationName", operator = Operator.LIKE)
    private String locationName;
	/**
  	 * 使用地点编码
   	 */
 	@CriteriaField(field = "locationCode", operator = Operator.LIKE)
    private String locationCode;
	/**
  	 * 地点类型（工厂、仓库、办公室、牧场、工地等）
   	 */
 	@CriteriaField(field = "locationType", operator = Operator.LIKE)
    private String locationType;


	/**
  	 * 省
   	 */
 	@CriteriaField(field = "province", operator = Operator.LIKE)
    private String province;
	/**
  	 * 市
   	 */
 	@CriteriaField(field = "city", operator = Operator.LIKE)
    private String city;
	/**
  	 * 区
   	 */
 	@CriteriaField(field = "district", operator = Operator.LIKE)
    private String district;
	/**
  	 * 详细地址
   	 */
 	@CriteriaField(field = "address", operator = Operator.LIKE)
    private String address;
	/**
  	 * 联系人
   	 */
 	@CriteriaField(field = "contactPerson", operator = Operator.LIKE)
    private String contactPerson;
	/**
  	 * 联系电话
   	 */
 	@CriteriaField(field = "contactPhone", operator = Operator.LIKE)
    private String contactPhone;
	/**
  	 * 状态
   	 */
 	@CriteriaField(field = "status", operator = Operator.LIKE)
    private String status;
	/**
  	 * 创建人名称
   	 */
 	@CriteriaField(field = "createByName", operator = Operator.LIKE)
    private String createByName;
	/**
  	 * 修改人名称
   	 */
 	@CriteriaField(field = "updateByName", operator = Operator.LIKE)
    private String updateByName;
	/**
  	 * 创建人
   	 */
 	@CriteriaField(field = "createBy", operator = Operator.LIKE)
    private String createBy;
	/**
  	 * 创建时间_开始
   	 */
	@CriteriaField(field = "createTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime createTimeStart;
	/**
  	 * 创建时间_结束
   	 */
    @CriteriaField(field = "createTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime createTimeEnd;
	/**
  	 * 修改人
   	 */
 	@CriteriaField(field = "updateBy", operator = Operator.LIKE)
    private String updateBy;
	/**
  	 * 修改时间_开始
   	 */
	@CriteriaField(field = "updateTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime updateTimeStart;
	/**
  	 * 修改时间_结束
   	 */
    @CriteriaField(field = "updateTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime updateTimeEnd;
	/**
  	 * 删除标识（0-正常、1-删除）
   	 */
	@CriteriaField(field = "delFlag", operator = Operator.EQ)
	private int delFlag;
	/**
  	 * 租户ID
   	 */
 	@CriteriaField(field = "tenantId", operator = Operator.LIKE)
    private Long tenantId;
	/**
  	 * 创建人ID
   	 */
 	@CriteriaField(field = "createById", operator = Operator.LIKE)
    private Long createById;
	/**
  	 * 更新人ID
   	 */
 	@CriteriaField(field = "updateById", operator = Operator.LIKE)
    private Long updateById;

    /**
    * 构建查询
    */
    public QueryWrapper<BaseUsageLocationEntity> toWrapper() {
        QueryWrapper<BaseUsageLocationEntity> queryWrapper = QueryWrapperHelper.fromBean(this, BaseUsageLocationEntity.class)
            .orderByDesc("id");
       return queryWrapper;
    }
}