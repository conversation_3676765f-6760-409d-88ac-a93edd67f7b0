package com.ylz.saas.codegen.base_payment_method_period.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付方式账期时间表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:34
 */
@Data
public class BasePaymentMethodPeriodVo implements Serializable {

    /**
  	 * 主键ID
   	 */
    private Long id;

    /**
  	 * 组织ID
   	 */
    private String deptId;

    /**
  	 * 支付方式ID
   	 */
    private Long paymentMethodId;

    /**
  	 * 账期天数
   	 */
    private BigDecimal periodDays;

    /**
  	 * 账期名称
   	 */
    private String periodName;

    /**
  	 * 账期描述
   	 */
    private String description;

    /**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
    private String status;

    /**
  	 * 排序
   	 */
    private Integer sortOrder;

    /**
  	 * 创建人名称
   	 */
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    private String updateByName;

    /**
  	 * 创建人
   	 */
    private String createBy;

    /**
  	 * 创建时间
   	 */
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
    private String updateBy;

    /**
  	 * 修改时间
   	 */
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
    private Long createById;

    /**
  	 * 更新人ID
   	 */
    private Long updateById;
}