package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmProcurementProjectFieldValue;
import com.ylz.saas.service.SrmProcurementProjectFieldValueService;
import com.ylz.saas.mapper.SrmProcurementProjectFieldValueMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【srm_procurement_project_field_value(采购立项字段值表)】的数据库操作Service实现
* @createDate 2025-06-11 11:21:09
*/
@Service
public class SrmProcurementProjectFieldValueServiceImpl extends ServiceImpl<SrmProcurementProjectFieldValueMapper, SrmProcurementProjectFieldValue>
    implements SrmProcurementProjectFieldValueService{

}




