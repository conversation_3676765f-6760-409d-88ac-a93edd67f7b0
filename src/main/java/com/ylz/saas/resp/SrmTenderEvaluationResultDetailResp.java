package com.ylz.saas.resp;

import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.EvaluationResultEnum;
import com.ylz.saas.enums.PublicNoticeStatusEnum;
import com.ylz.saas.enums.PublicityStatusEnum;
import com.ylz.saas.req.SrmTenderBidReq;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 评标结果详情响应
 */
@Data
public class SrmTenderEvaluationResultDetailResp {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 采购立项ID
     */
    private Long projectId;

    /**
     * 招标公告ID
     */
    private Long noticeId;

    /**
     * 标段ID
     */
    private Long sectionId;

    /**
     * 定标备注
     */
    private String awardRemark;

    /**
     * 定标报告状态
     * TO_APPROVE("待审批"),
     *     APPROVING("审批中"),
     *     APPROVE("审批通过"),
     *     APPROVE_REJECT("审批驳回"),
     *     APPROVE_REVOKE("审批撤销"),
     */
    private ApproveStatusEnum awardReportStatus;

    /**
     * 公示审核状态
     * TO_APPROVE("待审批"),
     *     APPROVING("审批中"),
     *     APPROVE("审批通过"),
     *     APPROVE_REJECT("审批驳回"),
     *     APPROVE_REVOKE("审批撤销"),
     */
    private ApproveStatusEnum publicityAuditStatus;
    /**
     * 公告审核状态
     * TO_APPROVE("待审批"),
     *     APPROVING("审批中"),
     *     APPROVE("审批通过"),
     *     APPROVE_REJECT("审批驳回"),
     *     APPROVE_REVOKE("审批撤销"),
     */
    private ApproveStatusEnum noticeAuditStatus;

    /**
     * 审批备注
     */
    private String examineRemark;

    /**
     * 定标报告内容
     */
    private String awardReportContent;

    /**
     * 定标报告时间
     */
    private LocalDateTime awardReportTime;

    /**
     * 定标报告模板ID
     */
    private Long awardTemplateId;

    /**
     * 定标附件
     */
    private List<SrmProjectAttachment> awardAttachments;

    /**
     * 公示状态(UNPUBLISHED-未公示、PUBLISHED-已公示、EXPIRED-已过期)
     */
    private PublicityStatusEnum publicityStatus;

    /**
     * 公示标题
     */
    private String publicityTitle;

    /**
     * 公示引用模版ID
     */
    private Long publicityTemplateId;

    /**
     * 公示内容
     */
    private String publicityContent;

    /**
     * 公示开始时间
     */
    private LocalDateTime publicityStartTime;

    /**
     * 公示结束时间
     */
    private LocalDateTime publicityEndTime;

    /**
     * 公告状态(UNPUBLISHED-未公告、PUBLISHED-已公告)
     */
    private PublicNoticeStatusEnum noticeStatus;

    /**
     * 公告标题
     */
    private String noticeTitle;

    /**
     * 公示引用模版ID
     */
    private Long noticeTemplateId;

    /**
     * 公告内容
     */
    private String noticeContent;

    /**
     * 公告时间
     */
    private LocalDateTime noticeTime;

    /**
     * 整体状态(DRAFT-草稿、PROCESSING-处理中、COMPLETED-已完成)
     */
    private EvaluationResultEnum resultStatus;

    /**
     * 当前报价轮次（来自srm_tender_open表）
     */
    private Integer currentRound;

    /**
     * 创建人ID
     */
    private Long createById;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    private Long updateById;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改人名称
     */
    private String updateByName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 中标公示附件列表
     */
    private List<SrmProjectAttachment> publicityAttachments;

    /**
     * 中标公告附件列表
     */
    private List<SrmProjectAttachment> noticeAttachments;

    /**
     * 支付方式id
     */
    private Long projectPaymentId;

    /**
     * 采购项目物料Code
     */
    private String materialCode;

    /**
     * 中标金额
     */
    private BigDecimal bidAmount;

    /**
     * 中标数量
     */
    private BigDecimal awardedQuantity;

    /**
     * 供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 采购项目物料名称
     */
    private List<SrmTenderBidReq.BidProjectItem> projectItemList;
}
