package com.ylz.saas.req;

import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.enums.InviteMethodEnum;
import com.ylz.saas.enums.ProjectProgressStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/19 15:59
 * @Description
 */
@Data
public class SrmProcurementProjectPageQueryReq {
    /**
     * 邀请方式(PUBLIC-公开、INVITE-邀请)
     */
    private List<InviteMethodEnum> inviteMethod;
    /**
     * 项目编号/名称
     */
    private String searchContent;

    /**
     * 项目进度
     */
    private List<ProjectProgressStatusEnum> progressStatus;

    /**
     * 项目负责人
     */
    private String projectLeaderName;

    /**
     * 寻源方式
     */
    private List<BaseServiceTypeFieldService.BuyWayEnum> sourcingType;

    /**
     * 采购部门
     */
    private String buyerDeptName;

    /**
     *项目状态
     */
    private List<String> status;

    /**
     * 开标开始时间
     */
    private LocalDateTime bidOpenStartTime;

    /**
     * 开标结束时间
     */
    private LocalDateTime bidOpenEndTime;

    /**
     * 节点状态
     */
    private List<SrmProcurementProject.NodeStatus> nodeStatus;

}
