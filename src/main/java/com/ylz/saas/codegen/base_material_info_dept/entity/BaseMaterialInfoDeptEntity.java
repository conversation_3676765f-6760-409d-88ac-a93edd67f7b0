package com.ylz.saas.codegen.base_material_info_dept.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ylz.saas.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 物料基础信息组织关联表
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@TableName("base_material_info_dept")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "物料基础信息组织关联表")
@TenantTable
public class BaseMaterialInfoDeptEntity extends Model<BaseMaterialInfoDeptEntity> {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 物料基础信息ID
     */
    @Schema(description = "物料基础信息ID")
    private Long materialInfoId;

    /**
     * 组织ID
     */
    @Schema(description = "组织ID")
    private Long deptId;

    /**
     * 组织名称（冗余字段，便于查询）
     */
    @TableField(exist = false)
    @Schema(description = "组织名称")
    private String deptName;


    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    /**
     * 创建人名称
     */
    @Schema(description="创建人名称")
    private String createByName;

    /**
     * 修改人名称
     */
    @Schema(description="修改人名称")
    private String updateByName;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "删除标识（0-正常、1-删除）")
    private int delFlag;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;


}
