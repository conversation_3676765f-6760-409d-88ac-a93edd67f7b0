package com.ylz.saas.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/6/26 19:47
 * @Description
 */
@Data
public class QuoteDetailQueryReq {

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 采购标段ID
     */
    @NotNull(message = "采购标段ID不能为空")
    private Long sectionId;


    /**
     * 供应商ID
     */
    @NotNull(message = "供应商ID不能为空")
    private Long tenantSupplierId;

}
