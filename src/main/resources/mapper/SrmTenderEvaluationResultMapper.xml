<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenderEvaluationResultMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenderEvaluationResult">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="projectId" column="project_id" jdbcType="BIGINT"/>
            <result property="noticeId" column="notice_id" jdbcType="BIGINT"/>
            <result property="sectionId" column="section_id" jdbcType="BIGINT"/>
            <result property="awardRemark" column="award_remark" jdbcType="VARCHAR"/>
            <result property="awardReportStatus" column="award_report_status" jdbcType="VARCHAR"/>
            <result property="awardReportContent" column="award_report_content" jdbcType="VARCHAR"/>
            <result property="awardReportTime" column="award_report_time" jdbcType="TIMESTAMP"/>
            <result property="publicityStatus" column="publicity_status" jdbcType="VARCHAR"/>
            <result property="publicityContent" column="publicity_content" jdbcType="VARCHAR"/>
            <result property="publicityStartTime" column="publicity_start_time" jdbcType="TIMESTAMP"/>
            <result property="publicityEndTime" column="publicity_end_time" jdbcType="TIMESTAMP"/>
            <result property="noticeStatus" column="notice_status" jdbcType="VARCHAR"/>
            <result property="noticeContent" column="notice_content" jdbcType="VARCHAR"/>
            <result property="noticeTime" column="notice_time" jdbcType="TIMESTAMP"/>
            <result property="resultStatus" column="result_status" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateById" column="update_by_id" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,
        project_id,notice_id,section_id,
        award_remark,award_report_status,award_report_content,
        award_report_time,publicity_status,publicity_content,
        publicity_start_time,publicity_end_time,notice_status,
        notice_content,notice_time,result_status,
        del_flag,create_by_id,create_by,
        create_by_name,create_time,update_by_id,
        update_by,update_by_name,update_time
    </sql>

    <!-- 通过ID获取评标结果详情（包含报价轮次） -->
    <select id="getEvaluationResultDetailById" resultType="com.ylz.saas.resp.SrmTenderEvaluationResultDetailResp">
        SELECT
            er.id,
            er.tenant_id,
            er.dept_id,
            er.project_id,
            er.notice_id,
            er.section_id,
            er.award_remark,
            er.award_report_status,
            er.examine_remark,
            er.award_report_content,
            er.award_report_time,
            er.publicity_status,
            er.publicity_title,
            er.publicity_template_id,
            er.publicity_content,
            er.publicity_start_time,
            er.publicity_end_time,
            er.notice_status,
            er.notice_title,
            er.notice_template_id,
            er.notice_content,
            er.notice_time,
            er.result_status,
            er.create_by_id,
            er.create_by,
            er.create_by_name,
            er.create_time,
            er.update_by_id,
            er.update_by,
            er.update_by_name,
            er.update_time,
            COALESCE(sto.current_round, 0) as current_round
        FROM srm_tender_evaluation_result er
        LEFT JOIN srm_tender_open sto ON er.project_id = sto.project_id
            AND er.notice_id = sto.notice_id
            AND er.section_id = sto.section_id
            AND sto.del_flag = 0
        WHERE er.id = #{id}
            AND er.del_flag = 0
    </select>

    <!-- 通过noticeId和projectId获取评标结果详情（包含报价轮次） -->
    <select id="getEvaluationResultDetailByNoticeAndProject" resultType="com.ylz.saas.resp.SrmTenderEvaluationResultDetailResp">
        SELECT
            er.*,
            COALESCE(sto.current_round, 0) as current_round
        FROM srm_tender_evaluation_result er
        LEFT JOIN srm_tender_open sto ON er.project_id = sto.project_id
            AND er.notice_id = sto.notice_id
            AND er.section_id = sto.section_id
            AND sto.del_flag = 0
        WHERE er.notice_id = #{noticeId}
            AND er.project_id = #{projectId}
            AND er.del_flag = 0
        limit 1
    </select>


    <!-- 通过noticeId和projectId获取评标结果详情（包含报价轮次） -->
    <select id="getEvaluationResultDetailByNoticeAndProjectList" resultType="com.ylz.saas.resp.SrmTenderEvaluationResultDetailResp">
        SELECT
            er.*,
            COALESCE(sto.current_round, 0) as current_round
        FROM srm_tender_evaluation_result er
        LEFT JOIN srm_tender_open sto ON er.project_id = sto.project_id
            AND er.notice_id = sto.notice_id
            AND er.section_id = sto.section_id
            AND sto.del_flag = 0
        WHERE er.notice_id = #{noticeId}
          <if test="sectionId != null">
            AND er.section_id = #{sectionId}
        </if>
            AND er.project_id = #{projectId}
            AND er.del_flag = 0
    </select>
</mapper>
