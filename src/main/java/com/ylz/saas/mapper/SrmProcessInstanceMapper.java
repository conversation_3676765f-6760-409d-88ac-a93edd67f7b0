package com.ylz.saas.mapper;

import com.ylz.saas.entity.SrmProcessInstance;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylz.saas.service.SrmProcessConfigService;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【srm_process_instance(供应商响应表)】的数据库操作Mapper
* @createDate 2025-06-12 16:20:53
* @Entity com.ylz.saas.entity.SrmProcessInstance
*/
@Mapper
public interface SrmProcessInstanceMapper extends BaseMapper<SrmProcessInstance> {


    /**
     * 通过bizId集合和type集合查询流程实例
     * @param bizIds 业务ID集合
     * @param types 业务类型集合
     * @return 流程实例列表
     */
    List<SrmProcessInstance> getInstancesByBizIdAndType(@Param("bizIds") Set<Long> bizIds,
                                                        @Param("types") Set<SrmProcessConfigService.BizTypeEnum> types);


}




