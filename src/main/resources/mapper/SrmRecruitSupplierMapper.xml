<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmRecruitSupplierMapper">


    <select id="pageResult" resultType="com.ylz.saas.resp.SrmRecruitResultPageResp">

        select
        srssu.*,
        srssu.create_time as signUpTime,
        srssu.id as signUpId,
        srs.store_approval_status
        from srm_recruit_supplier_sign_up srssu
        left join srm_recruit_supplier srs on srs.id = srssu.recruit_id
        where srssu.del_flag = 0
        <if test="req.supplierId !=null">
            and srssu.supplier_id = #{req.supplierId}
        </if>


    </select>
    <!-- 分页查询供应商招募信息 -->
    <select id="selectRecruitSupplierPage" resultType="com.ylz.saas.resp.SrmRecruitSupplierPageResp">
        SELECT
        IFNULL(srsm2.role,"RECRUIT_MEMBER") as currentRole,
        srs.id,
        srs.recruit_code,
        srs.recruit_title,
        CONCAT('报名：', COALESCE(signup_count.count, 0)) as sign_up_info,
        purchase_dept.name as purchase_dept_name,
        demand_dept.name as demand_dept_name,
        leader_info.user_name as project_leader,
        srs.create_time as publish_time,
        CASE
        WHEN srs.deadline IS NULL THEN '长期招募'
        ELSE DATE_FORMAT(srs.deadline, '%Y-%m-%d %H:%i:%s')
        END as deadline_display,
        srs.process,
        srs.create_by_name,
        srs.create_time,
        srs.termination_approval_status,
        srs.termination_reason
        FROM srm_recruit_supplier srs

        left join srm_recruit_supplier_member srsm2 on srsm2.recruit_id = srs.id and srsm2.user_id = #{req.memberId}
        LEFT JOIN sys_dept purchase_dept ON srs.purchase_dept_id = purchase_dept.dept_id


        LEFT JOIN sys_dept demand_dept ON srs.demand_dept_id = demand_dept.dept_id


        LEFT JOIN (
        SELECT
        srsm.recruit_id,
        su.name as user_name
        FROM srm_recruit_supplier_member srsm
        LEFT JOIN sys_user su ON srsm.user_id = su.user_id
        WHERE srsm.del_flag = 0
        AND srsm.role = 'RECRUIT_LEADER'
        ) leader_info ON srs.id = leader_info.recruit_id

        LEFT JOIN (
        SELECT
        recruit_id,
        COUNT(*) as count
        FROM srm_recruit_supplier_sign_up
        WHERE del_flag = 0
        GROUP BY recruit_id
        ) signup_count ON srs.id = signup_count.recruit_id

        <where>
            srs.del_flag = 0

            <if test="req.memberId !=null and req.purchaseDeptId !=null">
                and (srs.purchase_dept_id = #{req.purchaseDeptId} or srsm2.id is not null )
            </if>
            <if test="req.recruitCode != null and req.recruitCode != ''">
                AND srs.recruit_code LIKE CONCAT('%', #{req.recruitCode}, '%')
            </if>


            <if test="req.recruitTitle != null and req.recruitTitle != ''">
                AND srs.recruit_title LIKE CONCAT('%', #{req.recruitTitle}, '%')
            </if>


            <if test="req.recruitLeaderName != null and req.recruitLeaderName != ''">
                AND leader_info.user_name LIKE CONCAT('%', #{req.recruitLeaderName}, '%')
            </if>


            <if test="req.purchaseDeptName != null and req.purchaseDeptName != ''">
                AND purchase_dept.name LIKE CONCAT('%', #{req.purchaseDeptName}, '%')
            </if>


            <if test="req.signUpStartTime != null">
                AND srs.create_time >= #{req.signUpStartTime}
            </if>
            <if test="req.signUpEndTime != null">
                AND srs.create_time &lt;= #{req.signUpEndTime}
            </if>


            <if test="req.process != null and req.process != ''">
                AND srs.process = #{req.process}
            </if>
        </where>

        ORDER BY srs.create_time DESC
    </select>

    <!-- 查询招募小组成员列表（联查用户表和部门表） -->
    <select id="selectMemberList" resultType="com.ylz.saas.resp.SrmRecruitSupplierDetailResp$MemberInfo">
        SELECT
            srsm.id,
            srsm.role,
            srsm.user_id,
            su.name as user_name,
            srsm.contact_phone,
            srsm.dept_id,
            sd.name as dept_name
        FROM srm_recruit_supplier_member srsm
        LEFT JOIN sys_user su ON srsm.user_id = su.user_id
        LEFT JOIN sys_dept sd ON srsm.dept_id = sd.dept_id
        WHERE srsm.recruit_id = #{recruitId}
        AND srsm.del_flag = 0
        ORDER BY srsm.create_time ASC
    </select>

    <!-- 查询物资清单列表 -->
    <select id="selectMaterialList" resultType="com.ylz.saas.resp.SrmRecruitSupplierDetailResp$MaterialInfo">
        SELECT
            id,
            recruit_type,
            material_id,
            material_code,
            spec,
            unit,
            material_name,
            remark,
            engineering_context
        FROM srm_recruit_supplier_detail
        WHERE recruit_id = #{recruitId}
        AND del_flag = 0
        ORDER BY create_time ASC
    </select>

    <!-- 查询资质要求列表 -->
    <select id="selectCertificateList" resultType="com.ylz.saas.resp.SrmRecruitSupplierDetailResp$CertificateInfo">
        SELECT
            id,
            document_name,
            base_certificate_id
        FROM srm_recruit_supplier_certificate
        WHERE recruit_id = #{recruitId}
        AND del_flag = 0
        ORDER BY create_time ASC
    </select>

    <!-- 查询合作媒体列表 -->
    <select id="selectMediaList" resultType="com.ylz.saas.resp.SrmRecruitSupplierDetailResp$MediaInfo">
        SELECT
            id,
            media_name
        FROM srm_recruit_supplier_media
        WHERE recruit_id = #{recruitId}
        AND del_flag = 0
        ORDER BY create_time ASC
    </select>

</mapper>
