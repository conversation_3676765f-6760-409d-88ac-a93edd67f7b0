<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmClarifyAttachmentDownloadInfoMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmClarifyAttachmentDownloadInfo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="tenantSupplierId" column="tenant_supplier_id" jdbcType="BIGINT"/>
            <result property="supplierCode" column="supplier_code" jdbcType="VARCHAR"/>
            <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
            <result property="clarifyNoticeId" column="clarify_notice_id" jdbcType="BIGINT"/>
            <result property="downloadUserId" column="download_user_id" jdbcType="VARCHAR"/>
            <result property="downloadUserName" column="download_user_name" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,
        tenant_supplier_id,supplier_code,supplier_name,
        clarify_notice,download_user_id,download_user_name,
        del_flag,create_by,create_by_name,
        create_time,update_by,update_by_name,
        update_time
    </sql>
    <select id="querySupplierDownloadInfo" resultType="com.ylz.saas.resp.ClarifyAttachmentDownloadInfoResp">
        SELECT
        scadi.tenant_id,
        scadi.dept_id,
        scadi.tenant_supplier_id,
        scadi.supplier_code,
        scadi.supplier_name,
        scadi.clarify_notice_id,
        (
        SELECT scadi2.download_user_id
        FROM srm_clarify_attachment_download_info scadi2
        WHERE scadi2.tenant_supplier_id = scadi.tenant_supplier_id
        AND scadi2.supplier_code = scadi.supplier_code
        AND scadi2.supplier_name = scadi.supplier_name
        AND scadi2.clarify_notice_id = scadi.clarify_notice_id
        AND scadi2.del_flag = 0
        ORDER BY scadi2.create_time DESC
        LIMIT 1
        ) as download_user_id,
        (
        SELECT scadi2.download_user_name
        FROM srm_clarify_attachment_download_info scadi2
        WHERE scadi2.tenant_supplier_id = scadi.tenant_supplier_id
        AND scadi2.supplier_code = scadi.supplier_code
        AND scadi2.supplier_name = scadi.supplier_name
        AND scadi2.clarify_notice_id = scadi.clarify_notice_id
        AND scadi2.del_flag = 0
        ORDER BY scadi2.create_time DESC
        LIMIT 1
        ) as download_user_name,
        MAX(scadi.create_time) as lastDownloadTime,
        COUNT(scadi.id) as downloadCount
        FROM
        srm_clarify_attachment_download_info scadi
        WHERE
        scadi.del_flag = 0
        AND scadi.clarify_notice_id = #{clarifyNoticeId}
        <if test="supplierName != null and supplierName != ''">
            AND scadi.supplier_name LIKE CONCAT('%', #{supplierName}, '%')
        </if>
        GROUP BY
        scadi.tenant_supplier_id,
        scadi.supplier_code,
        scadi.supplier_name
    </select>
</mapper>
