package com.ylz.saas.resp;

import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.entity.SrmTenderBidderQuoteItem;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/4 14:55
 * @Description
 */
@Data
public class TenderBidderItemsResp {
    /**
     * 中标明细
     */
    private List<SrmTenderBidderQuoteItem> srmTenderBidderQuoteItems;

    /**
     * 报价附件
     */
    private List<SrmProjectAttachment> srmProjectAttachments;

    /**
     * 其它附件
     */
    private List<SrmProjectAttachment> otherSrmProjectAttachments;


}
