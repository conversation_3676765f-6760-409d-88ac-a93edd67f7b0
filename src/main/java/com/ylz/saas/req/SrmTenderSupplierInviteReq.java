package com.ylz.saas.req;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 供应商邀请表
 */
@Data
public class SrmTenderSupplierInviteReq {
    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    private Long sectionId;

    /**
     * 租户供应商ID
     */
    @Valid
    @NotNull(message = "租户供应商ID不能为空")
    @Size(min = 1, message = "租户供应商ID不能为空")
    private List<SupplierInfo> supplierInfoList;

    @Data
    public static class SupplierInfo{
        /**
         * 租户供应商ID
         */
        @NotNull(message = "租户供应商ID不能为空")
        private Long tenantSupplierId;

        /**
         * 供应商名称
         */
        @NotBlank(message = "供应商名称不能为空")
        private String supplierName;
    }

}