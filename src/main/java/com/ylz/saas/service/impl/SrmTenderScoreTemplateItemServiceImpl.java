package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmTenderScoreTemplateItem;
import com.ylz.saas.service.SrmTenderScoreTemplateItemService;
import com.ylz.saas.mapper.SrmTenderScoreTemplateItemMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【srm_tender_score_template_item(评审/评分项表)】的数据库操作Service实现
* @createDate 2025-07-09 16:12:21
*/
@Service
public class SrmTenderScoreTemplateItemServiceImpl extends ServiceImpl<SrmTenderScoreTemplateItemMapper, SrmTenderScoreTemplateItem>
    implements SrmTenderScoreTemplateItemService{

}




