package com.ylz.saas.codegen.base_material_info_dept.mapper;

import com.ylz.saas.codegen.base_material_info_dept.entity.BaseMaterialInfoDeptEntity;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BaseMaterialInfoDeptMapper extends SaasBaseMapper<BaseMaterialInfoDeptEntity> {

    /**
     * 根据物料ID查询关联的组织信息（包含组织名称）
     * @param materialInfoId 物料基础信息ID
     * @return 关联的组织信息列表
     */
    List<BaseMaterialInfoDeptEntity> selectDeptsByMaterialId(@Param("materialInfoId") Long materialInfoId);

    /**
     * 根据物料ID删除关联关系
     * @param materialInfoId 物料基础信息ID
     * @return 删除的记录数
     */
    int deleteByMaterialId(@Param("materialInfoId") Long materialInfoId);
}
