package com.ylz.saas.codegen.srm_procurement_decision.param;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ylz.saas.codegen.srm_procurement_decision.entity.SrmProcurementDecisionEntity;
import com.ylz.saas.codegen.srm_procurement_decision.enums.DecisionStatus;
import com.ylz.saas.codegen.srm_procurement_decision.enums.DecisionType;
import com.ylz.saas.common.data.mybatis.helper.Operator;
import com.ylz.saas.common.data.mybatis.helper.QueryWrapperHelper;
import com.ylz.saas.common.data.mybatis.wrapper.CriteriaField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;


/**
 * PageReq
 *
 * <AUTHOR>
 * @date 2025/8/6 16:41
 * @since v0.0.1
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PageReq {
    /**
     * 申请编号
     */
    @CriteriaField(field = "code", operator = Operator.LIKE)
    private String codeLike;
    /**
     * 申请标题
     */
    @CriteriaField(field = "title", operator = Operator.LIKE)
    private String titleLike;
    /**
     * 项目名称
     */
    @CriteriaField(field = "projectName", operator = Operator.LIKE)
    private String projectNameLike;
    /**
     * 项目code
     */
    @CriteriaField(field = "projectCode", operator = Operator.LIKE)
    private String projectCodeLike;
    /**
     * 申请类型
     */
    @CriteriaField(field = "decisionType", operator = Operator.IN)
    private List<DecisionType> decisionTypeIn;
    /**
     * 申请人姓名
     */
    @CriteriaField(field = "applicantName", operator = Operator.LIKE)
    private String applicantNameLike;
    /**
     * 申请日期_开始
     */
    @CriteriaField(field = "applyDate", operator = Operator.GE, filterBlank = true)
    private LocalDate applyDateStart;
    /**
     * 申请日期_结束
     */
    @CriteriaField(field = "applyDate", operator = Operator.LE, filterBlank = true)
    private LocalDate applyDateEnd;

    /**
     * 申请类型
     */
    @CriteriaField(field = "status", operator = Operator.IN)
    private List<DecisionStatus> status;

    /**
     * 构建查询
     */
    public QueryWrapper<SrmProcurementDecisionEntity> toWrapper() {
        QueryWrapper<SrmProcurementDecisionEntity> queryWrapper = QueryWrapperHelper.fromBean(this, SrmProcurementDecisionEntity.class)
                .orderByDesc("id");
        return queryWrapper;
    }
}