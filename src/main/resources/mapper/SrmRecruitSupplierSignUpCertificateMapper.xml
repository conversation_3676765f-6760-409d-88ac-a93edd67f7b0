<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmRecruitSupplierSignUpCertificateMapper">
<select id="getBySignUpId" resultType="com.ylz.saas.entity.SrmRecruitSupplierSignUpCertificate">

    select
        sr.document_url,
        s.document_name as documentName
    from srm_recruit_supplier_sign_up_certificate sr
             left join srm_recruit_supplier_certificate s on sr.supplier_certificate_id = s.id
    where sr.recruit_sign_up_id = #{signUpId}

</select>

</mapper>
