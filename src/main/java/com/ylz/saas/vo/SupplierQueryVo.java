package com.ylz.saas.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 供应商查询条件VO
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@Schema(description = "供应商查询条件VO")
public class SupplierQueryVo {

    /**
     * 供应商名称（模糊查询）
     */
    @Schema(description = "供应商名称（模糊查询）")
    private String supplierName;

    /**
     * 供应商类型列表（多选）
     * MANUFACTURER-生产型供应商、TRADER-贸易型供应商、SERVICE-服务型供应商、
     * LOGISTICS-物流承运商、PROJECT-工程建筑供应商、LABOR-劳务外包供应商、
     * RENTAL-租赁服务供应商、OTHER-其他
     */
    @Schema(description = "供应商类型列表（多选）")
    private List<String> supplierType;

    /**
     * 企业性质列表（多选）
     * STATE_OWNED-国有企业、PRIVATE-民营企业、FOREIGN-外资企业、
     * JOINT_VENTURE-合资企业、LISTED-上市公司、OTHER-其他
     */
    @Schema(description = "企业性质列表（多选）")
    private List<String> businessNature;

    /**
     * 启用状态
     * ENABLED-启用、DISABLED-禁用
     */
    @Schema(description = "启用状态")
    private String status;

    /**
     * 审批状态
     * DRAFT-草稿、APPROVING-审批中、APPROVED-已审批、REJECTED-已驳回
     */
    @Schema(description = "审批状态")
    private String approvalStatus;
}
