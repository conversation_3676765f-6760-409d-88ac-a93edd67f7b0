package com.ylz.saas.codegen.base_expert_achievement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.base_expert_achievement.entity.BaseExpertAchievementEntity;
import com.ylz.saas.codegen.base_expert_achievement.vo.BaseExpertAchievementVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

public interface BaseExpertAchievementService extends IService<BaseExpertAchievementEntity> {
	/**
     * 新增专家成果表
     * @param baseExpertAchievementVo 专家成果表
     */
    boolean saveBaseExpertAchievement(BaseExpertAchievementVo baseExpertAchievementVo);
	
	/**
     * 修改专家成果表
     * @param baseExpertAchievementVo 专家成果表
     */
    boolean updateBaseExpertAchievement(BaseExpertAchievementVo baseExpertAchievementVo);
	
	/**
     * 导入模板下载
     * @param response
     */
    void template(HttpServletResponse response);

    /**
     * 数据导入
     * @param file
     * @param response
     */
    void uploadData(MultipartFile file, HttpServletResponse response);
}