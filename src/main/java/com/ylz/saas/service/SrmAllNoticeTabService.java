package com.ylz.saas.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.req.SrmAllNoticePageReq;
import com.ylz.saas.req.SrmAppletNoticePageReq;
import com.ylz.saas.resp.SrmAllInvitePageResp;
import com.ylz.saas.resp.SrmAllNoticePageResp;
import com.ylz.saas.resp.SrmAllPublicNoticePageResp;
import com.ylz.saas.resp.SrmAllPublicityPageResp;

public interface SrmAllNoticeTabService {
    /**
     * 招标公告分页
     * @param req
     * @return
     */
    Page<SrmAllNoticePageResp> tenderNoticePage(SrmAllNoticePageReq req);

    /**
     * 邀请函分页
     * @param req
     * @return
     */
    Page<SrmAllInvitePageResp> invitePage(SrmAllNoticePageReq req);


    /**
     *  公示分页
     *
     * @param req
     * @return
     */
    Page<SrmAllPublicityPageResp> publicityPage(SrmAllNoticePageReq req);

    /**
     * 公告分页
     *
     * @param req
     * @return
     */
    Page<SrmAllPublicNoticePageResp> publicNoticePage(SrmAllNoticePageReq req);

    /**
     * 邀请函和公告分页
     *
     * @param req
     * @return
     */
    Page<SrmAllNoticePageResp> inviteAndNoticePage(SrmAppletNoticePageReq req);
}
