package com.ylz.saas.resp;

import com.ylz.saas.enums.ApproveStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 供应商招募公告变更分页响应
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@Schema(description = "供应商招募公告变更分页响应")
public class SrmRecruitSupplierChangePageResp {

    /**
     * 变更记录ID
     */
    @Schema(description = "变更记录ID")
    private Long id;

    /**
     * 招募公告ID
     */
    @Schema(description = "招募公告ID")
    private Long recruitId;

    /**
     * 招募公告编码
     */
    @Schema(description = "招募公告编码")
    private String recruitCode;

    /**
     * 招募标题
     */
    @Schema(description = "招募标题")
    private String recruitTitle;

    /**
     * 变更人ID
     */
    @Schema(description = "变更人ID")
    private Long changeById;

    /**
     * 变更人名称
     */
    @Schema(description = "变更人名称")
    private String changeByName;

    /**
     * 变更时间
     */
    @Schema(description = "变更时间")
    private LocalDateTime changeTime;

    /**
     * 变更审核状态
     */
    @Schema(description = "变更审核状态")
    private ApproveStatusEnum approvalStatus;


    /**
     * 变更说明
     */
    @Schema(description = "变更说明")
    private String changeRemark;

}
