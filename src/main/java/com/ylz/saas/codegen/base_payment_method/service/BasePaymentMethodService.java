package com.ylz.saas.codegen.base_payment_method.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.base_payment_method.entity.BasePaymentMethodEntity;
import com.ylz.saas.codegen.base_payment_method.vo.BasePaymentMethodVo;

import java.util.List;

public interface BasePaymentMethodService extends IService<BasePaymentMethodEntity> {
	/**
     * 新增支付方式表
     * @param basePaymentMethodVo 支付方式表
     */
    boolean saveBasePaymentMethod(BasePaymentMethodVo basePaymentMethodVo);
	
	/**
     * 修改支付方式表
     * @param basePaymentMethodVo 支付方式表
     */
    boolean updateBasePaymentMethod(BasePaymentMethodVo basePaymentMethodVo);
	
	/**
     * 查询支付方式表详情
     * @param id
     */
    BasePaymentMethodEntity getByIdBasePaymentMethod(Long id);
	
	/**
	* 通过id删除支付方式表
	*/
	boolean removeByIdsBasePaymentMethod(List<Long> ids);

    Object switchStatus(Long id);

}