package com.ylz.saas.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.common.data.datascope.annotation.DataPermission;
import com.ylz.saas.entity.SrmTenderOnlineQa;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import com.ylz.saas.req.SrmTenderOnlineQaPageReq;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【srm_tender_online_qa(在线问答表)】的数据库操作Mapper
* @createDate 2025-06-10 14:22:17
* @Entity com.ylz.saas.entity.SrmTenderOnlineQa
*/
@Mapper
//@DataPermission(userEnabled = true, dataPermissionRuleList = "[{\"dataTypeCode\":\"PROJECT\",\"targetField\":\"project_id\",\"dataSourceField\":\"ID\"}]")
public interface SrmTenderOnlineQaMapper extends SaasBaseMapper<SrmTenderOnlineQa> {

    /**
     * 查询问题列表
     *
     *
     * @param req
     * @return
     */
    Page<SrmTenderOnlineQa> pageQuestion(@Param("req") SrmTenderOnlineQaPageReq req);
}




