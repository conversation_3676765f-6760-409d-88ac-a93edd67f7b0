package com.ylz.saas.resp;

import com.ylz.saas.common.security.service.SaasUser;
import com.ylz.saas.enums.AnswerStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 在线问答表
 * @TableName srm_tender_online_qa
 */
@Data
public class SrmTenderOnlineQaPageResp {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 采购立项ID
     */
    private Long projectId;

    /**
     * 招标公告ID
     */
    private Long noticeId;

    /**
     * 租户供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 问题标题
     */
    private String questionTitle;

    /**
     * 问题描述
     */
    private String questionContent;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 提问人
     */
    private String submitBy;

    /**
     * 回复内容
     */
    private String answerContent;

    /**
     * 回复状态(PENDING-待回复、REPLIED-已回复)
     */
    private AnswerStatusEnum answerStatus;

    /**
     * 回复时间
     */
    private LocalDateTime answerTime;

    /**
     * 回复人
     */
    private String answerBy;

    /**
     * 回复人名称
     */
    private String answerByName;

    /**
     * 删除标识（0-正常、1-删除）
     */
    private Integer delFlag;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改人名称
     */
    private String updateByName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

}