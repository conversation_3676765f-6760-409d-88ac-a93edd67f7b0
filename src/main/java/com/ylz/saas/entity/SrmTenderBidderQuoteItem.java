package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ylz.saas.common.core.util.TenantTable;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 投标报价明细表
 * @TableName srm_tender_bidder_quote_item
 */
@TenantTable
@TableName(value ="srm_tender_bidder_quote_item")
@Data
public class SrmTenderBidderQuoteItem {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 采购立项ID
     */
    private Long projectId;

    /**
     * 招标公告ID
     */
    private Long noticeId;

    /**
     * 标段ID
     */
    private Long sectionId;

    /**
     * 报价结果ID
     */
    private Long resultId;

    /**
     * 租户供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 供应商响应ID
     */
    private Long tenderSupplierResponseId;

    /**
     * 采购立项明细ID
     */
    private Long projectItemId;

    /**
     * 采购立项支付方式ID
     */
    private Long procurementProjectPaymentId;

    /**
     * 轮次
     */
    private Integer roundNo;

    /**
     * 需求行号
     */
    private String requireNo;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 规格型号
     */
    private String specModel;

    /**
     * 单位
     */
    private String unit;

    /**
     * 需求数量
     */
    private BigDecimal requiredQuantity;

    /**
     * 需求牧场ID
     */
    private Long usageLocationId;

    /**
     * 质量指标ID
     */
    private Long qualityIndicatorId;

    /**
     * 可供数量
     */
    private BigDecimal availableQuantity;

    /**
     * 单价
     */
    private BigDecimal quotePrice;

    /**
     * 总价
     */
    private BigDecimal quoteAmount;


    /**
     * 报价时间
     */
    private LocalDateTime quoteTime;

    /**
     * 报价IP
     */
    private String quoteIp;

    /**
     * 是否中标(0-否、1-是)
     */
    private Integer awarded;

    /**
     * 中标数量
     */
    private BigDecimal awardedQuantity;

    /**
     * 合同数量
     */
    private BigDecimal contractQuantity;

    /**
     * 合同总价
     */
    private BigDecimal contractAmount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 报价项动态字段列表
     */
    @TableField(exist = false)
    private List<SrmTenderBidderQuoteFieldValue> srmTenderBidderQuoteFieldValueList;

    /**
     * 供应商名称
     */
    @TableField(exist = false)
    private String supplierName;


    /**
     * 质量指标名称
     */
    @TableField(exist = false)
    private String qualityIndicatorName;

    /**
     * 牧场名称
     */
    @TableField(exist = false)
    private String usageLocationName;

    /**
     * 牧场区域
     */
    @TableField(exist = false)
    private String usageLocationRegion;

    /**
     * 牧场地址
     */
    @TableField(exist = false)
    private String usageLocationAddress;

    /**
     * 定标模板id
     */
    @TableField(exist = false)
    private Long awardTemplateId;

    /**
     * 定标报告内容
     */
    @TableField(exist = false)
    private String awardReportContent;

    /**
     * 定标报告时间
     */
    @TableField(exist = false)
    private LocalDateTime awardReportTime;
}