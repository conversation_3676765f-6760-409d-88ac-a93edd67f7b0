package com.ylz.saas.codegen.base_payment_method.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.codegen.base_payment_method.entity.BasePaymentMethodEntity;
import com.ylz.saas.codegen.base_payment_method.param.BasePaymentMethodParam;
import com.ylz.saas.codegen.base_payment_method.service.BasePaymentMethodService;
import com.ylz.saas.codegen.base_payment_method.vo.BasePaymentMethodVo;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 支付方式表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:34
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/basePaymentMethod" )
@Tag(description = "basePaymentMethod" , name = "支付方式表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class BasePaymentMethodController {

    private final  BasePaymentMethodService basePaymentMethodService;
    /**
     * 启用/禁用
     * @return R
     */
    @Operation(summary = "启用/禁用" , description = "启用/禁用" )
    @PostMapping("/switch/{id}")
    public R switchStatus(@PathVariable("id") Long id) {
        return R.ok(basePaymentMethodService.switchStatus(id));
    }

    /**
     * 分页查询
     * @param page 分页对象
     * @param basePaymentMethod 支付方式表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @PostMapping("/page" )
    public R getBasePaymentMethodPage(@RequestBody BasePaymentMethodParam param, Page page) {
        QueryWrapper<BasePaymentMethodEntity> queryWrapper = param.toWrapper(); 
        return R.ok(basePaymentMethodService.page(page, queryWrapper));
    }

    /**
     * 通过id查询支付方式表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(basePaymentMethodService.getByIdBasePaymentMethod(id));
    }

    /**
     * 新增支付方式表
     * @param basePaymentMethodVo 支付方式表
     * @return R
     */
    @Operation(summary = "新增支付方式表" , description = "新增支付方式表" )
    @SysLog("新增支付方式表" )
    @PostMapping("/save")
    public R save(@Valid @RequestBody BasePaymentMethodVo basePaymentMethodVo) {
        return R.ok(basePaymentMethodService.saveBasePaymentMethod(basePaymentMethodVo));
    }

    /**
     * 修改支付方式表
     * @param basePaymentMethodVo 支付方式表
     * @return R
     */
    @Operation(summary = "修改支付方式表" , description = "修改支付方式表" )
    @SysLog("修改支付方式表" )
    @PostMapping("/update")
    public R updateById(@Valid @RequestBody BasePaymentMethodVo basePaymentMethodVo) {
        return R.ok(basePaymentMethodService.updateBasePaymentMethod(basePaymentMethodVo));
    }

    /**
     * 通过id删除支付方式表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除支付方式表" , description = "通过id删除支付方式表" )
    @SysLog("通过id删除支付方式表" )
    @GetMapping("remove")
    public R removeById(@RequestParam List<Long> ids) {
		boolean remove = basePaymentMethodService.removeByIdsBasePaymentMethod(ids);
		return R.ok(remove);
    }

}