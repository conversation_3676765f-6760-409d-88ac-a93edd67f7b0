package com.ylz.saas.mapper;

import com.ylz.saas.entity.SrmTenantSupplierCertificate;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【srm_tenant_supplier_certificate(租户供应商资质证书表)】的数据库操作Mapper
* @createDate 2025-06-10 18:08:09
* @Entity com.ylz.saas.entity.SrmTenantSupplierCertificate
*/
@Mapper
public interface SrmTenantSupplierCertificateMapper extends SaasBaseMapper<SrmTenantSupplierCertificate> {

    /**
     * 物理删除供应商资质证书
     * @param id 资质ID
     * @return 删除的记录数
     */
    int physicalDeleteById(@Param("id") Long id);

}




