package com.ylz.saas.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylz.saas.entity.SrmTenderEvaluationResult;
import com.ylz.saas.resp.SrmTenderEvaluationResultDetailResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tender_evaluation_result(评标结果表)】的数据库操作Mapper
* @createDate 2025-06-16 15:54:36
* @Entity com.ylz.saas.entity.SrmTenderEvaluationResult
*/
@Mapper
//@DataPermission(userEnabled = true, dataPermissionRuleList = "[{\"dataTypeCode\":\"PROJECT\",\"targetField\":\"project_id\",\"dataSourceField\":\"ID\"}]")
public interface SrmTenderEvaluationResultMapper extends BaseMapper<SrmTenderEvaluationResult> {

    /**
     * 通过ID获取评标结果详情（包含报价轮次）
     *
     * @param id 评标结果ID
     * @return 评标结果详情
     */
    SrmTenderEvaluationResultDetailResp getEvaluationResultDetailById(@Param("id") Long id);

    /**
     * 通过noticeId和projectId获取评标结果详情（包含报价轮次）
     *
     * @param noticeId 招标公告ID
     * @param projectId 采购立项ID
     * @return 评标结果详情
     */
    SrmTenderEvaluationResultDetailResp getEvaluationResultDetailByNoticeAndProject(@Param("noticeId") Long noticeId, @Param("projectId") Long projectId);

    /**
     * 通过noticeId和projectId获取评标结果详情（包含报价轮次)
     * @param noticeId
     * @param projectId
     * @return
     */
    List<SrmTenderEvaluationResultDetailResp> getEvaluationResultDetailByNoticeAndProjectList(@Param("noticeId") Long noticeId, @Param("projectId") Long projectId, @Param("sectionId") Long sectionId);
}




