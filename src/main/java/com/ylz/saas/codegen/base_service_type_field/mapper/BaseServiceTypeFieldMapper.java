package com.ylz.saas.codegen.base_service_type_field.mapper;

import com.ylz.saas.codegen.base_service_type_field.entity.BaseServiceTypeFieldEntity;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import com.ylz.saas.vo.LabelAndValueVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface BaseServiceTypeFieldMapper extends SaasBaseMapper<BaseServiceTypeFieldEntity> {


    /**
     * 执行动态查询 SQL
     * 1. 直接原样执行 SQL
     * @param sql 完整的 SQL 查询语句
     * @return 查询结果列表
     */
    @Select("${sql}")
    List<LabelAndValueVo> executeDynamicSelectQuery(@Param("sql") String sql);
}