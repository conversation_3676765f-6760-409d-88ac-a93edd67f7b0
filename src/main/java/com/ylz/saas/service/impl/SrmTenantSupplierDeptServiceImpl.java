package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmTenantSupplierDept;
import com.ylz.saas.enums.SupplierApprovalStatus;
import com.ylz.saas.mapper.SrmTenantSupplierDeptMapper;
import com.ylz.saas.service.SrmTenantSupplierDeptService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.collection.CollectionUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【srm_tenant_supplier_dept(租户供应商部门关联表)】的数据库操作Service实现
 * @createDate 2025-06-20
 */
@Service
@Slf4j
public class SrmTenantSupplierDeptServiceImpl extends ServiceImpl<SrmTenantSupplierDeptMapper, SrmTenantSupplierDept>
        implements SrmTenantSupplierDeptService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createSupplierDeptRelation(Long tenantSupplierId, Long deptId, String relationType) {
        log.info("开始创建供应商部门关联关系，供应商ID：{}，部门ID：{}，关联类型：{}", tenantSupplierId, deptId, relationType);

        // 检查是否已存在相同的关联关系
        long count = this.lambdaQuery()
                .eq(SrmTenantSupplierDept::getTenantSupplierId, tenantSupplierId)
                .eq(SrmTenantSupplierDept::getDeptId, deptId)
                .eq(SrmTenantSupplierDept::getRelationType, relationType)
                .count();

        if (count > 0) {
            log.info("供应商部门关联关系已存在，供应商ID：{}，部门ID：{}，关联类型：{}", tenantSupplierId, deptId, relationType);
            return true;
        }

        // 创建新的关联关系
        SrmTenantSupplierDept supplierDept = new SrmTenantSupplierDept();
        supplierDept.setTenantSupplierId(tenantSupplierId);
        supplierDept.setDeptId(deptId);
        supplierDept.setRelationType(relationType);
        supplierDept.setApprovalStatus(SupplierApprovalStatus.WAIT_APPROVAL.name());
        supplierDept.setDelFlag(0);

        boolean result = this.save(supplierDept);

        if (result) {
            log.info("供应商部门关联关系创建成功，关联ID：{}", supplierDept.getId());
        } else {
            log.error("供应商部门关联关系创建失败，供应商ID：{}，部门ID：{}", tenantSupplierId, deptId);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createSupplierDeptRelation(Long tenantSupplierId, Long deptId, String relationType, String approvalStatus) {
        log.info("开始创建供应商部门关联关系（带审核状态），供应商ID：{}，部门ID：{}，关联类型：{}，审核状态：{}",
                tenantSupplierId, deptId, relationType, approvalStatus);

        // 检查是否已存在相同的关联关系
        long count = this.lambdaQuery()
                .eq(SrmTenantSupplierDept::getTenantSupplierId, tenantSupplierId)
                .eq(SrmTenantSupplierDept::getDeptId, deptId)
                .eq(SrmTenantSupplierDept::getRelationType, relationType)
                .count();

        if (count > 0) {
            log.info("供应商部门关联关系已存在，供应商ID：{}，部门ID：{}，关联类型：{}", tenantSupplierId, deptId, relationType);
            return true;
        }

        // 创建新的关联关系
        SrmTenantSupplierDept supplierDept = new SrmTenantSupplierDept();
        supplierDept.setTenantSupplierId(tenantSupplierId);
        supplierDept.setDeptId(deptId);
        supplierDept.setRelationType(relationType);
        supplierDept.setApprovalStatus(approvalStatus);
        supplierDept.setDelFlag(0);

        boolean result = this.save(supplierDept);

        if (result) {
            log.info("供应商部门关联关系创建成功，关联ID：{}", supplierDept.getId());
        } else {
            log.error("供应商部门关联关系创建失败，供应商ID：{}，部门ID：{}", tenantSupplierId, deptId);
        }

        return result;
    }

    @Override
    public List<SrmTenantSupplierDept> getDeptsByTenantSupplierId(Long tenantSupplierId) {
        return this.baseMapper.getDeptsByTenantSupplierId(tenantSupplierId);
    }

    @Override
    public List<Long> getTenantSupplierIdsByDeptId(Long deptId) {
        return this.baseMapper.getTenantSupplierIdsByDeptId(deptId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSupplierDeptRelation(Long id) {
        log.info("开始删除供应商部门关联关系，关联ID：{}", id);

        // 使用物理删除
        int result = this.baseMapper.physicalDeleteById(id);

        if (result > 0) {
            log.info("供应商部门关联关系物理删除成功，关联ID：{}", id);
        } else {
            log.error("供应商部门关联关系物理删除失败，关联ID：{}", id);
        }

        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAllRelationsByTenantSupplierId(Long tenantSupplierId) {
        log.info("开始删除供应商的所有部门关联关系，供应商ID：{}", tenantSupplierId);

        // 使用物理删除
        int result = this.baseMapper.physicalDeleteByTenantSupplierId(tenantSupplierId);

        if (result > 0) {
            log.info("供应商部门关联关系批量物理删除成功，供应商ID：{}，删除记录数：{}", tenantSupplierId, result);
        } else {
            log.info("供应商没有部门关联关系需要删除，供应商ID：{}", tenantSupplierId);
        }

        return true; // 即使没有记录需要删除也返回true
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createSupplierDeptRelations(Long tenantSupplierId, List<Long> deptIds, String relationType) {
        log.info("开始批量创建供应商部门关联关系，供应商ID：{}，部门ID集合：{}，关联类型：{}",
                tenantSupplierId, deptIds, relationType);

        if (tenantSupplierId == null || CollectionUtil.isEmpty(deptIds)) {
            log.warn("供应商ID或部门ID集合为空，跳过创建关联关系");
            return true;
        }

        // 去重部门ID
        List<Long> distinctDeptIds = deptIds.stream().distinct().collect(Collectors.toList());

        // 查询已存在的关联关系
        List<SrmTenantSupplierDept> existingRelations = this.lambdaQuery()
                .eq(SrmTenantSupplierDept::getTenantSupplierId, tenantSupplierId)
                .in(SrmTenantSupplierDept::getDeptId, distinctDeptIds)
                .eq(SrmTenantSupplierDept::getRelationType, relationType)
                .list();

        // 获取已存在的部门ID
        List<Long> existingDeptIds = existingRelations.stream()
                .map(SrmTenantSupplierDept::getDeptId)
                .collect(Collectors.toList());

        // 过滤出需要新建的部门ID
        List<Long> newDeptIds = distinctDeptIds.stream()
                .filter(deptId -> !existingDeptIds.contains(deptId))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(newDeptIds)) {
            log.info("所有部门关联关系已存在，无需创建新的关联");
            return true;
        }

        // 构建新的关联关系实体列表
        List<SrmTenantSupplierDept> newRelations = newDeptIds.stream()
                .map(deptId -> {
                    SrmTenantSupplierDept supplierDept = new SrmTenantSupplierDept();
                    supplierDept.setTenantSupplierId(tenantSupplierId);
                    supplierDept.setDeptId(deptId);
                    supplierDept.setRelationType(relationType);
                    supplierDept.setApprovalStatus(SupplierApprovalStatus.WAIT_APPROVAL.name());
                    supplierDept.setDelFlag(0);
                    return supplierDept;
                })
                .collect(Collectors.toList());

        // 批量保存
        boolean result = this.saveBatch(newRelations);

        if (result) {
            log.info("供应商部门关联关系批量创建成功，供应商ID：{}，新建关联数量：{}",
                    tenantSupplierId, newRelations.size());
        } else {
            log.error("供应商部门关联关系批量创建失败，供应商ID：{}", tenantSupplierId);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createSupplierDeptRelations(Long tenantSupplierId, List<Long> deptIds, String relationType, String approvalStatus) {
        log.info("开始批量创建供应商部门关联关系（带审核状态），供应商ID：{}，部门ID集合：{}，关联类型：{}，审核状态：{}",
                tenantSupplierId, deptIds, relationType, approvalStatus);

        if (tenantSupplierId == null || CollectionUtil.isEmpty(deptIds)) {
            log.warn("供应商ID或部门ID集合为空，跳过创建关联关系");
            return true;
        }

        // 去重部门ID
        List<Long> distinctDeptIds = deptIds.stream().distinct().collect(Collectors.toList());

        // 查询已存在的关联关系
        List<SrmTenantSupplierDept> existingRelations = this.lambdaQuery()
                .eq(SrmTenantSupplierDept::getTenantSupplierId, tenantSupplierId)
                .in(SrmTenantSupplierDept::getDeptId, distinctDeptIds)
                .eq(SrmTenantSupplierDept::getRelationType, relationType)
                .list();

        // 获取已存在的部门ID
        List<Long> existingDeptIds = existingRelations.stream()
                .map(SrmTenantSupplierDept::getDeptId)
                .toList();

        // 过滤出需要新建的部门ID
        List<Long> newDeptIds = distinctDeptIds.stream()
                .filter(deptId -> !existingDeptIds.contains(deptId))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(newDeptIds)) {
            log.info("所有部门关联关系已存在，无需创建新的关联");
            return true;
        }
        List<SrmTenantSupplierDept> saveOrUpdateList = new ArrayList<>();
        List<SrmTenantSupplierDept> existUpdate = existingRelations.stream()
                .filter(e -> List.of(SupplierApprovalStatus.INVITE_REJECTED.name(), SupplierApprovalStatus.REJECTED.name()).contains(e.getApprovalStatus()) && deptIds.contains(e.getDeptId()))
                .peek(e -> e.setApprovalStatus(approvalStatus))
                .toList();
        // 构建新的关联关系实体列表
        List<SrmTenantSupplierDept> newRelations = newDeptIds.stream()
                .map(deptId -> {
                    SrmTenantSupplierDept supplierDept = new SrmTenantSupplierDept();
                    supplierDept.setTenantSupplierId(tenantSupplierId);
                    supplierDept.setDeptId(deptId);
                    supplierDept.setRelationType(relationType);
                    supplierDept.setApprovalStatus(approvalStatus);
                    supplierDept.setDelFlag(0);
                    return supplierDept;
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existUpdate)) {
            saveOrUpdateList.addAll(existUpdate);
        }
        if (CollectionUtils.isNotEmpty(newRelations)) {
            saveOrUpdateList.addAll(newRelations);
        }

        // 批量保存
        boolean result = this.saveOrUpdateBatch(saveOrUpdateList);

        if (result) {
            log.info("供应商部门关联关系批量创建成功，供应商ID：{}，新建关联数量：{}",
                    tenantSupplierId, newRelations.size());
        } else {
            log.error("供应商部门关联关系批量创建失败，供应商ID：{}", tenantSupplierId);
        }

        return result;
    }
}
