package com.ylz.saas.test.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ylz.saas.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalDateTime;


/**
 * 单表测试页面
 *
 * <AUTHOR>
 * @date 2025-05-19 15:35:40
 */
@Data
@TableName("single_table")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "单表测试页面")
@TenantTable
public class SingleTableEntity extends Model<SingleTableEntity> {


    /**
  	 * 主键
   	 */
    @TableId(type = IdType.AUTO)
    @Schema(description="主键")
    private Long id;

    /**
  	 * 名称
   	 */
    @Schema(description="名称")
    private String name;

    /**
  	 * 编码
   	 */
    @Schema(description="编码")
    private String code;

    /**
  	 * 创建人
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
  	 * 创建时间
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

    /**
  	 * 修改时间
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

    /**
  	 * 删除标记
   	 */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标记")
    private String delFlag;

    /**
  	 * 租户ID
   	 */
    @Schema(description="租户ID")
    private Long tenantId;
}