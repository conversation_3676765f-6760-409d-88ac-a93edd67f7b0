package com.ylz.saas.codegen.base_agent_org.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/10 15:20
 * @Description
 */
@Data
public class AgentUserInnerAccountReq {

    /**
     * 用户名称
     */
    @Schema(description="用户名称")
    private String userName;

    /**
     * 登录账号
     */
    @Schema(description="登录账号")
    private String loginAccount;
    /**
     * 社会统一信用代码
     */
    @Schema(description="社会统一信用代码")
    private String socialCreditCode;
    /**
     * 联系电话
     */
    @Schema(description="联系电话")
    private String contactPhone;

    /**
     * 不包括的用户id
     */
    @Schema(description="不包括的用户id")
    private List<Long> notInUserIds;
}
