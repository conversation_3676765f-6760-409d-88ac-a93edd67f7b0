package com.ylz.saas.controller;

import com.ylz.saas.common.core.util.R;
import com.ylz.saas.entity.SrmProcessInstance;
import com.ylz.saas.req.ProcessInstanceStartReq;
import com.ylz.saas.req.ProcessInstanceStopReq;
import com.ylz.saas.req.ProcessInstanceGroupQueryReq;
import com.ylz.saas.resp.ProcessInstanceStartResp;
import com.ylz.saas.resp.ProcessInstanceGroupResp;
import com.ylz.saas.service.SrmProcessConfigService;
import com.ylz.saas.service.SrmProcessInstanceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

import java.util.List;
import java.util.Set;

/**
 * 业务流流程管理控制层
 * <AUTHOR>
 * @Date 2025/6/12 16:12
 * @Description
 */
@RestController
@RequestMapping("/processManagement")
@RequiredArgsConstructor
@Tag(name = "流程管理", description = "业务流程实例管理接口")
public class ProcessManagementController {

    private final SrmProcessInstanceService srmProcessInstanceService;

    /**
     * 启动流程
     * @param req
     * @return
     */
    @PostMapping("/startProcess")
    @Operation(summary = "启动流程", description = "启动业务流程实例")
    public R<ProcessInstanceStartResp> startProcessInstance(@RequestBody @Validated ProcessInstanceStartReq req) {
        return R.ok(srmProcessInstanceService.startProcessInstance(req));
    }

    /**
     * 停止流程（撤销审批）
     * @param req
     * @return
     */
    @PostMapping("/stopProcess")
    @Operation(summary = "停止流程", description = "停止流程（撤销审批）")
    public R<Boolean> stopProcessInstance(@RequestBody @Validated ProcessInstanceStopReq req) {
        return R.ok(srmProcessInstanceService.stopProcessInstance(req));
    }


    /**
     * 通过bizId集合和type集合查询流程实例，返回分组封装对象
     * @param request 查询请求参数
     * @return 分组封装的流程实例对象
     */
    @PostMapping("/getInstancesGroupByBizIdAndType")
    @Operation(summary = "分组查询流程实例", description = "通过bizId集合和type集合查询流程实例，返回分组封装对象")
    public R<ProcessInstanceGroupResp> getInstancesGroupByBizIdAndType(@RequestBody ProcessInstanceGroupQueryReq request) {
        return R.ok(srmProcessInstanceService.getInstancesGroupByBizIdAndType(request.getBizIds(), request.getTypes()));
    }

}
