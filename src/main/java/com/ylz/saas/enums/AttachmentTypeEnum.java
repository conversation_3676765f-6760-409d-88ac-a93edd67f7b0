package com.ylz.saas.enums;

import lombok.Getter;

/**
 * 招标附件类型枚举
 */
@Getter
public enum AttachmentTypeEnum {
    PROJECT, // 采购立项
    NOTICE, // 招标公告
    INVITE_RECEIPT, // 邀请回执
    QUOTE_ATTACHMENT, // 报价附件
    QUOTE_OTHER_ATTACHMENT, // 报价其他附件
    PUBLICITY_ATTACHMENT, // 中标公示的附件
    NOTICE_ATTACHMENT, // 中标公告的附件
    BID_ATTACHMENT, // 定标公告的附件
    BID_DOC_ATTACHMENT, // 标书的附件
    NEGOTIATION_BID_DOC_SECTION, // 竞谈文件标段的附件信息
    CLARIFY_NOTICE_ATTACHMENT, // 澄清公告的附件信息
    TENDER_CONTRACT, // 合同
    ENSILAGE_QUALITY_STANDARD, // 青贮--质量标准
    ENSILAGE_PRICE_SYSTEM, // 青贮--计价体系
    PROCUREMENT_DECISION,  // 采购决策申请
    ;
}