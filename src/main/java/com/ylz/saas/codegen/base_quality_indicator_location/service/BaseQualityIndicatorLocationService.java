package com.ylz.saas.codegen.base_quality_indicator_location.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.base_quality_indicator_location.entity.BaseQualityIndicatorLocationEntity;
import com.ylz.saas.codegen.base_quality_indicator_location.vo.BaseQualityIndicatorLocationVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

public interface BaseQualityIndicatorLocationService extends IService<BaseQualityIndicatorLocationEntity> {
	/**
     * 新增质量指标与部门关联表
     * @param baseQualityIndicatorLocationVo 质量指标与部门关联表
     */
    boolean saveBaseQualityIndicatorLocation(BaseQualityIndicatorLocationVo baseQualityIndicatorLocationVo);
	
	/**
     * 修改质量指标与部门关联表
     * @param baseQualityIndicatorLocationVo 质量指标与部门关联表
     */
    boolean updateBaseQualityIndicatorLocation(BaseQualityIndicatorLocationVo baseQualityIndicatorLocationVo);
	
	/**
     * 导入模板下载
     * @param response
     */
    void template(HttpServletResponse response);

    /**
     * 数据导入
     * @param file
     * @param response
     */
    void uploadData(MultipartFile file, HttpServletResponse response);
}