package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.BaseTemplates;
import com.ylz.saas.entity.SrmTenderScoreTemplate;
import com.ylz.saas.entity.SrmTenderScoreTemplateItem;
import com.ylz.saas.mapper.SrmTenderScoreTemplateItemMapper;
import com.ylz.saas.req.SrmTenderScoreTemplateSaveReq;
import com.ylz.saas.resp.SrmTenderScoreTemplateDetailResp;
import com.ylz.saas.service.SrmTenderScoreTemplateService;
import com.ylz.saas.mapper.SrmTenderScoreTemplateMapper;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【srm_tender_score_template(评分模板表)】的数据库操作Service实现
* @createDate 2025-07-09 16:30:35
*/
@Service
public class SrmTenderScoreTemplateServiceImpl extends ServiceImpl<SrmTenderScoreTemplateMapper, SrmTenderScoreTemplate>
    implements SrmTenderScoreTemplateService{
    @Autowired
    private SrmTenderScoreTemplateItemMapper itemMapper;

    @Override
    public Page<SrmTenderScoreTemplate> query(Page<SrmTenderScoreTemplate> page, String templateName, String templateCode) {
        return baseMapper.selectPage(page, templateName, templateCode);
    }

    @Override
    public SrmTenderScoreTemplateDetailResp queryDetail(Long id) {
        SrmTenderScoreTemplate template = this.getById(id);
        if (template == null) {
            throw new RuntimeException("模板不存在");
        }

        SrmTenderScoreTemplateDetailResp detailResp = new SrmTenderScoreTemplateDetailResp();
        detailResp.setId(template.getId());
        detailResp.setTemplateName(template.getTemplateName());
        detailResp.setTemplateCode(template.getTemplateCode());
        detailResp.setTemplateDesc(template.getTemplateDesc());

        // 查询评审项并转换为 TemplateReviewItem
        List<SrmTenderScoreTemplateDetailResp.TemplateReviewItem> reviewItems = itemMapper.selectReviewItems(id, "REVIEW");


        // 查询评分项并转换为 TemplateScoreItem
        List<SrmTenderScoreTemplateDetailResp.TemplateScoreItem> scoreItems = itemMapper.selectScoreItems(id, "SCORE");


        // 按 node 分组
        detailResp.setTemplateReviewItemsByNode(reviewItems.stream()
                .collect(Collectors.groupingBy(SrmTenderScoreTemplateDetailResp.TemplateReviewItem::getNode)));

        detailResp.setTemplateScoreItemsByNode(scoreItems.stream()
                .collect(Collectors.groupingBy(SrmTenderScoreTemplateDetailResp.TemplateScoreItem::getNode)));

        return detailResp;
    }

    @Override
    public void saveTemplate(SrmTenderScoreTemplateSaveReq req) {
        // 1. 判断模板名称是否重复
        checkTemplateNameUnique(req.getTemplateName(), req.getId());
        // 2. 校验评分项的分值范围
        validateScoreItems(req.getTemplateScoreItems());
        // 3. 插入主表
        SrmTenderScoreTemplate template = new SrmTenderScoreTemplate();
        template.setTemplateName(req.getTemplateName());
        template.setTemplateCode(generateTemplateCode());
        template.setTemplateDesc(req.getTemplateDesc());
        template.setDelFlag(0);

        if (!this.save(template)) {
            throw new RuntimeException("保存模板失败");
        }

        Long templateId = template.getId();

        // 4. 处理评审项
        if (req.getTemplateReviewItems() != null && !req.getTemplateReviewItems().isEmpty()) {
            List<SrmTenderScoreTemplateItem> reviewEntities = req.getTemplateReviewItems().stream()
                    .map(item -> convertToReviewEntity(templateId, item))
                    .collect(Collectors.toList());

            itemMapper.batchInsertTemplateItems(reviewEntities);
        }

        // 5. 处理评分项
        if (req.getTemplateScoreItems() != null && !req.getTemplateScoreItems().isEmpty()) {
            List<SrmTenderScoreTemplateItem> scoreEntities = req.getTemplateScoreItems().stream()
                    .map(item -> convertToScoreEntity(templateId, item))
                    .collect(Collectors.toList());

            itemMapper.batchInsertTemplateItems(scoreEntities);
        }
    }

    @Override
    @Transactional
    public void updateTemplate(SrmTenderScoreTemplateSaveReq req) {
        // 1. 校验模板是否存在
        SrmTenderScoreTemplate template = this.getById(req.getId());
        if (template == null) {
            throw new RuntimeException("模板不存在");
        }

        // 2. 检查模板名称是否重复
        checkTemplateNameUnique(req.getTemplateName(), req.getId());

        // 3. 校验评分项的分值范围
        validateScoreItems(req.getTemplateScoreItems());

        // 4. 更新主表信息
        template.setTemplateName(req.getTemplateName());
        template.setTemplateDesc(req.getTemplateDesc());

        if (!this.updateById(template)) {
            throw new RuntimeException("更新模板失败");
        }

        Long templateId = template.getId();

        // 5. 删除旧的评审项和评分项
        itemMapper.deleteItemsByTemplateIdAndType(templateId, "REVIEW");
        itemMapper.deleteItemsByTemplateIdAndType(templateId, "SCORE");

        // 6. 插入新的评审项
        if (req.getTemplateReviewItems() != null && !req.getTemplateReviewItems().isEmpty()) {
            List<SrmTenderScoreTemplateItem> reviewEntities = req.getTemplateReviewItems().stream()
                    .map(item -> convertToReviewEntity(templateId, item))
                    .collect(Collectors.toList());

            itemMapper.batchInsertTemplateItems(reviewEntities);
        }

        // 7. 插入新的评分项
        if (req.getTemplateScoreItems() != null && !req.getTemplateScoreItems().isEmpty()) {
            List<SrmTenderScoreTemplateItem> scoreEntities = req.getTemplateScoreItems().stream()
                    .map(item -> convertToScoreEntity(templateId, item))
                    .collect(Collectors.toList());

            itemMapper.batchInsertTemplateItems(scoreEntities);
        }
    }

    @Override
    public void deleteTemplate(Long id) {
        if (id == null) {
            throw new RuntimeException("模板ID不能为空");
        }

        // 1. 校验模板是否存在
        SrmTenderScoreTemplate template = this.getById(id);
        if (template == null) {
            throw new RuntimeException("模板 ID：" + id + " 不存在");
        }
        // 2. 删除模板
        baseMapper.update(null,
                new LambdaUpdateWrapper<SrmTenderScoreTemplate>()
                        .eq(SrmTenderScoreTemplate::getId, id)
                        .set(SrmTenderScoreTemplate::getDelFlag, 1));

//        if (!this.updateById(template)) {
//            throw new RuntimeException("删除模板失败");
//        }
    }


    private boolean existsById(Long id) {
        return this.getById(id) != null;
    }


    private void checkTemplateNameUnique(String templateName, Long id) {
        SrmTenderScoreTemplate existing = baseMapper.selectOne(new QueryWrapper<SrmTenderScoreTemplate>()
                .eq("template_name", templateName));

        if (existing != null && (id == null || !existing.getId().equals(id))) {
            throw new RuntimeException("模板名称已存在");
        }
    }
    @Transactional
    public String generateTemplateCode() {
        // 查询最大 code 值
        String maxCode = baseMapper.getMaxTemplateCodeForUpdate();

        int nextNum = 1;
        if (maxCode != null && maxCode.startsWith("MB-")) {
            try {
                String numStr = maxCode.substring(3); // 取出数字部分
                nextNum = Integer.parseInt(numStr) + 1;
            } catch (NumberFormatException e) {
                nextNum = 1;
            }
        }

        return String.format("MB-%06d", nextNum);
    }
    // 评审项转换成 SrmTenderScoreTemplateItem
    private SrmTenderScoreTemplateItem convertToReviewEntity(Long templateId, SrmTenderScoreTemplateSaveReq.TemplateReviewItem item) {
        SrmTenderScoreTemplateItem entity = new SrmTenderScoreTemplateItem();
        entity.setTemplateId(templateId);
        entity.setName(item.getName());
        entity.setDetail(item.getDetail());
        entity.setNode(item.getNode());
        entity.setType("REVIEW");
        entity.setRejectCount(item.getRejectCount());
        return entity;
    }
    // 评分项转换成 SrmTenderScoreTemplateItem
    private SrmTenderScoreTemplateItem convertToScoreEntity(Long templateId, SrmTenderScoreTemplateSaveReq.TemplateScoreItem item) {
        SrmTenderScoreTemplateItem entity = new SrmTenderScoreTemplateItem();
        entity.setTemplateId(templateId);
        entity.setName(item.getName());
        entity.setDetail(item.getDetail());
        entity.setNode(item.getNode());
        entity.setType("SCORE");
        entity.setTotalScore(item.getTotalScore());
        entity.setWeight(item.getWeight());
        entity.setMinScore(item.getMinScore());
        entity.setMaxScore(item.getMaxScore());
        return entity;
    }
    private void validateScoreItems(List<SrmTenderScoreTemplateSaveReq.TemplateScoreItem> scoreItems) {
        if (scoreItems == null || scoreItems.isEmpty()) {
            return;
        }

        for (SrmTenderScoreTemplateSaveReq.TemplateScoreItem item : scoreItems) {
            BigDecimal totalScore = item.getTotalScore();
            BigDecimal minScore = item.getMinScore();
            BigDecimal maxScore = item.getMaxScore();

            // 1. 总分不能为空且 >= 0
            if (totalScore == null || totalScore.compareTo(BigDecimal.ZERO) < 0) {
                throw new RuntimeException("【" + item.getName() + "】总分必须大于等于0");
            }

            // 2. 最低分不能小于0
            if (minScore != null && minScore.compareTo(BigDecimal.ZERO) < 0) {
                throw new RuntimeException("【" + item.getName() + "】最低分不能小于0");
            }

            // 3. 最高分不能超过总分
            if (maxScore != null && maxScore.compareTo(totalScore) > 0) {
                throw new RuntimeException("【" + item.getName() + "】最高分不能超过总分");
            }

            // 4. 最高分必须大于最低分
            if (maxScore != null && minScore != null && maxScore.compareTo(minScore) <= 0) {
                throw new RuntimeException("【" + item.getName() + "】最高分必须大于最低分");
            }
        }
    }

}




