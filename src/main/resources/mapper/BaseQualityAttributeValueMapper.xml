<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.codegen.base_quality_attribute_value.mapper.BaseQualityAttributeValueMapper">

    <!-- 根据物料编码查询质量属性值列表（联查质量属性信息） -->
    <select id="selectQualityAttributeValueListByMaterialCode" 
            resultType="com.ylz.saas.codegen.base_quality_attribute_value.vo.BaseQualityAttributeValueWithAttributeVo">
        SELECT 
            bqav.id,
            bqav.dept_id,
            bqav.attribute_id,
            bqav.value_content,
            bqav.material_code,
            bqav.rejection_standard,
            bqav.penalty_standard,
            bqav.sort_order,
            bqav.create_by_name,
            bqav.update_by_name,
            bqav.create_by,
            bqav.create_time,
            bqav.update_by,
            bqav.update_time,
            bqav.tenant_id,
            bqav.create_by_id,
            bqav.update_by_id,
            bqa.attribute_code,
            bqa.attribute_name
        FROM base_quality_attribute_value bqav
        LEFT JOIN base_quality_attribute bqa ON bqav.attribute_id = bqa.id
        <where>
            bqav.material_code = #{materialCode}
            <if test="attributeName != null and attributeName != ''">
                AND bqa.attribute_name LIKE CONCAT('%', #{attributeName}, '%')
            </if>
        </where>
        ORDER BY bqav.sort_order ASC, bqav.id DESC
        LIMIT 10
    </select>

</mapper>
