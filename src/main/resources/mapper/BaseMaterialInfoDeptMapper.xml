<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.codegen.base_material_info_dept.mapper.BaseMaterialInfoDeptMapper">

    <!-- 根据物料ID查询关联的组织信息（包含组织名称） -->
    <select id="selectDeptsByMaterialId" resultType="com.ylz.saas.codegen.base_material_info_dept.entity.BaseMaterialInfoDeptEntity">
        SELECT 
            bmid.id,
            bmid.material_info_id,
            bmid.dept_id,
            sd.name as dept_name,
            bmid.create_by_id,
            bmid.create_by,
            bmid.create_time,
            bmid.update_by_id,
            bmid.update_by,
            bmid.update_time,
            bmid.del_flag,
            bmid.tenant_id
        FROM base_material_info_dept bmid
        LEFT JOIN sys_dept sd ON bmid.dept_id = sd.dept_id
        WHERE bmid.material_info_id = #{materialInfoId}
          AND bmid.del_flag = 0
        ORDER BY bmid.create_time ASC
    </select>

    <!-- 根据物料ID删除关联关系 -->
    <update id="deleteByMaterialId">


        delete
        from base_material_info_dept
        where material_info_id = #{materialInfoId}


    </update>

</mapper>
