package com.ylz.saas.codegen.base_material_info_dept.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.base_material_info_dept.entity.BaseMaterialInfoDeptEntity;

import java.util.List;

/**
 * 物料基础信息组织关联表Service接口
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface BaseMaterialInfoDeptService extends IService<BaseMaterialInfoDeptEntity> {

    /**
     * 根据物料ID查询关联的组织信息（包含组织名称）
     * @param materialInfoId 物料基础信息ID
     * @return 关联的组织信息列表
     */
    List<BaseMaterialInfoDeptEntity> getDeptsByMaterialId(Long materialInfoId);

    /**
     * 保存物料组织关联关系
     * @param materialInfoId 物料基础信息ID
     * @param deptIds 组织ID列表
     * @return 是否保存成功
     */
    boolean saveMaterialDeptRelations(Long materialInfoId, List<Long> deptIds);

    /**
     * 更新物料组织关联关系
     * @param materialInfoId 物料基础信息ID
     * @param deptIds 组织ID列表
     * @return 是否更新成功
     */
    boolean updateMaterialDeptRelations(Long materialInfoId, List<Long> deptIds);

    /**
     * 根据物料ID删除关联关系
     * @param materialInfoId 物料基础信息ID
     * @return 是否删除成功
     */
    boolean deleteByMaterialId(Long materialInfoId);

    /**
     * 批量删除物料组织关联关系
     * @param materialInfoIds 物料基础信息ID列表
     * @return 是否删除成功
     */
    boolean deleteByMaterialIds(List<Long> materialInfoIds);
}
