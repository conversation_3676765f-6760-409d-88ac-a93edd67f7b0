<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ylz.saas.codegen.srm_procurement_plan.mapper.SrmProcurementPlanMapper">

    <resultMap id="srmProcurementPlanMap"
               type="com.ylz.saas.codegen.srm_procurement_plan.entity.SrmProcurementPlanEntity">
        <id property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="planCode" column="plan_code"/>
        <result property="planName" column="plan_name"/>
        <result property="serviceTypeId" column="service_type_id"/>
        <result property="applyDate" column="apply_date"/>
        <result property="applyDeptId" column="apply_dept_id"/>
        <result property="applicant" column="applicant"/>
        <result property="applicantPhone" column="applicant_phone"/>
        <result property="procurementDeptId" column="procurement_dept_id"/>
        <result property="planRecruitMethod" column="plan_recruit_method"/>
        <result property="periodStartDate" column="period_start_date"/>
        <result property="procurementMethod" column="procurement_method"/>
        <result property="fundSource" column="fund_source"/>
        <result property="budgetAmount" column="budget_amount"/>
        <result property="status" column="status"/>
        <result property="createByName" column="create_by_name"/>
        <result property="updateByName" column="update_by_name"/>
        <result property="currentApprover" column="current_approver"/>
        <result property="baseAgentDeptId" column="base_agent_dept_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createById" column="create_by_id"/>
        <result property="updateById" column="update_by_id"/>
    </resultMap>

    <!-- 主表分页查询（受数据权限控制） -->
    <select id="planPage"
            resultType="com.ylz.saas.codegen.srm_procurement_plan.entity.SrmProcurementPlanEntity">
        SELECT * FROM srm_procurement_plan
        ${ew.customSqlSegment}
        ORDER BY create_time DESC
    </select>

    <select id="queryIgnorePermission" resultType="com.ylz.saas.codegen.srm_procurement_plan.entity.SrmProcurementPlanEntity">
        SELECT * FROM srm_procurement_plan
        ${ew.customSqlSegment}
    </select>

</mapper>