package com.ylz.saas.codegen.base_expert.vo;

import com.ylz.saas.codegen.base_expert_achievement.entity.BaseExpertAchievementEntity;
import com.ylz.saas.codegen.base_expert_education.entity.BaseExpertEducationEntity;
import com.ylz.saas.codegen.base_expert_work_experience.entity.BaseExpertWorkExperienceEntity;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 专家信息表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:42:08
 */
@Data
public class BaseExpertVo implements Serializable {

    /**
  	 * 主键ID
   	 */
    private Long id;

    /**
  	 * 组织ID
   	 */
    private Long deptId;

	/**
	 * 用户部门ID
	 */
	private Long deptTypeId;

    /**
  	 * 专家编码
   	 */
    private String expertCode;

    /**
  	 * 姓名
   	 */
    @NotBlank(message = "姓名不能为空")
    private String name;

    /**
  	 * 手机号
   	 */
	@Pattern(regexp = "^1(3|4|5|7|8|9)\\d{9}$",message = "手机号码格式错误")
    @NotBlank(message = "手机号不能为空")
    private String phone;

    /**
  	 * 专家分类
   	 */
    @NotBlank(message = "专家分类不能为空")
    private String expertCategory;

    /**
  	 * 登录账号
   	 */
    @NotBlank(message = "登录账号不能为空")
    private String loginAccount;

    /**
  	 * 所在地
   	 */
    @NotBlank(message = "所在地不能为空")
    private String address;

    /**
  	 * 身份证号码
   	 */
    private String idNumber;

    /**
  	 * 专家类型
   	 */
    private String expertType;

    /**
  	 * 关联用户ID
   	 */
    private Long userId;

    /**
  	 * 邮箱
   	 */
    private String email;

    // ========== 新建SysUser相关字段 ==========

    /**
     * 用户密码（外部专家新建账号时使用，如果不提供则使用默认密码123456）
     */
    private String password;

    /**
     * 用户昵称（外部专家新建账号时使用，如果不提供则使用姓名）
     */
    private String nickname;

    /**
     * 锁定标记（外部专家新建账号时使用，0-正常，9-锁定，默认为0）
     */
    private String lockFlag;

    /**
  	 * 状态
   	 */
    private String status;

    /**
  	 * 创建人名称
   	 */
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    private String updateByName;

    /**
  	 * 创建人
   	 */
    private String createBy;

    /**
  	 * 创建时间
   	 */
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
    private String updateBy;

    /**
  	 * 修改时间
   	 */
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
    private Long createById;

    /**
  	 * 更新人ID
   	 */
    private Long updateById;
	/**
	* 子表集合:base_expert_achievement
	*/
	private List<BaseExpertAchievementEntity> baseExpertAchievementList;
	/**
	* 子表集合:base_expert_education
	*/
	private List<BaseExpertEducationEntity> baseExpertEducationList;
	/**
	* 子表集合:base_expert_work_experience
	*/
	private List<BaseExpertWorkExperienceEntity> baseExpertWorkExperienceList;
}