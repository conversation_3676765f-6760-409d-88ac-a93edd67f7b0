<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenantSupplierBankAccountMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenantSupplierBankAccount">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="supplierId" column="supplier_id" jdbcType="BIGINT"/>
            <result property="bankName" column="bank_name" jdbcType="VARCHAR"/>
            <result property="bankCode" column="bank_code" jdbcType="VARCHAR"/>
            <result property="bankAddress" column="bank_address" jdbcType="VARCHAR"/>
            <result property="accountName" column="account_name" jdbcType="VARCHAR"/>
            <result property="accountNo" column="account_no" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateById" column="update_by_id" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,
        supplier_id,bank_name,bank_code,
        bank_address,account_name,account_no,
        remark,del_flag,create_by_id,
        create_by,create_by_name,create_time,
        update_by_id,update_by,update_by_name,
        update_time
    </sql>

    <delete id="physicalDeleteById">
        DELETE FROM srm_tenant_supplier_bank_account WHERE id = #{id}
    </delete>
</mapper>
