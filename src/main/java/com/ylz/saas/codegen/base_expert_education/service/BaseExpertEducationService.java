package com.ylz.saas.codegen.base_expert_education.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.base_expert_education.entity.BaseExpertEducationEntity;
import com.ylz.saas.codegen.base_expert_education.vo.BaseExpertEducationVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

public interface BaseExpertEducationService extends IService<BaseExpertEducationEntity> {
	/**
     * 新增专家教育经历表
     * @param baseExpertEducationVo 专家教育经历表
     */
    boolean saveBaseExpertEducation(BaseExpertEducationVo baseExpertEducationVo);
	
	/**
     * 修改专家教育经历表
     * @param baseExpertEducationVo 专家教育经历表
     */
    boolean updateBaseExpertEducation(BaseExpertEducationVo baseExpertEducationVo);
	
	/**
     * 导入模板下载
     * @param response
     */
    void template(HttpServletResponse response);

    /**
     * 数据导入
     * @param file
     * @param response
     */
    void uploadData(MultipartFile file, HttpServletResponse response);
}