package com.ylz.saas.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylz.saas.common.data.datascope.annotation.DataPermission;
import com.ylz.saas.entity.SrmTenderFailedLog;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【srm_tender_failed_log(流标记录表)】的数据库操作Mapper
* @createDate 2025-06-24 20:17:56
* @Entity com.ylz.saas.entity.SrmTenderFailedLog
*/
@Mapper
//@DataPermission(userEnabled = true, dataPermissionRuleList = "[{\"dataTypeCode\":\"PROJECT\",\"targetField\":\"project_id\",\"dataSourceField\":\"ID\"}]")
public interface SrmTenderFailedLogMapper extends BaseMapper<SrmTenderFailedLog> {

}




