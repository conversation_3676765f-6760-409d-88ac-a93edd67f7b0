<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenderBidEvaluationMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenderBidEvaluation">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
        <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
        <result property="projectId" column="project_id" jdbcType="BIGINT"/>
        <result property="noticeId" column="notice_id" jdbcType="BIGINT"/>
        <result property="sectionId" column="section_id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="contacts" column="contacts" jdbcType="VARCHAR"/>
        <result property="contactsPhone" column="contacts_phone" jdbcType="VARCHAR"/>
        <result property="approveStatus" column="approve_status" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="EvaluationWithMembersResultMap" type="com.ylz.saas.resp.SrmTenderBidEvaluationResp">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="reportTemplateId" column="report_template_id" jdbcType="BIGINT"/>
        <result property="reportContent" column="report_content" jdbcType="VARCHAR"/>
        <result property="projectId" column="project_id" jdbcType="BIGINT"/>
        <result property="noticeId" column="notice_id" jdbcType="BIGINT"/>
        <result property="sectionId" column="section_id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="contacts" column="contacts" jdbcType="VARCHAR"/>
        <result property="contactsPhone" column="contacts_phone" jdbcType="VARCHAR"/>
        <result property="approveStatus" column="approve_status" jdbcType="VARCHAR"/>
        <result property="summaryStatus" column="summary_status" jdbcType="VARCHAR"/>
        <result property="summaryBy" column="summary_by" jdbcType="VARCHAR"/>
        <result property="summaryByName" column="summary_by_name" jdbcType="VARCHAR"/>
        <result property="summaryTime" column="summary_time" jdbcType="TIMESTAMP"/>
        <result property="signedReportContent" column="signed_report_content" jdbcType="VARCHAR"/>
        <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <collection property="memberList" ofType="com.ylz.saas.resp.SrmTenderBidEvaluationResp$EvaluationMemberResp">
            <result property="userId" column="member_user_id" jdbcType="BIGINT"/>
            <result property="userName" column="member_user_name" jdbcType="VARCHAR"/>
            <result property="role" column="member_role" jdbcType="VARCHAR"/>
            <result property="roleName" column="member_role_name" jdbcType="VARCHAR"/>
            <result property="contactPhone" column="member_contact_phone" jdbcType="VARCHAR"/>
            <result property="expertCode" column="expert_code" jdbcType="VARCHAR"/>
            <result property="expertName" column="expert_name" jdbcType="VARCHAR"/>
            <result property="idNumber" column="expert_id_number" jdbcType="VARCHAR"/>
            <result property="expertCategory" column="expert_category" jdbcType="VARCHAR"/>
            <result property="expertType" column="expert_type" jdbcType="VARCHAR"/>
            <result property="sysRoleName" column="sys_role_name" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,project_id,notice_id,section_id,current_round,
        name,address,contacts,contacts_phone,approve_status,
        summary_status,summary_by,summary_by_name,summary_time,
        report_content,report_template_id,signed_report_content,
        del_flag,create_by,create_by_name,create_time,
        update_by,update_by_name,update_time
    </sql>



    <!-- 根据标段ID和报价轮次查询评标委员会详情（包含成员信息） -->
    <select id="getBySectionIdAndRoundWithMembers" resultMap="EvaluationWithMembersResultMap">
        SELECT
            e.id,
            e.project_id,
            e.notice_id,
            e.section_id,
            e.name,
            e.address,
            e.contacts,
            e.contacts_phone,
            e.approve_status,
            e.summary_status,
            e.summary_by,
            e.summary_by_name,
            e.summary_time,
            e.report_content,
            e.report_template_id,
            e.signed_report_content,
            e.create_by_name,
            e.create_time,
            e.update_by_name,
            e.update_time,
            m.user_id as member_user_id,
            u.username as member_user_name,
            m.role as member_role,
            CASE m.role
                WHEN 'EVALUATION_LEADER' THEN '评标组长'
                WHEN 'EVALUATION_MEMBER' THEN '评标成员'
                ELSE m.role
            END as member_role_name,
            m.contact_phone as member_contact_phone,
            be.expert_code,
            be.name as expert_name,
            be.id_number as expert_id_number,
            be.expert_category,
            be.expert_type,
            sr.role_name as sys_role_name
        FROM srm_tender_bid_evaluation e
        LEFT JOIN srm_project_member m ON e.id = m.business_id AND m.member_type = 'EVALUATION_MEMBER'
        LEFT JOIN sys_user u ON m.user_id = u.user_id
        LEFT JOIN base_expert be ON u.user_id = be.user_id
        LEFT JOIN sys_user_role sur ON u.user_id = sur.user_id
        LEFT JOIN sys_role sr ON sur.role_id = sr.role_id
        WHERE e.section_id = #{sectionId}
          AND be.del_flag = 0
          AND e.del_flag = 0
          AND m.del_flag = 0
        ORDER BY m.id
    </select>

    <!-- 分页查询项目标段列表 -->
    <select id="pageSections" resultType="com.ylz.saas.resp.SrmTenderSectionPageResp">
        SELECT
        s.id as sectionId,
        s.project_id as projectId,
        p.project_name as projectName,
        s.section_code as sectionCode,
        s.section_name as sectionName,
        CASE
        WHEN e.id IS NOT NULL THEN 'EXTRACTED'
        ELSE 'PENDING'
        END as expertExtractionStatus,
        COALESCE(e.summary_status, 'PENDING') as summaryStatus,
        e.summary_by_name as summaryByName,
        e.summary_time as summaryTime,
        e.report_content as reportContent,
        e.report_template_id as reportTemplateId,
        e.signed_report_content as signedReportContent,
        e.create_by_name as operatorName,
        e.create_time as operationTime,
        e.id as evaluationId,
        tn.id as noticeId,
        e.current_round as currentRound,
        pm.user_id as evaluationLeaderId,
        be.name as evaluationLeaderName,
        be.expert_code as evaluationLeaderCode,
        CASE
            WHEN NOT EXISTS (
                SELECT 1
                FROM srm_tender_evaluation_standard es2
                WHERE es2.section_id = s.id
                  AND es2.project_id = s.project_id
                  AND es2.notice_id = tn.id
                  AND es2.del_flag = 0
            ) THEN true
            WHEN NOT EXISTS (
                SELECT 1
                FROM srm_tender_supplier_response tr2
                JOIN srm_tender_bid_evaluation te2 ON tr2.project_id = te2.project_id
                    AND tr2.notice_id = te2.notice_id
                    AND tr2.section_id = te2.section_id
                JOIN srm_project_member pm2 ON te2.project_id = pm2.project_id
                    AND pm2.business_id = te2.id
                    AND pm2.member_type = 'EVALUATION_MEMBER'
                LEFT JOIN srm_tender_bid_evaluation_scoring tes2 ON pm2.project_id = tes2.project_id
                    AND pm2.user_id = tes2.user_id
                    AND tes2.evaluation_id = te2.id
                WHERE te2.section_id = s.id
                  AND tr2.del_flag = 0
                  AND te2.del_flag = 0
                  AND pm2.del_flag = 0
                  AND ((tes2.del_flag = 0 AND tes2.scoring_type = 'EXPERT_SCORING' AND tes2.status = 'WAIT_SUBMIT')
                       OR tes2.status IS NULL)
            ) THEN true
            ELSE false
        END as isEvaluationCompleted
        FROM srm_procurement_project_section s
        INNER JOIN srm_procurement_project p ON s.project_id = p.id
        LEFT JOIN srm_tender_notice tn ON p.id = tn.project_id AND tn.status = 'PUBLISHED'
        LEFT JOIN srm_tender_open topen ON s.id = topen.section_id AND topen.del_flag = 0
        LEFT JOIN srm_tender_bid_evaluation e ON s.id = e.section_id AND e.current_round = topen.current_round AND
        e.del_flag = 0
        LEFT JOIN srm_project_member pm ON e.id = pm.business_id AND pm.member_type = 'EVALUATION_MEMBER' AND pm.role =
        'EVALUATION_LEADER' AND pm.del_flag = 0
        LEFT JOIN base_expert be ON pm.user_id = be.user_id AND be.del_flag = 0
        WHERE s.del_flag = 0
        AND p.del_flag = 0

        <if test="req.projectId != null">
            AND s.project_id = #{req.projectId}
        </if>
        <if test="req.projectName != null and req.projectName != ''">
            AND p.project_name LIKE CONCAT('%', #{req.projectName}, '%')
        </if>
        <if test="req.sectionName != null and req.sectionName != ''">
            AND s.section_name LIKE CONCAT('%', #{req.sectionName}, '%')
        </if>
        <if test="req.expertExtractionStatus != null">
            <if test="req.expertExtractionStatus.name() == 'PENDING'">
                AND e.id IS NULL
            </if>
            <if test="req.expertExtractionStatus.name() == 'EXTRACTED'">
                AND e.id IS NOT NULL
            </if>
        </if>
        <if test="req.summaryStatus != null">
            <if test="req.summaryStatus.name() == 'PENDING'">
                AND (e.summary_status IS NULL OR e.summary_status = 'PENDING')
            </if>
            <if test="req.summaryStatus.name() == 'SUMMARIZED'">
                AND e.summary_status = 'SUMMARIZED'
            </if>
            <if test="req.summaryStatus.name() == 'SIGNED'">
                AND e.summary_status = 'SIGNED'
            </if>
        </if>
        <if test="req.operatorName != null and req.operatorName != ''">
            AND e.create_by_name LIKE CONCAT('%', #{req.operatorName}, '%')
        </if>
        <if test="req.summaryByName != null and req.summaryByName != ''">
            AND e.summary_by_name LIKE CONCAT('%', #{req.summaryByName}, '%')
        </if>
        ORDER BY s.create_time DESC
    </select>

    <!-- 批量查询标段的签名进度 -->
    <select id="batchGetSignatureProgress" resultType="com.ylz.saas.dto.SectionSignatureProgressDto">
        SELECT
            sig.section_id as sectionId,
            COUNT(CASE WHEN sig.signature_status = 'SIGNED' THEN 1 END) as signedCount,
            COUNT(*) as totalCount
        FROM srm_tender_evaluation_signature sig
        INNER JOIN srm_tender_bid_evaluation e ON sig.evaluation_id = e.id AND e.del_flag = 0
        WHERE sig.section_id IN
        <foreach collection="sectionIds" item="sectionId" open="(" separator="," close=")">
            #{sectionId}
        </foreach>
        AND sig.del_flag = 0
        GROUP BY sig.section_id
    </select>

    <!-- 查询标段下所有供应商基本信息 -->
    <select id="querySupplierBasicInfo" resultType="com.ylz.saas.resp.SrmTenderEvaluationSummarySupplierResp">
        SELECT
            sr.tenant_supplier_id as tenantSupplierId,
            sr.supplier_name as supplierName,
            sr.is_recommended_winner as isRecommendedWinner,
            sr.winner_candidate_order as winnerCandidateOrder
        FROM srm_tender_supplier_response sr
        WHERE sr.section_id = #{sectionId}
          AND sr.notice_id = #{noticeId}
          AND sr.project_id = #{projectId}
          AND sr.quote_status = 'COMPLETED'
          AND sr.del_flag = 0
        ORDER BY sr.supplier_name
    </select>

    <!-- 查询供应商评审项汇总 -->
    <select id="querySupplierReviewSummary" resultType="java.lang.String">
        SELECT
            CASE
                WHEN leader_summary_count > 0 THEN
                    CASE
                        WHEN leader_not_conform_count > 0 THEN '不符合'
                        ELSE '符合'
                    END
                ELSE
                    CASE
                        WHEN expert_not_conform_count > 0 THEN '不符合'
                        ELSE '符合'
                    END
            END as reviewSummary
        FROM (
            SELECT
                COUNT(leader_summary.scoring_detail_id) as leader_summary_count,
                COUNT(CASE WHEN leader_summary.is_conform = 0 THEN 1 END) as leader_not_conform_count,
                COUNT(CASE WHEN expert_scoring.is_conform = 0 THEN 1 END) as expert_not_conform_count
            FROM srm_tender_evaluation_standard es
            LEFT JOIN (
                SELECT scoring_detail_id, is_conform
                FROM srm_tender_bid_evaluation_scoring
                WHERE section_id = #{sectionId}
                  AND notice_id = #{noticeId}
                  AND project_id = #{projectId}
                  AND tenant_supplier_id = #{tenantSupplierId}
                  AND evaluation_id = #{evaluationId}
                  AND scoring_type = 'LEADER_SUMMARY'
                  AND current_round = #{currentRound}
                  AND del_flag = 0
            ) leader_summary ON es.id = leader_summary.scoring_detail_id
            LEFT JOIN (
                SELECT scoring_detail_id, is_conform
                FROM srm_tender_bid_evaluation_scoring
                WHERE section_id = #{sectionId}
                  AND notice_id = #{noticeId}
                  AND project_id = #{projectId}
                  AND tenant_supplier_id = #{tenantSupplierId}
                  AND evaluation_id = #{evaluationId}
                  AND current_round = #{currentRound}
                  AND (scoring_type = 'EXPERT_SCORING' OR scoring_type IS NULL)
                  AND del_flag = 0
            ) expert_scoring ON es.id = expert_scoring.scoring_detail_id
            WHERE es.section_id = #{sectionId}
              AND es.notice_id = #{noticeId}
              AND es.project_id = #{projectId}
              AND es.type = 'REVIEW'
              AND es.del_flag = 0
        ) summary_stats
    </select>

    <!-- 查询供应商各节点评分项分数 -->
    <select id="querySupplierNodeScores" resultType="com.ylz.saas.dto.NodeScoreDto">
        SELECT
            es.node_name as nodeName,
            CAST(COALESCE(
                CASE
                    WHEN leader_node_total.total_score IS NOT NULL THEN leader_node_total.total_score
                    ELSE SUM(expert_avg_score.avg_score * es.weight/100)
                END
            , 0) AS SIGNED) as totalScore
        FROM srm_tender_evaluation_standard es
        LEFT JOIN (
            SELECT
                es_inner.node_name,
                SUM(leader_score.score) as total_score
            FROM srm_tender_evaluation_standard es_inner
            INNER JOIN srm_tender_bid_evaluation_scoring leader_score
                ON es_inner.id = leader_score.scoring_detail_id
                AND leader_score.tenant_supplier_id = #{tenantSupplierId}
                AND leader_score.evaluation_id = #{evaluationId}
                AND leader_score.current_round = #{currentRound}
                AND leader_score.scoring_type = 'LEADER_SUMMARY'
                AND leader_score.del_flag = 0
            WHERE es_inner.section_id = #{sectionId}
              AND es_inner.notice_id = #{noticeId}
              AND es_inner.project_id = #{projectId}
              AND es_inner.type = 'SCORE'
              AND es_inner.del_flag = 0
            GROUP BY es_inner.node_name
        ) leader_node_total ON es.node_name = leader_node_total.node_name
        LEFT JOIN (
            SELECT
                scoring_detail_id,
                AVG(CAST(score AS DECIMAL(10,2))) as avg_score
            FROM srm_tender_bid_evaluation_scoring
            WHERE tenant_supplier_id = #{tenantSupplierId}
              AND evaluation_id = #{evaluationId}
              AND current_round = #{currentRound}
              AND scoring_type = 'EXPERT_SCORING'
              AND score IS NOT NULL
              AND del_flag = 0
            GROUP BY scoring_detail_id
        ) expert_avg_score ON es.id = expert_avg_score.scoring_detail_id
        WHERE es.section_id = #{sectionId}
          AND es.notice_id = #{noticeId}
          AND es.project_id = #{projectId}
          AND es.type = 'SCORE'
          AND es.del_flag = 0
        GROUP BY es.node_name, leader_node_total.total_score
        ORDER BY es.node_name
    </select>

    <!-- 查询供应商投标价格 -->
    <select id="querySupplierBidPrice" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(qi.quote_amount), 0) as bidPrice
        FROM srm_tender_bidder_quote_item qi
        WHERE qi.section_id = #{sectionId}
          AND qi.notice_id = #{noticeId}
          AND qi.project_id = #{projectId}
          AND qi.tenant_supplier_id = #{tenantSupplierId}
          AND qi.del_flag = 0
    </select>

    <!-- 查询评标节点列表 -->
    <select id="queryEvaluationNodes" resultType="java.lang.String">
        SELECT DISTINCT es.node_name
        FROM srm_tender_evaluation_standard es
        WHERE es.section_id = #{sectionId}
          AND es.notice_id = #{noticeId}
          AND es.project_id = #{projectId}
          AND es.type = 'SCORE'
          AND es.del_flag = 0
        ORDER BY es.node_name
    </select>

    <!-- 查询线下评标供应商基本信息 -->
    <select id="queryOfflineSupplierBasicInfo" resultType="com.ylz.saas.resp.SrmTenderOfflineEvaluationSummarySupplierResp">
        SELECT
            sr.tenant_supplier_id as tenantSupplierId,
            sr.supplier_name as supplierName,
            sr.is_recommended_winner as isRecommendedWinner,
            sr.winner_candidate_order as winnerCandidateOrder,
            CASE sr.winner_candidate_order
                WHEN 'FIRST' THEN '第一候选人'
                WHEN 'SECOND' THEN '第二候选人'
                WHEN 'THIRD' THEN '第三候选人'
                WHEN 'FOURTH' THEN '第四候选人'
                WHEN 'FIFTH' THEN '第五候选人'
                ELSE ''
            END as winnerCandidateOrderDesc,
            CASE
                WHEN sr.quote_amount IS NOT NULL AND sr.quote_amount > 0 THEN sr.quote_amount
                ELSE COALESCE(SUM(qi.quote_amount), 0)
            END as bidPrice,
            sr.total_score as totalScore
        FROM srm_tender_supplier_response sr
        LEFT JOIN (
            SELECT section_id, project_id, notice_id, tenant_supplier_id,min(quote_amount) quote_amount
            from srm_tender_bidder_quote_item
            where section_id = #{sectionId}
              AND notice_id = #{noticeId}
              AND project_id = #{projectId}
              AND round_no = #{quoteRound}
              AND del_flag = 0
            GROUP BY tenant_supplier_id, material_code
        ) qi ON sr.tenant_supplier_id = qi.tenant_supplier_id
            AND sr.section_id = qi.section_id
            AND sr.notice_id = qi.notice_id
            AND sr.project_id = qi.project_id
        WHERE sr.section_id = #{sectionId}
          AND sr.notice_id = #{noticeId}
          AND sr.project_id = #{projectId}
          AND sr.quote_status = 'COMPLETED'
          AND sr.del_flag = 0
        GROUP BY sr.tenant_supplier_id, sr.supplier_name, sr.is_recommended_winner, sr.winner_candidate_order, sr.quote_amount
        ORDER BY sr.supplier_name
    </select>

    <!-- 查询评审项节点列表 -->
    <select id="queryReviewNodes" resultType="java.lang.String">
        SELECT DISTINCT es.node_name
        FROM srm_tender_evaluation_standard es
        WHERE es.section_id = #{sectionId}
          AND es.notice_id = #{noticeId}
          AND es.project_id = #{projectId}
          AND es.type = 'REVIEW'
          AND es.del_flag = 0
        ORDER BY es.node_name
    </select>

    <!-- 查询专家对供应商的评审详情 -->
    <select id="queryExpertReviewDetails" resultType="com.ylz.saas.resp.SrmTenderSupplierReviewDetailQueryResp$ExpertReviewDetail">
        SELECT DISTINCT
            pm.user_id as userId,
            be.name as expertName,
            be.expert_code as expertCode,
            be.expert_category as expertCategory,
            CASE pm.role
                WHEN 'EVALUATION_LEADER' THEN '评标组长'
                WHEN 'EVALUATION_MEMBER' THEN '评标成员'
                ELSE pm.role
            END as role
        FROM srm_project_member pm
        LEFT JOIN base_expert be ON pm.user_id = be.user_id
        WHERE pm.business_id = #{evaluationId}
          AND pm.member_type = 'EVALUATION_MEMBER'
          AND pm.del_flag = 0
          AND be.del_flag = 0
        ORDER BY pm.role DESC, be.name
    </select>

    <!-- 查询专家对供应商各评审项的评审结果 -->
    <select id="queryExpertNodeReviewResults" resultType="com.ylz.saas.resp.SrmTenderSupplierReviewDetailQueryResp$NodeReviewResult">
        SELECT s.id                as scoringId,
               es.id               as standardId,
               es.node_name        as nodeName,
               es.item_name        as itemName,
               es.item_description as itemDescription,
               s.is_conform        as isConform,
               s.current_round as currentRound,
               CASE s.is_conform
                   WHEN 1 THEN '符合'
                   WHEN 0 THEN '不符合'
                   ELSE '未评审'
                   END             as isConformDesc,
               s.conclusion        as conclusion,
               s.update_time       as reviewTime
        FROM srm_tender_evaluation_standard es
                 LEFT JOIN srm_tender_bid_evaluation_scoring s ON es.id = s.scoring_detail_id
            AND s.user_id = #{userId}
            AND s.tenant_supplier_id = #{tenantSupplierId}
            AND (s.scoring_type = 'EXPERT_SCORING' OR s.scoring_type IS NULL)
            AND s.del_flag = 0
            and s.current_round = #{currentRound}
        WHERE es.section_id = #{sectionId}
          AND es.notice_id = #{noticeId}
          AND es.project_id = #{projectId}
          AND es.type = 'REVIEW'
          AND es.del_flag = 0
        ORDER BY es.node_name, es.create_time
    </select>

    <!-- 查询组长对供应商各评审项的汇总结果 -->
    <select id="queryLeaderSummaryResults" resultType="com.ylz.saas.resp.SrmTenderSupplierReviewDetailQueryResp$NodeReviewResult">
        SELECT s.id                as scoringId,
               es.id               as standardId,
               es.node_name        as nodeName,
               es.item_name        as itemName,
               es.item_description as itemDescription,
               s.is_conform        as isConform,
               s.current_round as currentRound,
               CASE s.is_conform
                   WHEN 1 THEN '符合'
                   WHEN 0 THEN '不符合'
                   ELSE '未评审'
                   END             as isConformDesc,
               s.conclusion        as conclusion,
               s.update_time       as reviewTime
        FROM srm_tender_evaluation_standard es
                 LEFT JOIN srm_tender_bid_evaluation_scoring s ON es.id = s.scoring_detail_id
            AND s.tenant_supplier_id = #{tenantSupplierId}
            AND s.scoring_type = 'LEADER_SUMMARY'
            AND s.del_flag = 0
           and s.current_round = #{currentRound}
        WHERE
           es.section_id = #{sectionId}
          AND es.notice_id = #{noticeId}
          AND es.project_id = #{projectId}
          AND es.type = 'REVIEW'
          AND es.del_flag = 0
        ORDER BY es.node_name, es.sort_order, es.create_time
    </select>

</mapper>
