package com.ylz.saas.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/6/25 19:18
 * @Description
 */
@Data
public class WithdrawnTenderRegisterReq {

    /**
     * 公告ID
     */
    @NotNull(message = "公告ID不能为空")
    private Long noticeId;

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    private Long sectionId;

    /**
     * 1:报名 2:保证金
     */
    @NotNull(message = "类型不能为空")
    private Integer type;

}
