package com.ylz.saas.service;

import com.ylz.saas.entity.SrmTenantSupplierDept;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【srm_tenant_supplier_dept(租户供应商部门关联表)】的数据库操作Service
 * @createDate 2025-06-20
 */
public interface SrmTenantSupplierDeptService extends IService<SrmTenantSupplierDept> {

    /**
     * 创建供应商与部门的关联关系
     *
     * @param tenantSupplierId 租户供应商ID
     * @param deptId 部门ID
     * @param relationType 关联类型
     * @return 是否创建成功
     */
    boolean createSupplierDeptRelation(Long tenantSupplierId, Long deptId, String relationType);

    /**
     * 创建供应商与部门的关联关系（带审核状态）
     *
     * @param tenantSupplierId 租户供应商ID
     * @param deptId 部门ID
     * @param relationType 关联类型
     * @param approvalStatus 审核状态
     * @return 是否创建成功
     */
    boolean createSupplierDeptRelation(Long tenantSupplierId, Long deptId, String relationType, String approvalStatus);

    /**
     * 根据供应商ID查询关联的部门列表
     * 
     * @param tenantSupplierId 租户供应商ID
     * @return 部门关联列表
     */
    List<SrmTenantSupplierDept> getDeptsByTenantSupplierId(Long tenantSupplierId);

    /**
     * 根据部门ID查询关联的供应商ID列表
     * 
     * @param deptId 部门ID
     * @return 供应商ID列表
     */
    List<Long> getTenantSupplierIdsByDeptId(Long deptId);

    /**
     * 删除供应商部门关联
     * 
     * @param id 关联ID
     * @return 是否删除成功
     */
    boolean deleteSupplierDeptRelation(Long id);

    /**
     * 根据供应商ID删除所有关联
     *
     * @param tenantSupplierId 租户供应商ID
     * @return 是否删除成功
     */
    boolean deleteAllRelationsByTenantSupplierId(Long tenantSupplierId);

    /**
     * 批量创建供应商与部门的关联关系
     *
     * @param tenantSupplierId 租户供应商ID
     * @param deptIds 部门ID集合
     * @param relationType 关联类型
     * @return 是否创建成功
     */
    boolean createSupplierDeptRelations(Long tenantSupplierId, List<Long> deptIds, String relationType);

    /**
     * 批量创建供应商与部门的关联关系（带审核状态）
     *
     * @param tenantSupplierId 租户供应商ID
     * @param deptIds 部门ID集合
     * @param relationType 关联类型
     * @param approvalStatus 审核状态
     * @return 是否创建成功
     */
    boolean createSupplierDeptRelations(Long tenantSupplierId, List<Long> deptIds, String relationType, String approvalStatus);
}
