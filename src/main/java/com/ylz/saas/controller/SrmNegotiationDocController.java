package com.ylz.saas.controller;

import com.ylz.saas.common.core.util.R;
import com.ylz.saas.req.SrmNegotiationAddReq;
import com.ylz.saas.service.SrmNegotiationBidDocService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 竞谈文件控制层
 *
 * <AUTHOR>
 * @since 2025-06-10 14:11:33
 */
@RestController
@RequestMapping("/negotiationDoc")
public class SrmNegotiationDocController {

    @Resource
    private SrmNegotiationBidDocService negotiationBidDocService;


    /**
     * 竞谈文件增加、修改
     *
     * @param req 筛选条件
     * @return 查询结果
     */
    @PostMapping("/upsert")
    public R<Void> addNegotiationDoc(@RequestBody @Validated SrmNegotiationAddReq req) {
        negotiationBidDocService.addNegotiationDoc(req);
        return R.ok();
    }

    /**
     * 竞谈文件详情
     *
     * @param noticeId 筛选条件
     */
    @PostMapping("/detail/{noticeId}")
    public R<SrmNegotiationAddReq> negotiationDocDetail(@PathVariable Long noticeId) {
        SrmNegotiationAddReq result = negotiationBidDocService.negotiationDocDetail(noticeId);
        return R.ok(result);
    }

//    /**
//     * 澄清公告新增
//     *
//     * @return 查询结果
//     */
//    @PostMapping("/clarifyNotice/upsert")
//    public R<Void> clarifyNoticeUpsert(@RequestBody @Validated ClarifyNoticeAddReq addReq) {
//        clarifyNoticeService.clarifyNoticeUpsert(addReq);
//        return R.ok();
//    }
//
//    /**
//     * 澄清公告查询接口
//     *
//     * @return 查询结果
//     */
//    @PostMapping("/clarifyNotice/list")
//    public R<List<SrmClarifyNoticeResp>> queryClarifyNotice(@RequestBody @Validated ClarifyNoticeReq clarifyNoticeReq) {
//        List<SrmClarifyNoticeResp> clarifyNotice = clarifyNoticeService.queryClarifyNotice(clarifyNoticeReq);
//        return R.ok(clarifyNotice);
//    }
//
//    /**
//     * 澄清公告，查询详情信息
//     */
//    @PostMapping("/clarifyNotice/detail/{id}")
//    public R<SrmClarifyNoticeResp> queryClarifyDetail(@PathVariable Long id) {
//        SrmClarifyNoticeResp detail = clarifyNoticeService.queryClarifyDetail(id);
//        return R.ok(detail);
//    }
//
//
//    /**
//     * 澄清公告供应商下载信息
//     */
//    @GetMapping("/clarifyNotice/supplier/downloadInfo")
//    public R<List<ClarifyAttachmentDownloadInfoResp>> querySupplierDownloadInfo(@RequestParam("clarifyNoticeId") Long clarifyNoticeId, @RequestParam(value = "supplierName",required = false) String supplierName) {
//        List<ClarifyAttachmentDownloadInfoResp> detail = downloadInfoService.querySupplierDownloadInfo(clarifyNoticeId, supplierName);
//        return R.ok(detail);
//    }
//
//    /**
//     * 供应商下载记录增减
//     */
//    @PostMapping("/downloadLog/add")
//    public R<Void> supplierDownloadInfoAdd(@RequestBody @Validated DownloadLogAddReq addReq) {
//        downloadInfoService.supplierDownloadInfoAdd(addReq);
//        return R.ok();
//    }

}

