package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.beust.jcommander.internal.Sets;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ylz.saas.admin.api.dto.BatchCreateUserIdentityDataPermissionDTO;
import com.ylz.saas.admin.api.dto.BatchDeleteUserIdentityDataPermissionDTO;
import com.ylz.saas.admin.api.dto.DataScopeDataDTO;
import com.ylz.saas.admin.api.entity.SysDept;
import com.ylz.saas.admin.api.entity.SysIdentity;
import com.ylz.saas.admin.api.entity.SysUser;
import com.ylz.saas.admin.api.entity.SysUserIdentityRoleRelation;
import com.ylz.saas.admin.mapper.SysIdentityMapper;
import com.ylz.saas.admin.mapper.SysUserIdentityRoleRelationMapper;
import com.ylz.saas.admin.service.SysDeptService;
import com.ylz.saas.admin.service.SysUserIdentityDataPermissionRelationService;
import com.ylz.saas.admin.service.SysUserService;
import com.ylz.saas.codegen.base_agent_user.entity.BaseAgentUserEntity;
import com.ylz.saas.codegen.base_agent_user.mapper.BaseAgentUserMapper;
import com.ylz.saas.codegen.base_payment_method.entity.BasePaymentMethodEntity;
import com.ylz.saas.codegen.base_payment_method.service.BasePaymentMethodService;
import com.ylz.saas.codegen.base_payment_method_period.entity.BasePaymentMethodPeriodEntity;
import com.ylz.saas.codegen.base_payment_method_period.service.BasePaymentMethodPeriodService;
import com.ylz.saas.codegen.base_quality_indicator.entity.BaseQualityIndicatorEntity;
import com.ylz.saas.codegen.base_quality_indicator.service.BaseQualityIndicatorService;
import com.ylz.saas.codegen.base_service_type.entity.BaseServiceTypeEntity;
import com.ylz.saas.codegen.base_service_type.service.BaseServiceTypeService;
import com.ylz.saas.codegen.base_service_type_field.entity.BaseServiceTypeFieldEntity;
import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.codegen.base_usage_location.entity.BaseUsageLocationEntity;
import com.ylz.saas.codegen.base_usage_location.service.BaseUsageLocationService;
import com.ylz.saas.codegen.srm_procurement_decision.entity.SrmProcurementDecisionEntity;
import com.ylz.saas.codegen.srm_procurement_plan.entity.SrmProcurementPlanEntity;
import com.ylz.saas.codegen.srm_procurement_plan.mapper.SrmProcurementPlanMapper;
import com.ylz.saas.codegen.srm_procurement_plan.service.SrmProcurementPlanService;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.core.exception.GlobalResultCode;
import com.ylz.saas.common.data.datascope.annotation.PreloadDataPermission;
import com.ylz.saas.common.enums.CodeGeneratorPrefixEnum;
import com.ylz.saas.common.sequence.generator.impl.DefaultCodeGenerator;
import com.ylz.saas.entity.*;
import com.ylz.saas.enums.*;
import com.ylz.saas.mapper.SrmProcurementProjectMapper;
import com.ylz.saas.mapper.SrmTenderFailedLogMapper;
import com.ylz.saas.mapper.SrmTenderNoticeChangeMapper;
import com.ylz.saas.req.ProcessInstanceStartReq;
import com.ylz.saas.req.RegisteredSupplierQueryReq;
import com.ylz.saas.req.SrmProcurementProjectCreateReq;
import com.ylz.saas.req.SrmProcurementProjectPageQueryReq;
import com.ylz.saas.resp.ProcessInstanceGroupResp;
import com.ylz.saas.resp.ProcessInstanceStartResp;
import com.ylz.saas.resp.ProjectArchiveResp;
import com.ylz.saas.service.*;
import com.ylz.saas.vo.DynamicFieldVo;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ylz.saas.enums.DataPermissionTypeEnum.PROJECT;
import static com.ylz.saas.enums.DefaultRoleEnum.IdentifyCode.*;
import static java.util.Arrays.stream;

/**
 * <AUTHOR>
 * @description 针对表【srm_procurement_project(采购立项主表)】的数据库操作Service实现
 * @createDate 2025-06-11 11:21:09
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SrmProcurementProjectServiceImpl extends ServiceImpl<SrmProcurementProjectMapper, SrmProcurementProject>
        implements SrmProcurementProjectService, ApproveRejectHookService {

    private final SrmProcurementProjectSectionService srmProcurementProjectSectionService;
    private final SrmProcurementProjectItemService srmProcurementProjectItemService;
    private final SrmProcurementProjectPaymentService srmProcurementProjectPaymentService;
    private final SrmProjectMemberService srmProjectMemberService;
    private final SrmProcurementProjectFieldValueService srmProcurementProjectFieldValueService;
    private final SrmProjectAttachmentService srmProjectAttachmentService;
    private final SrmProcurementPlanService srmProcurementPlanService;
    private final SrmProcessInstanceService processInstanceService;
    private final BaseServiceTypeService baseServiceTypeService;
    private final BaseServiceTypeFieldService baseServiceTypeFieldService;
    private final BasePaymentMethodService basePaymentMethodService;
    private final BasePaymentMethodPeriodService basePaymentMethodPeriodService;
    private final BaseUsageLocationService baseUsageLocationService;
    private final BaseQualityIndicatorService baseQualityIndicatorService;
    private final SysDeptService sysDeptService;
    private final SysUserService sysUserService;
    private final DefaultCodeGenerator defaultCodeGenerator;
    private final SrmProcurementPlanMapper srmProcurementPlanMapper;
    @Lazy
    @Autowired
    private SrmTenderNoticeService srmTenderNoticeService;
    @Autowired
    private SrmTenderRequirementService srmTenderRequirementService;
    @Lazy
    @Autowired
    private SrmTenderOpenService srmTenderOpenService;
    @Resource
    private SysUserIdentityDataPermissionRelationService dataPermissionRelationService;


    private final SrmTenderFailedLogMapper srmTenderFailedLogMapper;


    @Lazy
    @Autowired
    private SrmTenderEvaluationResultService srmTenderEvaluationResultService;
    @Autowired
    private SrmTenderNoticeChangeMapper srmTenderNoticeChangeMapper;
    @Resource
    private BaseAgentUserMapper baseAgentUserMapper;
    @Resource
    private SysIdentityMapper sysIdentityMapper;
    @Resource
    private SysUserIdentityRoleRelationMapper userIdentityRoleRelationMapper;

    @Lazy
    @Autowired
    private SrmTenderSupplierResponseService srmTenderSupplierResponseService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessInstanceStartResp upsertAndStartFlow(SrmProcurementProjectCreateReq req) {
        SrmProcurementProject project = this.upsertSrmProcurementProject(req, DocumentStatus.NEW, ApproveStatusEnum.TO_APPROVE,ProjectProgressStatusEnum.INIT);
        if (project == null) {
            return null;
        }

        // 8. 启动流程实例
        ProcessInstanceStartReq instanceStartReq = new ProcessInstanceStartReq();
        instanceStartReq.setBizType(SrmProcessConfigService.BizTypeEnum.SRM_PROCUREMENT_PROJECT);
        String projectCode = project.getProjectCode();
        instanceStartReq.setBizKey(projectCode);
        instanceStartReq.setBizId(project.getId());
        instanceStartReq.setApprovalType(req.getApprovalType());
        instanceStartReq.setSpecialProcessExecutorList(req.getSpecialProcessExecutorList());
        instanceStartReq.setArgs(List.of(projectCode, projectCode));
        ProcessInstanceStartResp processInstanceStartResp = processInstanceService.startProcessInstance(instanceStartReq);
        processInstanceStartResp.setProjectCode(projectCode);
        return processInstanceStartResp;
    }

    /**
     * 启动项目流程
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SrmProcurementProject upsertSrmProcurementProject(SrmProcurementProjectCreateReq req
            , DocumentStatus documentStatus, ApproveStatusEnum approveStatus, ProjectProgressStatusEnum progressStatusEnum) {
        ExceptionUtil.check(Objects.isNull(req.getPreQualification())
                        && (req.getSourcingType() != BaseServiceTypeFieldService.BuyWayEnum.NULL
                        && req.getSourcingType() != BaseServiceTypeFieldService.BuyWayEnum.KSXJ
                        && req.getSourcingType() != BaseServiceTypeFieldService.BuyWayEnum.ZJWT
                        && req.getInviteMethod() == InviteMethodEnum.PUBLICITY),
                GlobalResultCode.INVALID_PARAMS, "是否资格预审不能为空");
        // 资格预审相关校验
        if (Objects.equals(YesNoEnum.YES.getCode(), req.getPreQualification())) {
            ExceptionUtil.check(req.getRegisterStartTime() == null || req.getRegisterEndTime() == null, GlobalResultCode.INVALID_PARAMS, "资格预审开始截止时间不能为空");
            ExceptionUtil.check(req.getPreReviewStartTime() == null || req.getPreReviewEndTime() == null, GlobalResultCode.INVALID_PARAMS, "资格预审开始截止时间不能为空");
        }
        if (req.getSourcingType() != BaseServiceTypeFieldService.BuyWayEnum.NULL && req.getSourcingType() != BaseServiceTypeFieldService.BuyWayEnum.JJCG) {
            ExceptionUtil.checkNonNull(req.getBidOpenTime(), "开标时间不能为空");
        }
        ExceptionUtil.checkNotEmpty(req.getSections(), "标段信息不能为空");
        if (req.getSections().size() == 1) {
            SrmProcurementProjectCreateReq.Section section = req.getSections().get(0);
            section.setSectionName(req.getProjectName());
            section.setSectionCode(defaultCodeGenerator.generateWithDate(CodeGeneratorPrefixEnum.BD.name()));
        } else {
            req.getSections().forEach(section -> ExceptionUtil.check(
                    StringUtils.isAnyBlank(section.getSectionName(), section.getSectionCode()), GlobalResultCode.INVALID_PARAMS, "标段名称和标段编号不能为空"));
        }
        req.getSections().forEach(item -> ExceptionUtil.checkNotEmpty(item.getProjectItems(), item.getSectionName() + "标段未绑定物料"));
        ExceptionUtil.checkNotEmpty(req.getProjectMembers(), "项目组成员不能为空");
        ExceptionUtil.checkNotEmpty(req.getPaymentInfoList(), "支付信息不能为空");
        // 采购计划校验
        List<Long> planIds = req.getSections().stream().flatMap(item -> item.getProjectItems().stream())
                .map(SrmProcurementProjectCreateReq.ProjectItem::getPlanId).filter(Objects::nonNull).distinct().toList();
        if (CollectionUtils.isNotEmpty(planIds)) {
            List<SrmProcurementPlanEntity> plans = srmProcurementPlanService.listByIds(planIds);
            ExceptionUtil.check(plans.size() != planIds.size(), GlobalResultCode.INVALID_PARAMS, "关联采购计划不存在");
            long serviceTypeCount = plans.stream().map(SrmProcurementPlanEntity::getServiceTypeId).distinct().count();
            ExceptionUtil.check(serviceTypeCount > 1L, GlobalResultCode.INVALID_PARAMS, "关联采购计划服务类型不一致");
            long planRecruitMethodCount = plans.stream().map(SrmProcurementPlanEntity::getPlanRecruitMethod).distinct().count();
            ExceptionUtil.check(planRecruitMethodCount > 1L, GlobalResultCode.INVALID_PARAMS, "关联采购计划招标方式不一致");
        }

        final boolean isUpdate = Objects.nonNull(req.getId());

        // 1. 保存主表
        SrmProcurementProject project;
        if (isUpdate) {
            project = this.getById(req.getId());
            ExceptionUtil.checkNonNull(project, "项目不存在");
            ExceptionUtil.check(project.getStatus() == DocumentStatus.CANCEL, GlobalResultCode.INVALID_STATE, "项目已作废");
            // 项目生效或审批中：基础信息可改：需求部门、采购意图、项目概况；支付信息：账期备注；项目小组成员可修改
            if (project.getStatus() == DocumentStatus.EFFECT || project.getApproveStatus() == ApproveStatusEnum.APPROVE || project.getApproveStatus() == ApproveStatusEnum.APPROVING) {
                project.setPurchaseDeptId(project.getPurchaseDeptName());
                project.setPurchaseReason(project.getPurchaseReason());
                project.setProjectDesc(project.getProjectDesc());
                this.updateById(project);
                Long projectId = project.getId();
                // 查询数据库中当前项目的成员
                Set<Long> existingUserIds = srmProjectMemberService.lambdaQuery()
                        .eq(SrmProjectMember::getBusinessId, projectId)
                        .eq(SrmProjectMember::getMemberType, ProjectMemberTypeEnum.PROJECT_MEMBER)
                        .list().stream()
                        .map(SrmProjectMember::getUserId)
                        .collect(Collectors.toSet());
                this.upsertProjectMembers(req.getProjectMembers(), project.getId());
                // 插入用户的项目数据权限
                Set<Long> newUserIds = req.getProjectMembers().stream().map(SrmProcurementProjectCreateReq.ProjectMember::getUserId).collect(Collectors.toSet());
                insertProjectUserIdentityDataPermission(project, newUserIds, existingUserIds);
                List<SrmProcurementProjectPayment> paymentList = srmProcurementProjectPaymentService.lambdaQuery().eq(SrmProcurementProjectPayment::getProjectId, project.getId()).list();
                if (CollectionUtils.isNotEmpty(paymentList) && CollectionUtils.isNotEmpty(req.getPaymentInfoList())) {
                    paymentList.forEach(payment -> {
                        Optional<SrmProcurementProjectCreateReq.PaymentInfo> paymentInfoReqOpt = req.getPaymentInfoList().stream().filter(info ->
                                Objects.equals(payment.getPaymentMethodId(), info.getPaymentMethodId())
                                        && Objects.equals(payment.getPaymentPeriodId(), info.getPaymentPeriodId())).findFirst();
                        paymentInfoReqOpt.ifPresent(info -> payment.setPaymentRemark(info.getPaymentRemark()));
                    });
                    srmProcurementProjectPaymentService.updateBatchById(paymentList);
                }
                return project;
            }


        } else {
            project = new SrmProcurementProject();
            project.setProjectCode(defaultCodeGenerator.generateWithDate(CodeGeneratorPrefixEnum.CGXM.name()));
        }
        BeanUtils.copyProperties(req, project);
        project.setStatus(documentStatus);
        project.setApproveStatus(approveStatus);
        project.setProgressStatus(progressStatusEnum);
        project.setMultiSection(req.getSections().size() > 1 ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode());
        project.setDelFlag(YesNoEnum.NO.getCode());
        this.saveOrUpdate(project);
        Long projectId = project.getId();

        // 2. 保存标段
        Set<String> selectionCodes = req.getSections().stream().map(SrmProcurementProjectCreateReq.Section::getSectionCode).collect(Collectors.toSet());
        Set<String> selectionNames = req.getSections().stream().map(SrmProcurementProjectCreateReq.Section::getSectionName).collect(Collectors.toSet());
        ExceptionUtil.check(selectionCodes.size() != req.getSections().size(), GlobalResultCode.INVALID_PARAMS, "标段编号重复");
        ExceptionUtil.check(selectionNames.size() != req.getSections().size(), GlobalResultCode.INVALID_PARAMS, "标段名称重复");
        List<SrmProcurementProjectSection> existsSelections = srmProcurementProjectSectionService.lambdaQuery()
                .ne(SrmProcurementProjectSection::getProjectId, projectId)
                .in(SrmProcurementProjectSection::getSectionCode, selectionCodes).list();
        String existsSelectionCodes = Optional.ofNullable(existsSelections).stream().flatMap(Collection::stream).map(SrmProcurementProjectSection::getSectionCode).collect(Collectors.joining(","));
        ExceptionUtil.check(CollectionUtils.isNotEmpty(existsSelections), GlobalResultCode.INVALID_PARAMS, String.format("标段【%s】编号已存在", existsSelectionCodes));
        List<SrmProcurementProjectSection> sections = req.getSections().stream().map(s -> {
            SrmProcurementProjectSection section = new SrmProcurementProjectSection();
            section.setProjectId(projectId);
            section.setSectionCode(s.getSectionCode());
            section.setSectionName(s.getSectionName());
            section.setDelFlag(YesNoEnum.NO.getCode());
            return section;
        }).collect(Collectors.toList());
        srmProcurementProjectSectionService.lambdaUpdate().eq(SrmProcurementProjectSection::getProjectId, projectId).remove();
        srmProcurementProjectSectionService.saveBatch(sections);
        Map<String, Long> selectionCodeIdMap = sections.stream().collect(Collectors.toMap(SrmProcurementProjectSection::getSectionCode, SrmProcurementProjectSection::getId));

        // 3. 保存物料明细
        List<SrmProcurementProjectItem> projectItems = Lists.newArrayList();
        List<BaseServiceTypeFieldEntity> baseServiceTypeFields = baseServiceTypeFieldService.lambdaQuery()
                .eq(BaseServiceTypeFieldEntity::getServiceTypeId, project.getServiceTypeId())
                .eq(BaseServiceTypeFieldEntity::getBuyWay, project.getSourcingType()).list();
        Map<String, BaseServiceTypeFieldEntity> baseServiceTypeFieldMap = Optional.ofNullable(baseServiceTypeFields).stream().flatMap(Collection::stream)
                .collect(Collectors.toMap(BaseServiceTypeFieldEntity::getFieldCode, Function.identity(), (v1, v2) -> v1));
        req.getSections().forEach(s -> {
            List<SrmProcurementProjectItem> subProjectItems = s.getProjectItems().stream().map(item -> {
                SrmProcurementProjectItem projectItem = new SrmProcurementProjectItem();
                projectItem.setProjectId(projectId);
                projectItem.setSectionId(selectionCodeIdMap.get(s.getSectionCode()));
                projectItem.setServiceTypeId(project.getServiceTypeId());
                projectItem.setSourcingType(project.getSourcingType());
                projectItem.setPlanId(item.getPlanId());
                projectItem.setRequireNo(item.getRequireNo());
                projectItem.setDelFlag(YesNoEnum.NO.getCode());
                List<SrmProcurementProjectFieldValue> projectFieldValues = Lists.newArrayList();
                for (DynamicFieldVo dynamicFieldVo : item.getDynamicFieldList()) {
                    BaseServiceTypeFieldEntity baseServiceTypeFieldEntity = baseServiceTypeFieldMap.get(dynamicFieldVo.getCode());
                    ExceptionUtil.checkNonNull(baseServiceTypeFieldEntity, "业务类型字段不存在");
                    FixedFieldEnum fixedFieldEnum = FixedFieldEnum.getByCode(baseServiceTypeFieldEntity.getFieldCode());
                    if (fixedFieldEnum != null) {
                        if (FixedFieldEnum.isProjectRequireField(fixedFieldEnum)
                                && Objects.equals(com.ylz.saas.common.core.constant.enums.YesNoEnum.YES.getCode() ,baseServiceTypeFieldEntity.getProjectIsRequired())) {
                            ExceptionUtil.check(StringUtils.isBlank(dynamicFieldVo.getValue()), GlobalResultCode.INVALID_PARAMS, fixedFieldEnum.getDesc() + "不能为空");
                        }
                        if (StringUtils.isNotBlank(dynamicFieldVo.getValue())) {
                            switch (fixedFieldEnum) {
                                case REQUIRE_NO:
                                    projectItem.setRequireNo(dynamicFieldVo.getValue());
                                    break;
                                case MATERIAL_CODE:
                                    projectItem.setMaterialCode(dynamicFieldVo.getValue());
                                    break;
                                case MATERIAL_NAME:
                                    projectItem.setMaterialName(dynamicFieldVo.getValue());
                                    break;
                                case SPEC_MODEL:
                                    projectItem.setSpecModel(dynamicFieldVo.getValue());
                                    break;
                                case UNIT:
                                    projectItem.setUnit(dynamicFieldVo.getValue());
                                    break;
                                case REQUIRED_QUANTITY:
                                    projectItem.setRequiredQuantity(new BigDecimal(dynamicFieldVo.getValue()));
                                    break;
                                case USAGE_LOCATION_ID:
                                    projectItem.setUsageLocationId(Long.valueOf(dynamicFieldVo.getValue()));
                                    break;
                                case QUALITY_INDICATOR_ID:
                                    projectItem.setQualityIndicatorId(Long.valueOf(dynamicFieldVo.getValue()));
                                    break;
                            }
                        }
                    } else {
                        ExceptionUtil.check(Objects.equals(com.ylz.saas.common.core.constant.enums.YesNoEnum.YES.getCode(), baseServiceTypeFieldEntity.getPlanIsRequired())
                                        && StringUtils.isBlank(String.valueOf(dynamicFieldVo.getValue())), GlobalResultCode.INVALID_PARAMS,
                                baseServiceTypeFieldEntity.getFieldName() + "不能为空");
                        SrmProcurementProjectFieldValue fieldValue = new SrmProcurementProjectFieldValue();
                        fieldValue.setProjectId(project.getId());
                        fieldValue.setServiceTypeId(project.getServiceTypeId());
                        fieldValue.setFieldCode(baseServiceTypeFieldEntity.getFieldCode());
                        fieldValue.setFieldType(baseServiceTypeFieldEntity.getFieldType());
                        fieldValue.setFieldName(baseServiceTypeFieldEntity.getFieldName());
                        if (baseServiceTypeFieldEntity.getFieldType() == BaseServiceTypeFieldService.FieldTypeEnum.LINK_FORM) {
                            fieldValue.setEnumValues(null);
                        } else {
                            fieldValue.setEnumValues(baseServiceTypeFieldEntity.getEnumValues());
                        }
                        fieldValue.setFieldValue(dynamicFieldVo.getValue());
                        fieldValue.setDelFlag(YesNoEnum.NO.getCode());
                        projectFieldValues.add(fieldValue);
                    }
                }
                projectItem.setProjectFieldValueList(projectFieldValues);
                return projectItem;
            }).toList();
            projectItems.addAll(subProjectItems);
        });
        srmProcurementProjectItemService.lambdaUpdate().eq(SrmProcurementProjectItem::getProjectId, projectId).remove();
        srmProcurementProjectItemService.saveBatch(projectItems);

        // 4. 保存动态字段
        List<SrmProcurementProjectFieldValue> allProjectFieldValues = Lists.newArrayList();
        for (SrmProcurementProjectItem projectItem : projectItems) {
            if (CollectionUtils.isNotEmpty(projectItem.getProjectFieldValueList())) {
                projectItem.getProjectFieldValueList().forEach(item -> item.setProjectItemId(projectItem.getId()));
                allProjectFieldValues.addAll(projectItem.getProjectFieldValueList());
            }
        }
        srmProcurementProjectFieldValueService.lambdaUpdate().eq(SrmProcurementProjectFieldValue::getProjectId, projectId).remove();
        if (CollectionUtils.isNotEmpty(allProjectFieldValues)) {
            srmProcurementProjectFieldValueService.saveBatch(allProjectFieldValues);
        }

        // 5. 保存支付方式
        List<SrmProcurementProjectPayment> payments = req.getPaymentInfoList().stream().map(p -> {
            SrmProcurementProjectPayment payment = new SrmProcurementProjectPayment();
            BeanUtils.copyProperties(p, payment);
            payment.setProjectId(projectId);
            payment.setDelFlag(YesNoEnum.NO.getCode());
            return payment;
        }).collect(Collectors.toList());
        srmProcurementProjectPaymentService.lambdaUpdate().eq(SrmProcurementProjectPayment::getProjectId, projectId).remove();
        srmProcurementProjectPaymentService.saveBatch(payments);

        // 6. 保存项目成员
        this.upsertProjectMembers(req.getProjectMembers(), projectId);
        // 插入用户的项目数据权限
        Set<Long> newUserIds = req.getProjectMembers().stream().map(SrmProcurementProjectCreateReq.ProjectMember::getUserId).collect(Collectors.toSet());
        insertProjectUserIdentityDataPermission(project, newUserIds, new HashSet<>());

        // 7. 保存附件
        srmProjectAttachmentService.lambdaUpdate().eq(SrmProjectAttachment::getBusinessId, projectId)
                .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.PROJECT).remove();
        if (CollectionUtils.isNotEmpty(req.getAttachmentList())) {
            List<SrmProjectAttachment> attachmentList = req.getAttachmentList().stream().map(item -> {
                SrmProjectAttachment attachment = new SrmProjectAttachment();
                attachment.setProjectId(projectId);
                attachment.setBusinessId(projectId);
                attachment.setBusinessType(AttachmentTypeEnum.PROJECT);
                attachment.setFileName(item.getFileName());
                attachment.setFilePath(item.getFilePath());
                attachment.setDelFlag(YesNoEnum.NO.getCode());
                return attachment;
            }).toList();
            srmProjectAttachmentService.saveBatch(attachmentList);
        }
        return project;
    }

    /**
     * 插入用户的项目数据权限
     *
     * @param project 项目
     * @param newUserIds 传入的项目成员列表
     * @param existingUserIds 已经存在数据库的用户ID列表
     */
    @Override
    public void insertProjectUserIdentityDataPermission(SrmProcurementProject project,
                                                        Set<Long> newUserIds,
                                                        Set<Long> existingUserIds) {// 获取新成员的用户ID列表
        // 计算新增和删除的用户
        Set<Long> usersToAdd = new HashSet<>(newUserIds);
        // 新增的用户
        usersToAdd.removeAll(existingUserIds);
        Set<Long> usersToRemove = new HashSet<>(existingUserIds);
        // 需要删除的用户
        usersToRemove.removeAll(newUserIds);
        List<Long> projectUserIdList = new ArrayList<>(newUserIds);
        existingUserIds.addAll(projectUserIdList);
        Map<Long, List<SysUserIdentityRoleRelation>> userIdAndInfoListMap = userIdentityRoleRelationMapper.selectList(Wrappers.<SysUserIdentityRoleRelation>lambdaQuery()
                        .select(SysUserIdentityRoleRelation::getUserId, SysUserIdentityRoleRelation::getIdentityId)
                        .in(SysUserIdentityRoleRelation::getUserId, existingUserIds))
                .stream().collect(Collectors.groupingBy(SysUserIdentityRoleRelation::getUserId));
        // 查询默认的三种身份（排除供应方）
        Map<String, Long> identityCodeAndIdMap = sysIdentityMapper.selectList(Wrappers.<SysIdentity>lambdaQuery()
                        .select(SysIdentity::getId, SysIdentity::getIdentityCode)
                        .in(SysIdentity::getIdentityCode, List.of(BID_AGENCY_IDENTITY, PURCHASER_IDENTITY, BID_EXPERT_IDENTITY)))
                .stream().collect(Collectors.toMap(SysIdentity::getIdentityCode, SysIdentity::getId));
        // 处理删除的用户权限
        deleteUserIdentityDataPermission(project, usersToRemove, userIdAndInfoListMap, identityCodeAndIdMap);
        // 处理新增的用户权限
        insertUserIdentityDataPermission(project, identityCodeAndIdMap, usersToAdd, userIdAndInfoListMap);
    }

    /**
     * 处理新增的用户权限
     *
     * @param project  项目
     * @param identityCodeAndIdMap 身份Code和ID映射
     * @param usersToAdd 新增的用户
     * @param userIdAndInfoListMap 用户和身份关系
     */
    private void insertUserIdentityDataPermission(SrmProcurementProject project,
                                                  Map<String, Long> identityCodeAndIdMap,
                                                  Set<Long> usersToAdd,
                                                  Map<Long, List<SysUserIdentityRoleRelation>> userIdAndInfoListMap) {
        List<Long> needInsertAgentOrgUserIdentityDataPermissionUserIdList = new ArrayList<>();
        if (Objects.nonNull(project.getAgencyOrgId())) {
            // 如果设置了代理机构，就需要给代理机构的所有用户的代理机构身份增加这个项目的权限
            needInsertAgentOrgUserIdentityDataPermissionUserIdList =
                    baseAgentUserMapper.selectList(Wrappers.<BaseAgentUserEntity>lambdaQuery()
                                    .select(BaseAgentUserEntity::getUserId)
                                    .eq(BaseAgentUserEntity::getAgentOrgId, project.getAgencyOrgId()))
                            .stream().map(BaseAgentUserEntity::getUserId).collect(Collectors.toList());
        }
        // 组装项目的数据权限批量创建DTO
        BatchCreateUserIdentityDataPermissionDTO batchCreateUserIdentityDataPermissionDTO = assembleBatchCreateDataScopeDataDTO(project);
        List<BatchCreateUserIdentityDataPermissionDTO.UserIdentityCombination> userIdentityCombinations = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(needInsertAgentOrgUserIdentityDataPermissionUserIdList)) {
            assembleCreateUserIdentityDataPermissionDTO(userIdentityCombinations,
                    needInsertAgentOrgUserIdentityDataPermissionUserIdList, identityCodeAndIdMap, BID_AGENCY_IDENTITY);
        }
        // 处理新增的用户权限
        if (CollectionUtils.isNotEmpty(usersToAdd)) {
            Map<Long, List<Long>> identityIdAndUserIdListToAddMap = usersToAdd.stream()
                    .map(userIdAndInfoListMap::get)
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .collect(Collectors.groupingBy(SysUserIdentityRoleRelation::getIdentityId,
                            Collectors.mapping(SysUserIdentityRoleRelation::getUserId, Collectors.toList())));
            for (Map.Entry<String, Long> entry : identityCodeAndIdMap.entrySet()) {
                DefaultRoleEnum.IdentifyCode identifyCode = DefaultRoleEnum.IdentifyCode.getIdentifyCode(entry.getKey());
                if (identifyCode == null) {
                    continue;
                }
                if (!identityIdAndUserIdListToAddMap.containsKey(entry.getValue())) {
                    continue;
                }
                List<Long> userIdList = identityIdAndUserIdListToAddMap.get(entry.getValue());
                if (CollectionUtils.isEmpty(userIdList)) {
                    continue;
                }
                switch (identifyCode) {
                    case PURCHASER_IDENTITY -> // 采购方身份对应的数据权限DTO
                            assembleCreateUserIdentityDataPermissionDTO(userIdentityCombinations, userIdList, identityCodeAndIdMap, PURCHASER_IDENTITY);
                    case BID_EXPERT_IDENTITY -> // 评标专家身份对应数据权限DTO
                            assembleCreateUserIdentityDataPermissionDTO(userIdentityCombinations, userIdList, identityCodeAndIdMap, BID_EXPERT_IDENTITY);
                    default -> // 招标代理机构身份对应数据权限DTO
                            assembleCreateUserIdentityDataPermissionDTO(userIdentityCombinations, userIdList, identityCodeAndIdMap, BID_AGENCY_IDENTITY);
                }
            }
        }

        batchCreateUserIdentityDataPermissionDTO.setUserIdentityCombinations(userIdentityCombinations);
        dataPermissionRelationService.batchSaveUserIdentityDataPermission(batchCreateUserIdentityDataPermissionDTO);
    }

    /**
     * 处理删除的用户权限
     *
     * @param project  项目
     * @param usersToRemove 需要删除的用户
     * @param userIdAndInfoListMap 用户和身份关系
     * @param identityCodeAndIdMap 身份Code和ID映射
     */
    private void deleteUserIdentityDataPermission(SrmProcurementProject project,
                                                  Set<Long> usersToRemove,
                                                  Map<Long, List<SysUserIdentityRoleRelation>> userIdAndInfoListMap,
                                                  Map<String, Long> identityCodeAndIdMap) {
        if (usersToRemove.isEmpty()) {
            return;
        }
        // 组装项目的数据权限批量创建DTO
        BatchDeleteUserIdentityDataPermissionDTO batchDeleteUserIdentityDataPermissionDTO = assembleBatchDeleteDataScopeDataDTO(project);
        List<BatchDeleteUserIdentityDataPermissionDTO.UserIdentityCombination> userIdentityCombinations = new ArrayList<>();
        Map<Long, List<Long>> identityIdAndUserIdListToDeleteMap = usersToRemove.stream()
                .map(userIdAndInfoListMap::get)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(SysUserIdentityRoleRelation::getIdentityId,
                        Collectors.mapping(SysUserIdentityRoleRelation::getUserId, Collectors.toList())));

        if (identityIdAndUserIdListToDeleteMap.isEmpty()) {
            return;
        }
        for (Map.Entry<String, Long> entry : identityCodeAndIdMap.entrySet()) {
            DefaultRoleEnum.IdentifyCode identifyCode = DefaultRoleEnum.IdentifyCode.getIdentifyCode(entry.getKey());
            if (identifyCode == null) {
                continue;
            }
            if (!identityIdAndUserIdListToDeleteMap.containsKey(entry.getValue())) {
                continue;
            }
            List<Long> userIdList = identityIdAndUserIdListToDeleteMap.get(entry.getValue());
            if (CollectionUtils.isEmpty(userIdList)) {
                continue;
            }
            switch (identifyCode) {
                case PURCHASER_IDENTITY -> // 采购方身份对应的数据权限DTO
                        assembleDeleteUserIdentityDataPermissionDTO(userIdentityCombinations, userIdList, identityCodeAndIdMap, PURCHASER_IDENTITY);
                case BID_EXPERT_IDENTITY -> // 评标专家身份对应数据权限DTO
                        assembleDeleteUserIdentityDataPermissionDTO(userIdentityCombinations, userIdList, identityCodeAndIdMap, BID_EXPERT_IDENTITY);
                default -> // 招标代理机构身份对应数据权限DTO
                        assembleDeleteUserIdentityDataPermissionDTO(userIdentityCombinations, userIdList, identityCodeAndIdMap, BID_AGENCY_IDENTITY);
            }
        }
        batchDeleteUserIdentityDataPermissionDTO.setUserIdentityCombinations(userIdentityCombinations);
        dataPermissionRelationService.batchDeleteUserIdentityDataPermission(batchDeleteUserIdentityDataPermissionDTO);
    }

    /**
     * 组装项目的数据权限创建DTO
     *
     * @param project 项目
     * @return BatchCreateUserIdentityDataPermissionDTO 项目数据权限创建DTO
     */
    private static BatchCreateUserIdentityDataPermissionDTO assembleBatchCreateDataScopeDataDTO(SrmProcurementProject project) {
        BatchCreateUserIdentityDataPermissionDTO batchCreateUserIdentityDataPermissionDTO = new BatchCreateUserIdentityDataPermissionDTO();
        batchCreateUserIdentityDataPermissionDTO.setTypeCode(PROJECT.name());
        DataScopeDataDTO dataScopeDataDTO = new DataScopeDataDTO();
        dataScopeDataDTO.setId(project.getId());
        dataScopeDataDTO.setName(project.getProjectName());
        dataScopeDataDTO.setCode(project.getProjectCode());
        batchCreateUserIdentityDataPermissionDTO.setDataPermission(dataScopeDataDTO);
        return batchCreateUserIdentityDataPermissionDTO;
    }

    /**
     * 组装项目的数据权限删除DTO
     *
     * @param project 项目
     * @return BatchDeleteUserIdentityDataPermissionDTO 项目数据权限删除DTO
     */
    private static BatchDeleteUserIdentityDataPermissionDTO assembleBatchDeleteDataScopeDataDTO(SrmProcurementProject project) {
        BatchDeleteUserIdentityDataPermissionDTO batchDeleteUserIdentityDataPermissionDTO = new BatchDeleteUserIdentityDataPermissionDTO();
        batchDeleteUserIdentityDataPermissionDTO.setTypeCode(PROJECT.name());
        batchDeleteUserIdentityDataPermissionDTO.setDataPermissionId(project.getId());
        return batchDeleteUserIdentityDataPermissionDTO;
    }

    /**
     * 组装创建用户身份的数据权限DTO
     *
     * @param userIdentityCombinations 用户身份组合列表
     * @param userIdList 用户ID列表
     * @param identityCodeAndIdMap 身份编码和ID映射
     * @param purchaserIdentity 采购身份
     */
    private static void assembleCreateUserIdentityDataPermissionDTO(
            List<BatchCreateUserIdentityDataPermissionDTO.UserIdentityCombination> userIdentityCombinations,
            List<Long> userIdList, Map<String, Long> identityCodeAndIdMap,
            DefaultRoleEnum.IdentifyCode purchaserIdentity) {
        BatchCreateUserIdentityDataPermissionDTO.UserIdentityCombination userIdentityCombination =
                new BatchCreateUserIdentityDataPermissionDTO.UserIdentityCombination();
        userIdentityCombination.setUserIdList(userIdList);
        userIdentityCombination.setIdentityIdList(List.of(identityCodeAndIdMap.get(purchaserIdentity.name())));
        userIdentityCombinations.add(userIdentityCombination);
    }

    /**
     * 组装创建用户身份的数据权限DTO
     *
     * @param userIdentityCombinations 用户身份组合列表
     * @param userIdList 用户ID列表
     * @param identityCodeAndIdMap 身份编码和ID映射
     * @param purchaserIdentity 采购身份
     */
    private static void assembleDeleteUserIdentityDataPermissionDTO(
            List<BatchDeleteUserIdentityDataPermissionDTO.UserIdentityCombination> userIdentityCombinations,
            List<Long> userIdList, Map<String, Long> identityCodeAndIdMap,
            DefaultRoleEnum.IdentifyCode purchaserIdentity) {
        BatchDeleteUserIdentityDataPermissionDTO.UserIdentityCombination userIdentityCombination =
                new BatchDeleteUserIdentityDataPermissionDTO.UserIdentityCombination();
        userIdentityCombination.setUserIdList(userIdList);
        userIdentityCombination.setIdentityIdList(List.of(identityCodeAndIdMap.get(purchaserIdentity.name())));
        userIdentityCombinations.add(userIdentityCombination);
    }

    private void upsertProjectMembers(List<SrmProcurementProjectCreateReq.ProjectMember> projectMembers, long projectId) {
        List<SrmProjectMember> members = projectMembers.stream().map(m -> {
            SrmProjectMember member = new SrmProjectMember();
            BeanUtils.copyProperties(m, member);
            member.setProjectId(projectId);
            member.setBusinessId(projectId);
            member.setMemberType(ProjectMemberTypeEnum.PROJECT_MEMBER);
            member.setDelFlag(YesNoEnum.NO.getCode());
            return member;
        }).collect(Collectors.toList());
        srmProjectMemberService.lambdaUpdate().eq(SrmProjectMember::getBusinessId, projectId)
                .eq(SrmProjectMember::getMemberType, ProjectMemberTypeEnum.PROJECT_MEMBER).remove();
        srmProjectMemberService.saveBatch(members);
    }


    @Override
    public Page<SrmProcurementProject> pageQuery(SrmProcurementProjectPageQueryReq req, Page<SrmProcurementProject> page) {
        this.dealWithReqForBasicQuery(req);

        // 使用标准查询逻辑（不查询nodeStatus）
        return pageQueryStandard(req, page, false);
    }

    /**
     * 分页查询（使用审批流实例查询nodeStatus）
     * @param req 查询请求
     * @param page 分页参数
     * @return 分页结果
     */
    @Override
    @PreloadDataPermission
    public Page<SrmProcurementProject> pageQueryWithProcessInstance(SrmProcurementProjectPageQueryReq req, Page<SrmProcurementProject> page) {
        boolean hasNodeStatusFilter = CollectionUtils.isNotEmpty(req.getNodeStatus());

        this.dealWithReqForNodeStatusQuery(req);

        // 如果有nodeStatus过滤条件，使用特殊处理
        if (hasNodeStatusFilter) {
            return pageQueryWithNodeStatusFilterUsingProcessInstance(req, page);
        }

        // 使用标准查询逻辑（基于审批流实例）
        return pageQueryStandardUsingProcessInstance(req, page, true);
    }

    /**
     * 供应商分页查询（使用审批流实例查询nodeStatus，禁用数据权限控制）
     * @param req 查询请求
     * @param page 分页参数
     * @return 分页结果
     */
    @Override
    public Page<SrmProcurementProject> pageQueryWithProcessInstanceForSupplier(SrmProcurementProjectPageQueryReq req, Page<SrmProcurementProject> page) {
        boolean hasNodeStatusFilter = CollectionUtils.isNotEmpty(req.getNodeStatus());

        this.dealWithReqForNodeStatusQuery(req);

        // 如果有nodeStatus过滤条件，使用特殊处理
        if (hasNodeStatusFilter) {
            return pageQueryWithNodeStatusFilterUsingProcessInstanceForSupplier(req, page);
        }

        // 使用标准查询逻辑（基于审批流实例，禁用数据权限）
        return pageQueryStandardUsingProcessInstanceForSupplier(req, page, true);
    }

    /**
     * 处理基础查询请求（不涉及nodeStatus）
     */
    private void dealWithReqForBasicQuery(SrmProcurementProjectPageQueryReq req) {
        Set<InviteMethodEnum> inviteMethod = Sets.newHashSet();

        // 处理进度状态转换
        List<ProjectProgressStatusEnum> progressStatus = req.getProgressStatus();
        if (CollectionUtils.isNotEmpty(progressStatus)) {
            List<ProjectProgressStatusEnum> progressList = Lists.newArrayList();
            for (ProjectProgressStatusEnum status : progressStatus) {
                ProjectProgressStatusEnum inviteStatus = ProjectProgressStatusEnum.getInviteStatus(status);
                if (inviteStatus != null) {
                    status = inviteStatus;
                    inviteMethod.add(InviteMethodEnum.INVITE);
                } else {
                    ProjectProgressStatusEnum publicityStatus = ProjectProgressStatusEnum.getPublicityStatus(status);
                    if (publicityStatus != null) {
                        inviteMethod.add(InviteMethodEnum.PUBLICITY);
                    }
                }
                progressList.add(status);
            }
            req.setProgressStatus(progressList);
        }
    }

    /**
     * 处理nodeStatus查询请求
     */
    private void dealWithReqForNodeStatusQuery(SrmProcurementProjectPageQueryReq req) {
        Set<InviteMethodEnum> inviteMethod = Sets.newHashSet();

        // 处理nodeStatus相关逻辑
        if (CollectionUtils.isNotEmpty(req.getNodeStatus())) {
            List<SrmProcurementProject.NodeStatus> nodeStatusList = Lists.newArrayList();
            for (SrmProcurementProject.NodeStatus nodeStatus : req.getNodeStatus()) {
                SrmProcurementProject.NodeStatus inviteStatus = SrmProcurementProject.NodeStatus.getInviteStatus(nodeStatus);
                if (inviteStatus != null) {
                    nodeStatus = inviteStatus;
                    inviteMethod.add(InviteMethodEnum.INVITE);
                } else {
                    SrmProcurementProject.NodeStatus publicityStatus = SrmProcurementProject.NodeStatus.getPublicityStatus(nodeStatus);
                    if (publicityStatus != null) {
                        inviteMethod.add(InviteMethodEnum.PUBLICITY);
                    }
                }
                nodeStatusList.add(nodeStatus);
            }
            req.setNodeStatus(nodeStatusList);
        }

        // 处理进度状态转换
        List<ProjectProgressStatusEnum> progressStatus = req.getProgressStatus();
        if (CollectionUtils.isNotEmpty(progressStatus)) {
            List<ProjectProgressStatusEnum> progressList = Lists.newArrayList();
            for (ProjectProgressStatusEnum status : progressStatus) {
                ProjectProgressStatusEnum inviteStatus = ProjectProgressStatusEnum.getInviteStatus(status);
                if (inviteStatus != null) {
                    status = inviteStatus;
                    inviteMethod.add(InviteMethodEnum.INVITE);
                } else {
                    ProjectProgressStatusEnum publicityStatus = ProjectProgressStatusEnum.getPublicityStatus(status);
                    if (publicityStatus != null) {
                        inviteMethod.add(InviteMethodEnum.PUBLICITY);
                    }
                }
                progressList.add(status);
            }
            req.setProgressStatus(progressList);
        }
    }

    /**
     * 标准分页查询（不查询nodeStatus）
     * @param req 查询请求
     * @param page 分页参数
     */
    private Page<SrmProcurementProject> pageQueryStandard(SrmProcurementProjectPageQueryReq req, Page<SrmProcurementProject> page, boolean needNodeStatus) {
        // 构建查询条件并执行分页查询
        LambdaQueryWrapper<SrmProcurementProject> queryWrapper = buildQueryWrapper(req);
        Page<SrmProcurementProject> projectPage = this.page(new Page<>(page.getCurrent(), page.getSize()), queryWrapper);

        // 组装详情信息（不查询nodeStatus）
        assembleProjectDetailsWithoutNodeStatus(projectPage.getRecords());

        return projectPage;
    }

    /**
     * 标准分页查询（使用审批流实例查询nodeStatus）
     * @param req 查询请求
     * @param page 分页参数
     */
    private Page<SrmProcurementProject> pageQueryStandardUsingProcessInstance(SrmProcurementProjectPageQueryReq req, Page<SrmProcurementProject> page, boolean needNodeStatus) {
        // 构建查询条件并执行分页查询
        LambdaQueryWrapper<SrmProcurementProject> queryWrapper = buildQueryWrapper(req);
        Page<SrmProcurementProject> projectPage = baseMapper.selectPage(new Page<>(page.getCurrent(), page.getSize()), queryWrapper);

        // 组装详情信息（使用审批流实例查询nodeStatus）
        assembleProjectDetailsWithNodeStatusUsingProcessInstance(projectPage.getRecords());

        return projectPage;
    }

    /**
     * 供应商标准分页查询（使用审批流实例查询nodeStatus，禁用数据权限控制）
     * @param req 查询请求
     * @param page 分页参数
     */
    private Page<SrmProcurementProject> pageQueryStandardUsingProcessInstanceForSupplier(SrmProcurementProjectPageQueryReq req, Page<SrmProcurementProject> page, boolean needNodeStatus) {
        // 构建查询条件并执行分页查询（使用禁用数据权限的Mapper方法）
        LambdaQueryWrapper<SrmProcurementProject> queryWrapper = buildQueryWrapper(req);
        Page<SrmProcurementProject> projectPage = baseMapper.selectPageForSupplier(new Page<>(page.getCurrent(), page.getSize()), queryWrapper);

        // 组装详情信息（使用审批流实例查询nodeStatus）
        assembleProjectDetailsWithNodeStatusUsingProcessInstance(projectPage.getRecords());

        return projectPage;
    }


    /**
     * 包含nodeStatus过滤的分页查询（使用审批流实例）
     * 由于nodeStatus是计算字段，需要先查询所有符合条件的记录，计算nodeStatus后再过滤分页
     */
    private Page<SrmProcurementProject> pageQueryWithNodeStatusFilterUsingProcessInstance(SrmProcurementProjectPageQueryReq req, Page<SrmProcurementProject> page) {
        // 1. 查询所有符合基础条件的记录（不分页）
        LambdaQueryWrapper<SrmProcurementProject> queryWrapper = buildQueryWrapper(req);
        List<SrmProcurementProject> allProjects = this.list(queryWrapper);

        if (CollectionUtils.isEmpty(allProjects)) {
            return createEmptyPage(page);
        }

        // 2. 计算nodeStatus字段（使用审批流实例）
        this.assembleDetailUsingProcessInstance(allProjects);

        // 3. 根据nodeStatus过滤项目
        List<SrmProcurementProject> filteredProjects = filterByNodeStatus(allProjects, req.getNodeStatus());

        // 4. 手动分页处理
        return manualPagination(filteredProjects, page);
    }

    /**
     * 供应商包含nodeStatus过滤的分页查询（使用审批流实例，禁用数据权限控制）
     * 由于nodeStatus是计算字段，需要先查询所有符合条件的记录，计算nodeStatus后再过滤分页
     */
    private Page<SrmProcurementProject> pageQueryWithNodeStatusFilterUsingProcessInstanceForSupplier(SrmProcurementProjectPageQueryReq req, Page<SrmProcurementProject> page) {
        // 1. 查询所有符合基础条件的记录（不分页，使用禁用数据权限的查询）
        LambdaQueryWrapper<SrmProcurementProject> queryWrapper = buildQueryWrapper(req);
        // 使用禁用数据权限的Mapper方法查询所有记录
        Page<SrmProcurementProject> allProjectsPage = baseMapper.selectPageForSupplier(new Page<>(1, Integer.MAX_VALUE), queryWrapper);
        List<SrmProcurementProject> allProjects = allProjectsPage.getRecords();

        if (CollectionUtils.isEmpty(allProjects)) {
            return createEmptyPage(page);
        }

        // 2. 计算nodeStatus字段（使用审批流实例）
        this.assembleDetailUsingProcessInstance(allProjects);

        // 3. 根据nodeStatus过滤项目
        List<SrmProcurementProject> filteredProjects = filterByNodeStatus(allProjects, req.getNodeStatus());

        // 4. 手动分页处理
        return manualPagination(filteredProjects, page);
    }

    /**
     * 构建通用查询条件
     * @param req 查询请求参数
     * @return 查询构造器
     */
    private LambdaQueryWrapper<SrmProcurementProject> buildQueryWrapper(SrmProcurementProjectPageQueryReq req) {
        List<Long> queryBuyerDeptIds = getQueryBuyerDeptIds(req.getBuyerDeptName());
        List<Long> queryProjectIds = getQueryProjectIds(req.getProjectLeaderName());

        return new LambdaQueryWrapper<SrmProcurementProject>()
                // 基础条件
                .eq(SrmProcurementProject::getDelFlag, YesNoEnum.NO.getCode())
                // 搜索条件（项目名称或项目编码）
                .and(StringUtils.isNotBlank(req.getSearchContent()), wrapper ->
                        wrapper.like(SrmProcurementProject::getProjectName, req.getSearchContent())
                                .or()
                                .like(SrmProcurementProject::getProjectCode, req.getSearchContent())
                )
                // 状态条件
                .in(CollectionUtils.isNotEmpty(req.getStatus()), SrmProcurementProject::getStatus, req.getStatus())
                .in(CollectionUtils.isNotEmpty(req.getInviteMethod()), SrmProcurementProject::getInviteMethod, req.getInviteMethod())
                .in(CollectionUtils.isNotEmpty(req.getProgressStatus()), SrmProcurementProject::getProgressStatus, req.getProgressStatus())
                .in(CollectionUtils.isNotEmpty(req.getSourcingType()), SrmProcurementProject::getSourcingType, req.getSourcingType())
                // 关联条件
                .in(CollectionUtils.isNotEmpty(queryBuyerDeptIds), SrmProcurementProject::getBuyerDeptId, queryBuyerDeptIds)
                .in(CollectionUtils.isNotEmpty(queryProjectIds), SrmProcurementProject::getId, queryProjectIds)
                // 时间范围条件
                .ge(Objects.nonNull(req.getBidOpenStartTime()), SrmProcurementProject::getBidOpenTime, req.getBidOpenStartTime())
                .le(Objects.nonNull(req.getBidOpenEndTime()), SrmProcurementProject::getBidOpenTime, req.getBidOpenEndTime())
                .orderByDesc(SrmProcurementProject::getCreateTime)
                ;
    }

    /**
     * 组装项目详情信息（不查询nodeStatus）
     * @param projects 项目列表
     */
    private void assembleProjectDetailsWithoutNodeStatus(List<SrmProcurementProject> projects) {
        if (CollectionUtils.isEmpty(projects)) {
            return;
        }

        // 1. 组装基础详情信息（不查询nodeStatus）
        this.assembleDetailWithoutNodeStatus(projects);

        // 2. 组装项目成员信息
        this.assembleProjectMembers(projects);

        // 3. 关联招标公告信息，替换时间字段
        assembleTenderNoticeTimeFields(projects);
    }

    /**
     * 组装项目详情信息（使用审批流实例查询nodeStatus）
     * @param projects 项目列表
     */
    private void assembleProjectDetailsWithNodeStatusUsingProcessInstance(List<SrmProcurementProject> projects) {
        if (CollectionUtils.isEmpty(projects)) {
            return;
        }

        // 1. 组装基础详情信息（使用审批流实例查询nodeStatus）
        this.assembleDetailUsingProcessInstance(projects);

        // 2. 组装项目成员信息
        this.assembleProjectMembers(projects);

        // 3. 关联招标公告信息，替换时间字段
        assembleTenderNoticeTimeFields(projects);
    }

    /**
     * 根据nodeStatus过滤项目
     * @param projects 项目列表
     * @param nodeStatusList 需要过滤的nodeStatus列表
     * @return 过滤后的项目列表
     */
    private List<SrmProcurementProject> filterByNodeStatus(List<SrmProcurementProject> projects, List<SrmProcurementProject.NodeStatus> nodeStatusList) {
        return projects.stream()
                .filter(project -> nodeStatusList.contains(project.getNodeStatus()))
                .toList();
    }

    /**
     * 创建空的分页结果
     * @param page 分页参数
     * @return 空的分页结果
     */
    private Page<SrmProcurementProject> createEmptyPage(Page<SrmProcurementProject> page) {
        return new Page<>(page.getCurrent(), page.getSize(), 0);
    }

    /**
     * 关联招标公告信息，替换时间字段
     * @param projects 项目列表
     */
    private void assembleTenderNoticeTimeFields(List<SrmProcurementProject> projects) {
        if (CollectionUtils.isEmpty(projects)) {
            return;
        }

        // 批量查询招标公告信息
        List<Long> projectIds = projects.stream()
                .map(SrmProcurementProject::getId)
                .toList();

        Map<Long, SrmTenderNotice> tenderNoticeMap = getTenderNoticeMap(projectIds);

        // 替换时间字段
        projects.forEach(project -> {
            SrmTenderNotice tenderNotice = tenderNoticeMap.get(project.getId());
            if (tenderNotice != null) {
                replaceTenderNoticeTimeFields(project, tenderNotice);
            }
        });
    }

    /**
     * 批量查询招标公告信息
     * @param projectIds 项目ID列表
     * @return 项目ID -> 招标公告的映射
     */
    private Map<Long, SrmTenderNotice> getTenderNoticeMap(List<Long> projectIds) {
        List<SrmTenderNotice> tenderNotices = srmTenderNoticeService.lambdaQuery()
                .in(SrmTenderNotice::getProjectId, projectIds)
                .eq(SrmTenderNotice::getStatus, PublicNoticeStatusEnum.PUBLISHED)
                .list();

        return tenderNotices.stream()
                .collect(Collectors.toMap(
                        SrmTenderNotice::getProjectId,
                        Function.identity(),
                        (existing, replacement) -> existing // 如果有重复，保留第一个
                ));
    }

    /**
     * 替换项目中的时间字段为招标公告中的对应字段
     * @param project 采购项目
     * @param tenderNotice 招标公告
     */
    private void replaceTenderNoticeTimeFields(SrmProcurementProject project, SrmTenderNotice tenderNotice) {
        // 替换报名时间
        if (tenderNotice.getRegisterStartTime() != null) {
            project.setRegisterStartTime(tenderNotice.getRegisterStartTime());
        }
        if (tenderNotice.getRegisterEndTime() != null) {
            project.setRegisterEndTime(tenderNotice.getRegisterEndTime());
        }

        // 替换报价时间
        if (tenderNotice.getQuoteStartTime() != null) {
            project.setQuoteStartTime(tenderNotice.getQuoteStartTime());
        }
        if (tenderNotice.getQuoteEndTime() != null) {
            project.setQuoteEndTime(tenderNotice.getQuoteEndTime());
        }

        // 替换开标时间
        if (tenderNotice.getBidOpenTime() != null) {
            project.setBidOpenTime(tenderNotice.getBidOpenTime());
        }
    }

    /**
     * 组装项目成员信息
     * @param projects 项目列表
     */
    private void assembleProjectMembers(List<SrmProcurementProject> projects) {
        if (CollectionUtils.isEmpty(projects)) {
            return;
        }

        // 获取所有项目ID
        List<Long> projectIds = projects.stream()
                .map(SrmProcurementProject::getId)
                .toList();

        // 批量查询项目成员信息
        List<SrmProjectMember> allProjectMembers = srmProjectMemberService.lambdaQuery()
                .in(SrmProjectMember::getProjectId, projectIds)
                .eq(SrmProjectMember::getDelFlag, YesNoEnum.NO.getCode())
                .list();

        // 按项目ID分组
        Map<Long, List<SrmProjectMember>> projectMemberMap = allProjectMembers.stream()
                .collect(Collectors.groupingBy(SrmProjectMember::getProjectId));

        // 为每个项目设置成员列表
        projects.forEach(project -> {
            List<SrmProjectMember> members = projectMemberMap.getOrDefault(project.getId(), new ArrayList<>());
            project.setProjectMemberList(members);
        });
    }

    /**
     * 获取采购部门ID列表
     */
    private List<Long> getQueryBuyerDeptIds(String buyerDeptName) {
        if (StringUtils.isBlank(buyerDeptName)) {
            return null;
        }

        List<SysDept> deptList = sysDeptService.lambdaQuery()
                .like(SysDept::getName, buyerDeptName)
                .eq(SysDept::getDelFlag, YesNoEnum.NO.getCode())
                .list();

        if (CollectionUtils.isEmpty(deptList)) {
            return null;
        }

        return deptList.stream().map(SysDept::getDeptId).toList();
    }

    /**
     * 获取项目ID列表（根据项目负责人名称）
     */
    private List<Long> getQueryProjectIds(String projectLeaderName) {
        if (StringUtils.isBlank(projectLeaderName)) {
            return null;
        }

        List<SysUser> userList = sysUserService.lambdaQuery()
                .like(SysUser::getName, projectLeaderName)
                .eq(SysUser::getDelFlag, YesNoEnum.NO.getCode())
                .list();

        if (CollectionUtils.isEmpty(userList)) {
            return null;
        }

        List<Long> userIds = userList.stream().map(SysUser::getUserId).toList();
        List<SrmProjectMember> projectMembers = srmProjectMemberService.lambdaQuery()
                .in(SrmProjectMember::getUserId, userIds)
                .eq(SrmProjectMember::getMemberType, ProjectMemberTypeEnum.PROJECT_MEMBER)
                .eq(SrmProjectMember::getRole, ProjectMemberRoleEnum.PROJECT_LEADER)
                .eq(SrmProjectMember::getDelFlag, YesNoEnum.NO.getCode())
                .list();

        if (CollectionUtils.isEmpty(projectMembers)) {
            return null;
        }

        return projectMembers.stream().map(SrmProjectMember::getBusinessId).toList();
    }

    /**
     * 手动分页
     */
    private Page<SrmProcurementProject> manualPagination(List<SrmProcurementProject> allRecords, Page<SrmProcurementProject> page) {
        long total = allRecords.size();
        long current = page.getCurrent();
        long size = page.getSize();

        // 计算分页范围
        long startIndex = (current - 1) * size;
        long endIndex = Math.min(startIndex + size, total);

        List<SrmProcurementProject> pageRecords;
        if (startIndex >= total) {
            pageRecords = Collections.emptyList();
        } else {
            pageRecords = allRecords.subList((int) startIndex, (int) endIndex);
        }

        // 创建分页结果
        Page<SrmProcurementProject> result = new Page<>(current, size, total);
        result.setRecords(pageRecords);

        return result;
    }

    /**
     * 组装详情信息（不计算nodeStatus，提高性能）
     * 项目负责人、服务类型名称、部门名称、支付方式名称、关联采购计划单号
     */
    private void assembleDetailWithoutNodeStatus(List<SrmProcurementProject> projects) {
        List<Long> projectIds = projects.stream().map(SrmProcurementProject::getId).toList();

        // 查询项目负责人
        List<SrmProjectMember> leaders = srmProjectMemberService.lambdaQuery()
                .eq(SrmProjectMember::getMemberType, ProjectMemberTypeEnum.PROJECT_MEMBER)
                .eq(SrmProjectMember::getRole, ProjectMemberRoleEnum.PROJECT_LEADER)
                .in(SrmProjectMember::getBusinessId, projectIds)
                .list();

        Map<Long, String> projectLeaderNameMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(leaders)) {
            List<Long> leaderUserIds = leaders.stream().map(SrmProjectMember::getUserId).toList();
            List<SysUser> users = sysUserService.lambdaQuery().in(SysUser::getUserId, leaderUserIds).list();
            Map<Long, String> userNameMap = users.stream().collect(Collectors.toMap(SysUser::getUserId, SysUser::getName));
            projectLeaderNameMap = leaders.stream().collect(Collectors.toMap(SrmProjectMember::getBusinessId,
                    leader -> userNameMap.get(leader.getUserId())));
        }

        // 获取服务类型名称
        List<Long> serviceTypeIds = projects.stream().map(SrmProcurementProject::getServiceTypeId).toList();
        Map<Long, String> serviceTypeNameMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(serviceTypeIds)) {
            List<BaseServiceTypeEntity> baseServiceTypeEntities = baseServiceTypeService.listByIds(serviceTypeIds);
            serviceTypeNameMap = baseServiceTypeEntities.stream().collect(Collectors.toMap(BaseServiceTypeEntity::getId, BaseServiceTypeEntity::getTypeName));
        }

        // 获取部门名称
        List<Long> allDeptIds = projects.stream().map(item -> {
            List<Long> deptIds = new ArrayList<>();
            if (StringUtils.isNotBlank(item.getPurchaseDeptId())) {
                List<Long> purchaseDeptIds = stream(StringUtils.split(item.getPurchaseDeptId(), ",")).map(Long::parseLong).toList();
                deptIds.addAll(purchaseDeptIds);
            }
            if (Objects.nonNull(item.getBuyerDeptId())) {
                deptIds.add(item.getBuyerDeptId());
            }
            return deptIds;
        }).flatMap(Collection::stream).toList();
        Map<Long, String> deptNameMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(allDeptIds)) {
            List<SysDept> sysDepts = sysDeptService.listByIds(allDeptIds);
            sysDepts.forEach(item -> deptNameMap.put(item.getDeptId(), item.getName()));
        }

        // 获取项目所有的支付方式名称
        List<SrmProcurementProjectPayment> paymentList = srmProcurementProjectPaymentService.lambdaQuery()
                .in(SrmProcurementProjectPayment::getProjectId, projectIds).list();
        Map<Long, List<SrmProcurementProjectPayment>> paymentProjectIdGroup = Optional.ofNullable(paymentList).stream()
                .flatMap(Collection::stream).collect(Collectors.groupingBy(SrmProcurementProjectPayment::getProjectId));
        Map<Long, String> paymentNameMap = Maps.newHashMap();
        Map<Long, String> paymentPeriodNameMap = Maps.newHashMap();
        Map<Long, String> projectPaymentNameMap = Maps.newHashMap();
        Map<Long, String> projectPaymentPeriodNameMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(paymentList)) {
            List<Long> paymentIds = paymentList.stream().map(SrmProcurementProjectPayment::getPaymentMethodId).toList();
            if (CollectionUtils.isNotEmpty(paymentIds)) {
                List<BasePaymentMethodEntity> paymentMethods = basePaymentMethodService.listByIds(paymentIds);
                paymentNameMap.putAll(paymentMethods.stream()
                        .collect(Collectors.toMap(
                                BasePaymentMethodEntity::getId,
                                BasePaymentMethodEntity::getMethodName
                        )));
                // 设置支付方式名称
                paymentList.forEach(item -> item.setPaymentMethodName(paymentNameMap.get(item.getPaymentMethodId())));
            }

            List<Long> paymentPeriodIds = paymentList.stream().map(SrmProcurementProjectPayment::getPaymentPeriodId).filter(Objects::nonNull).toList();
            if (CollectionUtils.isNotEmpty(paymentPeriodIds)) {
                List<BasePaymentMethodPeriodEntity> periodEntities = basePaymentMethodPeriodService.listByIds(paymentPeriodIds);
                periodEntities.forEach(item -> paymentPeriodNameMap.put(item.getId(), item.getPeriodName()));
            }
            projectPaymentNameMap = paymentList.stream()
                    .collect(Collectors.groupingBy(SrmProcurementProjectPayment::getProjectId,
                            Collectors.mapping(item -> paymentNameMap.get(item.getPaymentMethodId()), Collectors.joining(","))));
            projectPaymentPeriodNameMap = paymentList.stream()
                    .collect(Collectors.groupingBy(SrmProcurementProjectPayment::getProjectId,
                            Collectors.mapping(item -> paymentPeriodNameMap.get(item.getPaymentPeriodId()), Collectors.filtering(Objects::nonNull, Collectors.joining(",")))));
        }

        // 查询招标公告信息，用于设置effectNoticeId
        Map<Long, List<SrmTenderNotice>> noticeMap = Maps.newHashMap();
        List<SrmTenderNotice> tenderNotices = srmTenderNoticeService.lambdaQuery()
                .eq(SrmTenderNotice::getStatus, PublicNoticeStatusEnum.PUBLISHED)
                .in(SrmTenderNotice::getProjectId, projectIds)
                .list();
        if (CollectionUtils.isNotEmpty(tenderNotices)) {
            noticeMap = tenderNotices.stream().collect(Collectors.groupingBy(SrmTenderNotice::getProjectId));
        }

        // 设置值（不计算nodeStatus）
        for (SrmProcurementProject record : projects) {
            // 设置effectNoticeId
            if (noticeMap.containsKey(record.getId())) {
                List<SrmTenderNotice> notices = noticeMap.get(record.getId());
                notices.stream().filter(e -> PublicNoticeStatusEnum.PUBLISHED.equals(e.getStatus())).findAny().ifPresent(e -> {
                    record.setEffectNoticeId(e.getId());
                });
            }
            record.setProjectLeaderName(projectLeaderNameMap.get(record.getId()));
            record.setServiceTypeName(serviceTypeNameMap.get(record.getServiceTypeId()));
            if (Objects.nonNull(record.getBuyerDeptId())) {
                record.setBuyerDeptName(deptNameMap.get(record.getBuyerDeptId()));
            }
            if (StringUtils.isNotBlank(record.getPurchaseDeptId())) {
                String purchaseDeptName = stream(StringUtils.split(record.getPurchaseDeptId(), ","))
                        .map(item -> deptNameMap.get(Long.parseLong(item))).collect(Collectors.joining(","));
                record.setPurchaseDeptName(purchaseDeptName);
            }
            record.setPaymentName(projectPaymentNameMap.get(record.getId()));
            record.setPaymentPeriodName(projectPaymentPeriodNameMap.get(record.getId()));
            record.setProjectPaymentList(paymentProjectIdGroup.get(record.getId()));
            // 不设置nodeStatus，保持为null或默认值，提高性能
        }

        // 获取关联计划
        List<SrmProcurementProjectItem> relationPlanItems = srmProcurementProjectItemService.lambdaQuery()
                .in(SrmProcurementProjectItem::getProjectId, projectIds)
                .isNotNull(SrmProcurementProjectItem::getPlanId).list();
        if (CollectionUtils.isNotEmpty(relationPlanItems)) {
            List<Long> planIds = relationPlanItems.stream().map(SrmProcurementProjectItem::getPlanId).toList();
            List<SrmProcurementPlanEntity> plans = srmProcurementPlanService.listByIds(planIds);
            Map<Long, List<Long>> projectIdGroup = relationPlanItems.stream().collect(Collectors.groupingBy(SrmProcurementProjectItem::getProjectId,
                    Collectors.mapping(SrmProcurementProjectItem::getPlanId, Collectors.toList())));
            projects.forEach(project -> {
                if (projectIdGroup.containsKey(project.getId())) {
                    // List<String> planCodes = plans.stream().filter(plan -> projectIdGroup.get(project.getId()).contains(plan.getId()))
                    //         .map(SrmProcurementPlanEntity::getPlanCode).toList();
                    List<SrmProcurementPlanEntity> planEntities = plans.stream().filter(plan -> projectIdGroup.get(project.getId()).contains(plan.getId()))
                            .toList();
                    List<String> planCodes = planEntities
                            .stream()
                            .map(SrmProcurementPlanEntity::getPlanCode).toList();
                    List<SrmProcurementDecisionEntity.RelationPlanVO> relationPlanVOS = planEntities
                            .stream()
                            .map(p -> SrmProcurementDecisionEntity.RelationPlanVO.builder()
                                    .planCode(p.getPlanCode())
                                    .planName(p.getPlanName())
                                    .build()).toList();
                    project.setRelationPlanCodeList(planCodes);
                    project.setRelationPlanList(relationPlanVOS);
                }
            });
        }

        // 青贮信息设置
        List<SrmProcurementProjectItem> projectItems = srmProcurementProjectItemService.lambdaQuery()
                .eq(SrmProcurementProjectItem::getSourcingType, BaseServiceTypeFieldService.BuyWayEnum.QZXJ)
                .in(SrmProcurementProjectItem::getProjectId, projectIds).list();
        if(CollectionUtils.isNotEmpty(projectItems)) {
            // 青贮设置物料信息
            Map<Long, SrmProcurementProjectItem> projectIdMapVo = projectItems.stream().filter(
                            info -> BaseServiceTypeFieldService.BuyWayEnum.QZXJ == info.getSourcingType())
                    .collect(Collectors.toMap(SrmProcurementProjectItem::getProjectId, Function.identity(), (x1, x2) -> x1));
            // 牧场
            List<Long> locationIds = projectItems.stream().map(SrmProcurementProjectItem::getUsageLocationId).distinct().toList();
            Map<Long, BaseUsageLocationEntity> locationEntityMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(locationIds)) {
                List<BaseUsageLocationEntity> baseUsageLocationEntities = baseUsageLocationService.listByIds(locationIds);
                locationEntityMap.putAll(Optional.ofNullable(baseUsageLocationEntities).stream().flatMap(Collection::stream)
                        .collect(Collectors.toMap(BaseUsageLocationEntity::getId, Function.identity())));
            }

            // 获取青贮信息
            List<SrmTenderNotice> tenderNoticeList = srmTenderNoticeService.lambdaQuery().in(SrmTenderNotice::getProjectId, projectIds)
                    .orderByDesc(SrmTenderNotice::getCreateTime)
                    .list();
            Map<Long, SrmTenderNotice> projectIdGroup = tenderNoticeList.stream().collect(
                    Collectors.toMap(SrmTenderNotice::getProjectId, Function.identity(), (x1, x2) -> x1));
            projects.forEach(project -> {
                SrmProcurementProjectItem projectItem = projectIdMapVo.get(project.getId());
                if (Objects.nonNull(projectItem)) {
                    project.setMaterialCode(projectItem.getMaterialCode());
                    project.setMaterialName(projectItem.getMaterialName());
                    project.setUsageLocationId(projectItem.getUsageLocationId());
                    project.setUnit(projectItem.getUnit());
                    project.setRequiredQuantity(projectItem.getRequiredQuantity());
                }
                BaseUsageLocationEntity baseUsageLocationEntity = locationEntityMap.get(project.getUsageLocationId());
                if (Objects.nonNull(baseUsageLocationEntity)) {
                    project.setUsageLocationRegion(baseUsageLocationEntity.getProvince() + "/" + baseUsageLocationEntity.getCity() + "/" + baseUsageLocationEntity.getDistrict());
                    project.setUsageLocationAddress(baseUsageLocationEntity.getAddress());
                    project.setUsageLocationName(baseUsageLocationEntity.getLocationName());
                }
                SrmTenderNotice srmTenderNotice = projectIdGroup.get(project.getId());
                if (Objects.nonNull(srmTenderNotice)) {
                    project.setInterceptPrice(srmTenderNotice.getInterceptPrice());
                    project.setContactPerson(srmTenderNotice.getContactPerson());
                    project.setContactPhone(srmTenderNotice.getContactPhone());
                    project.setCreateUser(srmTenderNotice.getCreateBy());
                    project.setCreateTime(srmTenderNotice.getCreateTime());
                    project.setTenderNotice(srmTenderNotice);
                    project.setDemandName(srmTenderNotice.getNoticeTitle());
                    project.setExecutorId(srmTenderNotice.getExecutorId());
                }
            });
        }
    }

    @Override
    public SrmProcurementProject detail(String projectCode, boolean simple) {
        Page<SrmProcurementProject> projectPage = baseMapper.selectPageForSupplier(new Page<>(), Wrappers.<SrmProcurementProject>lambdaQuery()
                .eq(SrmProcurementProject::getProjectCode, projectCode));
        ExceptionUtil.checkNonNull(CollectionUtils.isEmpty(projectPage.getRecords()), "项目不存在");
        SrmProcurementProject project = projectPage.getRecords().get(0);
        // 查询项目成员
        List<SrmProjectMember> projectMemberList = srmProjectMemberService.lambdaQuery()
                .eq(SrmProjectMember::getMemberType, ProjectMemberTypeEnum.PROJECT_MEMBER)
                .eq(SrmProjectMember::getBusinessId, project.getId()).list();
        project.setProjectMemberList(projectMemberList);
        if (simple) {
            return project;
        }
        // 标段
        List<SrmProcurementProjectSection> projectSectionList = srmProcurementProjectSectionService.lambdaQuery()
                .eq(SrmProcurementProjectSection::getProjectId, project.getId()).list();
        List<Long> sectionIds = projectSectionList.stream().map(SrmProcurementProjectSection::getId).toList();
        // 项目物料
        List<SrmProcurementProjectItem> projectItems = srmProcurementProjectItemService.lambdaQuery().in(SrmProcurementProjectItem::getSectionId, sectionIds).list();
        List<Long> itemIds = projectItems.stream().map(SrmProcurementProjectItem::getId).toList();
        // 项目物料字段值
        List<SrmProcurementProjectFieldValue> fieldValues = srmProcurementProjectFieldValueService.lambdaQuery().in(SrmProcurementProjectFieldValue::getProjectItemId, itemIds).list();
        // 组装分组
        Map<Long, List<SrmProcurementProjectFieldValue>> groupByItemId = fieldValues.stream().collect(Collectors.groupingBy(SrmProcurementProjectFieldValue::getProjectItemId));
        projectItems.forEach(item -> item.setProjectFieldValueList(groupByItemId.get(item.getId())));
        Map<Long, List<SrmProcurementProjectItem>> groupBySelectionId = projectItems.stream().collect(Collectors.groupingBy(SrmProcurementProjectItem::getSectionId));
        projectSectionList.forEach(section -> section.setProjectItemList(groupBySelectionId.get(section.getId())));
        project.setProjectSectionList(projectSectionList);
        // 项目附件
        List<SrmProjectAttachment> projectAttachmentList = srmProjectAttachmentService.lambdaQuery()
                .eq(SrmProjectAttachment::getBusinessId, project.getId())
                .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.PROJECT).list();
        project.setProjectAttachmentList(projectAttachmentList);

        // 牧场
        List<Long> locationIds = projectItems.stream().map(SrmProcurementProjectItem::getUsageLocationId).distinct().toList();
        Map<Long, BaseUsageLocationEntity> locationEntityMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(locationIds)) {
            List<BaseUsageLocationEntity> baseUsageLocationEntities = baseUsageLocationService.listByIds(locationIds);
            locationEntityMap = Optional.ofNullable(baseUsageLocationEntities).stream().flatMap(Collection::stream)
                    .collect(Collectors.toMap(BaseUsageLocationEntity::getId, Function.identity()));
        }
        // 质量指标
        List<Long> qualityIds = projectItems.stream().map(SrmProcurementProjectItem::getQualityIndicatorId).distinct().toList();
        Map<Long, String> qualityIndicatorEntityMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(qualityIds)) {
            List<BaseQualityIndicatorEntity> baseQualityIndicatorEntities = baseQualityIndicatorService.listByIds(qualityIds);
            qualityIndicatorEntityMap = Optional.ofNullable(baseQualityIndicatorEntities).stream().flatMap(Collection::stream)
                    .collect(Collectors.toMap(BaseQualityIndicatorEntity::getId, BaseQualityIndicatorEntity::getIndicatorName));
        }
        for (SrmProcurementProjectItem e : projectItems) {
            BaseUsageLocationEntity baseUsageLocationEntity = locationEntityMap.get(e.getUsageLocationId());
            if (Objects.nonNull(baseUsageLocationEntity)) {
                e.setUsageLocationName(baseUsageLocationEntity.getLocationName());
                e.setUsageLocationRegion(baseUsageLocationEntity.getProvince() + "/" + baseUsageLocationEntity.getCity() + "/" + baseUsageLocationEntity.getDistrict());
                e.setUsageLocationAddress(baseUsageLocationEntity.getAddress());
            }
            e.setQualityIndicatorName(qualityIndicatorEntityMap.get(e.getQualityIndicatorId()));
        }

        SrmTenderNotice effectNotice = srmTenderNoticeService.lambdaQuery()
                .eq(SrmTenderNotice::getProjectId, project.getId())
                .eq(SrmTenderNotice::getStatus, PublicNoticeStatusEnum.PUBLISHED)
                .one();
        if (Objects.nonNull(effectNotice)) {
            project.setEffectNoticeId(effectNotice.getId());
            // 有一个标段需要保证金，则需要保证金节点
            Map<Long, Boolean> depositNeedEffInfoBySectionIds = srmTenderRequirementService.isNeedEffInfoBySectionIds(effectNotice.getId(), sectionIds, BidsSegmentTypeEnum.FEE);
            effectNotice.setNeedDeposit(depositNeedEffInfoBySectionIds.containsValue(true));
            Map<Long, Boolean> tenderFeeNeedEffInfoBySectionIds = srmTenderRequirementService.isNeedEffInfoBySectionIds(effectNotice.getId(), sectionIds, BidsSegmentTypeEnum.BID_DOCUMENT);
            effectNotice.setNeedTenderFee(tenderFeeNeedEffInfoBySectionIds.containsValue(true));
        }
        project.setEffectNotice(effectNotice);
        // 项目对应的最新的公告信息
        SrmTenderNotice lastNotice = srmTenderNoticeService.lambdaQuery()
                .eq(SrmTenderNotice::getProjectId, project.getId())
                .orderByDesc(SrmTenderNotice::getCreateTime)
                .last(" limit 1")
                .one();
        project.setTenderNotice(lastNotice);

        this.assembleDetail(Lists.newArrayList(project));
        return project;
    }


    /**
     * 项目负责人、服务类型名称、部门名称、支付方式名称、关联采购计划单号
     */
    private void assembleDetail(List<SrmProcurementProject> projects) {
        List<Long> projectIds = projects.stream().map(SrmProcurementProject::getId).toList();
        // 查询项目负责人
        List<SrmProjectMember> leaders = srmProjectMemberService.lambdaQuery().eq(SrmProjectMember::getMemberType, ProjectMemberTypeEnum.PROJECT_MEMBER)
                .eq(SrmProjectMember::getRole, ProjectMemberRoleEnum.PROJECT_LEADER)
                .in(SrmProjectMember::getBusinessId, projectIds).list();
        Map<Long, String> projectLeaderNameMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(leaders)) {
            List<Long> leaderUserIds = leaders.stream().map(SrmProjectMember::getUserId).toList();
            List<SysUser> users = sysUserService.lambdaQuery().in(SysUser::getUserId, leaderUserIds).list();
            Map<Long, String> userMap = users.stream().collect(Collectors.toMap(SysUser::getUserId, SysUser::getName));
            for (SrmProjectMember leader : leaders) {
                projectLeaderNameMap.put(leader.getBusinessId(), userMap.get(leader.getUserId()));
            }
        }

        // 获取服务类型名称
        List<Long> serviceTypeIds = projects.stream().map(SrmProcurementProject::getServiceTypeId).toList();
        Map<Long, String> serviceTypeNameMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(serviceTypeIds)) {
            List<BaseServiceTypeEntity> baseServiceTypeEntities = baseServiceTypeService.listByIds(serviceTypeIds);
            serviceTypeNameMap = baseServiceTypeEntities.stream().collect(Collectors.toMap(BaseServiceTypeEntity::getId, BaseServiceTypeEntity::getTypeName));
        }
        // 获取部门名称
        List<Long> allDeptIds = projects.stream().map(item -> {
            List<Long> deptIds = new ArrayList<>();
            if (StringUtils.isNotBlank(item.getPurchaseDeptId())) {
                List<Long> purchaseDeptIds = stream(StringUtils.split(item.getPurchaseDeptId(), ",")).map(Long::parseLong).toList();
                deptIds.addAll(purchaseDeptIds);
            }
            if (Objects.nonNull(item.getBuyerDeptId())) {
                deptIds.add(item.getBuyerDeptId());
            }
            return deptIds;
        }).flatMap(Collection::stream).toList();
        Map<Long, String> deptNameMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(allDeptIds)) {
            List<SysDept> sysDepts = sysDeptService.listByIds(allDeptIds);
            sysDepts.forEach(item -> deptNameMap.put(item.getDeptId(), item.getName()));
        }

        // 获取项目所有的支付方式名称
        List<SrmProcurementProjectPayment> paymentList = srmProcurementProjectPaymentService.lambdaQuery()
                .in(SrmProcurementProjectPayment::getProjectId, projectIds).list();
        Map<Long, List<SrmProcurementProjectPayment>> paymentProjectIdGroup = Optional.ofNullable(paymentList).stream()
                .flatMap(Collection::stream).collect(Collectors.groupingBy(SrmProcurementProjectPayment::getProjectId));
        Map<Long, String> paymentNameMap = Maps.newHashMap();
        Map<Long, String> paymentPeriodNameMap = Maps.newHashMap();
        Map<Long, String> projectPaymentNameMap = Maps.newHashMap();
        Map<Long, String> projectPaymentPeriodNameMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(paymentList)) {
            List<Long> paymentIds = paymentList.stream().map(SrmProcurementProjectPayment::getPaymentMethodId).toList();
            if (CollectionUtils.isNotEmpty(paymentIds)) {
                List<BasePaymentMethodEntity> paymentMethods = basePaymentMethodService.listByIds(paymentIds);
                paymentNameMap.putAll(paymentMethods.stream()
                        .collect(Collectors.toMap(
                                BasePaymentMethodEntity::getId,
                                BasePaymentMethodEntity::getMethodName
                        )));
                // 设置支付方式名称
                paymentList.forEach(item -> item.setPaymentMethodName(paymentNameMap.get(item.getPaymentMethodId())));
            }

            List<Long> paymentPeriodIds = paymentList.stream().map(SrmProcurementProjectPayment::getPaymentPeriodId).filter(Objects::nonNull).toList();
            if (CollectionUtils.isNotEmpty(paymentPeriodIds)) {
                List<BasePaymentMethodPeriodEntity> periodEntities = basePaymentMethodPeriodService.listByIds(paymentPeriodIds);
                periodEntities.forEach(item -> paymentPeriodNameMap.put(item.getId(), item.getPeriodName()));
            }
            projectPaymentNameMap = paymentList.stream()
                    .collect(Collectors.groupingBy(SrmProcurementProjectPayment::getProjectId,
                            Collectors.mapping(item -> paymentNameMap.get(item.getPaymentMethodId()), Collectors.joining(","))));
            projectPaymentPeriodNameMap = paymentList.stream()
                    .collect(Collectors.groupingBy(SrmProcurementProjectPayment::getProjectId,
                            Collectors.mapping(item -> paymentPeriodNameMap.get(item.getPaymentPeriodId()), Collectors.filtering(Objects::nonNull, Collectors.joining(",")))));
        }

//        获得标段
        List<SrmProcurementProjectSection> sections = srmProcurementProjectSectionService.lambdaQuery()
                .in(SrmProcurementProjectSection::getProjectId, projectIds)
                .list();
        Map<Long, List<SrmProcurementProjectSection>> sectionMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(sections)) {
            sectionMap = sections.stream().collect(Collectors.groupingBy(SrmProcurementProjectSection::getProjectId));
        }
//        获取招标公告/邀请
        List<SrmTenderNotice> tenderNotice = srmTenderNoticeService.lambdaQuery()
                .in(SrmTenderNotice::getProjectId, projectIds)
//                .eq(SrmTenderNotice::getStatus, PublicNoticeStatusEnum.PUBLISHED)
                .list();
        Map<Long, List<SrmTenderNotice>> tenderNoticeMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(tenderNotice)) {
            tenderNoticeMap = tenderNotice.stream().collect(Collectors.groupingBy(SrmTenderNotice::getProjectId));
        }

//        获取变更
        List<SrmTenderNoticeChange> changes = srmTenderNoticeChangeMapper.selectList(new LambdaQueryWrapper<SrmTenderNoticeChange>()
                .in(SrmTenderNoticeChange::getProjectId, projectIds)
        );
        Map<Long, List<SrmTenderNoticeChange>> changeMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(changes)) {
            changeMap = changes.stream().collect(Collectors.groupingBy(SrmTenderNoticeChange::getProjectId));
        }

//        获取流标记录
        List<SrmTenderFailedLog> failedLogs = srmTenderFailedLogMapper.selectList(new LambdaQueryWrapper<SrmTenderFailedLog>()
                .in(SrmTenderFailedLog::getProjectId, projectIds));
        Map<Long, List<SrmTenderFailedLog>> failedLogsMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(failedLogs)) {
            failedLogsMap = failedLogs.stream().collect(Collectors.groupingBy(SrmTenderFailedLog::getProjectId));
        }

//        获取中标记录
//        List<SrmTenderAwardNotice> awardNotices = srmTenderAwardNoticeService.lambdaQuery()
//                .in(SrmTenderAwardNotice::getProjectId, projectIds)
//                .list();
//        Map<Long, List<SrmTenderAwardNotice>> awardNoticesMap = Maps.newHashMap();
//
//        if (CollectionUtils.isNotEmpty(awardNotices)) {
//            awardNoticesMap = awardNotices.stream().collect(Collectors.groupingBy(SrmTenderAwardNotice::getProjectId));
//        }

//        获取定标结果
        List<SrmTenderEvaluationResult> resultList = srmTenderEvaluationResultService.lambdaQuery()
                .in(SrmTenderEvaluationResult::getProjectId, projectIds)
                .list();
        Map<Long, List<SrmTenderEvaluationResult>> resultMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(resultList)) {
            resultMap = resultList.stream().collect(Collectors.groupingBy(SrmTenderEvaluationResult::getProjectId));
        }

        ProjectContext projectContext = new ProjectContext(sectionMap, tenderNoticeMap, changeMap, failedLogsMap, resultMap);

        // 设置值
        for (SrmProcurementProject record : projects) {

            this.setProcessStatus(record, projectContext);
            List<SrmTenderNotice> projectNotices = projectContext.getTenderNotice(record.getId());

            if (CollectionUtils.isNotEmpty(projectNotices)) {
                projectNotices.stream().filter(e -> e.getStatus() == PublicNoticeStatusEnum.PUBLISHED).findAny().ifPresent(e -> {
                    record.setEffectNoticeId(e.getId());
                });
            }


            record.setProjectLeaderName(projectLeaderNameMap.get(record.getId()));
            record.setServiceTypeName(serviceTypeNameMap.get(record.getServiceTypeId()));
            if (Objects.nonNull(record.getBuyerDeptId())) {
                record.setBuyerDeptName(deptNameMap.get(record.getBuyerDeptId()));
            }
            if (StringUtils.isNotBlank(record.getPurchaseDeptId())) {
                String purchaseDeptName = stream(StringUtils.split(record.getPurchaseDeptId(), ","))
                        .map(item -> deptNameMap.get(Long.parseLong(item))).collect(Collectors.joining(","));
                record.setPurchaseDeptName(purchaseDeptName);
            }
            record.setPaymentName(projectPaymentNameMap.get(record.getId()));
            record.setPaymentPeriodName(projectPaymentPeriodNameMap.get(record.getId()));
            record.setProjectPaymentList(paymentProjectIdGroup.get(record.getId()));
        }

        // 获取关联计划
        List<SrmProcurementProjectItem> relationPlanItems = srmProcurementProjectItemService.lambdaQuery()
                .in(SrmProcurementProjectItem::getProjectId, projectIds)
                .isNotNull(SrmProcurementProjectItem::getPlanId).list();
        if (CollectionUtils.isNotEmpty(relationPlanItems)) {
            List<Long> planIds = relationPlanItems.stream().map(SrmProcurementProjectItem::getPlanId).toList();
            List<SrmProcurementPlanEntity> plans = srmProcurementPlanService.listByIds(planIds);
            Map<Long, List<Long>> projectIdGroup = relationPlanItems.stream().collect(Collectors.groupingBy(SrmProcurementProjectItem::getProjectId,
                    Collectors.mapping(SrmProcurementProjectItem::getPlanId, Collectors.toList())));
            projects.forEach(project -> {
                if (projectIdGroup.containsKey(project.getId())) {
                    List<String> planCodes = plans.stream().filter(plan -> projectIdGroup.get(project.getId()).contains(plan.getId()))
                            .map(SrmProcurementPlanEntity::getPlanCode).toList();
                    project.setRelationPlanCodeList(planCodes);
                }
            });
        }

        // 青贮信息设置
        List<SrmProcurementProjectItem> projectItems = srmProcurementProjectItemService.lambdaQuery()
                .eq(SrmProcurementProjectItem::getSourcingType, BaseServiceTypeFieldService.BuyWayEnum.QZXJ)
                .in(SrmProcurementProjectItem::getProjectId, projectIds).list();
        if(CollectionUtils.isNotEmpty(projectItems)) {
            // 青贮设置物料信息
            Map<Long, SrmProcurementProjectItem> projectIdMapVo = projectItems.stream().filter(
                            info -> BaseServiceTypeFieldService.BuyWayEnum.QZXJ == info.getSourcingType())
                    .collect(Collectors.toMap(SrmProcurementProjectItem::getProjectId, Function.identity(), (x1, x2) -> x1));
            // 牧场
            List<Long> locationIds = projectItems.stream().map(SrmProcurementProjectItem::getUsageLocationId).distinct().toList();
            Map<Long, BaseUsageLocationEntity> locationEntityMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(locationIds)) {
                List<BaseUsageLocationEntity> baseUsageLocationEntities = baseUsageLocationService.listByIds(locationIds);
                locationEntityMap.putAll(Optional.ofNullable(baseUsageLocationEntities).stream().flatMap(Collection::stream)
                        .collect(Collectors.toMap(BaseUsageLocationEntity::getId, Function.identity())));
            }

            // 获取青贮信息
            List<SrmTenderNotice> tenderNoticeList = srmTenderNoticeService.lambdaQuery().in(SrmTenderNotice::getProjectId, projectIds)
                    .orderByDesc(SrmTenderNotice::getCreateTime)
                    .list();
            Map<Long, SrmTenderNotice> projectIdGroup = tenderNoticeList.stream().collect(
                    Collectors.toMap(SrmTenderNotice::getProjectId, Function.identity(), (x1, x2) -> x1));
            projects.forEach(project -> {
                SrmProcurementProjectItem projectItem = projectIdMapVo.get(project.getId());
                if (Objects.nonNull(projectItem)) {
                    project.setMaterialCode(projectItem.getMaterialCode());
                    project.setMaterialName(projectItem.getMaterialName());
                    project.setUsageLocationId(projectItem.getUsageLocationId());
                    project.setUnit(projectItem.getUnit());
                    project.setRequiredQuantity(projectItem.getRequiredQuantity());
                }
                BaseUsageLocationEntity baseUsageLocationEntity = locationEntityMap.get(project.getUsageLocationId());
                if (Objects.nonNull(baseUsageLocationEntity)) {
                    project.setUsageLocationRegion(baseUsageLocationEntity.getProvince() + "/" + baseUsageLocationEntity.getCity() + "/" + baseUsageLocationEntity.getDistrict());
                    project.setUsageLocationAddress(baseUsageLocationEntity.getAddress());
                    project.setUsageLocationName(baseUsageLocationEntity.getLocationName());
                }
                SrmTenderNotice srmTenderNotice = projectIdGroup.get(project.getId());
                if (Objects.nonNull(srmTenderNotice)) {
                    project.setInterceptPrice(srmTenderNotice.getInterceptPrice());
                    project.setContactPerson(srmTenderNotice.getContactPerson());
                    project.setContactPhone(srmTenderNotice.getContactPhone());
                    project.setCreateUser(srmTenderNotice.getCreateBy());
                    project.setCreateTime(srmTenderNotice.getCreateTime());
                    project.setTenderNotice(srmTenderNotice);
                }
            });
        }
    }

    /**
     * 项目负责人、服务类型名称、部门名称、支付方式名称、关联采购计划单号（使用审批流实例查询nodeStatus）
     */
    private void assembleDetailUsingProcessInstance(List<SrmProcurementProject> projects) {
        List<Long> projectIds = projects.stream().map(SrmProcurementProject::getId).toList();

        // 查询项目负责人
        List<SrmProjectMember> leaders = srmProjectMemberService.lambdaQuery().eq(SrmProjectMember::getMemberType, ProjectMemberTypeEnum.PROJECT_MEMBER)
                .eq(SrmProjectMember::getRole, ProjectMemberRoleEnum.PROJECT_LEADER)
                .in(SrmProjectMember::getBusinessId, projectIds).list();
        Map<Long, String> projectLeaderNameMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(leaders)) {
            List<Long> leaderUserIds = leaders.stream().map(SrmProjectMember::getUserId).toList();
            List<SysUser> users = sysUserService.lambdaQuery().in(SysUser::getUserId, leaderUserIds).list();
            Map<Long, String> userMap = users.stream().collect(Collectors.toMap(SysUser::getUserId, SysUser::getName));
            for (SrmProjectMember leader : leaders) {
                projectLeaderNameMap.put(leader.getBusinessId(), userMap.get(leader.getUserId()));
            }
        }

        // 获取服务类型名称
        List<Long> serviceTypeIds = projects.stream().map(SrmProcurementProject::getServiceTypeId).toList();
        Map<Long, String> serviceTypeNameMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(serviceTypeIds)) {
            List<BaseServiceTypeEntity> baseServiceTypeEntities = baseServiceTypeService.listByIds(serviceTypeIds);
            serviceTypeNameMap = baseServiceTypeEntities.stream().collect(Collectors.toMap(BaseServiceTypeEntity::getId, BaseServiceTypeEntity::getTypeName));
        }

        // 获取部门名称
        List<Long> allDeptIds = projects.stream().map(item -> {
            List<Long> deptIds = new ArrayList<>();
            if (StringUtils.isNotBlank(item.getPurchaseDeptId())) {
                List<Long> purchaseDeptIds = stream(StringUtils.split(item.getPurchaseDeptId(), ",")).map(Long::parseLong).toList();
                deptIds.addAll(purchaseDeptIds);
            }
            if (Objects.nonNull(item.getBuyerDeptId())) {
                deptIds.add(item.getBuyerDeptId());
            }
            return deptIds;
        }).flatMap(Collection::stream).toList();
        Map<Long, String> deptNameMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(allDeptIds)) {
            List<SysDept> sysDepts = sysDeptService.listByIds(allDeptIds);
            sysDepts.forEach(item -> deptNameMap.put(item.getDeptId(), item.getName()));
        }

        // 获取项目所有的支付方式名称
        List<SrmProcurementProjectPayment> paymentList = srmProcurementProjectPaymentService.lambdaQuery()
                .in(SrmProcurementProjectPayment::getProjectId, projectIds).list();
        Map<Long, List<SrmProcurementProjectPayment>> paymentProjectIdGroup = Optional.ofNullable(paymentList).stream()
                .flatMap(Collection::stream).collect(Collectors.groupingBy(SrmProcurementProjectPayment::getProjectId));
        Map<Long, String> paymentNameMap = Maps.newHashMap();
        Map<Long, String> paymentPeriodNameMap = Maps.newHashMap();
        Map<Long, String> projectPaymentNameMap = Maps.newHashMap();
        Map<Long, String> projectPaymentPeriodNameMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(paymentList)) {
            List<Long> paymentIds = paymentList.stream().map(SrmProcurementProjectPayment::getPaymentMethodId).toList();
            if (CollectionUtils.isNotEmpty(paymentIds)) {
                List<BasePaymentMethodEntity> paymentMethods = basePaymentMethodService.listByIds(paymentIds);
                paymentNameMap.putAll(paymentMethods.stream()
                        .collect(Collectors.toMap(
                                BasePaymentMethodEntity::getId,
                                BasePaymentMethodEntity::getMethodName
                        )));
                // 设置支付方式名称
                paymentList.forEach(item -> item.setPaymentMethodName(paymentNameMap.get(item.getPaymentMethodId())));
            }

            List<Long> paymentPeriodIds = paymentList.stream().map(SrmProcurementProjectPayment::getPaymentPeriodId).filter(Objects::nonNull).toList();
            if (CollectionUtils.isNotEmpty(paymentPeriodIds)) {
                List<BasePaymentMethodPeriodEntity> periodEntities = basePaymentMethodPeriodService.listByIds(paymentPeriodIds);
                periodEntities.forEach(item -> paymentPeriodNameMap.put(item.getId(), item.getPeriodName()));
            }
            projectPaymentNameMap = paymentList.stream()
                    .collect(Collectors.groupingBy(SrmProcurementProjectPayment::getProjectId,
                            Collectors.mapping(item -> paymentNameMap.get(item.getPaymentMethodId()), Collectors.joining(","))));
            projectPaymentPeriodNameMap = paymentList.stream()
                    .collect(Collectors.groupingBy(SrmProcurementProjectPayment::getProjectId,
                            Collectors.mapping(item -> paymentPeriodNameMap.get(item.getPaymentPeriodId()), Collectors.filtering(Objects::nonNull, Collectors.joining(",")))));
        }

        // 使用审批流实例查询nodeStatus
        this.setProcessStatusUsingProcessInstance(projects);
//        获取招标公告/邀请
        List<SrmTenderNotice> tenderNotice = srmTenderNoticeService.lambdaQuery()
                .in(SrmTenderNotice::getProjectId, projectIds)
//                .eq(SrmTenderNotice::getStatus, PublicNoticeStatusEnum.PUBLISHED)
                .list();
        Map<Long, List<SrmTenderNotice>> tenderNoticeMap = Maps.newHashMap();
        Map<Long, Boolean> needDepositMap = Maps.newHashMap();
        Map<Long, Boolean> needTenderFeeMap = Maps.newHashMap();

        if (CollectionUtils.isNotEmpty(tenderNotice)) {
            tenderNoticeMap = tenderNotice.stream().collect(Collectors.groupingBy(SrmTenderNotice::getProjectId));
            List<Long> noticeIds = tenderNotice.stream().map(SrmTenderNotice::getId).toList();
            needDepositMap.putAll(srmTenderRequirementService.isNeedRequirement(noticeIds, BidsSegmentTypeEnum.FEE));
            needTenderFeeMap.putAll(srmTenderRequirementService.isNeedRequirement(noticeIds, BidsSegmentTypeEnum.BID_DOCUMENT));
        }
        // 设置基础信息
        for (SrmProcurementProject record : projects) {
            if (tenderNoticeMap.containsKey(record.getId())) {
                List<SrmTenderNotice> projectNotices = tenderNoticeMap.get(record.getId());
                if (CollectionUtils.isNotEmpty(projectNotices)) {
                    projectNotices.stream().filter(e -> e.getStatus() == PublicNoticeStatusEnum.PUBLISHED).findAny().ifPresent(e -> {
                        record.setEffectNoticeId(e.getId());
                        record.setEffectNotice(e);
                        record.setNeedDeposit(needDepositMap.get(e.getId()));
                        record.setNeedTenderFee(needTenderFeeMap.get(e.getId()));
                    });
                }
            }


            record.setProjectLeaderName(projectLeaderNameMap.get(record.getId()));
            record.setServiceTypeName(serviceTypeNameMap.get(record.getServiceTypeId()));
            if (Objects.nonNull(record.getBuyerDeptId())) {
                record.setBuyerDeptName(deptNameMap.get(record.getBuyerDeptId()));
            }
            if (StringUtils.isNotBlank(record.getPurchaseDeptId())) {
                String purchaseDeptName = stream(StringUtils.split(record.getPurchaseDeptId(), ","))
                        .map(item -> deptNameMap.get(Long.parseLong(item))).collect(Collectors.joining(","));
                record.setPurchaseDeptName(purchaseDeptName);
            }
            record.setPaymentName(projectPaymentNameMap.get(record.getId()));
            record.setPaymentPeriodName(projectPaymentPeriodNameMap.get(record.getId()));
            record.setProjectPaymentList(paymentProjectIdGroup.get(record.getId()));
        }

        // 获取关联计划
        List<SrmProcurementProjectItem> relationPlanItems = srmProcurementProjectItemService.lambdaQuery()
                .in(SrmProcurementProjectItem::getProjectId, projectIds)
                .isNotNull(SrmProcurementProjectItem::getPlanId).list();
        if (CollectionUtils.isNotEmpty(relationPlanItems)) {
            List<Long> planIds = relationPlanItems.stream().map(SrmProcurementProjectItem::getPlanId).toList();
            List<SrmProcurementPlanEntity> plans = srmProcurementPlanMapper.queryIgnorePermission(Wrappers.<SrmProcurementPlanEntity>lambdaQuery().in(SrmProcurementPlanEntity::getId, planIds));
            Map<Long, List<Long>> projectIdGroup = relationPlanItems.stream().collect(Collectors.groupingBy(SrmProcurementProjectItem::getProjectId,
                    Collectors.mapping(SrmProcurementProjectItem::getPlanId, Collectors.toList())));

            projects.forEach(project -> {
                if (projectIdGroup.containsKey(project.getId())) {
                    List<String> planCodes = plans.stream().filter(plan -> projectIdGroup.get(project.getId()).contains(plan.getId()))
                            .map(SrmProcurementPlanEntity::getPlanCode).toList();
                    project.setRelationPlanCodeList(planCodes);
                }
            });
        }
        // 青贮信息设置
        List<SrmProcurementProjectItem> projectItems = srmProcurementProjectItemService.lambdaQuery()
                .eq(SrmProcurementProjectItem::getSourcingType, BaseServiceTypeFieldService.BuyWayEnum.QZXJ)
                .in(SrmProcurementProjectItem::getProjectId, projectIds).list();
        if(CollectionUtils.isNotEmpty(projectItems)){
            // 青贮设置物料信息
            Map<Long, SrmProcurementProjectItem> projectIdMapVo = projectItems.stream().filter(
                            info -> BaseServiceTypeFieldService.BuyWayEnum.QZXJ == info.getSourcingType())
                    .collect(Collectors.toMap(SrmProcurementProjectItem::getProjectId, Function.identity(), (x1, x2) -> x1));
            // 牧场
            List<Long> locationIds = projectItems.stream().map(SrmProcurementProjectItem::getUsageLocationId).distinct().toList();
            Map<Long, BaseUsageLocationEntity> locationEntityMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(locationIds)) {
                List<BaseUsageLocationEntity> baseUsageLocationEntities = baseUsageLocationService.listByIds(locationIds);
                locationEntityMap.putAll(Optional.ofNullable(baseUsageLocationEntities).stream().flatMap(Collection::stream)
                        .collect(Collectors.toMap(BaseUsageLocationEntity::getId, Function.identity())));
            }

            // 获取青贮信息
            List<SrmTenderNotice> tenderNoticeList = srmTenderNoticeService.lambdaQuery().in(SrmTenderNotice::getProjectId, projectIds)
                    .orderByDesc(SrmTenderNotice::getCreateTime)
                    .list();
            Map<Long, SrmTenderNotice> projectIdGroup = tenderNoticeList.stream().collect(
                    Collectors.toMap(SrmTenderNotice::getProjectId,Function.identity(), (x1, x2) -> x1));
            projects.forEach(project -> {
                SrmProcurementProjectItem projectItem = projectIdMapVo.get(project.getId());
                if (Objects.nonNull(projectItem)) {
                    project.setMaterialCode(projectItem.getMaterialCode());
                    project.setMaterialName(projectItem.getMaterialName());
                    project.setUsageLocationId(projectItem.getUsageLocationId());
                    project.setUnit(projectItem.getUnit());
                    project.setRequiredQuantity(projectItem.getRequiredQuantity());
                }
                BaseUsageLocationEntity baseUsageLocationEntity = locationEntityMap.get(project.getUsageLocationId());
                if (Objects.nonNull(baseUsageLocationEntity)) {
                    project.setUsageLocationRegion(baseUsageLocationEntity.getProvince() + "/" + baseUsageLocationEntity.getCity() + "/" + baseUsageLocationEntity.getDistrict());
                    project.setUsageLocationAddress(baseUsageLocationEntity.getAddress());
                    project.setUsageLocationName(baseUsageLocationEntity.getLocationName());
                }
                SrmTenderNotice srmTenderNotice = projectIdGroup.get(project.getId());
                if (Objects.nonNull(srmTenderNotice)) {
                    project.setInterceptPrice(srmTenderNotice.getInterceptPrice());
                    project.setContactPerson(srmTenderNotice.getContactPerson());
                    project.setContactPhone(srmTenderNotice.getContactPhone());
                    project.setCreateUser(srmTenderNotice.getCreateBy());
                    project.setCreateTime(srmTenderNotice.getCreateTime());
                    project.setTenderNotice(srmTenderNotice);
                    project.setExecutorId(srmTenderNotice.getExecutorId());
                    project.setDemandName(srmTenderNotice.getNoticeTitle());
                }
            });

        }
    }

    /**
     * 设置项目流程状态
     * 根据主流程表SrmProcurementProject的子表审核状态来规划返回一个统一的状态
     * 状态优先级：中标公告 > 中标公示 > 定标 > 流标 > 变更 > 招标公告
     */
    private void setProcessStatus(SrmProcurementProject record, ProjectContext projectContext) {
        Long projectId = record.getId();

        // 获取各子表数据
        List<SrmProcurementProjectSection> sections = projectContext.getSection(projectId);
        List<SrmTenderNotice> tenderNotice = projectContext.getTenderNotice(projectId);
        List<SrmTenderNoticeChange> changes = projectContext.getChanges(projectId);
        List<SrmTenderFailedLog> failedLogs = projectContext.getFailedLogs(projectId);
        List<SrmTenderEvaluationResult> evaluationResults = projectContext.getResult(projectId);

        NodeStatusResult result = new NodeStatusResult(SrmProcurementProject.NodeStatus.NULL);

        // 1. 招标公告状态
        result = determineNoticeStatusWithBizKey(tenderNotice, result);

        // 4.竞谈文件状态
        result = determineTenderDocStatusWithBizKey(tenderNotice, result);

        // 2. 变更状态（取最新的变更记录）
        result = determineChangeStatusWithBizKey(changes, result);

        // 3. 按标段处理流标、定标、公示、公告状态
        result = determineSectionStatusWithBizKey(sections, failedLogs, evaluationResults, result, record);

        record.setNodeStatus(result.getNodeStatus());
        record.setBizKey(result.getBizKey());
    }

    private NodeStatusResult determineTenderDocStatusWithBizKey(List<SrmTenderNotice> tenderNotice, NodeStatusResult currentResult) {
        if (CollectionUtils.isEmpty(tenderNotice)) {
            return currentResult;
        }

        // 取最新的招标公告记录
        SrmTenderNotice latestNotice = tenderNotice.stream()
                .max(Comparator.comparing(SrmTenderNotice::getCreateTime))
                .orElse(null);

        if (latestNotice == null || latestNotice.getDocStatus() == null) {
            return currentResult;
        }

        SrmProcurementProject.NodeStatus newStatus = switch (latestNotice.getDocStatus()) {
            case APPROVE -> SrmProcurementProject.NodeStatus.TENDER_DOC_APPROVED;
            case APPROVE_REVOKE -> SrmProcurementProject.NodeStatus.TENDER_DOC_REVOKE;
            case APPROVE_REJECT -> SrmProcurementProject.NodeStatus.TENDER_DOC_REJECTED;
            case TO_APPROVE, APPROVING -> SrmProcurementProject.NodeStatus.TENDER_DOC_AUDITING;
            default -> currentResult.getNodeStatus();
        };

        // 如果状态发生变化，更新bizKey
        if (newStatus != currentResult.getNodeStatus()) {
            return new NodeStatusResult(newStatus, String.valueOf(latestNotice.getId()));
        }

        return currentResult;
    }

    /**
     * 设置项目流程状态（使用审批流实例查询）
     * 根据审批流实例状态来确定nodeStatus
     */
    private void setProcessStatusUsingProcessInstance(List<SrmProcurementProject> projects) {
        if (CollectionUtils.isEmpty(projects)) {
            return;
        }

        // 获取项目ID集合
        Set<Long> projectIds = projects.stream().map(SrmProcurementProject::getId).collect(Collectors.toSet());

        // 获取审批相关的业务类型（排除邀请供应商审核，因为邀请函和公告都是tenderNotice对象）
        Set<SrmProcessConfigService.BizTypeEnum> approveTypes = new HashSet<>(SrmProcessConfigService.BizTypeEnum.getApproveTypes());
        approveTypes.remove(SrmProcessConfigService.BizTypeEnum.SRM_INVITE_SUPPLIER_AUDIT);

        // 查询审批流实例
        ProcessInstanceGroupResp processInstanceGroup = processInstanceService.getInstancesGroupByBizIdAndType(projectIds, approveTypes);

        // 为每个项目设置nodeStatus
        for (SrmProcurementProject project : projects) {
            NodeStatusResult result = determineNodeStatusFromProcessInstance(project.getId(), processInstanceGroup, project.getInviteMethod());
            project.setNodeStatus(result.getNodeStatus());
            project.setBizKey(result.getBizKey());
        }
    }

    /**
     * 根据审批流实例确定nodeStatus
     * @param projectId 项目ID
     * @param processInstanceGroup 审批流实例分组
     * @param inviteMethod 邀请方式，用于区分邀请函和公告
     * @return nodeStatus结果
     */
    private NodeStatusResult determineNodeStatusFromProcessInstance(Long projectId, ProcessInstanceGroupResp processInstanceGroup, InviteMethodEnum inviteMethod) {
        // 按优先级检查各种审批状态（优先级低的先检查，高的后检查，这样高优先级会覆盖低优先级）
        // 优先级：邀请函/公告 < 招标/竞谈文件 < 变更 < 流标 < 定标 < 中标公示 < 中标公告

        NodeStatusResult result = new NodeStatusResult(SrmProcurementProject.NodeStatus.NULL);

        // 1. 检查招标公告/邀请函审批（优先级最低）
        NodeStatusResult noticeResult = checkProcessInstanceStatus(projectId, SrmProcessConfigService.BizTypeEnum.SRM_TENDER_NOTICE_AUDIT,
                processInstanceGroup, SrmProcurementProject.NodeStatus.NOTICE_AUDITING,
                SrmProcurementProject.NodeStatus.NOTICE_APPROVED, SrmProcurementProject.NodeStatus.NOTICE_REJECTED,
                SrmProcurementProject.NodeStatus.NOTICE_REVOKE);
        if (noticeResult.getNodeStatus() != SrmProcurementProject.NodeStatus.NULL) {
            // 根据inviteMethod区分邀请函和公告
            if (inviteMethod == InviteMethodEnum.INVITE) {
                // 邀请方式：转换为邀请函状态
                SrmProcurementProject.NodeStatus inviteStatus = convertNoticeStatusToInviteStatus(noticeResult.getNodeStatus());
                result = new NodeStatusResult(inviteStatus, noticeResult.getBizKey());
            } else {
                // 公开方式：保持公告状态
                result = noticeResult;
            }
        }

        // 青贮审批
        NodeStatusResult ensilageResult = checkProcessInstanceStatus(projectId, SrmProcessConfigService.BizTypeEnum.SRM_TENDER_ENSILAGE_AUDIT,
                processInstanceGroup, SrmProcurementProject.NodeStatus.ENSILAGE_AUDITING,
                SrmProcurementProject.NodeStatus.ENSILAGE_APPROVED, SrmProcurementProject.NodeStatus.ENSILAGE_REJECTED,
                SrmProcurementProject.NodeStatus.ENSILAGE_REVOKE);
        if (ensilageResult.getNodeStatus() != SrmProcurementProject.NodeStatus.NULL) {
            result = ensilageResult;
        }

        NodeStatusResult tenderDocResult = checkProcessInstanceStatus(projectId, SrmProcessConfigService.BizTypeEnum.SRM_TENDER_DOC_AUDIT,
                processInstanceGroup, SrmProcurementProject.NodeStatus.TENDER_DOC_AUDITING,
                SrmProcurementProject.NodeStatus.TENDER_DOC_APPROVED, SrmProcurementProject.NodeStatus.TENDER_DOC_REJECTED,
                SrmProcurementProject.NodeStatus.TENDER_DOC_REVOKE);
        if (tenderDocResult.getNodeStatus() != SrmProcurementProject.NodeStatus.NULL) {
            result = tenderDocResult;
        }


        // 2. 检查变更审批
        NodeStatusResult changeResult = checkProcessInstanceStatus(projectId, SrmProcessConfigService.BizTypeEnum.SRM_CHANGE_AUDIT,
                processInstanceGroup, SrmProcurementProject.NodeStatus.CHANGE_AUDITING,
                SrmProcurementProject.NodeStatus.CHANGE_APPROVED, SrmProcurementProject.NodeStatus.CHANGE_REJECTED,
                SrmProcurementProject.NodeStatus.CHANGE_REVOKE);
        if (changeResult.getNodeStatus() != SrmProcurementProject.NodeStatus.NULL) {
            result = changeResult;
        }

        // 3. 检查流标审批
        NodeStatusResult failedResult = checkProcessInstanceStatus(projectId, SrmProcessConfigService.BizTypeEnum.SRM_TENDER_FAILED_AUDIT,
                processInstanceGroup, SrmProcurementProject.NodeStatus.FAILED_AUDITING,
                SrmProcurementProject.NodeStatus.FAILED_APPROVED, SrmProcurementProject.NodeStatus.FAILED_REJECTED,
                SrmProcurementProject.NodeStatus.FAILED_REVOKE);
        if (failedResult.getNodeStatus() != SrmProcurementProject.NodeStatus.NULL) {
            result = failedResult;
        }

        // 4. 检查定标审批
        NodeStatusResult awardResult = checkProcessInstanceStatus(projectId, SrmProcessConfigService.BizTypeEnum.SRM_TENDER_BID_AUDIT,
                processInstanceGroup, SrmProcurementProject.NodeStatus.AWARD_AUDITING,
                SrmProcurementProject.NodeStatus.AWARD_APPROVED, SrmProcurementProject.NodeStatus.AWARD_REJECTED,
                SrmProcurementProject.NodeStatus.AWARD_REVOKE);
        if (awardResult.getNodeStatus() != SrmProcurementProject.NodeStatus.NULL) {
            result = awardResult;
        }

        // 5. 检查中标公示审批
        NodeStatusResult publicityResult = checkProcessInstanceStatus(projectId, SrmProcessConfigService.BizTypeEnum.SRM_PUBLICITY_AUDITING,
                processInstanceGroup, SrmProcurementProject.NodeStatus.PUBLICITY_AUDITING,
                SrmProcurementProject.NodeStatus.PUBLICITY_APPROVED, SrmProcurementProject.NodeStatus.PUBLICITY_REJECTED,
                SrmProcurementProject.NodeStatus.PUBLICITY_REVOKE);
        if (publicityResult.getNodeStatus() != SrmProcurementProject.NodeStatus.NULL) {
            result = publicityResult;
        }

        // 6. 检查中标公告审批（优先级最高）
        NodeStatusResult publicNoticeResult = checkProcessInstanceStatus(projectId, SrmProcessConfigService.BizTypeEnum.SRM_BID_NOTICE_AUDITING,
                processInstanceGroup, SrmProcurementProject.NodeStatus.PUBLIC_NOTICE_AUDITING,
                SrmProcurementProject.NodeStatus.PUBLIC_NOTICE_APPROVED, SrmProcurementProject.NodeStatus.PUBLIC_NOTICE_REJECTED,
                SrmProcurementProject.NodeStatus.PUBLIC_NOTICE_REVOKE);
        if (publicNoticeResult.getNodeStatus() != SrmProcurementProject.NodeStatus.NULL) {
            result = publicNoticeResult;
        }

        return result;
    }

    /**
     * 将公告状态转换为邀请函状态
     * @param noticeStatus 公告状态
     * @return 邀请函状态
     */
    private SrmProcurementProject.NodeStatus convertNoticeStatusToInviteStatus(SrmProcurementProject.NodeStatus noticeStatus) {
        return switch (noticeStatus) {
            case NOTICE_AUDITING -> SrmProcurementProject.NodeStatus.INVITE_AUDITING;
            case NOTICE_APPROVED -> SrmProcurementProject.NodeStatus.INVITE_APPROVED;
            case NOTICE_REJECTED -> SrmProcurementProject.NodeStatus.INVITE_REJECTED;
            case NOTICE_REVOKE -> SrmProcurementProject.NodeStatus.INVITE_REVOKE;
            default -> noticeStatus;
        };
    }

    /**
     * 检查特定业务类型的审批流实例状态
     * @param projectId 项目ID
     * @param bizType 业务类型
     * @param processInstanceGroup 审批流实例分组
     * @param auditingStatus 审批中状态
     * @param approvedStatus 审批通过状态
     * @param rejectedStatus 审批驳回状态
     * @param revokeStatus 审批撤回状态
     * @return nodeStatus结果
     */
    private NodeStatusResult checkProcessInstanceStatus(Long projectId, SrmProcessConfigService.BizTypeEnum bizType,
                                                        ProcessInstanceGroupResp processInstanceGroup,
                                                        SrmProcurementProject.NodeStatus auditingStatus,
                                                        SrmProcurementProject.NodeStatus approvedStatus,
                                                        SrmProcurementProject.NodeStatus rejectedStatus,
                                                        SrmProcurementProject.NodeStatus revokeStatus) {
        List<SrmProcessInstance> instances = processInstanceGroup.getInstancesByBizIdAndType(projectId, bizType);
        if (CollectionUtils.isEmpty(instances)) {
            return new NodeStatusResult(SrmProcurementProject.NodeStatus.NULL);
        }

        // 取最新的实例
        SrmProcessInstance latestInstance = instances.stream()
                .max(Comparator.comparing(SrmProcessInstance::getCreateTime))
                .orElse(null);

        if (latestInstance == null) {
            return new NodeStatusResult(SrmProcurementProject.NodeStatus.NULL);
        }

        // 根据审批状态映射到nodeStatus
        ApproveStatusEnum approvalStatus = latestInstance.getApprovalStatus();
        SrmProcurementProject.NodeStatus nodeStatus = switch (approvalStatus) {
            case APPROVING -> auditingStatus;
            case APPROVE -> approvedStatus;
            case APPROVE_REJECT -> rejectedStatus;
            case APPROVE_REVOKE -> revokeStatus;
            default -> SrmProcurementProject.NodeStatus.NULL;
        };

        return new NodeStatusResult(nodeStatus, latestInstance.getBizKey());
    }

    /**
     * 确定招标公告状态（包含bizKey）
     */
    private NodeStatusResult determineNoticeStatusWithBizKey(List<SrmTenderNotice> tenderNotice, NodeStatusResult currentResult) {
        if (CollectionUtils.isEmpty(tenderNotice)) {
            return currentResult;
        }

        // 取最新的招标公告记录
        SrmTenderNotice latestNotice = tenderNotice.stream()
                .max(Comparator.comparing(SrmTenderNotice::getCreateTime))
                .orElse(null);

        if (latestNotice == null) {
            return currentResult;
        }

        SrmProcurementProject.NodeStatus newStatus = switch (latestNotice.getNoticeStatus()) {
            case APPROVE -> SrmProcurementProject.NodeStatus.NOTICE_APPROVED;
            case APPROVE_REVOKE -> SrmProcurementProject.NodeStatus.NOTICE_REVOKE;
            case APPROVE_REJECT -> SrmProcurementProject.NodeStatus.NOTICE_REJECTED;
            case TO_APPROVE, APPROVING -> SrmProcurementProject.NodeStatus.NOTICE_AUDITING;
            default -> currentResult.getNodeStatus();
        };

        // 如果状态发生变化，更新bizKey
        if (newStatus != currentResult.getNodeStatus()) {
            return new NodeStatusResult(newStatus, String.valueOf(latestNotice.getId()));
        }

        return currentResult;
    }

    /**
     * 确定变更状态（包含bizKey）
     */
    private NodeStatusResult determineChangeStatusWithBizKey(List<SrmTenderNoticeChange> changes, NodeStatusResult currentResult) {
        if (CollectionUtils.isEmpty(changes)) {
            return currentResult;
        }

        // 取最新的变更记录
        SrmTenderNoticeChange latestChange = changes.stream()
                .max(Comparator.comparing(SrmTenderNoticeChange::getCreateTime))
                .orElse(null);

        if (latestChange == null) {
            return currentResult;
        }

        SrmProcurementProject.NodeStatus newStatus = switch (latestChange.getApprovedStatus()) {
            case APPROVE -> SrmProcurementProject.NodeStatus.CHANGE_APPROVED;
            case APPROVE_REVOKE -> SrmProcurementProject.NodeStatus.CHANGE_REVOKE;
            case APPROVE_REJECT -> SrmProcurementProject.NodeStatus.CHANGE_REJECTED;
            case TO_APPROVE, APPROVING -> SrmProcurementProject.NodeStatus.CHANGE_AUDITING;
            default -> currentResult.getNodeStatus();
        };

        // 如果状态发生变化，更新bizKey
        if (newStatus != currentResult.getNodeStatus()) {
            return new NodeStatusResult(newStatus, String.valueOf(latestChange.getId()));
        }

        return currentResult;
    }

    /**
     * 确定标段相关状态（包含bizKey）
     * 按优先级处理：中标公告 > 中标公示 > 定标 > 流标
     */
    private NodeStatusResult determineSectionStatusWithBizKey(List<SrmProcurementProjectSection> sections,
                                                              List<SrmTenderFailedLog> failedLogs,
                                                              List<SrmTenderEvaluationResult> evaluationResults,
                                                              NodeStatusResult currentResult,
                                                              SrmProcurementProject record) {
        if (CollectionUtils.isEmpty(sections)) {
            return currentResult;
        }

        NodeStatusResult result = currentResult;

        // 处理流标状态
        result = determineFailedStatusWithBizKey(failedLogs, result, record);

        // 处理定标、公示、公告状态
        result = determineEvaluationStatusWithBizKey(evaluationResults, result);

        return result;
    }

    /**
     * 确定流标状态（包含bizKey）
     */
    private NodeStatusResult determineFailedStatusWithBizKey(List<SrmTenderFailedLog> sectionFailedLogs,
                                                             NodeStatusResult currentResult,
                                                             SrmProcurementProject record) {
        if (CollectionUtils.isEmpty(sectionFailedLogs)) {
            return currentResult;
        }

        // 按优先级检查状态：审核中>审核拒绝>审核通过>审核撤回
        if (sectionFailedLogs.stream().anyMatch(log -> ApproveStatusEnum.TO_APPROVE.equals(log.getApprovalStatus()))) {
            SrmTenderFailedLog srmTenderFailedLog = sectionFailedLogs.stream()
                    .filter(log -> ApproveStatusEnum.TO_APPROVE.equals(log.getApprovalStatus()))
                    .findFirst().orElse(null);
            if (srmTenderFailedLog != null) {
                record.setFailedNoticeId(srmTenderFailedLog.getId());
                return new NodeStatusResult(SrmProcurementProject.NodeStatus.FAILED_AUDITING, String.valueOf(srmTenderFailedLog.getId()));
            }
        }
        if (sectionFailedLogs.stream().anyMatch(log -> ApproveStatusEnum.APPROVE_REJECT.equals(log.getApprovalStatus()))) {
            SrmTenderFailedLog srmTenderFailedLog = sectionFailedLogs.stream()
                    .filter(log -> ApproveStatusEnum.APPROVE_REJECT.equals(log.getApprovalStatus()))
                    .findFirst().orElse(null);
            if (srmTenderFailedLog != null) {
                record.setFailedNoticeId(srmTenderFailedLog.getId());
                return new NodeStatusResult(SrmProcurementProject.NodeStatus.FAILED_REJECTED, String.valueOf(srmTenderFailedLog.getId()));
            }
        }
        if (sectionFailedLogs.stream().anyMatch(log -> ApproveStatusEnum.APPROVE_REVOKE.equals(log.getApprovalStatus()))) {
            SrmTenderFailedLog srmTenderFailedLog = sectionFailedLogs.stream()
                    .filter(log -> ApproveStatusEnum.APPROVE_REVOKE.equals(log.getApprovalStatus()))
                    .findFirst().orElse(null);
            if (srmTenderFailedLog != null) {
                record.setFailedNoticeId(srmTenderFailedLog.getId());
                return new NodeStatusResult(SrmProcurementProject.NodeStatus.FAILED_REVOKE, String.valueOf(srmTenderFailedLog.getId()));
            }
        }
        if (sectionFailedLogs.stream().anyMatch(log -> ApproveStatusEnum.APPROVE.equals(log.getApprovalStatus()))) {
            SrmTenderFailedLog srmTenderFailedLog = sectionFailedLogs.stream()
                    .filter(log -> ApproveStatusEnum.APPROVE.equals(log.getApprovalStatus()))
                    .findFirst().orElse(null);
            if (srmTenderFailedLog != null) {
                record.setFailedNoticeId(srmTenderFailedLog.getId());
                return new NodeStatusResult(SrmProcurementProject.NodeStatus.FAILED_APPROVED, String.valueOf(srmTenderFailedLog.getId()));
            }
        }

        return currentResult;
    }

    /**
     * 确定评标结果相关状态（包含bizKey）
     * 按优先级处理：中标公告 > 中标公示 > 定标
     */
    private NodeStatusResult determineEvaluationStatusWithBizKey(List<SrmTenderEvaluationResult> sectionResults,
                                                                 NodeStatusResult currentResult) {
        if (CollectionUtils.isEmpty(sectionResults)) {
            return currentResult;
        }

        // 优先级最高：中标公告状态
        NodeStatusResult noticeResult = determineNoticeAuditStatusWithBizKey(sectionResults);
        if (noticeResult.getNodeStatus() != currentResult.getNodeStatus()) {
            return noticeResult;
        }

        // 优先级次高：中标公示状态
        NodeStatusResult publicityResult = determinePublicityAuditStatusWithBizKey(sectionResults);
        if (publicityResult.getNodeStatus() != currentResult.getNodeStatus()) {
            return publicityResult;
        }

        // 优先级最低：定标状态
        NodeStatusResult awardResult = determineAwardReportStatusWithBizKey(sectionResults);
        if (awardResult.getNodeStatus() != currentResult.getNodeStatus()) {
            return awardResult;
        }

        return currentResult;
    }

    /**
     * 确定定标报告状态（包含bizKey）
     */
    private NodeStatusResult determineAwardReportStatusWithBizKey(List<SrmTenderEvaluationResult> sectionResults) {
        // 按优先级检查状态：待审批 > 撤回 > 驳回 > 通过
        Optional<SrmTenderEvaluationResult> toApproveResult = sectionResults.stream()
                .filter(result -> ApproveStatusEnum.TO_APPROVE.equals(result.getAwardReportStatus()))
                .findFirst();
        if (toApproveResult.isPresent()) {
            return new NodeStatusResult(SrmProcurementProject.NodeStatus.AWARD_AUDITING, String.valueOf(toApproveResult.get().getId()));
        }

        Optional<SrmTenderEvaluationResult> revokeResult = sectionResults.stream()
                .filter(result -> ApproveStatusEnum.APPROVE_REVOKE.equals(result.getAwardReportStatus()))
                .findFirst();
        if (revokeResult.isPresent()) {
            return new NodeStatusResult(SrmProcurementProject.NodeStatus.AWARD_REVOKE, String.valueOf(revokeResult.get().getId()));
        }

        Optional<SrmTenderEvaluationResult> rejectResult = sectionResults.stream()
                .filter(result -> ApproveStatusEnum.APPROVE_REJECT.equals(result.getAwardReportStatus()))
                .findFirst();
        if (rejectResult.isPresent()) {
            return new NodeStatusResult(SrmProcurementProject.NodeStatus.AWARD_REJECTED, String.valueOf(rejectResult.get().getId()));
        }

        Optional<SrmTenderEvaluationResult> approveResult = sectionResults.stream()
                .filter(result -> ApproveStatusEnum.APPROVE.equals(result.getAwardReportStatus()))
                .findFirst();
        if (approveResult.isPresent()) {
            return new NodeStatusResult(SrmProcurementProject.NodeStatus.AWARD_APPROVED, String.valueOf(approveResult.get().getId()));
        }

        return new NodeStatusResult(SrmProcurementProject.NodeStatus.NULL);
    }

    /**
     * 确定中标公示审核状态（包含bizKey）
     */
    private NodeStatusResult determinePublicityAuditStatusWithBizKey(List<SrmTenderEvaluationResult> sectionResults) {
        // 按优先级检查状态：审批中 > 撤回 > 驳回 > 通过
        Optional<SrmTenderEvaluationResult> auditingResult = sectionResults.stream()
                .filter(result -> ApproveStatusEnum.APPROVING.equals(result.getPublicityAuditStatus()))
                .findFirst();
        if (auditingResult.isPresent()) {
            return new NodeStatusResult(SrmProcurementProject.NodeStatus.PUBLICITY_AUDITING, String.valueOf(auditingResult.get().getId()));
        }

        Optional<SrmTenderEvaluationResult> revokeResult = sectionResults.stream()
                .filter(result -> ApproveStatusEnum.APPROVE_REVOKE.equals(result.getPublicityAuditStatus()))
                .findFirst();
        if (revokeResult.isPresent()) {
            return new NodeStatusResult(SrmProcurementProject.NodeStatus.PUBLICITY_REVOKE, String.valueOf(revokeResult.get().getId()));
        }

        Optional<SrmTenderEvaluationResult> rejectResult = sectionResults.stream()
                .filter(result -> ApproveStatusEnum.APPROVE_REJECT.equals(result.getPublicityAuditStatus()))
                .findFirst();
        if (rejectResult.isPresent()) {
            return new NodeStatusResult(SrmProcurementProject.NodeStatus.PUBLICITY_REJECTED, String.valueOf(rejectResult.get().getId()));
        }

        Optional<SrmTenderEvaluationResult> approveResult = sectionResults.stream()
                .filter(result -> ApproveStatusEnum.APPROVE.equals(result.getPublicityAuditStatus()))
                .findFirst();
        if (approveResult.isPresent()) {
            return new NodeStatusResult(SrmProcurementProject.NodeStatus.PUBLICITY_APPROVED, String.valueOf(approveResult.get().getId()));
        }

        return new NodeStatusResult(SrmProcurementProject.NodeStatus.NULL);
    }

    /**
     * 确定中标公告审核状态（包含bizKey）
     */
    private NodeStatusResult determineNoticeAuditStatusWithBizKey(List<SrmTenderEvaluationResult> sectionResults) {
        // 按优先级检查状态：审批中 > 驳回 > 撤回 > 通过
        Optional<SrmTenderEvaluationResult> auditingResult = sectionResults.stream()
                .filter(result -> ApproveStatusEnum.APPROVING.equals(result.getNoticeAuditStatus()))
                .findFirst();
        if (auditingResult.isPresent()) {
            return new NodeStatusResult(SrmProcurementProject.NodeStatus.PUBLIC_NOTICE_AUDITING, String.valueOf(auditingResult.get().getId()));
        }

        Optional<SrmTenderEvaluationResult> rejectResult = sectionResults.stream()
                .filter(result -> ApproveStatusEnum.APPROVE_REJECT.equals(result.getNoticeAuditStatus()))
                .findFirst();
        if (rejectResult.isPresent()) {
            return new NodeStatusResult(SrmProcurementProject.NodeStatus.PUBLIC_NOTICE_REJECTED, String.valueOf(rejectResult.get().getId()));
        }

        Optional<SrmTenderEvaluationResult> revokeResult = sectionResults.stream()
                .filter(result -> ApproveStatusEnum.APPROVE_REVOKE.equals(result.getNoticeAuditStatus()))
                .findFirst();
        if (revokeResult.isPresent()) {
            return new NodeStatusResult(SrmProcurementProject.NodeStatus.PUBLIC_NOTICE_REVOKE, String.valueOf(revokeResult.get().getId()));
        }

        Optional<SrmTenderEvaluationResult> approveResult = sectionResults.stream()
                .filter(result -> ApproveStatusEnum.APPROVE.equals(result.getNoticeAuditStatus()))
                .findFirst();
        if (approveResult.isPresent()) {
            return new NodeStatusResult(SrmProcurementProject.NodeStatus.PUBLIC_NOTICE_APPROVED, String.valueOf(approveResult.get().getId()));
        }

        return new NodeStatusResult(SrmProcurementProject.NodeStatus.NULL);
    }


    /**
     * 节点状态结果封装类
     */
    @Getter
    @Setter
    @AllArgsConstructor
    public static class NodeStatusResult {
        private SrmProcurementProject.NodeStatus nodeStatus;
        private String bizKey;

        public NodeStatusResult(SrmProcurementProject.NodeStatus nodeStatus) {
            this.nodeStatus = nodeStatus;
            this.bizKey = null;
        }
    }

    @Getter
    @Setter
    @AllArgsConstructor
    public static class ProjectContext {
        private Map<Long, List<SrmProcurementProjectSection>> sectionMap;
        //        private Map<Long, List<SrmTenderAwardNotice>> awardNoticesMap;
        private Map<Long, List<SrmTenderNotice>> tenderNoticeMap;
        private Map<Long, List<SrmTenderNoticeChange>> changeMap;
        private Map<Long, List<SrmTenderFailedLog>> failedLogsMap;
        private Map<Long, List<SrmTenderEvaluationResult>> resultMap;


        List<SrmProcurementProjectSection> getSection(Long projectId) {
            return this.sectionMap.get(projectId);
        }

//        List<SrmTenderAwardNotice> getAwardNotice(Long projectId) {
//            return this.awardNoticesMap.get(projectId);
//        }

        List<SrmTenderNotice> getTenderNotice(Long projectId) {
            return this.tenderNoticeMap.get(projectId);
        }

        List<SrmTenderNoticeChange> getChanges(Long projectId) {
            return this.changeMap.get(projectId);
        }

        List<SrmTenderFailedLog> getFailedLogs(Long projectId) {
            return this.failedLogsMap.get(projectId);
        }

        List<SrmTenderEvaluationResult> getResult(Long projectId) {
            return this.resultMap.get(projectId);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        SrmProcurementProject project = this.getById(id);
        ExceptionUtil.checkNonNull(project, "项目不存在");
        ExceptionUtil.check(project.getStatus() != DocumentStatus.NEW, GlobalResultCode.INVALID_STATE, "项目不是新建状态");
        ExceptionUtil.check(project.getProgressStatus() != ProjectProgressStatusEnum.INIT, GlobalResultCode.INVALID_STATE, "项目进度不是初始状态");
        ExceptionUtil.check(project.getApproveStatus() == ApproveStatusEnum.APPROVE || project.getApproveStatus() == ApproveStatusEnum.APPROVING,
                GlobalResultCode.INVALID_STATE, "项目审批中或已审批通过");
        this.removeById(id);
        srmProcurementProjectSectionService.lambdaUpdate().eq(SrmProcurementProjectSection::getProjectId, id).remove();
        srmProcurementProjectItemService.lambdaUpdate().eq(SrmProcurementProjectItem::getProjectId, id).remove();
        srmProcurementProjectFieldValueService.lambdaUpdate().eq(SrmProcurementProjectFieldValue::getProjectId, id).remove();
        srmProcurementProjectPaymentService.lambdaUpdate().eq(SrmProcurementProjectPayment::getProjectId, id).remove();
        srmProjectMemberService.lambdaUpdate().eq(SrmProjectMember::getBusinessId, id)
                .eq(SrmProjectMember::getMemberType, ProjectMemberTypeEnum.PROJECT_MEMBER).remove();
        srmProjectAttachmentService.lambdaUpdate().eq(SrmProjectAttachment::getBusinessId, id)
                .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.PROJECT).remove();
    }


    @Override
    public boolean checkStatus(String projectCode, DocumentStatus newDocStatus, ApproveStatusEnum newApproveStatus, ProjectProgressStatusEnum newProgressStatus, boolean throwEx) {
        SrmProcurementProject project = this.getByProjectCode(projectCode);
        boolean result = true;
        if (newDocStatus != null) {
            result = switch (newDocStatus) {
                case NEW -> false;
                case EFFECT -> project.getStatus() == DocumentStatus.NEW;
                case CANCEL -> project.getStatus() == DocumentStatus.EFFECT;
            };
            ExceptionUtil.check(throwEx && !result, GlobalResultCode.INVALID_STATE, "项目状态无法改为" + newDocStatus);
        }
        if (newApproveStatus != null) {
            result = switch (newApproveStatus) {
                case TO_APPROVE -> false;
                case APPROVING -> project.getApproveStatus() == ApproveStatusEnum.TO_APPROVE
                        || project.getApproveStatus() == ApproveStatusEnum.APPROVE_REJECT
                        || project.getApproveStatus() == ApproveStatusEnum.APPROVE_REVOKE;
                case APPROVE, APPROVE_REJECT, APPROVE_REVOKE ->
                        project.getApproveStatus() == ApproveStatusEnum.APPROVING;
            };
            ExceptionUtil.check(throwEx && !result, GlobalResultCode.INVALID_STATE, "项目审批状态无法改为" + newApproveStatus);
        }
        if (newProgressStatus != null) {
            result = switch (newProgressStatus) {
                case INIT -> false;
                case TO_NOTICE -> project.getProgressStatus() == ProjectProgressStatusEnum.INIT;
                case NOTICE -> project.getProgressStatus() == ProjectProgressStatusEnum.TO_NOTICE;
                case TENDER_DOC -> project.getProgressStatus() == ProjectProgressStatusEnum.NOTICE;
                case REGISTER -> project.getProgressStatus() == ProjectProgressStatusEnum.NOTICE
                        || project.getProgressStatus() == ProjectProgressStatusEnum.TENDER_DOC;
                case QUOTING -> project.getProgressStatus() == ProjectProgressStatusEnum.REGISTER
                        || project.getProgressStatus() == ProjectProgressStatusEnum.NOTICE
                        || project.getProgressStatus() == ProjectProgressStatusEnum.TENDER_DOC
                        || project.getProgressStatus() == ProjectProgressStatusEnum.BID_OPENED; // 再次报价
                case TO_BID_OPEN -> project.getProgressStatus() == ProjectProgressStatusEnum.QUOTING;
                case BID_OPENED -> project.getProgressStatus() == ProjectProgressStatusEnum.TO_BID_OPEN
                        || project.getProgressStatus() == ProjectProgressStatusEnum.QUOTING;
                case END_OPENED -> project.getProgressStatus() == ProjectProgressStatusEnum.BID_OPENED;
                case EVALUATING -> project.getProgressStatus() == ProjectProgressStatusEnum.END_OPENED;
                case EVALUATED -> project.getProgressStatus() == ProjectProgressStatusEnum.END_OPENED
                        || project.getProgressStatus() == ProjectProgressStatusEnum.EVALUATING;
                case AWARDED -> project.getProgressStatus() == ProjectProgressStatusEnum.EVALUATED
                        || project.getProgressStatus() == ProjectProgressStatusEnum.END_OPENED;
                case BID_WON_PUBLICITY -> project.getProgressStatus() == ProjectProgressStatusEnum.AWARDED;
                case BID_WON_NOTICE -> project.getProgressStatus() == ProjectProgressStatusEnum.BID_WON_PUBLICITY
                        || project.getProgressStatus() == ProjectProgressStatusEnum.AWARDED;
                case COMPLETED -> project.getProgressStatus() == ProjectProgressStatusEnum.AWARDED
                        || project.getProgressStatus() == ProjectProgressStatusEnum.BID_WON_PUBLICITY
                        || project.getProgressStatus() == ProjectProgressStatusEnum.BID_WON_NOTICE
                        || project.getProgressStatus() == ProjectProgressStatusEnum.CONTRACTED;
                case FAILED -> project.getProgressStatus() == ProjectProgressStatusEnum.NOTICE
                        || project.getProgressStatus() == ProjectProgressStatusEnum.TENDER_DOC
                        || project.getProgressStatus() == ProjectProgressStatusEnum.REGISTER
                        || project.getProgressStatus() == ProjectProgressStatusEnum.QUOTING
                        || project.getProgressStatus() == ProjectProgressStatusEnum.TO_BID_OPEN
                        || project.getProgressStatus() == ProjectProgressStatusEnum.BID_OPENED
                        || project.getProgressStatus() == ProjectProgressStatusEnum.EVALUATED;
                default -> false;
            };
            ExceptionUtil.check(throwEx && !result, GlobalResultCode.INVALID_STATE, "项目进度状态无法改为" + newProgressStatus);
        }
        return result;
    }

    @Override
    public void updateApproveStatus(String projectCode, ApproveStatusEnum newApproveStatus, boolean throwEx) {
        SrmProcurementProject project = this.getByProjectCode(projectCode);
        boolean canUpdate = this.checkStatus(projectCode, null, newApproveStatus, null, throwEx);
        if (canUpdate) {
            project.setApproveStatus(newApproveStatus);
            this.updateById(project);
        }
    }

    @Override
    public void updateProgressStatus(String projectCode, ProjectProgressStatusEnum newProgressStatus, boolean throwEx) {
        SrmProcurementProject project = this.getByProjectCode(projectCode);
        boolean canUpdate = this.checkStatus(projectCode, null, null, newProgressStatus, throwEx);
        if (canUpdate) {
            project.setProgressStatus(newProgressStatus);
            this.updateById(project);
        }
    }


    @Override
    public void approvePassed(String projectCode) {
        boolean checkStatus = this.checkStatus(projectCode, DocumentStatus.EFFECT, ApproveStatusEnum.APPROVE, ProjectProgressStatusEnum.TO_NOTICE, true);
        if (checkStatus) {
            SrmProcurementProject project = this.getByProjectCode(projectCode);
            project.setStatus(DocumentStatus.EFFECT);
            project.setApproveStatus(ApproveStatusEnum.APPROVE);
            project.setProgressStatus(ProjectProgressStatusEnum.TO_NOTICE);
            this.updateById(project);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoUpdateProgressStatus() {
        List<SrmTenderOpen> finishQuoteTenderOpens = srmTenderOpenService.lambdaQuery()
                .le(SrmTenderOpen::getLatestQuoteEndTime, LocalDateTime.now())
                .ge(SrmTenderOpen::getLatestQuoteEndTime, LocalDate.now().atStartOfDay().minusDays(1L))
                .list();
        if (CollectionUtils.isEmpty(finishQuoteTenderOpens)) {
            return;
        }
        List<Long> projectIds = new ArrayList<>(finishQuoteTenderOpens.stream().map(SrmTenderOpen::getProjectId).toList());
        List<SrmTenderNotice> offlineTenderNotice = srmTenderNoticeService.lambdaQuery()
                .eq(SrmTenderNotice::getTenderWay, TenderWayEnum.OFFLINE)
                .in(SrmTenderNotice::getProjectId, projectIds).list();
        // 线下开标，直接流转为评标中
        if (CollectionUtils.isNotEmpty(offlineTenderNotice)) {
            List<Long> offlineTenderProjectIds = offlineTenderNotice.stream().map(SrmTenderNotice::getProjectId).toList();
            this.lambdaUpdate()
                    .set(SrmProcurementProject::getProgressStatus, ProjectProgressStatusEnum.EVALUATING)
                    .eq(SrmProcurementProject::getProgressStatus, ProjectProgressStatusEnum.QUOTING)
                    .in(SrmProcurementProject::getId, offlineTenderProjectIds)
                    .update();

            srmTenderOpenService.lambdaUpdate().in(SrmTenderOpen::getProjectId, offlineTenderProjectIds)
                    .set(SrmTenderOpen::getOpenStatus, OpenStatusEnum.END_OPENED)
                    .update();
            projectIds.removeIf(offlineTenderProjectIds::contains);
        }
        if (CollectionUtils.isNotEmpty(projectIds)) {
            this.lambdaUpdate()
                    .set(SrmProcurementProject::getProgressStatus, ProjectProgressStatusEnum.TO_BID_OPEN)
                    .eq(SrmProcurementProject::getProgressStatus, ProjectProgressStatusEnum.QUOTING)
                    .in(SrmProcurementProject::getId, projectIds)
                    .update();
        }
    }


    @Override
    public ProjectArchiveResp getProjectArchive(String projectCode) {
        SrmProcurementProject project = this.getByProjectCode(projectCode);
        SrmTenderNotice effectNotice = srmTenderNoticeService.getEffectNotice(project.getId());
        ExceptionUtil.checkNonNull(effectNotice, "没有生效的招标公告！");
        ProjectArchiveResp projectArchiveResp = new ProjectArchiveResp();

        projectArchiveResp.setProjectAttachmentList(srmProjectAttachmentService.lambdaQuery()
                .eq(SrmProjectAttachment::getBusinessId, project.getId())
                .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.PROJECT).list());
        projectArchiveResp.setNoticeTitle(effectNotice.getNoticeTitle());
        projectArchiveResp.setNoticeContent(effectNotice.getNoticeContent());
        projectArchiveResp.setNoticeAttachmentList(srmProjectAttachmentService.lambdaQuery()
                .eq(SrmProjectAttachment::getBusinessId, effectNotice.getId())
                .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.NOTICE).list());

        List<SrmTenderEvaluationResult> evaluationResultList = srmTenderEvaluationResultService.lambdaQuery()
                .eq(SrmTenderEvaluationResult::getProjectId, project.getId())
                .eq(SrmTenderEvaluationResult::getNoticeId, effectNotice.getId()).list();
        if (CollectionUtils.isNotEmpty(evaluationResultList)) {
            List<Long> evaluationResultIds = evaluationResultList.stream().map(SrmTenderEvaluationResult::getId).toList();
            List<SrmProjectAttachment> evaluationResultAttachment = srmProjectAttachmentService.lambdaQuery()
                    .in(SrmProjectAttachment::getBusinessId, evaluationResultIds)
                    .in(SrmProjectAttachment::getBusinessType, Lists.newArrayList(AttachmentTypeEnum.BID_ATTACHMENT,
                            AttachmentTypeEnum.TENDER_CONTRACT, AttachmentTypeEnum.PUBLICITY_ATTACHMENT, AttachmentTypeEnum.NOTICE_ATTACHMENT)).list();
            if (CollectionUtils.isNotEmpty(evaluationResultAttachment)) {
                evaluationResultList.forEach(item -> {
                    Map<AttachmentTypeEnum, List<SrmProjectAttachment>> subEvaluationResultAttachmentMap = evaluationResultAttachment
                            .stream().filter(info -> Objects.equals(info.getBusinessId(), item.getId()))
                            .collect(Collectors.groupingBy(SrmProjectAttachment::getBusinessType));
                    item.setAwardAttachmentList(subEvaluationResultAttachmentMap.get(AttachmentTypeEnum.BID_ATTACHMENT));
                    item.setContractAttachmentList(subEvaluationResultAttachmentMap.get(AttachmentTypeEnum.TENDER_CONTRACT));
                });
            }

            // 中标公示、公告，不分标段
            Map<Long, SrmTenderEvaluationResult> awardPublicityAndNoticeMap = evaluationResultList.stream()
                    .collect(Collectors.toMap(SrmTenderEvaluationResult::getProjectId, Function.identity(), (v1, v2) -> v1));
            SrmTenderEvaluationResult awardPublicityAndNoticeResult = awardPublicityAndNoticeMap.get(project.getId());
            projectArchiveResp.setAwardPublicityTitle(awardPublicityAndNoticeResult.getPublicityTitle());
            projectArchiveResp.setAwardPublicityContent(awardPublicityAndNoticeResult.getPublicityContent());
            projectArchiveResp.setAwardNoticeTitle(awardPublicityAndNoticeResult.getNoticeTitle());
            projectArchiveResp.setAwardNoticeContent(awardPublicityAndNoticeResult.getNoticeContent());
            if (CollectionUtils.isNotEmpty(evaluationResultAttachment)) {
                projectArchiveResp.setAwardPublicityAttachmentList(evaluationResultAttachment.stream()
                        .filter(item -> Objects.equals(item.getBusinessId(), awardPublicityAndNoticeResult.getId())
                                && Objects.equals(item.getBusinessType(), AttachmentTypeEnum.PUBLICITY_ATTACHMENT)).toList());
                projectArchiveResp.setAwardNoticeAttachmentList(evaluationResultAttachment.stream()
                        .filter(item -> Objects.equals(item.getBusinessId(), awardPublicityAndNoticeResult.getId())
                                && Objects.equals(item.getBusinessType(), AttachmentTypeEnum.NOTICE_ATTACHMENT)).toList());
            }

        }
        projectArchiveResp.setEvaluationResultList(evaluationResultList);

        List<ProjectArchiveResp.SectionSupplierAttachment> sectionSupplierAttachmentList = new ArrayList<>();
        List<SrmProcurementProjectSection> sectionList = srmProcurementProjectSectionService.lambdaQuery().eq(SrmProcurementProjectSection::getProjectId, project.getId()).list();
        if (CollectionUtils.isNotEmpty(sectionList)) {
            for (SrmProcurementProjectSection section : sectionList) {
                ProjectArchiveResp.SectionSupplierAttachment sectionSupplierAttachment = new ProjectArchiveResp.SectionSupplierAttachment();
                sectionSupplierAttachment.setSectionId(section.getId());
                sectionSupplierAttachment.setSectionName(section.getSectionName());

                RegisteredSupplierQueryReq supplierQueryReq = new RegisteredSupplierQueryReq();
                supplierQueryReq.setNoticeId(effectNotice.getId());
                supplierQueryReq.setSectionId(section.getId());
                List<SrmTenderSupplierResponse> supplierList = srmTenderSupplierResponseService.getRegisteredSupplierList(supplierQueryReq);
                if (CollectionUtils.isNotEmpty(supplierList)) {
                    List<Long> supplierRespIds = supplierList.stream().map(SrmTenderSupplierResponse::getId).toList();
                    List<SrmProjectAttachment> quoteAttachmentList = srmProjectAttachmentService.lambdaQuery()
                            .in(SrmProjectAttachment::getBusinessId, supplierRespIds)
                            .in(SrmProjectAttachment::getBusinessType, Lists.newArrayList(AttachmentTypeEnum.QUOTE_ATTACHMENT,
                                    AttachmentTypeEnum.QUOTE_OTHER_ATTACHMENT, AttachmentTypeEnum.INVITE_RECEIPT, AttachmentTypeEnum.TENDER_CONTRACT))
                            .list();
                    if (CollectionUtils.isNotEmpty(quoteAttachmentList)) {
                        Map<Long, List<SrmProjectAttachment>> quoteAttachmentMap = quoteAttachmentList.stream()
                                .filter(info -> info.getBusinessType() == AttachmentTypeEnum.QUOTE_ATTACHMENT || info.getBusinessType() == AttachmentTypeEnum.QUOTE_OTHER_ATTACHMENT)
                                .collect(Collectors.groupingBy(SrmProjectAttachment::getBusinessId));
                        Map<Long, List<SrmProjectAttachment>> inviteAttachmentMap = quoteAttachmentList.stream()
                                .filter(info -> info.getBusinessType() == AttachmentTypeEnum.INVITE_RECEIPT)
                                .collect(Collectors.groupingBy(SrmProjectAttachment::getBusinessId));
                        Map<Long, List<SrmProjectAttachment>> contractAttachmentMap = quoteAttachmentList.stream()
                                .filter(info -> info.getBusinessType() == AttachmentTypeEnum.TENDER_CONTRACT)
                                .collect(Collectors.groupingBy(SrmProjectAttachment::getBusinessId));
                        supplierList.forEach(item -> {
                            item.setQuoteAttachmentList(quoteAttachmentMap.get(item.getId()));
                            item.setInviteAttachmentList(inviteAttachmentMap.get(item.getId()));
                            item.setContractAttachmentList(contractAttachmentMap.get(item.getId()));
                        });
                    }
                }
                sectionSupplierAttachment.setSrmTenderSupplierResponseList(supplierList);
                sectionSupplierAttachmentList.add(sectionSupplierAttachment);
            }
        }
        projectArchiveResp.setSectionSupplierAttachmentList(sectionSupplierAttachmentList);
        return projectArchiveResp;
    }

    private SrmProcurementProject getByProjectCode(String projectCode) {
        SrmProcurementProject project = this.lambdaQuery().eq(SrmProcurementProject::getProjectCode, projectCode).one();
        ExceptionUtil.checkNonNull(project, "项目不存在");
        ExceptionUtil.check(project.getStatus() == DocumentStatus.CANCEL, GlobalResultCode.INVALID_STATE, "项目已作废");
        return project;
    }


    @Override
    public boolean match(SrmProcessConfigService.BizTypeEnum bizType) {
        return bizType == SrmProcessConfigService.BizTypeEnum.SRM_PROCUREMENT_PROJECT_CANCEL;
    }

    @Override
    public void approveRejectHook(String bizKey) {
//        this.updateApproveStatus(bizKey, ApproveStatusEnum.APPROVE_REVOKE, true);
        // 业务流中修改状态了，这里就不处理了
    }

}




