<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenantSupplierContactMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenantSupplierContact">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="tenantSupplierId" column="tenant_supplier_id" jdbcType="BIGINT"/>
            <result property="platformSupplierId" column="platform_supplier_id" jdbcType="BIGINT"/>
            <result property="platformContactId" column="platform_contact_id" jdbcType="BIGINT"/>
            <result property="contactName" column="contact_name" jdbcType="VARCHAR"/>
            <result property="contactPhone" column="contact_phone" jdbcType="VARCHAR"/>
            <result property="idNumber" column="id_number" jdbcType="VARCHAR"/>
            <result property="loginAccount" column="login_account" jdbcType="VARCHAR"/>
            <result property="accountStatus" column="account_status" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,
        tenant_supplier_id,platform_supplier_id,platform_contact_id,
        contact_name,contact_phone,id_number,
        login_account,account_status,remark,
        del_flag,create_by,create_by_name,
        create_time,update_by,update_by_name,update_time
    </sql>

   <select id="getContactsBySupplierId" resultType="com.ylz.saas.entity.SrmTenantSupplierContact">
        select <include refid="Base_Column_List"/>
        from srm_tenant_supplier_contact
        where tenant_supplier_id = #{supplierId}
          and del_flag = 0
        order by create_time desc
    </select>

    <select id="getContactsBySupplierIdPageWithUser" resultType="com.ylz.saas.entity.SrmTenantSupplierContact">
        SELECT
            stsc.id,
            stsc.tenant_id,
            stsc.dept_id,
            stsc.tenant_supplier_id,
            stsc.platform_supplier_id,
            stsc.platform_contact_id,
            stsc.contact_name,
            stsc.contact_phone,
            stsc.id_number,
            stsc.login_account,
            stsc.account_status,
            stsc.remark,
            stsc.id_attachment,
            stsc.del_flag,
            stsc.create_by,
            stsc.create_by_name,
            stsc.create_time,
            stsc.update_by,
            stsc.update_by_name,
            stsc.update_time,
            su.password as password,
            sd.name as deptName,
            stsc.generate_account as generateAccount
        FROM srm_tenant_supplier_contact stsc
        LEFT JOIN sys_user su ON stsc.login_account = su.username
                                     left join sys_dept sd on su.dept_id = sd.dept_id
            AND stsc.tenant_id = su.tenant_id
            AND su.del_flag = '0'
        WHERE stsc.tenant_supplier_id = #{supplierId}
          AND stsc.del_flag = 0
        ORDER BY stsc.create_time DESC
    </select>

    <delete id="physicalDeleteById">
        DELETE FROM srm_tenant_supplier_contact WHERE id = #{id}
    </delete>
    <select id="getByUserAccount" resultType="com.ylz.saas.entity.SrmTenantSupplierContact">
        select sc.*,
               si.supplier_name
        from srm_tenant_supplier_contact sc
                 left join srm_tenant_supplier_info si on sc.tenant_supplier_id = si.id
        where sc.login_account = #{account}
    </select>
</mapper>
