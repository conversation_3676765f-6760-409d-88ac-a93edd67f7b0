package com.ylz.saas.codegen.base_usage_location.vo;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 使用地点信息表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:41:24
 */
@Data
public class BaseUsageLocationVo implements Serializable {

    /**
  	 * 主键ID
   	 */
    private Long id;

    /**
  	 * 组织ID
   	 */
    private Long deptId;

    /**
  	 * 使用地点名称
   	 */
    @NotBlank(message = "使用地点名称不能为空")
    private String locationName;

    /**
  	 * 使用地点编码
   	 */
    private String locationCode;

    /**
  	 * 地点类型（工厂、仓库、办公室、牧场、工地等）
   	 */
    private String locationType;



    /**
  	 * 省
   	 */
    private String province;

    /**
  	 * 市
   	 */
    private String city;

    /**
  	 * 区
   	 */
    @NotBlank(message = "区不能为空")
    private String district;

    /**
  	 * 详细地址
   	 */
    @NotBlank(message = "详细地址不能为空")
    private String address;

    /**
  	 * 联系人
   	 */
    private String contactPerson;

    /**
  	 * 联系电话
   	 */
    private String contactPhone;

    /**
  	 * 状态
   	 */
    private String status;

    /**
  	 * 创建人名称
   	 */
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    private String updateByName;

    /**
  	 * 创建人
   	 */
    private String createBy;

    /**
  	 * 创建时间
   	 */
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
    private String updateBy;

    /**
  	 * 修改时间
   	 */
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
    private Long createById;

    /**
  	 * 更新人ID
   	 */
    private Long updateById;
}