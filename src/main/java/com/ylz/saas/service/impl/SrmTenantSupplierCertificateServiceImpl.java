package com.ylz.saas.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmTenantSupplierCertificate;
import com.ylz.saas.service.SrmTenantSupplierCertificateService;
import com.ylz.saas.mapper.SrmTenantSupplierCertificateMapper;
import com.ylz.saas.vo.SupplierCertificateInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tenant_supplier_certificate(租户供应商资质证书表)】的数据库操作Service实现
* @createDate 2025-06-10 18:08:09
*/
@Service
@Slf4j
public class SrmTenantSupplierCertificateServiceImpl extends ServiceImpl<SrmTenantSupplierCertificateMapper, SrmTenantSupplierCertificate>
    implements SrmTenantSupplierCertificateService{



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateSupplierCertificateInfo(SupplierCertificateInfoVo supplierCertificateInfoVo) {
        log.info("开始保存供应商资质信息，证书名称：{}", supplierCertificateInfoVo.getCertificateName());

        SrmTenantSupplierCertificate certificate;
        boolean isUpdate = supplierCertificateInfoVo.getId() != null;

        if (isUpdate) {
            // 更新操作
            certificate = this.getById(supplierCertificateInfoVo.getId());
            if (certificate == null) {
                log.error("资质证书不存在，ID：{}", supplierCertificateInfoVo.getId());
                return false;
            }
            BeanUtil.copyProperties(supplierCertificateInfoVo, certificate, "id", "createBy", "createTime");
        } else {
            // 新增操作
            certificate = new SrmTenantSupplierCertificate();
            BeanUtil.copyProperties(supplierCertificateInfoVo, certificate);
            certificate.setDelFlag(0);
        }

        // 注意：SrmTenantSupplierCertificate设置租户ID，为租户数据
        // certificate.setTenantId(null); // 移除这行，让框架自动设置租户ID

        boolean result = this.saveOrUpdate(certificate);

        if (result) {
            log.info("供应商资质信息保存成功，资质ID：{}", certificate.getId());
        } else {
            log.error("供应商资质信息保存失败");
        }

        return result;
    }

    @Override
    public List<SrmTenantSupplierCertificate> getCertificatesBySupplierId(Long supplierId) {
        return this.list(new LambdaQueryWrapper<SrmTenantSupplierCertificate>()
                .eq(SrmTenantSupplierCertificate::getSupplierId, supplierId)
                .eq(SrmTenantSupplierCertificate::getDelFlag, 0)
                .orderByDesc(SrmTenantSupplierCertificate::getCreateTime));
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSupplierCertificate(Long id) {
        log.info("开始物理删除供应商资质信息，资质ID：{}", id);

        // 使用物理删除
        int result = this.baseMapper.physicalDeleteById(id);

        if (result > 0) {
            log.info("供应商资质信息物理删除成功，资质ID：{}", id);
        } else {
            log.error("供应商资质信息物理删除失败，资质ID：{}", id);
        }

        return result > 0;
    }

    @Override
    public Page<SrmTenantSupplierCertificate> getCertificatesBySupplierIdPage(Long supplierId, Page<SrmTenantSupplierCertificate> page) {
        return this.lambdaQuery()
                .eq(SrmTenantSupplierCertificate::getSupplierId, supplierId)
                .eq(SrmTenantSupplierCertificate::getDelFlag, 0)
                .orderByDesc(SrmTenantSupplierCertificate::getCreateTime)
                .page(page);
    }
}




