<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.codegen.base_usage_location.mapper.BaseUsageLocationMapper">


    <select id="removeByIdsBaseUsageLocation" >
        delete from base_usage_location
        where id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">#{item}</foreach>


    </select>

</mapper>
