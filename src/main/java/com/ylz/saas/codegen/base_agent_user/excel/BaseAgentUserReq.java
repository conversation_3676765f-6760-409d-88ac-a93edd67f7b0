package com.ylz.saas.codegen.base_agent_user.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 代理机构用户表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:11
 */
@Data
public class BaseAgentUserReq implements Serializable {


    /**
  	 * 组织ID
   	 */
	@ExcelProperty(value = "组织ID")
    private Long deptId;


    /**
  	 * 代理结构id
   	 */
	@ExcelProperty(value = "代理结构id")
    private Long agentOrgId;


    /**
  	 * 用户ID
   	 */
	@ExcelProperty(value = "用户ID")
    private Long userId;


    /**
  	 * 用户名称
   	 */
	@ExcelProperty(value = "用户名称")
    private String userName;


    /**
  	 * 登录账号
   	 */
	@ExcelProperty(value = "登录账号")
    private String loginAccount;


    /**
  	 * 联系电话
   	 */
	@ExcelProperty(value = "联系电话")
    private String contactPhone;


    /**
  	 * 创建人名称
   	 */
	@ExcelProperty(value = "创建人名称")
    private String createByName;


    /**
  	 * 修改人名称
   	 */
	@ExcelProperty(value = "修改人名称")
    private String updateByName;









}