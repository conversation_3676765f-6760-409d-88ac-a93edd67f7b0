<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenderScoreTemplateItemMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenderScoreTemplateItem">
            <id property="id" column="id" />
            <result property="tenantId" column="tenant_id" />
            <result property="deptId" column="dept_id" />
            <result property="templateId" column="template_id" />
            <result property="name" column="name" />
            <result property="detail" column="detail" />
            <result property="node" column="node" />
            <result property="type" column="type" />
            <result property="rejectCount" column="reject_count" />
            <result property="totalScore" column="total_score" />
            <result property="weight" column="weight" />
            <result property="minScore" column="min_score" />
            <result property="maxScore" column="max_score" />
            <result property="createBy" column="create_by" />
            <result property="createByName" column="create_by_name" />
            <result property="createTime" column="create_time" />
            <result property="createById" column="create_by_id" />
            <result property="updateById" column="update_by_id" />
            <result property="updateBy" column="update_by" />
            <result property="updateByName" column="update_by_name" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,template_id,name,detail,node,
        type,reject_count,total_score,weight,min_score,
        max_score,create_by,create_by_name,create_time,create_by_id,
        update_by_id,update_by,update_by_name,update_time
    </sql>
    <delete id="deleteItemsByTemplateIdAndType">
        DELETE FROM srm_tender_score_template_item
        WHERE template_id = #{templateId} AND type = #{type}
    </delete>
    <!-- 查询评审项 -->
    <select id="selectReviewItems" resultType="com.ylz.saas.resp.SrmTenderScoreTemplateDetailResp$TemplateReviewItem">
        SELECT name, detail, node, reject_count AS rejectCount
        FROM srm_tender_score_template_item
        WHERE template_id = #{id} AND type = #{type} ORDER BY create_time ASC
    </select>
    <!-- 查询评分项 -->
    <select id="selectScoreItems" resultType="com.ylz.saas.resp.SrmTenderScoreTemplateDetailResp$TemplateScoreItem">
        SELECT
            name,
            detail,
            node,
            total_score AS totalScore,
            weight,
            min_score AS minScore,
            max_score AS maxScore
        FROM srm_tender_score_template_item
        WHERE template_id = #{id} AND type = #{type} ORDER BY create_time ASC;
    </select>

    <insert id="batchInsertTemplateItems">
        INSERT INTO srm_tender_score_template_item
        (template_id, name, detail, node, type, reject_count, total_score, weight, min_score, max_score)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.templateId},
            #{item.name},
            #{item.detail},
            #{item.node},
            #{item.type},
            #{item.rejectCount},
            #{item.totalScore},
            #{item.weight},
            #{item.minScore},
            #{item.maxScore}
            )
        </foreach>
    </insert>
</mapper>
