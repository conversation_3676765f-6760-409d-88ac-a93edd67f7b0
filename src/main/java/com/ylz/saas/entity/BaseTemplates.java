package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.ylz.saas.common.core.util.TenantTable;
import lombok.Data;

/**
 * 模板表
 * @TableName base_templates
 */
@TableName(value ="base_templates")
@TenantTable
@Data
public class BaseTemplates {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 组织ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板场景ID
     */
    private Long templateSceneId;

    /**
     * 类型
     */
    private String type;

    /**
     * 编号
     */
    private String code;

    /**
     * 版本号
     */
    private String version;

    /**
     * 使用量
     */
    private Integer usageCount;

    /**
     * 状态（ENABLE-启用、DISABLE-禁用）
     */
    private String status;

    /**
     * 模板说明
     */
    private String description;

    /**
     * 图标
     */
    private String iconUrl;

    /**
     * 模板内容
     */
    private String content;
    /**
     * 是否默认（0-否、1-是）
     */
    private Integer isDefault;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createById;

    /**
     * 更新人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateById;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}