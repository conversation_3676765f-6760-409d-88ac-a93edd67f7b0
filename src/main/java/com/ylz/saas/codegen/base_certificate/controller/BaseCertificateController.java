package com.ylz.saas.codegen.base_certificate.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.codegen.base_certificate.entity.BaseCertificateEntity;
import com.ylz.saas.codegen.base_certificate.excel.BaseCertificateResp;
import com.ylz.saas.codegen.base_certificate.param.BaseCertificateParam;
import com.ylz.saas.codegen.base_certificate.service.BaseCertificateService;
import com.ylz.saas.codegen.base_certificate.vo.BaseCertificateVo;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.common.excel.annotation.ResponseExcel;
import com.ylz.saas.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * 证件信息表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:46
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/baseCertificate" )
@Tag(description = "baseCertificate" , name = "证件信息表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class BaseCertificateController {

    private final BaseCertificateService baseCertificateService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param baseCertificate 证件信息表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @PostMapping("/page" )
    public R getBaseCertificatePage(@RequestBody BaseCertificateParam param, Page page) {
        QueryWrapper<BaseCertificateEntity> queryWrapper = param.toWrapper(); 
        return R.ok(baseCertificateService.page(page, queryWrapper));
    }


    /**
     * 通过id查询证件信息表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(baseCertificateService.getById(id));
    }

    /**
     * 新增证件信息表
     * @param baseCertificateVo 证件信息表
     * @return R
     */
    @Operation(summary = "新增证件信息表" , description = "新增证件信息表" )
    @SysLog("新增证件信息表" )
    @PostMapping("/save")
    public R save(@Valid @RequestBody BaseCertificateVo baseCertificateVo) {
        return R.ok(baseCertificateService.saveBaseCertificate(baseCertificateVo));
    }

    /**
     * 修改证件信息表
     * @param baseCertificateVo 证件信息表
     * @return R
     */
    @Operation(summary = "修改证件信息表" , description = "修改证件信息表" )
    @SysLog("修改证件信息表" )
    @PostMapping("/update")
    public R updateById(@Valid @RequestBody BaseCertificateVo baseCertificateVo) {
        return R.ok(baseCertificateService.updateBaseCertificate(baseCertificateVo));
    }

    /**
     * 通过id删除证件信息表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除证件信息表" , description = "通过id删除证件信息表" )
    @SysLog("通过id删除证件信息表" )
    @GetMapping("remove")
    public R removeById(@RequestParam List<Long> ids) {
		boolean remove = baseCertificateService.removeByIds(ids);
		return R.ok(remove);
    }


    /**
     * 导出excel 表格
     * @param baseCertificate 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @PostMapping("/export")
	@Operation(summary = "导出excel" , description = "导出excel" )
    public List<BaseCertificateResp> export(@RequestBody BaseCertificateParam param) {
		QueryWrapper<BaseCertificateEntity> queryWrapper = param.toWrapper(); 
		List<BaseCertificateEntity> entityList = baseCertificateService.list(queryWrapper);
        return BeanUtil.copyToList(entityList, BaseCertificateResp.class);
    }
	
	/**
     * 导入excel 表格
     * @param file
     * @param response
     * @return
     */
    @PostMapping("/import")
    @Operation(summary = "导入excel" , description = "导入excel" )
    public R uploadData(@RequestParam("file") MultipartFile file,HttpServletResponse response) {
        baseCertificateService.uploadData(file,response);
        return R.ok();
    }

    /**
     * excel模板下载
     * @param response
     */
    @GetMapping("/template")
    @Operation(summary = "excel模板下载" , description = "excel模板下载" )
    public void template(HttpServletResponse response) {
        baseCertificateService.template(response);
    }
}