package com.ylz.saas.codegen.base_expert_achievement.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 专家成果表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:42:08
 */
@Data
public class BaseExpertAchievementResp implements Serializable {

    /**
  	 * 主键ID
   	 */
	@ExcelProperty(value = "主键ID")
    private Long id;


    /**
  	 * 附件
   	 */
	@ExcelProperty(value = "附件")
    private String attachment;


    /**
  	 * 专业领域
   	 */
	@ExcelProperty(value = "专业领域")
    private String professionalField;

    /**
  	 * 专业成果
   	 */
	@ExcelProperty(value = "专业成果")
    private String achievement;

    /**
  	 * 备注
   	 */
	@ExcelProperty(value = "备注")
    private String remarks;









}