package com.ylz.saas.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 供应商评审项详情查询请求类
 * <AUTHOR>
 * @createDate 2025-07-14
 */
@Data
@Schema(description = "供应商评审项详情查询请求类")
public class SrmTenderSupplierReviewDetailQueryReq implements Serializable {

    /**
     * 报价轮次
     */
    @NotNull(message = "报价轮次")
    @Schema(description = "报价轮次")
    private Integer currentRound;
    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 租户供应商ID
     */
    @NotNull(message = "租户供应商ID不能为空")
    @Schema(description = "租户供应商ID")
    private Long tenantSupplierId;

    /**
     * 招标委员会ID
     */
    @NotNull(message = "招标委员会ID不能为空")
    private Long evaluationId;

    private static final long serialVersionUID = 1L;
}
