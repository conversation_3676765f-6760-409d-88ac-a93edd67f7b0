package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.BaseSceneFields;
import com.ylz.saas.entity.BaseTemplateScenes;
import com.ylz.saas.req.BaseAnnouncementSceneAddReq;
import com.ylz.saas.req.BaseAnnouncementSceneUpdateReq;
import com.ylz.saas.req.SceneWithFieldsReq;
import com.ylz.saas.resp.BaseAnnouncementTemplateSceneQueryResp;
import com.ylz.saas.service.BaseSceneFieldsService;
import com.ylz.saas.service.BaseTemplateScenesService;
import com.ylz.saas.mapper.BaseTemplateScenesMapper;
import io.micrometer.common.util.StringUtils;
import org.anyline.annotation.Autowired;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.stereotype.Service;
import com.ylz.saas.resp.SceneWithFieldsResp;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【base_template_scenes(模板场景表)】的数据库操作Service实现
* @createDate 2025-06-11 13:07:56
*/
@Service
public class BaseTemplateScenesServiceImpl extends ServiceImpl<BaseTemplateScenesMapper, BaseTemplateScenes>
    implements BaseTemplateScenesService{
    private final BaseSceneFieldsService sceneFieldsService;

    @Autowired
    public BaseTemplateScenesServiceImpl(BaseSceneFieldsService sceneFieldsService) {
        this.sceneFieldsService = sceneFieldsService;
    }

    @Override
    public boolean saveScene(BaseAnnouncementSceneAddReq req) {
        // 检查是否已有相同名称的场景
        boolean exists = query().eq("scene_name", req.getSceneName()).exists();
        if (exists) {
            throw new IllegalArgumentException("场景名称已存在");
        }

        BaseTemplateScenes scene = new BaseTemplateScenes();
        scene.setSceneName(req.getSceneName());
        scene.setDescription(req.getDescription());

        return save(scene);
    }

    @Override
    public boolean updateScene(BaseAnnouncementSceneUpdateReq req) {
        // 查询当前要更新的记录
        BaseTemplateScenes existingScene = getById(req.getId());
        if (existingScene == null) {
            throw new IllegalArgumentException("场景不存在");
        }

        // 如果名称未改变，无需校验重复
        if (!existingScene.getSceneName().equals(req.getSceneName())) {
            boolean exists = query()
                .ne("id", req.getId()) // 排除自己
                .eq("scene_name", req.getSceneName())
                .exists();

            if (exists) {
                throw new IllegalArgumentException("场景名称已存在");
            }
        }

        // 更新字段
        existingScene.setSceneName(req.getSceneName());
        existingScene.setDescription(req.getDescription());

        return updateById(existingScene);
    }


    @Override
    public boolean deleteSceneById(Long id) {
        // 删除 base_scene_fields 中该场景下的所有字段（物理删除）
        sceneFieldsService.remove(new QueryWrapper<BaseSceneFields>().eq("template_scene_id", id));

        return removeById(id);
    }


    @Override
    public SceneWithFieldsResp getSceneWithFields(Long sceneId) throws InvocationTargetException, IllegalAccessException {
        BaseTemplateScenes scene = getById(sceneId);
        if (scene == null) {
            return null;
        }
        SceneWithFieldsResp resp = new SceneWithFieldsResp();
        BeanUtils.copyProperties(resp, scene); // 复制基础字段

        List<BaseSceneFields> fields = sceneFieldsService.list(
                new QueryWrapper<BaseSceneFields>().eq("template_scene_id", sceneId));
        resp.setFields(fields);

        return resp;
    }

    @Override
    public boolean saveSceneWithFields(SceneWithFieldsReq req) {
        Set<String> fieldNameSet = new HashSet<>();
        for (SceneWithFieldsReq.SceneFieldItemReq f : req.getFields()) {
            if (!fieldNameSet.add(f.getFieldName())) {
                throw new IllegalArgumentException("字段名称不能重复: " + f.getFieldName());
            }
        }
        // 保存主表
        BaseTemplateScenes scene = new BaseTemplateScenes();
        scene.setSceneName(req.getSceneName());
        scene.setDescription(req.getDescription());

        if (!save(scene)) {
            return false;
        }

        // 保存字段
        if (req.getFields() != null && !req.getFields().isEmpty()) {
            List<BaseSceneFields> fieldList = req.getFields().stream().map(f -> {
                BaseSceneFields field = new BaseSceneFields();
                field.setTemplateSceneId(scene.getId());
                field.setFieldName(f.getFieldName());
                field.setDescription(f.getDescription());
                field.setFieldDisplayName(f.getFieldDisplayName());
                field.setFieldType(f.getFieldType());
                field.setFieldLength(f.getFieldLength());
                field.setIsRequired(f.getIsRequired());
                field.setDefaultValue(f.getDefaultValue());

                return field;
            }).collect(Collectors.toList());

            return sceneFieldsService.saveBatch(fieldList);
        }

        return true;
    }

    @Override
    public boolean updateSceneWithFields(SceneWithFieldsReq req) {
        // 更新主表
        BaseTemplateScenes scene = getById(req.getId());
        if (scene == null) {
            return false;
        }

        scene.setSceneName(req.getSceneName());
        scene.setDescription(req.getDescription());
        updateById(scene);

        Long sceneId = scene.getId();

        // 获取当前数据库中的字段列表
        List<BaseSceneFields> dbFields = sceneFieldsService.getSceneFieldsBySceneId(sceneId);
        Map<String, BaseSceneFields> dbFieldMap = dbFields.stream()
                .collect(Collectors.toMap(BaseSceneFields::getFieldName, f -> f));

        // 用于保存需要新增或更新的字段
        List<BaseSceneFields> newFields = new ArrayList<>();
        List<BaseSceneFields> updateFields = new ArrayList<>();

        // 用于记录哪些字段在请求中出现过
        Set<String> incomingFieldNames = new HashSet<>();

        for (SceneWithFieldsReq.SceneFieldItemReq fieldItem : req.getFields()) {
            String fieldName = fieldItem.getFieldName();
            incomingFieldNames.add(fieldName); // 记录字段名

            BaseSceneFields existingField = dbFieldMap.get(fieldName);

            if (existingField != null) {
                // 字段名已存在：更新已有字段
                existingField.setDescription(fieldItem.getDescription());
                existingField.setFieldDisplayName(fieldItem.getFieldDisplayName());
                existingField.setFieldType(fieldItem.getFieldType());
                existingField.setFieldLength(fieldItem.getFieldLength());
                existingField.setIsRequired(fieldItem.getIsRequired());
                existingField.setDefaultValue(fieldItem.getDefaultValue());
                updateFields.add(existingField);
            } else {
                // 字段名不存在：新增字段
                BaseSceneFields newField = new BaseSceneFields();
                newField.setTemplateSceneId(sceneId);
                newField.setFieldName(fieldName);
                newField.setDescription(fieldItem.getDescription());
                newField.setFieldDisplayName(fieldItem.getFieldDisplayName());
                newField.setFieldType(fieldItem.getFieldType());
                newField.setFieldLength(fieldItem.getFieldLength());
                newField.setIsRequired(fieldItem.getIsRequired());
                newField.setDefaultValue(fieldItem.getDefaultValue());
                newFields.add(newField);
            }
        }

        // 执行更新和新增操作
        if (!newFields.isEmpty()) {
            sceneFieldsService.saveBatch(newFields);
        }

        if (!updateFields.isEmpty()) {
            sceneFieldsService.updateBatchById(updateFields);
        }

        // 删除不在请求中的字段
        List<Long> idsToDelete = dbFields.stream()
                .filter(f -> !incomingFieldNames.contains(f.getFieldName())) // 只保留不在请求中的字段
                .map(BaseSceneFields::getId)
                .collect(Collectors.toList());

        if (!idsToDelete.isEmpty()) {
            sceneFieldsService.removeByIds(idsToDelete); // 批量删除
        }

        return true;
    }

//    @Override
//    public Page<BaseAnnouncementTemplateSceneQueryResp> query(Page<BaseAnnouncementTemplateSceneQueryResp> page, String sceneName) {
//        LambdaQueryWrapper<BaseTemplateScenes> wrapper = new LambdaQueryWrapper<>();
//
//        wrapper.like(StringUtils.isNotBlank(sceneName), BaseTemplateScenes::getSceneName, sceneName);
//        // 添加排序条件
//        wrapper.orderByDesc(BaseTemplateScenes::getCreateTime);
//
//        return baseMapper.selectPage(page, wrapper);
//    }
    @Override
    public Page<BaseAnnouncementTemplateSceneQueryResp> query(Page<BaseAnnouncementTemplateSceneQueryResp> page, String sceneName) {
        LambdaQueryWrapper<BaseTemplateScenes> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.isNotBlank(sceneName), BaseTemplateScenes::getSceneName, sceneName);
        wrapper.orderByDesc(BaseTemplateScenes::getCreateTime);

        // 查询场景分页数据
        Page<BaseTemplateScenes> scenePage = baseMapper.selectPage(new Page<>(page.getCurrent(), page.getSize()), wrapper);

        // 转换为响应对象并填充字段信息
        List<BaseAnnouncementTemplateSceneQueryResp> respList = scenePage.getRecords().stream()
                .map(scene -> {
                    BaseAnnouncementTemplateSceneQueryResp resp = new BaseAnnouncementTemplateSceneQueryResp();
                    try {
                        BeanUtils.copyProperties(resp, scene); // 复制基础字段
                    } catch (IllegalAccessException | InvocationTargetException e) {
                        throw new RuntimeException("属性拷贝失败", e);
                    }

                    // 查询该场景对应的字段列表
                    List<BaseSceneFields> fields = sceneFieldsService.list(
                            new QueryWrapper<BaseSceneFields>().eq("template_scene_id", scene.getId()));
                    resp.setFields(fields);

                    return resp;
                }).collect(Collectors.toList());

        // 构造新的 Page 对象用于返回
        Page<BaseAnnouncementTemplateSceneQueryResp> resultPage = new Page<>();
        resultPage.setTotal(scenePage.getTotal());
        resultPage.setSize(scenePage.getSize());
        resultPage.setCurrent(scenePage.getCurrent());
        resultPage.setPages(scenePage.getPages());
        resultPage.setRecords(respList);

        return resultPage;
    }



}




