package com.ylz.saas.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 报价明细（物料信息）查询请求参数
 * <AUTHOR>
 * @createDate 2025-07-17
 */
@Data
@Schema(description = "报价明细（物料信息）查询请求参数")
public class SrmTenderQuoteDetailByMaterialQueryReq {
    /**
     * 租户供应商ID
     */
    @Schema(description = "租户供应商ID")
    @NotNull(message = "供应商id")
    private Long tenantSupplierId;
    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    @Schema(description = "采购立项ID", required = true)
    private Long projectId;

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    @Schema(description = "标段ID", required = true)
    private Long sectionId;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    @Schema(description = "招标公告ID", required = true)
    private Long noticeId;

    /**
     * 物料名称（可选，用于筛选）
     */
    @Schema(description = "物料名称（可选，用于筛选）")
    private String materialName;

    /**
     * 物料编码（可选，用于筛选）
     */
    @Schema(description = "物料编码（可选，用于筛选）")
    private String materialCode;
}
