package com.ylz.saas.req;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.entity.SrmTenderOnlineQa;
import com.ylz.saas.enums.AnswerStatusEnum;
import lombok.Data;

/**
 * 在线问答表
 * @TableName srm_tender_online_qa
 */
@Data
public class SrmTenderOnlineQaPageReq extends Page<SrmTenderOnlineQa> {
    /**
     * 采购立项ID
     */
    private Long projectId;

    /**
     * 招标公告ID
     */
    private Long noticeId;

    /**
     * 租户供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 问题标题
     */
    private String questionTitle;

    /**
     * 问题描述
     */
    private String questionContent;

    /**
     * 回复状态(PENDING-待回复、REPLIED-已回复)
     */
    private AnswerStatusEnum answerStatus;

    /**
     * 是否是供应商
     */
    private Boolean isSupplier;

}