package com.ylz.saas.service;

import com.ylz.saas.entity.SrmTenderSupplierInvite;
import com.ylz.saas.entity.SrmTenderSupplierResponse;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.enums.TenderSupplierResponseStageEnum;
import com.ylz.saas.req.*;
import com.ylz.saas.resp.SrmTenderRegisterInfoListResp;
import com.ylz.saas.vo.ValidateTenderResult;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tender_supplier_response(供应商响应表)】的数据库操作Service
* @createDate 2025-06-10 15:49:11
*/
public interface SrmTenderSupplierResponseService extends IService<SrmTenderSupplierResponse> {

    void tenderRegister(SrmTenderSupplierResponseApplyReq req);
    
    void payDeposit(PayDepositReq req);

    void withdrawn(WithdrawnTenderRegisterReq req);

    SrmTenderSupplierResponse getOrCreateSupplierResponse(ValidateTenderResult validateResult, long sectionId, TenderSupplierResponseStageEnum stage);

    ValidateTenderResult validateSupplierAndProject(Long noticeId, Long tenantSupplierId);

    List<SrmTenderRegisterInfoListResp> getSelectionRegisterInfo(SrmTenderRegisterInfoQueryReq req);

    SrmTenderSupplierResponse getRegisteredInfo(NoticeAndSectionIdReq req);

    List<SrmTenderSupplierResponse> getRegisteredSupplierList(RegisteredSupplierQueryReq req);

    void review(ReviewTenderRegisterReq req);

    void inviteResponse(InviteResponseReq req);

    SrmTenderSupplierInvite inviteDetail(InviteDetailQueryReq req);

    void downloadedBidFileDownloadStatus(Long noticeId, Long sectionId);

}
