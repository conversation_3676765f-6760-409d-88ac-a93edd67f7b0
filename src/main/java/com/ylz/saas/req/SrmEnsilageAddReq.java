package com.ylz.saas.req;

import com.ylz.saas.enums.ApproveStatusEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 青贮添加参数
 */
@Data
public class SrmEnsilageAddReq implements Serializable {
    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 公告id
     */
    private Long noticeId;

    /**
     * 需求名称
     */
    @NotBlank(message = "需求名称不能为空")
    private String demandName;

    /**
     * 物料名称
     */
    @NotBlank(message = "物料名称不能为空")
    private String materialName;

    /**
     * 物料名称
     */
    @NotBlank(message = "物料编码不能为空")
    private String materialCode;

    /**
     * 需求牧场id
     */
    @NotNull(message = "需求牧场ID不能为空")
    private Long usageLocationId;

    /**
     * 省
     */
    @NotBlank(message = "省不能为空")
    private String province;

    /**
     * 市
     */
    @NotBlank(message = "市不能为空")
    private String city;

    /**
     * 区
     */
    @NotBlank(message = "区不能为空")
    private String district;

    /**
     * 详细地址
     */
    @NotBlank(message = "详细地址不能为空")
    private String address;

    /**
     * 需求数量
     */
    @NotNull(message = "需求数量不能为空")
    private BigDecimal requiredQuantity;

    /**
     * 需求单位
     */
    @NotBlank(message = "需求单位不能为空")
    private String unit;

    /**
     * 拦标价
     */
    @NotNull(message = "拦标价不能为空")
    private BigDecimal interceptPrice;

    /**
     * 线下通知供应商
     */
    private Boolean offlineNoticeSupplier;

    /**
     * 供货时间段开始
     */
    private LocalDateTime supplyStartTime;

    /**
     * 供货时间段结束
     */
    private LocalDateTime supplyEndTime;

    /**
     * 是否收取保证金
     */
    @NotNull(message = "是否收取保证金不能为空")
    private Boolean needDeposit;

    /**
     * 要求名称/资质证件ID
     */
    private String requirementName;

    /**
     * 要求内容/补充说明/金额银行信息（json）
     */
    private String requirementContent;

    /**
     * 保证金设置--保证金额
     */
    private BigDecimal guaranteeAmount;

    /**
     * 保证金设置--付款账号
     */
    private String payAccount;

    /**
     * 保证金设置--付款银行
     */
    private String payBank;

    /**
     * 保证金设置--付款开户行
     */
    private String openAccountBank;

    /**
     * 是否牛信支付
     */
    @NotNull(message = "是否牛信支付不能为空")
    private Boolean isNxPay;

    /**
     * 采购联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 固定电话
     */
    private String contactFixedPhone;

    /**
     * 电子邮件
     */
    private String contactEmail;

    /**
     * 采购执行人id
     */
    private Long executorId;

    /**
     * 报价/投标/竞价开始时间
     */
    @NotNull(message = "报价/投标/竞价开始时间不能为空")
    private LocalDateTime quoteStartTime;

    /**
     * 报价/投标/竞价截止时间
     */
    @NotNull(message = "报价/投标/竞价截止时间不能为空")
    private LocalDateTime quoteEndTime;

    /**
     * 报价有效期
     */
    private Integer quoteValidity;

    /**
     * 付款方式
     */
    @NotBlank(message = "付款方式不能为空")
    private String projectPaymentStr;

    /**
     * 备注
     */
    private String remark;

    /**
     * 附件信息
     */
    private List<AttachmentInfoReq> attachmentInfos;

    /**
     * 邀请供应商--供应商信息
     */
    private List<SrmTenderNoticeAddReq.InviteSuppliers> inviteSuppliers;

    /**
     * 审批状态（变更使用）
     */
    private ApproveStatusEnum noticeStatus;


}