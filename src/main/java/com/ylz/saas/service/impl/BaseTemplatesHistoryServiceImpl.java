package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ylz.saas.entity.BaseTemplates;
import com.ylz.saas.entity.BaseTemplatesHistory;
import com.ylz.saas.service.BaseTemplatesHistoryService;
import com.ylz.saas.mapper.BaseTemplatesHistoryMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【base_templates_history(历史模板记录表)】的数据库操作Service实现
* @createDate 2025-06-11 13:07:56
*/
@Service
public class BaseTemplatesHistoryServiceImpl extends ServiceImpl<BaseTemplatesHistoryMapper, BaseTemplatesHistory>
    implements BaseTemplatesHistoryService{

    @Override
    public void saveToHistory(BaseTemplates template) {
        try {
            // 构建 content 数据
            Map<String, Object> contentMap = new HashMap<>();
            contentMap.put("template_name", template.getTemplateName());
            contentMap.put("template_scene_id", template.getTemplateSceneId());
            contentMap.put("type", template.getType());
            contentMap.put("code", template.getCode());
            contentMap.put("usage_count", template.getUsageCount());
            contentMap.put("status", template.getStatus());
            contentMap.put("description", template.getDescription());
            contentMap.put("icon_url", template.getIconUrl());
            contentMap.put("content", template.getContent());
            contentMap.put("del_flag", template.getDelFlag());

            String jsonContent;
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                jsonContent = objectMapper.writeValueAsString(contentMap);
            } catch (JsonProcessingException e) {
                throw new RuntimeException("序列化 contentMap 为 JSON 失败", e);
            }
            // 构建历史记录对象
            BaseTemplatesHistory history = new BaseTemplatesHistory();
            history.setTenantId(template.getTenantId());
            history.setDeptId(template.getDeptId());
            history.setTemplateId(template.getId());
            history.setVersion(template.getVersion());
            history.setCreateBy(template.getCreateBy());
            history.setCreateByName(template.getCreateByName());
            history.setCreateById(template.getCreateById());
            history.setCreateTime(template.getCreateTime());
            history.setUpdateById(template.getUpdateById());
            history.setUpdateBy(template.getUpdateBy());
            history.setUpdateByName(template.getUpdateByName());
            history.setUpdateTime(LocalDateTime.now());
            history.setContent(jsonContent);

            // 保存到历史表
            this.save(history);
            System.out.println("保存模板历史成功");
        } catch (Exception e) {
            throw new RuntimeException("保存模板历史失败", e);
        }
    }

    @Override
    public List<BaseTemplatesHistory> getHistoryByTemplateId(Long templateId) {
        return this.list(new LambdaQueryWrapper<BaseTemplatesHistory>()
                .eq(BaseTemplatesHistory::getTemplateId, templateId)
                .orderByDesc(BaseTemplatesHistory::getVersion)); // 按版本号降序排列
    }

}


