package com.ylz.saas.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.req.SrmRecruitResultPageReq;
import com.ylz.saas.req.SrmRecruitSignUpReq;
import com.ylz.saas.req.SrmRecruitSupplierPageReq;
import com.ylz.saas.resp.SrmRecruitResultPageResp;
import com.ylz.saas.resp.SrmRecruitSignUpResp;
import com.ylz.saas.resp.SrmRecruitSupplierPageResp;
import com.ylz.saas.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

/**
 * 供应商招募报名
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/srmRecruitSupplierSignUp")
@Tag(description = "srmRecruitSupplierSignUp", name = "供应商招募报名")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SrmRecruitSupplierSignUpController {

    private final SrmRecruitSupplierService srmRecruitSupplierService;



    /**
     * 分页查询供应商招募信息
     *
     * @param req 查询条件
     * @return 分页结果
     */
    @Operation(summary = "分页查询供应商招募信息", description = "分页查询供应商招募信息")
    @PostMapping("/page")
    public R<IPage<SrmRecruitSupplierPageResp>> pageRecruitSupplier(@RequestBody SrmRecruitSupplierPageReq req, Page<SrmRecruitSupplierPageResp> page) {

//        todo 现在需要将供方的信息拿到 判断有无报名

        return R.ok(srmRecruitSupplierService.pageRecruitSupplier(req,page));
    }


    /**
     * 在线报名
     *
     * @param req 在线报名
     */
    @Operation(summary = "在线报名", description = "在线报名")
    @PostMapping("/signUp")
    public R signUp (@RequestBody SrmRecruitSignUpReq req) {
        return R.ok(srmRecruitSupplierService.signUp(req));
    }


    /**
     * 查看招募结果
     *
     * @param req 查询条件
     * @return 分页结果
     */
    @Operation(summary = "查看招募结果", description = "查看招募结果")
    @PostMapping("/pageResult")
    public R<IPage<SrmRecruitResultPageResp>> pageResult(@RequestBody SrmRecruitResultPageReq req, Page<SrmRecruitResultPageResp> page) {
//        todo

        return R.ok(srmRecruitSupplierService.pageSignUp(req,page));
    }

    /**
     * 查看报名详情
     *
     * @param signUpId 查询条件
     * @return 分页结果
     */
    @Operation(summary = "查看报名详情", description = "查看报名详情")
    @GetMapping("/detail/{id}")
    public R<SrmRecruitSignUpResp> getSignUpDetail(@PathVariable("id") Long signUpId) {
        return R.ok(srmRecruitSupplierService.getSignUpDetail(signUpId));
    }
}
