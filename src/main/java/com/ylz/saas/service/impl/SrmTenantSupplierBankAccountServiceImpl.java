package com.ylz.saas.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmTenantSupplierBankAccount;
import com.ylz.saas.service.SrmTenantSupplierBankAccountService;
import com.ylz.saas.mapper.SrmTenantSupplierBankAccountMapper;
import com.ylz.saas.vo.SupplierBankAccountInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tenant_supplier_bank_account(租户供应商银行账户表)】的数据库操作Service实现
* @createDate 2025-06-10 18:08:09
*/
@Service
@Slf4j
public class SrmTenantSupplierBankAccountServiceImpl extends ServiceImpl<SrmTenantSupplierBankAccountMapper, SrmTenantSupplierBankAccount>
    implements SrmTenantSupplierBankAccountService{



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateSupplierBankAccountInfo(SupplierBankAccountInfoVo supplierBankAccountInfoVo) {
        log.info("开始保存供应商银行账户信息，开户行：{}", supplierBankAccountInfoVo.getBankName());

        SrmTenantSupplierBankAccount bankAccount;
        boolean isUpdate = supplierBankAccountInfoVo.getId() != null;

        if (isUpdate) {
            // 更新操作
            bankAccount = this.getById(supplierBankAccountInfoVo.getId());
            if (bankAccount == null) {
                log.error("银行账户不存在，ID：{}", supplierBankAccountInfoVo.getId());
                return false;
            }
            BeanUtil.copyProperties(supplierBankAccountInfoVo, bankAccount, "id", "createBy", "createTime");
        } else {
            // 新增操作
            bankAccount = new SrmTenantSupplierBankAccount();
            BeanUtil.copyProperties(supplierBankAccountInfoVo, bankAccount);
            bankAccount.setDelFlag(0);
        }

        // 注意：SrmTenantSupplierBankAccount设置租户ID，为租户数据
        // bankAccount.setTenantId(null); // 移除这行，让框架自动设置租户ID

        boolean result = this.saveOrUpdate(bankAccount);

        if (result) {
            log.info("供应商银行账户信息保存成功，银行账户ID：{}", bankAccount.getId());
        } else {
            log.error("供应商银行账户信息保存失败");
        }

        return result;
    }

    @Override
    public List<SrmTenantSupplierBankAccount> getBankAccountsBySupplierId(Long supplierId) {
        return this.list(new LambdaQueryWrapper<SrmTenantSupplierBankAccount>()
                .eq(SrmTenantSupplierBankAccount::getSupplierId, supplierId)
                .eq(SrmTenantSupplierBankAccount::getDelFlag, 0)
                .orderByDesc(SrmTenantSupplierBankAccount::getCreateTime));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSupplierBankAccount(Long id) {
        log.info("开始物理删除供应商银行账户信息，银行账户ID：{}", id);

        // 使用物理删除
        int result = this.baseMapper.physicalDeleteById(id);

        if (result > 0) {
            log.info("供应商银行账户信息物理删除成功，银行账户ID：{}", id);
        } else {
            log.error("供应商银行账户信息物理删除失败，银行账户ID：{}", id);
        }

        return result > 0;
    }

    @Override
    public Page<SrmTenantSupplierBankAccount> getBankAccountsBySupplierIdPage(Long supplierId, Page<SrmTenantSupplierBankAccount> page) {
        return this.lambdaQuery()
                .eq(SrmTenantSupplierBankAccount::getSupplierId, supplierId)
                .eq(SrmTenantSupplierBankAccount::getDelFlag, 0)
                .orderByDesc(SrmTenantSupplierBankAccount::getCreateTime)
                .page(page);
    }
}




