package com.ylz.saas.codegen.base_quality_indicator.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.base_quality_indicator.entity.BaseQualityIndicatorEntity;
import com.ylz.saas.codegen.base_quality_indicator.param.BaseQualityIndicatorParam;
import com.ylz.saas.codegen.base_quality_indicator.vo.BaseQualityIndicatorVo;

import java.util.List;

public interface BaseQualityIndicatorService extends IService<BaseQualityIndicatorEntity> {
	/**
     * 新增质量指标表
     * @param baseQualityIndicatorVo 质量指标表
     */
    boolean saveBaseQualityIndicator(BaseQualityIndicatorVo baseQualityIndicatorVo);
	
	/**
     * 修改质量指标表
     * @param baseQualityIndicatorVo 质量指标表
     */
    boolean updateBaseQualityIndicator(BaseQualityIndicatorVo baseQualityIndicatorVo);
	
	/**
     * 查询质量指标表详情
     * @param id
     */
    BaseQualityIndicatorEntity getByIdBaseQualityIndicator(Long id);
	
	/**
	* 通过id删除质量指标表
	*/
	boolean removeByIdsBaseQualityIndicator(List<Long> ids);
	/**
	 * 开关
	 * @param id
	 * @return
	 */
	Object switchStatus(Long id);

	/**
	 * 分页查询
	 * @param page
	 * @param param
	 * @return
	 */
	Page pageInfo(Page page, BaseQualityIndicatorParam param);

}