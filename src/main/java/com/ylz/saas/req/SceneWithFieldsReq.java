package com.ylz.saas.req;

import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class SceneWithFieldsReq {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 模板场景名称
     */
    private String sceneName;

    /**
     * 模板场景描述
     */
    private String description;

    /**
     * 字段信息
     */
    private List<SceneFieldItemReq> fields;
    @Data
    public static class SceneFieldItemReq {
        /**
         * 主键ID
         */
        private Long id;

        /**
         * 字段名称
         */
        private String fieldName;

        /**
         * 显示名称
         */
        private String fieldDisplayName;

        /**
         * 字段类型
         */
        private String fieldType;

        /**
         * 字段长度
         */
        private Integer fieldLength;

        /**
         * 是否必须
         */
        private Integer isRequired;

        /**
         * 默认值
         */
        private String defaultValue;

        /**
         * 字段描述
         */
        private String description;

    }
}


