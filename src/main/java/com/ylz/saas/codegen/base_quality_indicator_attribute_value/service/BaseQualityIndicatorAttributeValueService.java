package com.ylz.saas.codegen.base_quality_indicator_attribute_value.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.base_quality_indicator_attribute_value.entity.BaseQualityIndicatorAttributeValueEntity;
import com.ylz.saas.codegen.base_quality_indicator_attribute_value.vo.BaseQualityIndicatorAttributeValueVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

public interface BaseQualityIndicatorAttributeValueService extends IService<BaseQualityIndicatorAttributeValueEntity> {
	/**
     * 新增质量指标属性值表
     * @param baseQualityIndicatorAttributeValueVo 质量指标属性值表
     */
    boolean saveBaseQualityIndicatorAttributeValue(BaseQualityIndicatorAttributeValueVo baseQualityIndicatorAttributeValueVo);
	
	/**
     * 修改质量指标属性值表
     * @param baseQualityIndicatorAttributeValueVo 质量指标属性值表
     */
    boolean updateBaseQualityIndicatorAttributeValue(BaseQualityIndicatorAttributeValueVo baseQualityIndicatorAttributeValueVo);
	
	/**
     * 导入模板下载
     * @param response
     */
    void template(HttpServletResponse response);

    /**
     * 数据导入
     * @param file
     * @param response
     */
    void uploadData(MultipartFile file, HttpServletResponse response);
}