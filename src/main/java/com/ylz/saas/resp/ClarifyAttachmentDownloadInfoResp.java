package com.ylz.saas.resp;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 澄清公告附件下载信息
 * @TableName srm_clarify_attachment_download_info
 */
@Data
public class ClarifyAttachmentDownloadInfoResp implements Serializable {

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 租户供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 供应商编号
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 澄清公告ID
     */
    private Long clarifyNoticeId;

    /**
     * 下载人id
     */
    private String downloadUserId;

    /**
     * 下载人名称
     */
    private String downloadUserName;

    /**
     * 最新下载时间
     */
    private LocalDateTime lastDownloadTime;

    /**
     * 下载次数
     */
    private Long downloadCount;

}