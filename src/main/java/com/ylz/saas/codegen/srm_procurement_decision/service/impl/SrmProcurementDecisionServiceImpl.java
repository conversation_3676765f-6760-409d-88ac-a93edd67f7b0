package com.ylz.saas.codegen.srm_procurement_decision.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.beust.jcommander.internal.Lists;
import com.ylz.saas.admin.api.entity.SysDept;
import com.ylz.saas.codegen.srm_procurement_decision.entity.SrmProcurementDecisionEntity;
import com.ylz.saas.codegen.srm_procurement_decision.enums.DecisionStatus;
import com.ylz.saas.codegen.srm_procurement_decision.mapper.SrmProcurementDecisionMapper;
import com.ylz.saas.codegen.srm_procurement_decision.param.PageReq;
import com.ylz.saas.codegen.srm_procurement_decision.param.UpsertReq;
import com.ylz.saas.codegen.srm_procurement_decision.service.SrmProcurementDecisionService;
import com.ylz.saas.codegen.srm_procurement_plan.mapper.SrmProcurementPlanQueryMapper;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.enums.CodeGeneratorPrefixEnum;
import com.ylz.saas.common.sequence.generator.CodeGenerator;
import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.enums.AttachmentTypeEnum;
import com.ylz.saas.service.SrmProjectAttachmentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * SrmProcurementDecisionServiceImpl
 *
 * <AUTHOR>
 * @date 2025/8/5 16:32
 * @since v0.0.1
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SrmProcurementDecisionServiceImpl extends ServiceImpl<SrmProcurementDecisionMapper, SrmProcurementDecisionEntity> implements SrmProcurementDecisionService {
    private final CodeGenerator codeGenerator;
    private final SrmProjectAttachmentService projectAttachmentService;
    private final SrmProcurementPlanQueryMapper srmProcurementPlanQueryMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void upsert(UpsertReq req) {
        //1 参数校验
        req.validate();

        //2 新增或编辑决策
        SrmProcurementDecisionEntity buildOne = req.buildOne();
        List<SrmProjectAttachment> batchAttachments = Lists.newArrayList();
        if (Objects.isNull(req.getId())) {
            // 2.1 新增
            // 编码：CGJC+年月日（yyyyMMdd）+四位数计数（如0001）
            String code = codeGenerator.generateWithDate(CodeGeneratorPrefixEnum.CGJC.name());
            buildOne.setCode(code);
            buildOne.setProjectId(req.getProjectId());
            buildOne.setProjectCode(req.getProjectCode());
            buildOne.setProjectName(req.getProjectName());
            buildOne.setRelationPlanList(req.getRelationPlanList());
            buildOne.setApplicant(req.getApplicant());
            buildOne.setApplicantName(req.getApplicantName());
            buildOne.setApplyDeptId(req.getApplyDeptId());
            buildOne.setStatus(DecisionStatus.NEW);
            buildOne.setDelFlag(false);
            //2.1.1 TODO 发起生效审批？ 自动 和 自动审核人
        } else {
            // 2.2 编辑
            SrmProcurementDecisionEntity existOne = this.getById(req.getId());
            ExceptionUtil.check(Objects.isNull(existOne), "500", String.format("采购决策（id:%s）不存在,请检查！", req.getId()));
            // 新建审批撤回 和 新建审批不通过才能编辑
            boolean n_eq1 = !existOne.getStatus().equals(DecisionStatus.NEW_APPROVE_REVOKE);
            boolean n_eq2 = !existOne.getStatus().equals(DecisionStatus.NEW_APPROVE_REJECT);
            ExceptionUtil.check(n_eq1 && n_eq2, "500", "新建审批撤回或新建审批不通过状态才能编辑");

            req.setProjectId(existOne.getProjectId());
            // 附件先删
            projectAttachmentService.remove(Wrappers.lambdaQuery(SrmProjectAttachment.class)
                    .eq(SrmProjectAttachment::getBusinessId, existOne.getId())
                    .eq(SrmProjectAttachment::getProjectId, req.getProjectId())
                    .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.PROCUREMENT_DECISION));
        }

        // 3 决策落库
        this.saveOrUpdate(buildOne);
        // 3.1 附件再增或新增 附件落库
        if (CollUtil.isNotEmpty(req.getAttachments())) {
            batchAttachments = req.getAttachments()
                    .stream()
                    .map(a -> SrmProjectAttachment.builder()
                            .projectId(req.getProjectId())
                            .businessType(AttachmentTypeEnum.PROCUREMENT_DECISION)
                            .businessId(buildOne.getId())
                            .fileName(a.getFileName())
                            .filePath(a.getFilePath()).build())
                    .toList();
        }
        if (CollUtil.isNotEmpty(batchAttachments)) {
            projectAttachmentService.saveBatch(batchAttachments);
        }
    }

    @Override
    public SrmProcurementDecisionEntity detail(Long id) {
        SrmProcurementDecisionEntity existOne = this.getById(id);
        ExceptionUtil.check(Objects.isNull(existOne), "500", String.format("采购决策（id:%s）不存在,请检查！", id));
        // 附件
        List<UpsertReq.AttachmentBO> attachmentList = projectAttachmentService.list(Wrappers.lambdaQuery(SrmProjectAttachment.class)
                        .eq(SrmProjectAttachment::getBusinessId, id)
                        .eq(SrmProjectAttachment::getProjectId, existOne.getProjectId())
                        .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.PROCUREMENT_DECISION))
                .stream()
                .map(a -> UpsertReq.AttachmentBO.builder()
                        .fileName(a.getFileName())
                        .filePath(a.getFilePath())
                        .build()).toList();
        existOne.setAttachmentList(attachmentList);
        // 申请部门名称
        List<SysDept> depts = srmProcurementPlanQueryMapper.getDeptsByIds(Lists.newArrayList(existOne.getApplyDeptId()), null);
        if (CollectionUtils.isNotEmpty(depts)) {
            existOne.setApplyDeptName(depts.get(0).getName());
        }
        return existOne;
    }

    @Override
    public Page<SrmProcurementDecisionEntity> page(Page page, PageReq req) {
        QueryWrapper<SrmProcurementDecisionEntity> queryWrapper = req.toWrapper();
        Page resultPage = this.page(page, queryWrapper);
        // 申请部门名称和附件
        if (CollectionUtils.isNotEmpty(resultPage.getRecords())) {
            List<SrmProcurementDecisionEntity> records = resultPage.getRecords();
            List<Long> applyDeptIds = records.stream()
                    .map(SrmProcurementDecisionEntity::getApplyDeptId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .toList();
            // 批量查询部门信息（不受数据权限控制）
            Map<Long, SysDept> deptMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(applyDeptIds)) {
                deptMap = srmProcurementPlanQueryMapper.getDeptsByIds(applyDeptIds, null)
                        .stream()
                        .collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
            }
            final Map<Long, SysDept> finalDeptMap = deptMap;

            // 批量查附件
            List<Long> ids = records.stream()
                    .map(SrmProcurementDecisionEntity::getId)
                    .toList();
            List<Long> projectIds = records.stream()
                    .map(SrmProcurementDecisionEntity::getProjectId)
                    .toList();
            // attachmentMap 决策id-附件map
            Map<Long, List<SrmProjectAttachment>> attachmentMap = projectAttachmentService.list(Wrappers.lambdaQuery(SrmProjectAttachment.class)
                            .in(SrmProjectAttachment::getBusinessId, ids)
                            .in(SrmProjectAttachment::getProjectId, projectIds)
                            .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.PROCUREMENT_DECISION))
                    .stream()
                    .collect(Collectors.groupingBy(SrmProjectAttachment::getBusinessId));
            records.forEach(record -> {
                // 申请部门名称
                if (Objects.nonNull(record.getApplyDeptId())) {
                    String deptName = Optional.ofNullable(finalDeptMap.get(record.getApplyDeptId()))
                            .map(SysDept::getName).orElse("-");
                    record.setApplyDeptName(deptName);
                }
                // 附件
                List<UpsertReq.AttachmentBO> attachmentList = attachmentMap.getOrDefault(record.getId(), Lists.newArrayList())
                        .stream()
                        .map(a -> UpsertReq.AttachmentBO.builder()
                                .fileName(a.getFileName())
                                .filePath(a.getFilePath())
                                .build()).toList();
                record.setAttachmentList(attachmentList);
            });
        }

        return resultPage;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logicDelete(Long id) {
        SrmProcurementDecisionEntity existOne = this.getById(id);
        ExceptionUtil.check(Objects.isNull(existOne), "500", String.format("采购决策（id:%s）不存在,请检查！", id));
        // 新建审批撤回 和 新建审批不通过才能删除
        boolean n_eq1 = !existOne.getStatus().equals(DecisionStatus.NEW_APPROVE_REVOKE);
        boolean n_eq2 = !existOne.getStatus().equals(DecisionStatus.NEW_APPROVE_REJECT);
        ExceptionUtil.check(n_eq1 && n_eq2, "500", "新建审批撤回或新建审批不通过状态才能删除");
        // 删附件
        projectAttachmentService.remove(Wrappers.lambdaQuery(SrmProjectAttachment.class)
                .eq(SrmProjectAttachment::getBusinessId, id)
                .eq(SrmProjectAttachment::getProjectId, existOne.getProjectId())
                .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.PROCUREMENT_DECISION));
        // 逻辑删决策
        this.removeById(SrmProcurementDecisionEntity.builder().id(id).build());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void decisionInvalid(Long id) {
        SrmProcurementDecisionEntity existOne = this.getById(id);
        ExceptionUtil.check(Objects.isNull(existOne), "500", String.format("采购决策（id:%s）不存在,请检查！", id));
        // 前置状态为：已生效
        ExceptionUtil.check(!existOne.getStatus().equals(DecisionStatus.EFFECT), "500", "只能作废已生效决策！");

        // 状态 => 作废待审批（查看、审批撤销）
        // 发起作废审批
    }

    @Override
    public void approveRevoke(Long id) {
        SrmProcurementDecisionEntity existOne = this.getById(id);
        ExceptionUtil.check(Objects.isNull(existOne), "500", String.format("采购决策（id:%s）不存在,请检查！", id));
        // 新建待审批 和 作废待审批才能撤销审批
        boolean n_eq1 = !existOne.getStatus().equals(DecisionStatus.NEW);
        boolean n_eq2 = !existOne.getStatus().equals(DecisionStatus.INVALID_APPROVE);
        ExceptionUtil.check(n_eq1 && n_eq2, "500", "新建待审批和作废待审批才能撤销审批状态才能撤销审批");
        // 新建待审批撤销
        if(existOne.getStatus().equals(DecisionStatus.NEW)){
            // 状态 => 新建审批撤回
        }

        // 作废待审批
        if(existOne.getStatus().equals(DecisionStatus.INVALID_APPROVE)){
            // 状态 => 已生效
        }
    }
}