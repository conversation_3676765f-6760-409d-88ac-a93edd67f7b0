package com.ylz.saas.req;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

@Data
public class BaseAnnouncementTemplateUpdateReq {

    /**
     * 主键ID
     */
    @NotNull(message = "模板ID不能为空")
    private Long id;

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    private String templateName;

    /**
     * 类型
     */
    @NotBlank(message = "模板类型不能为空")
    private String type;

    /**
     * 版本号
     */
    @NotBlank(message = "模板版本号不能为空")
    private String version;

    /**
     * 状态（ENABLE-启用、DISABLE-禁用）
     */
    @NotBlank(message = "模板状态不能为空")
    private String status;

    /**
     * 模板说明
     */
    private String description;

    /**
     * 图标
     */
    private String iconUrl;

    /**
     * 模板内容
     */
    private String content;
    /**
     * 是否默认
     */
    private Integer isDefault;

}
