package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ylz.saas.admin.api.dto.MessageSmsDTO;
import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.common.core.exception.GlobalResultCode;
import com.ylz.saas.req.*;
import com.ylz.saas.resp.SrmTenderRegisterInfoListResp;
import com.ylz.saas.vo.BidderResponseVo;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.security.service.SaasUser;
import com.ylz.saas.common.security.util.SecurityUtils;
import com.ylz.saas.entity.*;
import com.ylz.saas.enums.*;
import com.ylz.saas.enums.DepositStatusEnum;
import com.ylz.saas.service.*;
import com.ylz.saas.mapper.SrmTenderSupplierResponseMapper;
import com.ylz.saas.util.IpUtils;
import com.ylz.saas.vo.ValidateTenderResult;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【srm_tender_supplier_response(供应商响应表)】的数据库操作Service实现
* @createDate 2025-06-10 15:49:11
*/
@Service
@Slf4j
@RequiredArgsConstructor
public class SrmTenderSupplierResponseServiceImpl extends ServiceImpl<SrmTenderSupplierResponseMapper, SrmTenderSupplierResponse>
    implements SrmTenderSupplierResponseService{

    private final SrmProcurementProjectService srmProcurementProjectService;
    private final SrmProcurementProjectSectionService srmProcurementProjectSectionService;
    private final SrmTenderNoticeService srmTenderNoticeService;
    private final SrmTenantSupplierContactService srmTenantSupplierContactService;
    private final SrmTenderSupplierInviteService srmTenderSupplierInviteService;
    private final SrmTenderRequirementService srmTenderRequirementService;
    private final SrmProjectAttachmentService srmProjectAttachmentService;
    private final SrmTenderSupplierBidderResponseService srmTenderSupplierBidderResponseService;
    private final SrmTenantSupplierInfoService srmTenantSupplierInfoService;
    @Resource
    private AsyncSmsService asyncSmsService;

    @Value("${app.api-url:https://ayn.canpanscp.com}")
    private String appApiUrl;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void tenderRegister(SrmTenderSupplierResponseApplyReq req) {
        // 校验供应商、公告和标段信息
        ValidateTenderResult validateResult = validateSupplierAndProject(req.getNoticeId(), null);
        SrmProcurementProject project = validateResult.getProject();
        SrmTenderNotice tenderNotice = validateResult.getTenderNotice();
        // 校验报名时间
        LocalDateTime now = LocalDateTime.now();
        if (tenderNotice.getRegisterStartTime() != null && now.isBefore(tenderNotice.getRegisterStartTime())) {
            ExceptionUtil.checkNonNull(null, "报名尚未开始");
        }
        if (tenderNotice.getRegisterEndTime() != null && now.isAfter(tenderNotice.getRegisterEndTime())) {
            ExceptionUtil.checkNonNull(null, "报名已结束");
        }
        // 校验标段信息
        SrmProcurementProjectSection section = srmProcurementProjectSectionService.getById(req.getSectionId());
        ExceptionUtil.checkNonNull(section, "标段不存在");

        SrmTenantSupplierInfo tenantSupplierInfo = validateResult.getTenantSupplierInfo();

        this.checkTenderRequirement(tenderNotice, section, req.getBidderResponseList());

        // 删除已存在的
        this.lambdaUpdate()
                .eq(SrmTenderSupplierResponse::getNoticeId, tenderNotice.getId())
                .eq(SrmTenderSupplierResponse::getSectionId, section.getId())
                .eq(SrmTenderSupplierResponse::getTenantSupplierId, tenantSupplierInfo.getId())
                .remove();
        // 获取或创建供应商响应记录
        SrmTenderSupplierResponse supplierResponse = getOrCreateSupplierResponse(validateResult, section.getId(), TenderSupplierResponseStageEnum.REGISTER);
        // 设置联系信息
        supplierResponse.setContactPerson(req.getContactPerson());
        supplierResponse.setContactPhone(req.getContactPhone());
        supplierResponse.setContactEmail(req.getContactEmail());
        supplierResponse.setInviteReceipt(req.getInviteReceipt());
        if(StringUtils.isNotBlank(req.getAttachmentPath())){
            SrmProjectAttachment srmProjectAttachment = new SrmProjectAttachment();
            srmProjectAttachment.setProjectId(project.getId());
            srmProjectAttachment.setBusinessId(supplierResponse.getId());
            srmProjectAttachment.setBusinessType(AttachmentTypeEnum.INVITE_RECEIPT);
            srmProjectAttachment.setFileName(req.getAttachmentName());
            srmProjectAttachment.setFilePath(req.getAttachmentPath());
            srmProjectAttachmentService.save(srmProjectAttachment);
        }

        this.updateById(supplierResponse);

        // 处理投标响应
        if (CollectionUtils.isNotEmpty(req.getBidderResponseList())) {
            // 获取要求信息并构建映射
            List<Long> requirementIds = req.getBidderResponseList().stream()
                    .map(BidderResponseVo::getRequirementId)
                    .toList();
            List<SrmTenderRequirement> srmTenderRequirements = srmTenderRequirementService.listByIds(requirementIds);
            Map<Long, SrmTenderRequirement> requirementMap = srmTenderRequirements.stream()
                    .collect(Collectors.toMap(SrmTenderRequirement::getId, Function.identity()));
                    
            // 创建新的投标响应记录
            List<SrmTenderSupplierBidderResponse> bidderResponseList = req.getBidderResponseList().stream()
                    .map(item -> this.getSrmTenderSupplierBidderResponse(validateResult, item, requirementMap.get(item.getRequirementId()), supplierResponse)).toList();
            
            // 批量保存
            srmTenderSupplierBidderResponseService.lambdaUpdate()
                    .eq(SrmTenderSupplierBidderResponse::getNoticeId, tenderNotice.getId())
                    .eq(SrmTenderSupplierBidderResponse::getSectionId, section.getId())
                    .eq(SrmTenderSupplierBidderResponse::getTenantSupplierId, tenantSupplierInfo.getId())
                    .in(SrmTenderSupplierBidderResponse::getRequirementId, requirementIds)
                    .remove();
            srmTenderSupplierBidderResponseService.saveBatch(bidderResponseList);
        }
        // 修改项目状态
        srmProcurementProjectService.updateProgressStatus(project.getProjectCode(), ProjectProgressStatusEnum.REGISTER, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void payDeposit(PayDepositReq req) {
        ExceptionUtil.checkNotEmpty(req.getDepositResponseList(), "费用信息不能为空");
        List<Long> requirementIds = req.getDepositResponseList().stream().map(item -> item.getBidderResponse().getRequirementId()).toList();
        ExceptionUtil.checkNotEmpty(requirementIds, "费用要求id不能为空");

        // 标段信息
        List<Long> sectionIds = req.getDepositResponseList().stream().map(PayDepositReq.DepositResponse::getSectionId).toList();
        List<SrmProcurementProjectSection> sectionList = srmProcurementProjectSectionService.listByIds(sectionIds);
        ExceptionUtil.checkNotEmpty(sectionList, "标段不存在");
        Map<Long, SrmProcurementProjectSection> sectionMap = sectionList.stream().collect(Collectors.toMap(SrmProcurementProjectSection::getId, Function.identity()));

        // 校验供应商、公告和标段信息
        ValidateTenderResult validateResult = validateSupplierAndProject(req.getNoticeId(), null);
        SrmTenderNotice tenderNotice = validateResult.getTenderNotice();

        List<SrmTenderRequirement> srmTenderRequirements = srmTenderRequirementService.listByIds(requirementIds);
        Map<Long, SrmTenderRequirement> requirementMap = srmTenderRequirements.stream()
                .collect(Collectors.toMap(SrmTenderRequirement::getId, Function.identity()));
        for (PayDepositReq.DepositResponse depositResponse : req.getDepositResponseList()) {
            SrmProcurementProjectSection section = sectionMap.get(depositResponse.getSectionId());
            ExceptionUtil.checkNonNull(section, "标段不存在");
            SrmTenderRequirement requirement = requirementMap.get(depositResponse.getBidderResponse().getRequirementId());
            if (requirement == null) {
                continue;
            }

            SrmTenderSupplierResponse supplierResponse = null;
            if (requirement.getRequirementType() == BidsSegmentTypeEnum.FEE) {
                ExceptionUtil.check(LocalDateTime.now().isAfter(tenderNotice.getQuoteEndTime()), GlobalResultCode.INVALID_STATE, "报价已截止，无法提交保证金");
                // 获取或创建供应商响应记录
                supplierResponse = getOrCreateSupplierResponse(validateResult, section.getId(), TenderSupplierResponseStageEnum.PAY_DEPOSIT);
                supplierResponse.setDepositStatus(DepositStatusEnum.PAID); // 已缴纳，待审核
                this.updateById(supplierResponse);
            }
            if (requirement.getRequirementType() == BidsSegmentTypeEnum.BID_DOCUMENT) {
                ExceptionUtil.check(LocalDateTime.now().isBefore(tenderNotice.getBidDocPayStartTime())
                                || LocalDateTime.now().isAfter(tenderNotice.getBidDocPayEndTime()),
                        GlobalResultCode.INVALID_STATE, "投标已截止，无法缴纳标书费");
                supplierResponse = getOrCreateSupplierResponse(validateResult, section.getId(), TenderSupplierResponseStageEnum.PAY_TENDER_FEE);
                supplierResponse.setTenderFeeStatus(TenderFeeStatusEnum.PAID); // 已缴纳，待审核
                this.updateById(supplierResponse);
            }

            srmTenderSupplierBidderResponseService.lambdaUpdate()
                    .eq(SrmTenderSupplierBidderResponse::getNoticeId, tenderNotice.getId())
                    .eq(SrmTenderSupplierBidderResponse::getSectionId, section.getId())
                    .eq(SrmTenderSupplierBidderResponse::getTenantSupplierId, validateResult.getTenantSupplierInfo().getId())
                    .eq(SrmTenderSupplierBidderResponse::getRequirementId, requirement.getId())
                    .remove();
            if (supplierResponse != null) {
                SrmTenderSupplierBidderResponse payDepositResp = this.getSrmTenderSupplierBidderResponse(validateResult, depositResponse.getBidderResponse(), requirement, supplierResponse);
                srmTenderSupplierBidderResponseService.save(payDepositResp);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void withdrawn(WithdrawnTenderRegisterReq req) {
        ValidateTenderResult validateTenderResult = this.validateSupplierAndProject(req.getNoticeId(), null);
        SrmTenderSupplierResponse supplierResponse = this.lambdaQuery().eq(SrmTenderSupplierResponse::getNoticeId, req.getNoticeId())
                .eq(SrmTenderSupplierResponse::getSectionId, req.getSectionId())
                .eq(SrmTenderSupplierResponse::getTenantSupplierId, validateTenderResult.getTenantSupplierInfo().getId())
                .one();
        switch (req.getType()) {
            // 撤回报名
            case 1:
                if (supplierResponse == null) {
                    return;
                }
                supplierResponse.setRegisterStatus(TenderSupplierRegisterStatusEnum.WITHDRAWN);
                this.updateById(supplierResponse);
                srmTenderSupplierBidderResponseService.lambdaUpdate()
                        .eq(SrmTenderSupplierBidderResponse::getTenderSupplierResponseId, supplierResponse.getId())
                        .in(SrmTenderSupplierBidderResponse::getRequirementType, Lists.newArrayList(BidsSegmentTypeEnum.QUALIFICATION, BidsSegmentTypeEnum.CONDITION))
                        .remove();
                break;
                // 撤回保证金
            case 2:
                if (supplierResponse == null) {
                    return;
                }
                supplierResponse.setDepositStatus(DepositStatusEnum.WITHDRAWN);
                this.updateById(supplierResponse);
                srmTenderSupplierBidderResponseService.lambdaUpdate()
                        .eq(SrmTenderSupplierBidderResponse::getTenderSupplierResponseId, supplierResponse.getId())
                        .eq(SrmTenderSupplierBidderResponse::getRequirementType, BidsSegmentTypeEnum.FEE)
                        .remove();
                break;
                // 撤回邀请回执
            case 3:
                SrmTenderSupplierInvite supplierInvite = srmTenderSupplierInviteService.lambdaQuery()
                        .eq(SrmTenderSupplierInvite::getNoticeId, req.getNoticeId())
                        .eq(SrmTenderSupplierInvite::getSectionId, req.getSectionId())
                        .eq(SrmTenderSupplierInvite::getTenantSupplierId, validateTenderResult.getTenantSupplierInfo().getId())
                        .one();
                ExceptionUtil.checkNonNull(supplierInvite, "未邀请供应商");
                ExceptionUtil.check(supplierInvite.getInviteStatus() != InviteStatusEnum.ACCEPTED, GlobalResultCode.INVALID_STATE, "供应商响应状态不是已提交状态");
                srmTenderSupplierInviteService.lambdaUpdate()
                        .set(SrmTenderSupplierInvite::getInviteStatus, InviteStatusEnum.WITHDRAWN)
                        .set(SrmTenderSupplierInvite::getTenderSupplierResponseId, null)
                        .set(SrmTenderSupplierInvite::getResponseContent, null)
                        .set(SrmTenderSupplierInvite::getConcatName, null)
                        .set(SrmTenderSupplierInvite::getConcatPhone, null)
                        .set(SrmTenderSupplierInvite::getConcatEmail, null)
                        .set(SrmTenderSupplierInvite::getResponseTime, null)
                        .eq(SrmTenderSupplierInvite::getNoticeId, req.getNoticeId())
                        .eq(SrmTenderSupplierInvite::getSectionId, req.getSectionId())
                        .eq(SrmTenderSupplierInvite::getTenantSupplierId, validateTenderResult.getTenantSupplierInfo().getId())
                        .update();
                srmProjectAttachmentService.lambdaUpdate().eq(SrmProjectAttachment::getBusinessId, supplierInvite.getId())
                        .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.INVITE_RECEIPT).remove();
                break;
                // 撤回标书费缴纳
            case 4:
                if (supplierResponse == null) {
                    return;
                }
                supplierResponse.setTenderFeeStatus(TenderFeeStatusEnum.WITHDRAWN);
                this.updateById(supplierResponse);
                srmTenderSupplierBidderResponseService.lambdaUpdate()
                        .eq(SrmTenderSupplierBidderResponse::getTenderSupplierResponseId, supplierResponse.getId())
                        .eq(SrmTenderSupplierBidderResponse::getRequirementType, BidsSegmentTypeEnum.BID_DOCUMENT)
                        .remove();
                break;

        }
    }

    private SrmTenderSupplierBidderResponse getSrmTenderSupplierBidderResponse(ValidateTenderResult validateResult, BidderResponseVo bidderResponseVo,
                                                                               SrmTenderRequirement requirement, SrmTenderSupplierResponse supplierResponse) {
        SrmTenderSupplierBidderResponse srmTenderSupplierBidderResponse = new SrmTenderSupplierBidderResponse();
        srmTenderSupplierBidderResponse.setProjectId(validateResult.getProject().getId());
        srmTenderSupplierBidderResponse.setNoticeId(validateResult.getTenderNotice().getId());
        srmTenderSupplierBidderResponse.setSectionId(supplierResponse.getSectionId());
        srmTenderSupplierBidderResponse.setTenantSupplierId(validateResult.getTenantSupplierInfo().getId());
        // 使用新创建的供应商响应ID
        srmTenderSupplierBidderResponse.setTenderSupplierResponseId(supplierResponse.getId());
        srmTenderSupplierBidderResponse.setRequirementId(bidderResponseVo.getRequirementId());
        if (requirement != null) {
            srmTenderSupplierBidderResponse.setRequirementType(requirement.getRequirementType());
            srmTenderSupplierBidderResponse.setResponseName(requirement.getRequirementName());
        }
        srmTenderSupplierBidderResponse.setResponseContent(bidderResponseVo.getResponseContent());
        srmTenderSupplierBidderResponse.setDelFlag(YesNoEnum.NO.getCode());
        return srmTenderSupplierBidderResponse;
    }
    
    /**
     * 获取或创建供应商响应记录
     * 
     * @param validateResult 校验结果
     * @return 供应商响应记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SrmTenderSupplierResponse getOrCreateSupplierResponse(ValidateTenderResult validateResult, long sectionId, TenderSupplierResponseStageEnum stage) {

        SrmTenantSupplierInfo tenantSupplierInfo = validateResult.getTenantSupplierInfo();
        SrmTenderNotice tenderNotice = validateResult.getTenderNotice();
        SrmProcurementProject project = validateResult.getProject();

        if (project.getInviteMethod() == InviteMethodEnum.INVITE && validateResult.isSupplierSelfOps()) {
            SrmTenderSupplierInvite supplierInvite = srmTenderSupplierInviteService.lambdaQuery()
                    .eq(SrmTenderSupplierInvite::getNoticeId, tenderNotice.getId())
                    .eq(SrmTenderSupplierInvite::getSectionId, sectionId)
                    .eq(SrmTenderSupplierInvite::getTenantSupplierId, tenantSupplierInfo.getId())
                    .one();
            ExceptionUtil.checkNonNull(supplierInvite, "未邀请供应商");
        }

        SrmTenderSupplierResponse srmTenderSupplierResponse = this.lambdaQuery()
                .eq(SrmTenderSupplierResponse::getNoticeId, tenderNotice.getId())
                .eq(SrmTenderSupplierResponse::getSectionId, sectionId)
                .eq(SrmTenderSupplierResponse::getTenantSupplierId, tenantSupplierInfo.getId())
                .one();

        if (srmTenderSupplierResponse == null) {
            if (stage != TenderSupplierResponseStageEnum.INVITE &&
                    (project.getSourcingType() == BaseServiceTypeFieldService.BuyWayEnum.ZB
                            || project.getSourcingType() == BaseServiceTypeFieldService.BuyWayEnum.JZTP)) {
                ExceptionUtil.check(tenderNotice.getDocStatus() != ApproveStatusEnum.APPROVE, GlobalResultCode.INVALID_STATE, "招标/竞谈文件未审批通过");
            }
            // 创建新记录
            srmTenderSupplierResponse = new SrmTenderSupplierResponse();
            srmTenderSupplierResponse.setProjectId(project.getId());
            srmTenderSupplierResponse.setNoticeId(tenderNotice.getId());
            srmTenderSupplierResponse.setSectionId(sectionId);
            srmTenderSupplierResponse.setTenantSupplierId(tenantSupplierInfo.getId());
            srmTenderSupplierResponse.setSupplierName(tenantSupplierInfo.getSupplierName());
            srmTenderSupplierResponse.setDelFlag(YesNoEnum.NO.getCode());
        }
        if (stage != TenderSupplierResponseStageEnum.INVITE) {
            // 设置响应时间和IP
            if (srmTenderSupplierResponse.getResponseTime() == null) {
                srmTenderSupplierResponse.setResponseTime(LocalDateTime.now());
            }
            if (srmTenderSupplierResponse.getResponseIp() == null) {
                srmTenderSupplierResponse.setResponseIp(IpUtils.getClientIP());
            }
            if (srmTenderSupplierResponse.getResponseType() == null) {
                srmTenderSupplierResponse.setResponseType(project.getInviteMethod() == InviteMethodEnum.INVITE ? TenderResponseTypeEnum.INVITE : TenderResponseTypeEnum.SELF);
            }
        }

        // 设置状态
        switch (stage) {
            case REGISTER:
                if (srmTenderSupplierResponse.getRegisterStatus() == null) {
                    srmTenderSupplierResponse.setRegisterStatus(TenderSupplierRegisterStatusEnum.REGISTERED);
                }
                break;
            case PAY_DEPOSIT:
                if (srmTenderSupplierResponse.getDepositStatus() == null) {
                    srmTenderSupplierResponse.setDepositStatus(DepositStatusEnum.PAID);
                }
                break;
            case PAY_TENDER_FEE:
                if (srmTenderSupplierResponse.getTenderFeeStatus() == null) {
                    srmTenderSupplierResponse.setTenderFeeStatus(TenderFeeStatusEnum.PAID);
                }
                break;
            case DOWNLOAD_BID_FILE:
                ExceptionUtil.check(srmTenderSupplierResponse.getTenderFeeStatus() != TenderFeeStatusEnum.VERIFIED, GlobalResultCode.INVALID_STATE, "标书费用未支付或未审核通过！");
                if (srmTenderSupplierResponse.getBidFileDownloadStatus() == null) {
                    srmTenderSupplierResponse.setBidFileDownloadStatus(DownloadStatusEnum.DOWNLOADED);
                }
                break;
            case INVITE:
                srmTenderSupplierResponse.setResponseTime(null);
                break;
            case INVITE_RESPONSE:
                break;
            case QUOTE:
                if (srmTenderSupplierResponse.getQuoteStatus() == null) {
                    srmTenderSupplierResponse.setQuoteStatus(QuoteStatusEnum.WAITING);
                }
                break;
            default:
                ExceptionUtil.checkNonNull(null, "未知的供应商响应阶段！");
        }

        // 保存新记录
        this.saveOrUpdate(srmTenderSupplierResponse);
        return srmTenderSupplierResponse;
    }

    /**
     * 校验供应商、公告和标段信息
     *
     * @param noticeId         公告ID
     * @param tenantSupplierId
     * @return 校验结果
     */
    @Override
    public ValidateTenderResult validateSupplierAndProject(Long noticeId, Long tenantSupplierId) {
        SaasUser user = SecurityUtils.getUser();

        boolean supplierSelfOps;
        SrmTenantSupplierContact tenantSupplierContact = srmTenantSupplierContactService.lambdaQuery()
                .eq(SrmTenantSupplierContact::getLoginAccount, user.getUsername()).one();
        if (Objects.isNull(tenantSupplierId)) {
            // 校验供应商信息
            ExceptionUtil.checkNonNull(tenantSupplierContact, "供应商用户不存在");
            tenantSupplierId = tenantSupplierContact.getTenantSupplierId();
            supplierSelfOps = true;
        } else {
            supplierSelfOps = tenantSupplierContact != null && Objects.equals(tenantSupplierId, tenantSupplierContact.getTenantSupplierId());
        }

        SrmTenantSupplierInfo tenantSupplierInfo = srmTenantSupplierInfoService.getById(tenantSupplierId);
        ExceptionUtil.checkNonNull(tenantSupplierInfo, "租户供应商不存在");

        // 校验公告信息
        SrmTenderNotice tenderNotice = srmTenderNoticeService.getById(noticeId);
        ExceptionUtil.checkNonNull(tenderNotice, "公告不存在");
        
        SrmProcurementProject project = srmProcurementProjectService.getById(tenderNotice.getProjectId());
        ExceptionUtil.checkNonNull(project, "项目不存在");
        
        return new ValidateTenderResult(tenantSupplierInfo, tenderNotice, project, supplierSelfOps);
    }


    private void checkTenderRequirement(SrmTenderNotice tenderNotice, SrmProcurementProjectSection section, List<BidderResponseVo> bidderResponseList) {
        List<SrmTenderRequirement> requirementList = srmTenderRequirementService.lambdaQuery()
                .eq(SrmTenderRequirement::getNoticeId, tenderNotice.getId())
                .eq(SrmTenderRequirement::getSectionId, section.getId())
                .in(SrmTenderRequirement::getRequirementType, Lists.newArrayList(BidsSegmentTypeEnum.CONDITION, BidsSegmentTypeEnum.QUALIFICATION))
                .list();
        if (CollectionUtils.isEmpty(requirementList)) {
            return;
        }
        List<Long> responseRequirementIds = Optional.ofNullable(bidderResponseList).stream().flatMap(Collection::stream)
                .map(BidderResponseVo::getRequirementId).toList();
        List<SrmTenderRequirement> requirements = requirementList.stream()
                .filter(requirement -> Objects.equals(YesNoEnum.YES.getCode(), requirement.getNeedResponse())
                        && !responseRequirementIds.contains(requirement.getId())).toList();
        if (CollectionUtils.isNotEmpty(requirements)) {
            ExceptionUtil.checkNonNull(null, "供应商响应缺少以下必填项：" + requirements.stream().map(SrmTenderRequirement::getRequirementName).collect(Collectors.joining(",")));
        }
    }


    @Override
    public List<SrmTenderRegisterInfoListResp> getSelectionRegisterInfo(SrmTenderRegisterInfoQueryReq req) {
        ValidateTenderResult validateTenderResult = this.validateSupplierAndProject(req.getNoticeId(), null);
        SrmTenderNotice tenderNotice = validateTenderResult.getTenderNotice();
        SrmProcurementProject project = validateTenderResult.getProject();
        SrmTenantSupplierInfo tenantSupplierInfo = validateTenderResult.getTenantSupplierInfo();

        List<SrmProcurementProjectSection> sectionList = srmProcurementProjectSectionService.lambdaQuery()
                .eq(SrmProcurementProjectSection::getProjectId, project.getId())
                .like(StringUtils.isNotBlank(req.getSelectionName()), SrmProcurementProjectSection::getSectionName, req.getSelectionName())
                .eq(SrmProcurementProjectSection::getDelFlag, YesNoEnum.NO.getCode()).list();
        if (CollectionUtils.isEmpty(sectionList)) {
            return Lists.newArrayList();
        }

        // 如果是邀请，则只返回邀请的标段
        if (project.getInviteMethod() == InviteMethodEnum.INVITE) {
            List<SrmTenderSupplierInvite> inviteList = srmTenderSupplierInviteService.lambdaQuery()
                    .eq(SrmTenderSupplierInvite::getProjectId, project.getId())
                    .eq(SrmTenderSupplierInvite::getNoticeId, tenderNotice.getId())
                    .eq(SrmTenderSupplierInvite::getTenantSupplierId, tenantSupplierInfo.getId())
                    .list();
            if (CollectionUtils.isEmpty(inviteList)) {
                return Lists.newArrayList();
            }
            List<Long> inviteSectionIds = inviteList.stream().map(SrmTenderSupplierInvite::getSectionId).toList();
            sectionList.removeIf(section -> !inviteSectionIds.contains(section.getId()));
            if (CollectionUtils.isEmpty(sectionList)) {
                return Lists.newArrayList();
            }
        }

        List<Long> sectionIds = sectionList.stream().map(SrmProcurementProjectSection::getId).toList();
        List<SrmTenderSupplierResponse> supplierResponseList = this.lambdaQuery()
                .eq(SrmTenderSupplierResponse::getNoticeId, tenderNotice.getId())
                .in(SrmTenderSupplierResponse::getSectionId, sectionIds)
                .eq(SrmTenderSupplierResponse::getTenantSupplierId, tenantSupplierInfo.getId()).list();

        // 报名响应
        Map<Long, SrmTenderSupplierResponse> respSelectionIdMap = Optional.ofNullable(supplierResponseList).stream().flatMap(Collection::stream)
                .collect(Collectors.toMap(SrmTenderSupplierResponse::getSectionId, Function.identity(), (v1, v2) -> v1));

        // 保证金+标书费
        List<SrmTenderSupplierBidderResponse> srmTenderSupplierBidderResponseList = srmTenderSupplierBidderResponseService.lambdaQuery()
                .eq(SrmTenderSupplierBidderResponse::getNoticeId, tenderNotice.getId())
                .in(SrmTenderSupplierBidderResponse::getSectionId, sectionIds)
                .eq(SrmTenderSupplierBidderResponse::getTenantSupplierId, tenantSupplierInfo.getId())
                .in(SrmTenderSupplierBidderResponse::getRequirementType, Lists.newArrayList(BidsSegmentTypeEnum.FEE, BidsSegmentTypeEnum.BID_DOCUMENT)).list();
        Map<Long, SrmTenderSupplierBidderResponse> depositResponseMap = Optional.ofNullable(srmTenderSupplierBidderResponseList)
                .stream().flatMap(Collection::stream)
                .filter(item -> Objects.equals(BidsSegmentTypeEnum.FEE, item.getRequirementType()))
                .collect(Collectors.toMap(SrmTenderSupplierBidderResponse::getSectionId, Function.identity(), (v1, v2) -> v1));
        Map<Long, SrmTenderSupplierBidderResponse> tenderFeeResponseMap = Optional.ofNullable(srmTenderSupplierBidderResponseList)
                .stream().flatMap(Collection::stream)
                .filter(item -> Objects.equals(BidsSegmentTypeEnum.BID_DOCUMENT, item.getRequirementType()))
                .collect(Collectors.toMap(SrmTenderSupplierBidderResponse::getSectionId, Function.identity(), (v1, v2) -> v1));


        List<SrmTenderRequirement> requirementList = srmTenderRequirementService.lambdaQuery().eq(SrmTenderRequirement::getNoticeId, tenderNotice.getId())
                .in(SrmTenderRequirement::getSectionId, sectionIds)
                .in(SrmTenderRequirement::getRequirementType, Lists.newArrayList(BidsSegmentTypeEnum.FEE, BidsSegmentTypeEnum.BID_DOCUMENT))
                .list();
        Map<Long, SrmTenderRequirement> depositRequirementMap = Optional.ofNullable(requirementList).stream().flatMap(Collection::stream)
                .filter(item -> Objects.equals(BidsSegmentTypeEnum.FEE, item.getRequirementType()))
                .collect(Collectors.toMap(SrmTenderRequirement::getSectionId, Function.identity(), (v1, v2) -> v1));
        Map<Long, SrmTenderRequirement> tenderFeeRequirementMap = Optional.ofNullable(requirementList).stream().flatMap(Collection::stream)
                .filter(item -> Objects.equals(BidsSegmentTypeEnum.BID_DOCUMENT, item.getRequirementType()))
                .collect(Collectors.toMap(SrmTenderRequirement::getSectionId, Function.identity(), (v1, v2) -> v1));

        List<SrmTenderSupplierInvite> inviteList = srmTenderSupplierInviteService.lambdaQuery()
                .eq(SrmTenderSupplierInvite::getNoticeId, req.getNoticeId())
                .in(SrmTenderSupplierInvite::getSectionId, sectionIds)
                .eq(SrmTenderSupplierInvite::getTenantSupplierId, tenantSupplierInfo.getId())
                .list();
        Map<Long, SrmTenderSupplierInvite> inviteMap = Optional.ofNullable(inviteList).stream().flatMap(Collection::stream)
                .collect(Collectors.toMap(SrmTenderSupplierInvite::getSectionId, Function.identity(), (v1, v2) -> v1));

        return sectionList.stream().map(item -> {
            SrmTenderRegisterInfoListResp registerInfoListResp = new SrmTenderRegisterInfoListResp();
            registerInfoListResp.setProjectId(project.getId());
            registerInfoListResp.setProjectName(project.getProjectName());
            registerInfoListResp.setSectionId(item.getId());
            registerInfoListResp.setSelectionName(item.getSectionName());
            registerInfoListResp.setBidDocPayStartTime(tenderNotice.getBidDocPayStartTime());
            registerInfoListResp.setBidDocPayEndTime(tenderNotice.getBidDocPayEndTime());
            registerInfoListResp.setFileObtainStartTime(tenderNotice.getFileObtainStartTime());
            registerInfoListResp.setFileObtainEndTime(tenderNotice.getFileObtainEndTime());
            SrmTenderSupplierResponse srmTenderSupplierResponse = respSelectionIdMap.get(item.getId());
            registerInfoListResp.setRegistered(srmTenderSupplierResponse != null);
            if (srmTenderSupplierResponse != null) {
                registerInfoListResp.setRegisterTime(srmTenderSupplierResponse.getResponseTime());
                registerInfoListResp.setRegisterStatus(srmTenderSupplierResponse.getRegisterStatus());
                registerInfoListResp.setDepositStatus(srmTenderSupplierResponse.getDepositStatus());
                registerInfoListResp.setQuoteStatus(srmTenderSupplierResponse.getQuoteStatus());
                registerInfoListResp.setTenderFeeStatus(srmTenderSupplierResponse.getTenderFeeStatus());
                registerInfoListResp.setBidFileDownloadStatus(srmTenderSupplierResponse.getBidFileDownloadStatus());
                registerInfoListResp.setCurrentQuoteRound(srmTenderSupplierResponse.getCurrentRound());
                registerInfoListResp.setQuoteTime(srmTenderSupplierResponse.getNewestQuoteTime());
                registerInfoListResp.setQuoteIp(srmTenderSupplierResponse.getNewestQuoteIp());
            }
            SrmTenderRequirement depositRequirement = depositRequirementMap.get(item.getId());
            if (depositRequirement != null) {
                registerInfoListResp.setDepositRequirementId(depositRequirement.getId());
                registerInfoListResp.setDepositContent(depositRequirement.getRequirementContent());
            }
            SrmTenderRequirement tenderFeeRequirement = tenderFeeRequirementMap.get(item.getId());
            if (tenderFeeRequirement != null) {
                registerInfoListResp.setTenderFeeRequirementId(tenderFeeRequirement.getId());
                registerInfoListResp.setTenderFeeContent(tenderFeeRequirement.getRequirementContent());
            }
            SrmTenderSupplierBidderResponse depositResponse = depositResponseMap.get(item.getId());
            if (depositResponse != null) {
                registerInfoListResp.setDepositRespContent(depositResponse.getResponseContent());
                registerInfoListResp.setDepositRespBy(depositResponse.getCreateByName());
                registerInfoListResp.setDepositTime(depositResponse.getCreateTime());
            }
            SrmTenderSupplierBidderResponse tenderFeeResponse = tenderFeeResponseMap.get(item.getId());
            if (tenderFeeResponse != null) {
                registerInfoListResp.setTenderFeeRespContent(tenderFeeResponse.getResponseContent());
                registerInfoListResp.setTenderFeeRespBy(tenderFeeResponse.getCreateByName());
                registerInfoListResp.setTenderFeeTime(tenderFeeResponse.getCreateTime());
            }
            SrmTenderSupplierInvite invite = inviteMap.get(item.getId());
            if(invite != null){
                registerInfoListResp.setInviteTime(invite.getCreateTime());
                registerInfoListResp.setInviteStatus(invite.getInviteStatus());
                registerInfoListResp.setInviteResponseTime(invite.getResponseTime());
            }
            return registerInfoListResp;
        }).toList();
    }


    @Override
    public SrmTenderSupplierResponse getRegisteredInfo(NoticeAndSectionIdReq req) {
        ValidateTenderResult validateTenderResult = validateSupplierAndProject(req.getNoticeId(), req.getTenantSupplierId());
        SrmTenderSupplierResponse response = this.lambdaQuery().eq(SrmTenderSupplierResponse::getNoticeId, req.getNoticeId())
                .eq(SrmTenderSupplierResponse::getSectionId, req.getSectionId())
                .eq(SrmTenderSupplierResponse::getTenantSupplierId, validateTenderResult.getTenantSupplierInfo().getId())
                .one();
        List<SrmTenderSupplierBidderResponse> bidderResponseList = srmTenderSupplierBidderResponseService.lambdaQuery()
                .eq(SrmTenderSupplierBidderResponse::getNoticeId, req.getNoticeId())
                .eq(SrmTenderSupplierBidderResponse::getSectionId, req.getSectionId())
                .eq(SrmTenderSupplierBidderResponse::getTenantSupplierId, validateTenderResult.getTenantSupplierInfo().getId())
                .eq(SrmTenderSupplierBidderResponse::getTenderSupplierResponseId, response.getId()).list();
        if (CollectionUtils.isNotEmpty(bidderResponseList)) {
            List<Long> requirementIds = bidderResponseList.stream().map(SrmTenderSupplierBidderResponse::getRequirementId).toList();
            List<SrmTenderRequirement> srmTenderRequirements = srmTenderRequirementService.listByIds(requirementIds);
            Map<Long, SrmTenderRequirement> requirementMap = srmTenderRequirements.stream().collect(Collectors.toMap(SrmTenderRequirement::getId, Function.identity()));
            bidderResponseList.forEach(item -> item.setSrmTenderRequirement(requirementMap.get(item.getRequirementId())));
            response.setSrmTenderSupplierBidderResponseList(bidderResponseList);
        }
        response.setTenantSupplierCode(validateTenderResult.getTenantSupplierInfo().getSupplierCode());
        return response;
    }

    @Override
    public List<SrmTenderSupplierResponse> getRegisteredSupplierList(RegisteredSupplierQueryReq req) {
        List<Long> tenantSupplierList = null;
        if (CollectionUtils.isNotEmpty(req.getInviteStatusList())) {
            List<SrmTenderSupplierInvite> inviteList = srmTenderSupplierInviteService.lambdaQuery()
                    .eq(SrmTenderSupplierInvite::getNoticeId, req.getNoticeId())
                    .eq(SrmTenderSupplierInvite::getSectionId, req.getSectionId())
                    .in(SrmTenderSupplierInvite::getInviteStatus, req.getInviteStatusList())
                    .list();
            if (CollectionUtils.isNotEmpty(inviteList)) {
                tenantSupplierList = inviteList.stream().map(SrmTenderSupplierInvite::getTenantSupplierId).toList();
            } else {
                return Lists.newArrayList();
            }
        }
        List<SrmTenderSupplierResponse> supplierResponseList = this.lambdaQuery().eq(SrmTenderSupplierResponse::getNoticeId, req.getNoticeId())
                .eq(SrmTenderSupplierResponse::getSectionId, req.getSectionId())
                .eq(StringUtils.isNotEmpty(req.getSupplierName()), SrmTenderSupplierResponse::getSupplierName, req.getSupplierName())
                .eq(StringUtils.isNotEmpty(req.getRegisterStatus()), SrmTenderSupplierResponse::getRegisterStatus, req.getRegisterStatus())
                .in(CollectionUtils.isNotEmpty(req.getDepositStatusList()), SrmTenderSupplierResponse::getDepositStatus, req.getDepositStatusList())
                .in(CollectionUtils.isNotEmpty(req.getTenderFeeStatusList()), SrmTenderSupplierResponse::getTenderFeeStatus, req.getTenderFeeStatusList())
                .in(CollectionUtils.isNotEmpty(tenantSupplierList), SrmTenderSupplierResponse::getTenantSupplierId, tenantSupplierList)
                .list();
        if (CollectionUtils.isEmpty(supplierResponseList)) {
            return Lists.newArrayList();
        }
        List<Long> responseIds = supplierResponseList.stream().map(SrmTenderSupplierResponse::getId).toList();
        // 保证金
        List<SrmTenderSupplierBidderResponse> srmTenderSupplierBidderResponseList = srmTenderSupplierBidderResponseService.lambdaQuery()
                .eq(SrmTenderSupplierBidderResponse::getNoticeId, req.getNoticeId())
                .in(SrmTenderSupplierBidderResponse::getTenderSupplierResponseId, responseIds)
                .in(SrmTenderSupplierBidderResponse::getRequirementType, Lists.newArrayList(BidsSegmentTypeEnum.FEE, BidsSegmentTypeEnum.BID_DOCUMENT))
                .list();
        Map<Long, SrmTenderSupplierBidderResponse> depositResponseMap = Optional.ofNullable(srmTenderSupplierBidderResponseList).stream().flatMap(Collection::stream)
                .filter(item -> Objects.equals(item.getRequirementType(), BidsSegmentTypeEnum.FEE))
                .collect(Collectors.toMap(SrmTenderSupplierBidderResponse::getTenderSupplierResponseId, Function.identity(), (v1, v2) -> v1));
        Map<Long, SrmTenderSupplierBidderResponse> tenderFeeResponseMap = Optional.ofNullable(srmTenderSupplierBidderResponseList).stream().flatMap(Collection::stream)
                .filter(item -> Objects.equals(item.getRequirementType(), BidsSegmentTypeEnum.BID_DOCUMENT))
                .collect(Collectors.toMap(SrmTenderSupplierBidderResponse::getTenderSupplierResponseId, Function.identity(), (v1, v2) -> v1));

        // 邀请回执
        List<Long> tenantSupplierIds = supplierResponseList.stream().map(SrmTenderSupplierResponse::getTenantSupplierId).toList();
        List<SrmTenderSupplierInvite> inviteList = srmTenderSupplierInviteService.lambdaQuery()
                .eq(SrmTenderSupplierInvite::getNoticeId, req.getNoticeId())
                .eq(SrmTenderSupplierInvite::getSectionId, req.getSectionId())
                .in(SrmTenderSupplierInvite::getTenantSupplierId, tenantSupplierIds)
                .list();
        Map<Long, SrmTenderSupplierInvite> inviteMap = Optional.ofNullable(inviteList).stream().flatMap(Collection::stream)
                .collect(Collectors.toMap(SrmTenderSupplierInvite::getTenantSupplierId, Function.identity(), (v1, v2) -> v1));

        // 供应商code
        List<SrmTenantSupplierInfo> srmTenantSupplierInfos = srmTenantSupplierInfoService.listByIds(tenantSupplierIds);
        Map<Long, SrmTenantSupplierInfo> srmTenantSupplierInfoMap = srmTenantSupplierInfos.stream()
                .collect(Collectors.toMap(SrmTenantSupplierInfo::getId, Function.identity()));

        for (SrmTenderSupplierResponse srmTenderSupplierResponse : supplierResponseList) {
            SrmTenderSupplierBidderResponse depositResponse = depositResponseMap.get(srmTenderSupplierResponse.getId());
            if (depositResponse != null) {
                srmTenderSupplierResponse.setDepositTime(depositResponse.getCreateTime());
                srmTenderSupplierResponse.setDepositRespContent(depositResponse.getResponseContent());
            }
            SrmTenderSupplierBidderResponse tenderFeeResponse = tenderFeeResponseMap.get(srmTenderSupplierResponse.getId());
            if (tenderFeeResponse != null) {
                srmTenderSupplierResponse.setTenderFeeTime(tenderFeeResponse.getCreateTime());
                srmTenderSupplierResponse.setTenderFeeRespContent(tenderFeeResponse.getResponseContent());
            }
            SrmTenderSupplierInvite invite = inviteMap.get(srmTenderSupplierResponse.getTenantSupplierId());
            if(invite != null){
                srmTenderSupplierResponse.setInviteTime(invite.getCreateTime());
                srmTenderSupplierResponse.setInviteStatus(invite.getInviteStatus());
                srmTenderSupplierResponse.setInviteResponseTime(invite.getResponseTime());
            }
            srmTenderSupplierResponse.setTenantSupplierCode(srmTenantSupplierInfoMap.get(srmTenderSupplierResponse.getTenantSupplierId()).getSupplierCode());
        }
        return supplierResponseList;
    }

    @Override
    public void review(ReviewTenderRegisterReq req) {
        ValidateTenderResult validateTenderResult = this.validateSupplierAndProject(req.getNoticeId(), req.getTenantSupplierId());
        SrmTenderSupplierResponse supplierResponse = this.lambdaQuery().eq(SrmTenderSupplierResponse::getNoticeId, req.getNoticeId())
                .eq(SrmTenderSupplierResponse::getSectionId, req.getSectionId())
                .eq(SrmTenderSupplierResponse::getTenantSupplierId, validateTenderResult.getTenantSupplierInfo().getId())
                .one();
        switch (req.getType()) {
            // 审核报名
            case 1:
                if (supplierResponse == null) {
                    return;
                }
                ExceptionUtil.check(supplierResponse.getRegisterStatus() == TenderSupplierRegisterStatusEnum.PASSED
                        || supplierResponse.getRegisterStatus() == TenderSupplierRegisterStatusEnum.REJECTED, GlobalResultCode.INVALID_STATE, "供应商已审核");
                supplierResponse.setRegisterStatus(req.getPass() ? TenderSupplierRegisterStatusEnum.PASSED :TenderSupplierRegisterStatusEnum.REJECTED);
                supplierResponse.setRegisterReviewTime(LocalDateTime.now());
                supplierResponse.setRegisterRejectReason(req.getRejectReason());
                this.updateById(supplierResponse);
                // 发送报名结果短信通知
                sendRegisterReviewSms(validateTenderResult, req.getPass(), req.getNoticeId());
                break;
                // 审核保证金
            case 2:
                if (supplierResponse == null) {
                    return;
                }
                ExceptionUtil.check(supplierResponse.getDepositStatus() == DepositStatusEnum.VERIFIED
                        || supplierResponse.getDepositStatus() == DepositStatusEnum.REJECTED, GlobalResultCode.INVALID_STATE, "保证金已审核");
                supplierResponse.setDepositStatus(req.getPass() ? DepositStatusEnum.VERIFIED :DepositStatusEnum.REJECTED);
                supplierResponse.setDepositReviewTime(LocalDateTime.now());
                supplierResponse.setDepositRejectReason(req.getRejectReason());
                this.updateById(supplierResponse);
                break;
                // 审核邀请回执
            case 3:
                SrmTenderSupplierInvite supplierInvite = srmTenderSupplierInviteService.lambdaQuery()
                        .eq(SrmTenderSupplierInvite::getNoticeId, req.getNoticeId())
                        .eq(SrmTenderSupplierInvite::getSectionId, req.getSectionId())
                        .eq(SrmTenderSupplierInvite::getTenantSupplierId, validateTenderResult.getTenantSupplierInfo().getId())
                        .one();
                ExceptionUtil.checkNonNull(supplierInvite, "未邀请供应商");
                ExceptionUtil.check(supplierInvite.getInviteStatus() == InviteStatusEnum.PASSED
                        || supplierInvite.getInviteStatus() == InviteStatusEnum.REJECTED, GlobalResultCode.INVALID_STATE, "邀请回执已审核");
                supplierInvite.setInviteStatus(req.getPass() ? InviteStatusEnum.PASSED : InviteStatusEnum.REJECTED);
                supplierInvite.setReviewTime(LocalDateTime.now());
                supplierInvite.setRejectReason(req.getRejectReason());
                srmTenderSupplierInviteService.updateById(supplierInvite);
                // 发送邀请回执审核短信通知
                sendInviteReceiptReviewSms(validateTenderResult, req.getPass(), req.getNoticeId());
                break;
                // 审核标书费用
            case 4:
                if (supplierResponse == null) {
                    return;
                }
                ExceptionUtil.check(supplierResponse.getTenderFeeStatus() == TenderFeeStatusEnum.VERIFIED
                        || supplierResponse.getTenderFeeStatus() == TenderFeeStatusEnum.REJECTED, GlobalResultCode.INVALID_STATE, "标书费已审核");
                supplierResponse.setTenderFeeStatus(req.getPass() ? TenderFeeStatusEnum.VERIFIED :TenderFeeStatusEnum.REJECTED);
                supplierResponse.setTenderFeeReviewTime(LocalDateTime.now());
                supplierResponse.setTenderFeeRejectReason(req.getRejectReason());
                this.updateById(supplierResponse);
                break;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void inviteResponse(InviteResponseReq req) {
        ValidateTenderResult validateTenderResult = this.validateSupplierAndProject(req.getNoticeId(), null);
        SrmTenderSupplierInvite supplierInvite = srmTenderSupplierInviteService.lambdaQuery()
                .eq(SrmTenderSupplierInvite::getNoticeId, req.getNoticeId())
                .eq(SrmTenderSupplierInvite::getSectionId, req.getSectionId())
                .eq(SrmTenderSupplierInvite::getTenantSupplierId, validateTenderResult.getTenantSupplierInfo().getId())
                .one();
        ExceptionUtil.checkNonNull(supplierInvite, "未邀请供应商");

        SrmTenderSupplierResponse supplierResponse = this.getOrCreateSupplierResponse(validateTenderResult, req.getSectionId(), TenderSupplierResponseStageEnum.INVITE_RESPONSE);
        supplierInvite.setTenderSupplierResponseId(supplierResponse.getId());
        supplierInvite.setResponseContent(req.getResponseContent());
        supplierInvite.setConcatName(req.getConcatName());
        supplierInvite.setConcatPhone(req.getConcatPhone());
        supplierInvite.setConcatEmail(req.getConcatEmail());
        supplierInvite.setInviteStatus(InviteStatusEnum.ACCEPTED);
        supplierInvite.setResponseTime(LocalDateTime.now());
        srmTenderSupplierInviteService.updateById(supplierInvite);

        boolean updateResponse = false;
        if (StringUtils.isBlank(supplierResponse.getContactPerson())) {
            supplierResponse.setContactPerson(supplierInvite.getConcatName());
            updateResponse = true;
        }
        if (StringUtils.isBlank(supplierResponse.getContactPhone())) {
            supplierResponse.setContactPhone(supplierInvite.getConcatPhone());
            updateResponse = true;
        }
        if (StringUtils.isBlank(supplierResponse.getContactEmail())) {
            supplierResponse.setContactEmail(supplierInvite.getConcatEmail());
            updateResponse = true;
        }
        if (updateResponse) {
            this.updateById(supplierResponse);
        }

        srmProjectAttachmentService.lambdaUpdate().eq(SrmProjectAttachment::getBusinessId, supplierInvite.getId())
                .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.INVITE_RECEIPT).remove();
        if(StringUtils.isNotEmpty(req.getAttachmentUrl())){
            SrmProjectAttachment attachment = new SrmProjectAttachment();
            attachment.setProjectId(validateTenderResult.getProject().getId());
            attachment.setBusinessId(supplierInvite.getId());
            attachment.setBusinessType(AttachmentTypeEnum.INVITE_RECEIPT);
            attachment.setFileName(req.getAttachmentName());
            attachment.setFilePath(req.getAttachmentUrl());
            attachment.setDelFlag(YesNoEnum.NO.getCode());
            srmProjectAttachmentService.save(attachment);
        }
    }


    @Override
    public SrmTenderSupplierInvite inviteDetail(InviteDetailQueryReq req) {
        SrmTenderSupplierInvite supplierInvite = srmTenderSupplierInviteService.lambdaQuery()
                .eq(SrmTenderSupplierInvite::getNoticeId, req.getNoticeId())
                .eq(SrmTenderSupplierInvite::getSectionId, req.getSectionId())
                .eq(SrmTenderSupplierInvite::getTenantSupplierId, req.getTenantSupplierId())
                .one();
        ExceptionUtil.checkNonNull(supplierInvite, "未邀请供应商");
        supplierInvite.setAttachmentList(srmProjectAttachmentService.lambdaQuery()
                .eq(SrmProjectAttachment::getBusinessId, supplierInvite.getId())
                .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.INVITE_RECEIPT).list());
        return supplierInvite;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void downloadedBidFileDownloadStatus(Long noticeId, Long sectionId) {
        ValidateTenderResult validateTenderResult = this.validateSupplierAndProject(noticeId, null);
        this.getOrCreateSupplierResponse(validateTenderResult, sectionId, TenderSupplierResponseStageEnum.DOWNLOAD_BID_FILE);
    }
    /**
     * 发送报名审核结果短信通知
     *
     * @param validateTenderResult 校验结果
     * @param pass 审核是否通过
     * @param noticeId 公告ID
     */
    private void sendRegisterReviewSms(ValidateTenderResult validateTenderResult, boolean pass, Long noticeId) {
        try {
            SrmTenantSupplierInfo tenantSupplierInfo = validateTenderResult.getTenantSupplierInfo();
            SrmTenderNotice tenderNotice = validateTenderResult.getTenderNotice();
            SrmProcurementProject project = validateTenderResult.getProject();

            // 获取供应商联系人电话
            String contactPhone = tenantSupplierInfo.getLegalPersonPhone();
            if (StringUtils.isBlank(contactPhone)) {
                log.warn("供应商 {} 未设置联系电话，无法发送短信通知", tenantSupplierInfo.getSupplierName());
                return;
            }

            // 构造短信参数
            MessageSmsDTO messageSmsDTO = MessageSmsDTO.builder()
                    .mobile(contactPhone)
                    .biz("AYN_APPLY_AUDIT")
                    .param("company_name", tenantSupplierInfo.getSupplierName() != null ? tenantSupplierInfo.getSupplierName() : "供应商")
                    .param("project_name", project != null ? project.getProjectName() : "")
                    .param("project_code", project != null ? project.getProjectCode() : "")
                    .param("result", pass ? "通过" : "不通过")
                    .param("quote_start_time", tenderNotice.getQuoteStartTime() != null ? tenderNotice.getQuoteStartTime().toString() : "")
                    .param("quote_end_time", tenderNotice.getQuoteEndTime() != null ? tenderNotice.getQuoteEndTime().toString() : "")
                    .param("login_url", "https://ayn.canpanscp.com")
                    .build();

            // 异步发送短信
            asyncSmsService.sendSmsAsync(messageSmsDTO);
            log.info("已发送报名审核结果短信通知给供应商: {}", tenantSupplierInfo.getSupplierName());
        } catch (Exception e) {
            log.error("发送报名审核结果短信通知失败", e);
        }
    }
    /**
     * 发送邀请回执审核结果短信通知
     *
     * @param validateTenderResult 校验结果
     * @param pass 审核是否通过
     * @param noticeId 公告ID
     */
    private void sendInviteReceiptReviewSms(ValidateTenderResult validateTenderResult, boolean pass, Long noticeId) {
        try {
            SrmTenantSupplierInfo tenantSupplierInfo = validateTenderResult.getTenantSupplierInfo();
            SrmTenderNotice tenderNotice = validateTenderResult.getTenderNotice();
            SrmProcurementProject project = validateTenderResult.getProject();

            // 获取供应商联系人电话
            String contactPhone = tenantSupplierInfo.getLegalPersonPhone();
            if (StringUtils.isBlank(contactPhone)) {
                log.warn("供应商 {} 未设置联系电话，无法发送短信通知", tenantSupplierInfo.getSupplierName());
                return;
            }

            // 构造短信参数
            MessageSmsDTO messageSmsDTO = MessageSmsDTO.builder()
                    .mobile(contactPhone)
                    .biz("AYN_INVITE_RECEIPT_AUDIT")
                    .param("company_name", tenantSupplierInfo.getSupplierName() != null ? tenantSupplierInfo.getSupplierName() : "供应商")
                    .param("project_name", project != null ? project.getProjectName() : "")
                    .param("project_code", project != null ? project.getProjectCode() : "")
                    .param("quote_start_time", tenderNotice.getQuoteStartTime() != null ? tenderNotice.getQuoteStartTime().toString() : "")
                    .param("quote_end_time", tenderNotice.getQuoteEndTime() != null ? tenderNotice.getQuoteEndTime().toString() : "")
                    .param("login_url", "https://ayn.canpanscp.com")
                    .build();

            // 异步发送短信
            asyncSmsService.sendSmsAsync(messageSmsDTO);
            log.info("已发送邀请回执审核结果短信通知给供应商: {}", tenantSupplierInfo.getSupplierName());
        } catch (Exception e) {
            log.error("发送邀请回执审核结果短信通知失败", e);
        }
    }


}




