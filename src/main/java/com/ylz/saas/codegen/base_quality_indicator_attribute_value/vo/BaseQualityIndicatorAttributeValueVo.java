package com.ylz.saas.codegen.base_quality_indicator_attribute_value.vo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 质量指标属性值表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:00
 */
@Data
public class BaseQualityIndicatorAttributeValueVo implements Serializable {

    /**
  	 * 主键ID
   	 */
    private Long id;

    /**
  	 * 组织ID
   	 */
    private Long deptId;

    /**
  	 * 质量指标ID
   	 */
    private Long qualityIndicatorId;

    /**
  	 * 质量属性ID
   	 */
    @NotBlank(message = "质量属性ID不能为空")
    private String attributeId;

    /**
  	 * 质量属性值ID
   	 */
    @NotBlank(message = "质量属性值ID不能为空")
    private String attributeValueId;

    /**
  	 * 属性值内容
   	 */
    @Size(max = 200, message = "属性值内容不能超过200字符")
    private String valueContent;

    /**
  	 * 拒收标准
   	 */
    @Size(max = 200, message = "拒收标准不能超过200字符")
    private String rejectionStandard;

    /**
  	 * 扣款标准
   	 */
    @Size(max = 200, message = "扣款标准不能超过200字符")
    private String penaltyStandard;

    /**
  	 * 排序
   	 */
    private Integer sortOrder;

    /**
  	 * 创建人名称
   	 */
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    private String updateByName;

    /**
  	 * 创建人
   	 */
    private String createBy;

    /**
  	 * 创建时间
   	 */
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
    private String updateBy;

    /**
  	 * 修改时间
   	 */
    private LocalDateTime updateTime;

    /**
  	 * 租户ID
   	 */
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
    private Long createById;

    /**
  	 * 更新人ID
   	 */
    private Long updateById;
}