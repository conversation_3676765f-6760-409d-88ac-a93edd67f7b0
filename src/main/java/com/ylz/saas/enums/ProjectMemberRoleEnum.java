package com.ylz.saas.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @<PERSON> huanghong
 * @Date 2025/6/18 17:04
 * @Description
 */
@AllArgsConstructor
@Getter
public enum ProjectMemberRoleEnum {

    PROJECT_LEADER(ProjectMemberTypeEnum.PROJECT_MEMBER, "项目负责人"),
    PROJECT_MEMBER(ProjectMemberTypeEnum.PROJECT_MEMBER, "项目成员"),
    EVALUATION_LEADER(ProjectMemberTypeEnum.EVALUATION_MEMBER, "评标小组负责人"),
    EVALUATION_MEMBER(ProjectMemberTypeEnum.EVALUATION_MEMBER, "评标小组成员"),
    SUPERVISION(ProjectMemberTypeEnum.SUPERVISION, "监督人员"),
    BID_OPEN(ProjectMemberTypeEnum.BID_OPEN, "开标人员"),


    ;
    private final ProjectMemberTypeEnum memberType;
    private final String desc;

}
