package com.ylz.saas.codegen.srm_procurement_plan.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.codegen.srm_procurement_plan.entity.SrmProcurementPlanEntity;
import com.ylz.saas.codegen.srm_procurement_plan.mapper.SrmProcurementPlanMapper;
import com.ylz.saas.codegen.srm_procurement_plan.param.SrmProcurementPlanParam;
import com.ylz.saas.codegen.srm_procurement_plan.service.SrmProcurementPlanService;
import com.ylz.saas.codegen.srm_procurement_plan.vo.BizFlowUpdateVo;
import com.ylz.saas.codegen.srm_procurement_plan.vo.SrmProcurementPlanEntireVo;
import com.ylz.saas.codegen.srm_procurement_plan.vo.SrmProcurementPlanVo;
import com.ylz.saas.codegen.srm_procurement_plan.vo.UpdateSrmProcurementPlanStatusVo;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 采购计划表
 *
 * <AUTHOR>
 * @date 2025-06-03 20:39:40
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/srmProcurementPlan")
@Tag(description = "srmProcurementPlan", name = "采购计划表管理")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SrmProcurementPlanController {

    private final SrmProcurementPlanService srmProcurementPlanService;

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 采购计划表
     * @return
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @PostMapping("/page")
    public R<Page<SrmProcurementPlanEntity>> planPage(@RequestBody SrmProcurementPlanParam param, Page page) {
        return R.ok(srmProcurementPlanService.planPage(page, param));
    }

    /**
     * 通过id查询采购计划表
     *
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询", description = "通过id查询")
    @GetMapping("/{id}")
    public R<SrmProcurementPlanEntity> getById(@PathVariable("id") Long id) {
        return R.ok(srmProcurementPlanService.getByIdSrmProcurementPlan(id));
    }

    /**
     * 通过采购计划编码查询采购计划表
     *
     * @param code code
     * @return R
     */
    @Operation(summary = "通过采购计划编码查询采购计划表", description = "通过采购计划编码查询采购计划表")
    @GetMapping("/byPlanCode")
    public R<SrmProcurementPlanEntity> getByRPCode(@RequestParam("code") String code) {
        return R.ok(srmProcurementPlanService.getByPlanCode(code));
    }

    /**
     * 通过计划编码查询采购计划数据
     *
     * @param planCode planCode
     * @return R
     */
    @Operation(summary = "通过计划编码查询采购计划数据", description = "通过计划编码查询采购计划数据")
    @GetMapping("/planCode/{planCode}")
    public R<SrmProcurementPlanEntity> getByPlanCode(@PathVariable("planCode") String planCode) {
        return R.ok(srmProcurementPlanService.getByPlanCode(planCode));
    }

    /**
     * 新增采购计划表
     *
     * @param srmProcurementPlanVo 采购计划表
     * @return R
     */
    @Operation(summary = "新增采购计划表", description = "新增采购计划表")
    @SysLog("新增采购计划表")
    @PostMapping("/save")
    public R<Boolean> save(@Valid @RequestBody SrmProcurementPlanVo srmProcurementPlanVo) {
        return R.ok(srmProcurementPlanService.saveSrmProcurementPlan(srmProcurementPlanVo));
    }

    /**
     * 新增采购计划、采购明细及动态字段值
     *
     * @param srmProcurementPlanEntireVo 采购计划所有字段数据
     * @return R
     */
    @Operation(summary = "新增采购计划、采购明细及动态字段值", description = "新增采购计划、采购明细及动态字段值")
    @SysLog("新增采购计划、采购明细及动态字段值")
    @PostMapping("/entire/save")
    public R<Boolean> entireSave(@Valid @RequestBody SrmProcurementPlanEntireVo srmProcurementPlanEntireVo) {
        return R.ok(srmProcurementPlanService.entireSave(srmProcurementPlanEntireVo));
    }

    /**
     * 修改采购计划表
     *
     * @param srmProcurementPlanVo 采购计划表
     * @return R
     */
    @Operation(summary = "修改采购计划表", description = "修改采购计划表")
    @SysLog("修改采购计划表")
    @PostMapping("/update")
    public R<Boolean> updateById(@Valid @RequestBody SrmProcurementPlanVo srmProcurementPlanVo) {
        return R.ok(srmProcurementPlanService.updateSrmProcurementPlan(srmProcurementPlanVo));
    }

    /**
     * 仅修改采购计划主表数据
     *
     * @param srmProcurementPlanVo 仅修改采购计划主表数据
     * @return R
     */
    @Operation(summary = "仅修改采购计划主表数据", description = "仅修改采购计划主表数据")
    @SysLog("仅修改采购计划主表数据")
    @PostMapping("/update/only")
    public R<Boolean> onlyUpdatePlanById(@Valid @RequestBody SrmProcurementPlanVo srmProcurementPlanVo) {
        return R.ok(srmProcurementPlanService.onlyUpdatePlanById(srmProcurementPlanVo));
    }

    /**
     * 修改采购计划单据状态
     *
     * @param updateSrmProcurementPlanStatusVo 采购计划表状态
     * @return R
     */
    @Operation(summary = "修改采购计划单据状态", description = "修改采购计划单据状态")
    @SysLog("修改采购计划单据状态")
    @PostMapping("/updateStatus")
    public R<Boolean> updateStatus(@Valid @RequestBody UpdateSrmProcurementPlanStatusVo updateSrmProcurementPlanStatusVo) {
        return R.ok(srmProcurementPlanService.updateSrmProcurementStatus(updateSrmProcurementPlanStatusVo));
    }

    /**
     * 业务审批流执行后的更新方法
     *
     * @param bizFlowUpdateVo 业务审批流执行后的更新实体
     * @return R
     */
    @Operation(summary = "业务审批流执行后的更新方法", description = "业务审批流执行后的更新方法")
    @SysLog("业务审批流执行后的更新方法")
    @PostMapping("/bizFlowUpdate")
    public R<Boolean> bizFlowUpdate(@Valid @RequestBody BizFlowUpdateVo bizFlowUpdateVo) {
        return R.ok(srmProcurementPlanService.bizFlowUpdate(bizFlowUpdateVo));
    }

    /**
     * 通过id删除采购计划表
     *
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除采购计划表", description = "通过id删除采购计划表")
    @SysLog("通过id删除采购计划表")
    @GetMapping("remove")
    public R<Boolean> removeById(@RequestParam List<Long> ids) {
        boolean remove = srmProcurementPlanService.removeByIdsSrmProcurementPlan(ids);
        return R.ok(remove);
    }

}