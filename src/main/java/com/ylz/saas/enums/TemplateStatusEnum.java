package com.ylz.saas.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/6/18 13:44
 * @Description
 */
@Getter
@AllArgsConstructor
public enum TemplateStatusEnum {


    ENABLE("ENABLE"),
    DISABLE("DISABLE");


    private final String value;

    public static void fromValue(String value) {
        for (TemplateStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return;
            }
        }
        throw new IllegalArgumentException("Invalid template status: " + value);
    }
}
