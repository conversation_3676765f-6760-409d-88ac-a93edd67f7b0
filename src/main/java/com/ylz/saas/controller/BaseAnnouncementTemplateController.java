package com.ylz.saas.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.entity.BaseSceneFields;
import com.ylz.saas.entity.BaseTemplateScenes;
import com.ylz.saas.entity.BaseTemplates;
import com.ylz.saas.entity.BaseTemplatesHistory;
import com.ylz.saas.req.*;
import com.ylz.saas.resp.BaseAnnouncementTemplateQueryResp;
import com.ylz.saas.resp.BaseAnnouncementTemplateSceneQueryResp;
import com.ylz.saas.resp.SceneWithFieldsResp;
import com.ylz.saas.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

/**
 * 公告模板管理
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/baseAnnouncementTemplate")
@Tag(name = "公告模板管理", description = "公告模板的增删改查操作")
@RequiredArgsConstructor
public class BaseAnnouncementTemplateController {

    private final BaseSceneFieldsService sceneFieldsService;//场景字段服务
    private final BaseTemplateScenesService templateScenesService;//模板场景服务
    private final BaseTemplatesHistoryService  templatesHistoryService;//模板历史服务
    private final BaseTemplatesService  templatesService;//模板服务

    /**
     * 按名称模糊搜搜分页查询模板场景
     */
    @GetMapping("/getTemplateScenes/page")
    @Operation(summary = "查询模板场景", description = "支持按名称模糊查询,分页获取场景信息")
    public R<Page<BaseAnnouncementTemplateSceneQueryResp>> getTemplateScenes(BaseAnnouncementSceneQueryReq req) {
        Page<BaseAnnouncementTemplateSceneQueryResp> page = new Page<>(req.getCurrent(), req.getSize());
        return R.ok(templateScenesService.query(page, req.getSceneName()));
    }


    /**
     * 根据id查询模板
     */
    @GetMapping("/getById/{id}")
    @Operation(summary = "根据ID查询模板", description = "返回指定ID的公告模板详细信息")
    public R<BaseTemplates> getById(@PathVariable Long id) {
        BaseTemplates template = templatesService.getById(id);
        if (template == null) {
            return R.failed("未找到该模板");
        }
        return R.ok(template);
    }

    /**
     * 新增模板场景
     */
    @PostMapping("/addScene")
    @Operation(summary = "新增模板场景", description = "创建新的模板场景")
    public R<Boolean> addScene(@RequestBody @Validated BaseAnnouncementSceneAddReq req) {
        boolean result = templateScenesService.saveScene(req);
        return result ? R.ok(true) : R.failed("新增失败");
    }

    /**
     * 修改模板场景
     */
    @PutMapping("/updateScene")
    @Operation(summary = "修改模板场景", description = "更新模板场景信息")
    public R<Boolean> updateScene(@RequestBody @Validated BaseAnnouncementSceneUpdateReq req) {
        boolean result = templateScenesService.updateScene(req);
        return result ? R.ok(true) : R.failed("更新失败");
    }

    /**
     * 删除模板场景
     */
    @DeleteMapping("/deleteScene/{id}")
    @Operation(summary = "删除模板场景", description = "根据ID删除模板场景")
    public R<Boolean> deleteScene(@PathVariable Long id) {
        boolean result = templateScenesService.deleteSceneById(id);
        return result ? R.ok(true) : R.failed("删除失败");
    }

    /**
     * 查询场景字段信息
     */
    @GetMapping("/getSceneFields/{templateSceneId}")
    @Operation(summary = "查询场景字段", description = "根据模板场景ID查询base_scene_fields表中的字段信息")
    public R<List<BaseSceneFields>> getSceneFields(@PathVariable Long templateSceneId) {
        List<BaseSceneFields> fields = sceneFieldsService.getSceneFieldsBySceneId(templateSceneId);
        return R.ok(fields);
    }

    /**
     * 新增场景字段
     */
    @PostMapping("/addField")
    @Operation(summary = "新增场景字段", description = "为指定场景添加字段")
    public R<Boolean> addField(@RequestBody @Validated BaseAnnouncementSceneFieldAddReq req) {
        QueryWrapper<BaseSceneFields> wrapper = new QueryWrapper<>();
        wrapper.eq("template_scene_id", req.getTemplateSceneId());
        wrapper.eq("field_name", req.getFieldName());

        if (sceneFieldsService.count(wrapper) > 0) {
            return R.failed("该场景下已存在同名字段: " + req.getFieldName());
        }
        BaseSceneFields field = new BaseSceneFields();
        field.setTemplateSceneId(req.getTemplateSceneId());
        field.setFieldName(req.getFieldName());
        field.setDescription(req.getDescription());
        field.setFieldDisplayName(req.getDisplayName());
        field.setFieldType(req.getFieldType());
        field.setFieldLength(req.getFieldLength());
        field.setIsRequired(req.getIsRequired());
        field.setDefaultValue(req.getDefaultValue());


        boolean result = sceneFieldsService.save(field);
        return result ? R.ok(true) : R.failed("新增失败");
    }


    /**
     * 修改场景字段信息
     */
    @PutMapping("/updateField")
    @Operation(summary = "修改场景字段", description = "更新已有字段信息")
    public R<Boolean> updateField(@RequestBody @Validated BaseAnnouncementSceneFieldUpdateReq req) {
        BaseSceneFields field = sceneFieldsService.getById(req.getId());
        if (field == null) {
            return R.failed("字段不存在");
        }

        field.setFieldName(req.getFieldName());
        field.setDescription(req.getDescription());
        field.setFieldDisplayName(req.getDisplayName());
        field.setFieldType(req.getFieldType());
        field.setFieldLength(req.getFieldLength());
        field.setIsRequired(req.getIsRequired());
        field.setDefaultValue(req.getDefaultValue());


        boolean result = sceneFieldsService.updateById(field);
        return result ? R.ok(true) : R.failed("更新失败");
    }

    /**
     * 删除场景字段
     */
    @DeleteMapping("/deleteField/{id}")
    @Operation(summary = "删除场景字段", description = "根据ID删除字段")
    public R<Boolean> deleteField(@PathVariable Long id) {
        boolean result = sceneFieldsService.removeById(id);
        return result ? R.ok(true) : R.failed("删除失败");
    }


    /**
     * 分页查询模板（选取场景type，输入searchKey模糊查询名称或编号）
     */
    @GetMapping("/query/page")
    @Operation(summary = "分页查询（", description = "分页查询（传入搜索的关键词则模糊查询编号或名称）")
    public R<Page<BaseAnnouncementTemplateQueryResp>> query(BaseAnnouncementTemplateQueryReq req) {
        Page<BaseAnnouncementTemplateQueryResp> page = new Page<>(req.getCurrent(), req.getSize());
        return R.ok(templatesService.query(page, req.getSearchKey(), req.getType()));
    }

    /**
     * 逻辑删除模板
     */
    @PutMapping("/delete/{id}")
    @Operation(summary = "逻辑删除模板", description = "根据ID、租户ID标记删除模板")
    public R<Boolean> logicDeleteTemplate(@PathVariable Long id) {
        boolean result = templatesService.logicDeleteTemplate(id);
        return result ? R.ok(true) : R.failed("删除失败");
    }

    /**
     * 新增模板
     */
    @PostMapping("/add")
    @Operation(summary = "新增模板", description = "创建新的公告模板")
    public R<Boolean> addTemplate(@RequestBody @Validated BaseAnnouncementTemplateAddReq req) {
        boolean result = templatesService.addTemplate(req);
        return result ? R.ok(true) : R.failed("新增失败");
    }



    /**
     * 编辑模板
     */
    @PutMapping("/update")
    @Operation(summary = "编辑模板", description = "编辑公告模板信息")
    public R<Boolean> updateTemplate(@RequestBody @Validated BaseAnnouncementTemplateUpdateReq req) {
        boolean result = templatesService.updateTemplateWithHistory(req);
        return result ? R.ok(true) : R.failed("更新失败");
    }

    /**
     * 查询模板历史版本（未定）
     */
    @PostMapping("/getHistoryByTemplateId")
    @Operation(summary = "查询模板历史", description = "根据模板ID获取所有历史版本")
    public R<List<BaseTemplatesHistory>> getHistoryByTemplateId(@RequestBody @Validated BaseAnnouncementTemplateDeleteReq req) {
        List<BaseTemplatesHistory> historyList = templatesHistoryService.getHistoryByTemplateId(req.getId());
        return R.ok(historyList);
    }

    /**
     * 修改模板状态
     */
    @PutMapping("/changeStatus")
    @Operation(summary = "启用/禁用模板", description = "根据ID和状态修改模板状态（ENABLE启用或DISABLE禁用）")
    public R<Boolean> changeTemplateStatus(@RequestBody @Validated BaseAnnouncementTemplateChangeStatusReq req) {
        boolean result = templatesService.changeTemplateStatus(req.getId(),req.getStatus());
        return result ? R.ok(true) : R.failed("操作失败");
    }

    /**
     * 获取场景及字段
     */
    @GetMapping("/getSceneWithFields/{sceneId}")
    @Operation(summary = "获取场景及字段", description = "根据ID获取场景及其所有字段")
    public R<SceneWithFieldsResp> getSceneWithFields(@PathVariable Long sceneId) throws InvocationTargetException, IllegalAccessException {
        SceneWithFieldsResp data = templateScenesService.getSceneWithFields(sceneId);
        return data != null ? R.ok(data) : R.failed("未找到数据");
    }


    /**
     * 新增或更新场景及字段（场景无id新建，有id修改）
     */
    @PostMapping("/saveSceneWithFields")
    @Operation(summary = "新增或更新场景及字段", description = "提交场景和字段信息")
    public R<Boolean> saveSceneWithFields(@RequestBody @Validated SceneWithFieldsReq req) {
        boolean result = req.getId() == null ?
                templateScenesService.saveSceneWithFields(req) :
                templateScenesService.updateSceneWithFields(req);

        return result ? R.ok(true) : R.failed("操作失败");
    }

}