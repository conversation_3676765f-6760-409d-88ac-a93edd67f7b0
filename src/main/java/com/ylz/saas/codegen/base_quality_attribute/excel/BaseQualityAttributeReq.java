package com.ylz.saas.codegen.base_quality_attribute.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;


/**
 * 质量属性表
 *
 * <AUTHOR>
 */
@Data
public class BaseQualityAttributeReq extends Model<BaseQualityAttributeReq> {

    /**
     * 属性名称
     */
    @ExcelProperty(value = "属性名称")
    private String attributeName;


    /**
     * 属性描述
     */
    @ExcelProperty(value = "属性描述")
    private String description;

}