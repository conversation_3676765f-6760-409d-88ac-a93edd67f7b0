package com.ylz.saas.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.entity.SrmTenderBidEvaluation;
import com.ylz.saas.req.SrmTenderBidEvaluationPageReq;
import com.ylz.saas.req.SrmTenderBidEvaluationReq;
import com.ylz.saas.req.SrmTenderEvaluationSummaryReq;
import com.ylz.saas.req.SrmTenderEvaluationSummaryQueryReq;
import com.ylz.saas.req.SrmTenderOfflineEvaluationSummaryQueryReq;
import com.ylz.saas.req.SrmTenderOfflineEvaluationSummaryReq;
import com.ylz.saas.req.SrmTenderSupplierReviewDetailQueryReq;
import com.ylz.saas.req.SrmTenderLeaderUpdateReviewReq;
import com.ylz.saas.req.SrmTenderLeaderSummaryReviewReq;
import com.ylz.saas.req.SrmTenderSectionPageReq;
import com.ylz.saas.resp.SrmTenderBidEvaluationResp;
import com.ylz.saas.resp.SrmTenderEvaluationSignatureResp;
import com.ylz.saas.resp.SrmTenderEvaluationSummaryQueryResp;
import com.ylz.saas.resp.SrmTenderOfflineEvaluationSummaryQueryResp;
import com.ylz.saas.resp.SrmTenderSupplierReviewDetailQueryResp;
import com.ylz.saas.resp.SrmTenderSectionPageResp;

import java.util.List;

/**
 * 针对表【srm_tender_bid_evaluation(评标委员会)】的数据库操作Service
 * <AUTHOR>
 * @createDate 2025-07-09
 */
public interface SrmTenderBidEvaluationService extends IService<SrmTenderBidEvaluation> {

    /**
     * 新增评标委员会
     * @param req 评标委员会信息
     * @return 是否成功
     */
    boolean saveBidEvaluation(SrmTenderBidEvaluationReq req);

    /**
     * 修改评标委员会
     * @param req 评标委员会信息
     * @return 是否成功
     */
    boolean updateBidEvaluation(SrmTenderBidEvaluationReq req);

    /**
     * 根据ID查询评标委员会详情
     * @param id 主键ID
     * @return 评标委员会详情
     */
//    SrmTenderBidEvaluationResp getByIdBidEvaluation(Long id);

    /**
     * 分页查询评标委员会
     * @param req 查询条件
     * @return 分页结果
     */
//    IPage<SrmTenderBidEvaluationResp> pageBidEvaluation(SrmTenderBidEvaluationPageReq req);

    /**
     * 根据项目ID和公告ID查询评标委员会列表
     * @param projectId 项目ID
     * @param noticeId 公告ID
     * @return 评标委员会列表
     */
//    List<SrmTenderBidEvaluationResp> listByProjectAndNotice(Long projectId, Long noticeId);

    /**
     * 根据标段ID查询评标委员会（查询当前轮次的评标委员会）
     * @param sectionId 标段ID
     * @return 评标委员会信息
     */
    SrmTenderBidEvaluationResp getBySectionId(Long sectionId);

    /**
     * 删除评标委员会
     * @param ids 主键ID列表
     * @return 是否成功
     */
//    boolean removeByIdsBidEvaluation(List<Long> ids);

    /**
     * 分页查询项目标段列表
     * @param req 查询条件
     * @return 分页结果
     */
    IPage<SrmTenderSectionPageResp> pageSections(SrmTenderSectionPageReq req);

    /**
     * 评标汇总（新版）
     * @param req 汇总请求
     * @return 是否成功
     */
    boolean summaryEvaluation(SrmTenderEvaluationSummaryReq req);

    /**
     * 查询评标汇总供应商信息
     * @param req 查询请求
     * @return 汇总信息
     */
    SrmTenderEvaluationSummaryQueryResp queryEvaluationSummary(SrmTenderEvaluationSummaryQueryReq req);

    /**
     * 查询线下评标汇总供应商信息
     * @param req 查询请求
     * @return 线下评标汇总信息
     */
    SrmTenderOfflineEvaluationSummaryQueryResp queryOfflineEvaluationSummary(SrmTenderOfflineEvaluationSummaryQueryReq req);

    /**
     * 线下评标汇总保存
     * @param req 线下评标汇总请求
     * @return 是否成功
     */
    boolean summaryOfflineEvaluation(SrmTenderOfflineEvaluationSummaryReq req);

    /**
     * 查询供应商评审项详情
     * @param req 查询请求
     * @return 供应商评审项详情
     */
    SrmTenderSupplierReviewDetailQueryResp querySupplierReviewDetail(SrmTenderSupplierReviewDetailQueryReq req);


    /**
     * 组长保存汇总评审结果
     * @param req 汇总请求，包含指定供应商的所有评审项结果，不按节点归纳
     * @return 是否成功
     */
    boolean leaderSaveSummaryReview(SrmTenderLeaderSummaryReviewReq req);

    boolean getNoticeEvaluationCompleteStatus(Long noticeId);


}
