package com.ylz.saas.codegen.base_material_category.param;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ylz.saas.codegen.base_material_category.entity.BaseMaterialCategoryEntity;
import com.ylz.saas.common.data.mybatis.helper.Operator;
import com.ylz.saas.common.data.mybatis.helper.QueryWrapperHelper;
import com.ylz.saas.common.data.mybatis.wrapper.CriteriaField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * mybatis-plus的查询帮助类
 * 
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseMaterialCategoryParam {

	/**
  	 * 创建人ID
   	 */
 	@CriteriaField(field = "createById", operator = Operator.LIKE)
    private Long createById;
	/**
  	 * 主键ID
   	 */
 	@CriteriaField(field = "id", operator = Operator.LIKE)
    private Long id;
	/**
  	 * 组织ID
   	 */
 	@CriteriaField(field = "deptId", operator = Operator.LIKE)
    private String deptId;
	/**
  	 * 更新人ID
   	 */
 	@CriteriaField(field = "updateById", operator = Operator.LIKE)
    private Long updateById;
	/**
  	 * 物料分类编码
   	 */
 	@CriteriaField(field = "categoryCode", operator = Operator.LIKE)
    private String categoryCode;
	/**
  	 * 物料分类名称
   	 */
 	@CriteriaField(field = "categoryName", operator = Operator.LIKE)
    private String categoryName;
	/**
  	 * 上级物料分类编码
   	 */
 	@CriteriaField(field = "parentCategoryCode", operator = Operator.LIKE)
    private String parentCategoryCode;
	/**
  	 * 上级物料分类名称
   	 */
 	@CriteriaField(field = "parentCategoryName", operator = Operator.LIKE)
    private String parentCategoryName;
	/**
  	 * 来源标识
   	 */
 	@CriteriaField(field = "dataSource", operator = Operator.LIKE)
    private String dataSource;
	/**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
 	@CriteriaField(field = "status", operator = Operator.IN)
    private List<String> statusList;
	/**
  	 * 创建人名称
   	 */
 	@CriteriaField(field = "createByName", operator = Operator.LIKE)
    private String createByName;
	/**
  	 * 修改人名称
   	 */
 	@CriteriaField(field = "updateByName", operator = Operator.LIKE)
    private String updateByName;
	/**
  	 * 创建人
   	 */
 	@CriteriaField(field = "createBy", operator = Operator.LIKE)
    private String createBy;
	/**
  	 * 创建时间_开始
   	 */
	@CriteriaField(field = "createTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime createTimeStart;
	/**
  	 * 创建时间_结束
   	 */
    @CriteriaField(field = "createTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime createTimeEnd;
	/**
  	 * 修改人
   	 */
 	@CriteriaField(field = "updateBy", operator = Operator.LIKE)
    private String updateBy;
	/**
  	 * 修改时间_开始
   	 */
	@CriteriaField(field = "updateTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime updateTimeStart;
	/**
  	 * 修改时间_结束
   	 */
    @CriteriaField(field = "updateTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime updateTimeEnd;
	/**
  	 * 删除标识（0-正常、1-删除）
   	 */
	@CriteriaField(field = "delFlag", operator = Operator.EQ)
	private int delFlag;
	/**
  	 * 租户ID
   	 */
 	@CriteriaField(field = "tenantId", operator = Operator.LIKE)
    private Long tenantId;

    /**
    * 构建查询
    */
    public QueryWrapper<BaseMaterialCategoryEntity> toWrapper() {
        QueryWrapper<BaseMaterialCategoryEntity> queryWrapper = QueryWrapperHelper.fromBean(this, BaseMaterialCategoryEntity.class)
            .orderByDesc("id");
       return queryWrapper;
    }
}