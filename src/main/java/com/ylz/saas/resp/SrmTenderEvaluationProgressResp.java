package com.ylz.saas.resp;

import com.ylz.saas.enums.ProjectMemberRoleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 评标进度查询响应类
 */
@Data
@Schema(description = "评标进度查询响应")
public class SrmTenderEvaluationProgressResp implements Serializable {

    /**
     * 评标委员会信息
     */
    @Schema(description = "评标委员会信息")
    private EvaluationCommitteeInfo committeeInfo;

    /**
     * 评审节点列表（表格列头）
     */
    @Schema(description = "评审节点列表")
    private List<EvaluationNode> evaluationNodes;

    /**
     * 评标人员进度列表（表格行数据）
     */
    @Schema(description = "评标人员进度列表")
    private List<EvaluatorProgress> evaluatorProgressList;

    /**
     * 当前用户在该项目中的角色权限
     */
    @Schema(description = "当前用户在该项目中的角色权限")
    private ProjectMemberRoleEnum currentUserRole;

    /**
     * 评标委员会信息
     */
    @Data
    @Schema(description = "评标委员会信息")
    public static class EvaluationCommitteeInfo implements Serializable {
        /**
         * 评标委员会ID
         */
        @Schema(description = "评标委员会ID")
        private Long id;

        /**
         * 评标委员会名称
         */
        @Schema(description = "评标委员会名称")
        private String name;

        /**
         * 标段ID
         */
        @Schema(description = "标段ID")
        private Long sectionId;

        /**
         * 招标公告ID
         */
        @Schema(description = "招标公告ID")
        private Long noticeId;

        /**
         * 总成员数
         */
        @Schema(description = "总成员数")
        private Integer totalMembers;

        /**
         * 总评审节点数
         */
        @Schema(description = "总评审节点数")
        private Integer totalEvaluationNodes;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 评审节点信息
     */
    @Data
    @Schema(description = "评审节点信息")
    public static class EvaluationNode implements Serializable {
        /**
         * 评审类型（评分项、评审项）
         */
        @Schema(description = "评审类型")
        private String type;

        /**
         * 评审类型名称
         */
        @Schema(description = "评审类型名称")
        private String typeName;

        /**
         * 节点名称
         */
        @Schema(description = "节点名称")
        private String nodeName;

        /**
         * 该节点下的评审项数量
         */
        @Schema(description = "该节点下的评审项数量")
        private Integer itemCount;

        /**
         * 该节点下的评审项列表
         */
        @Schema(description = "该节点下的评审项列表")
        private List<EvaluationItem> items;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 评审项信息
     */
    @Data
    @Schema(description = "评审项信息")
    public static class EvaluationItem implements Serializable {
        /**
         * 评审项ID
         */
        @Schema(description = "评审项ID")
        private Long id;

        /**
         * 评审项名称
         */
        @Schema(description = "评审项名称")
        private String itemName;

        /**
         * 评审类型
         */
        @Schema(description = "评审类型")
        private String type;

        /**
         * 节点名称
         */
        @Schema(description = "节点名称")
        private String nodeName;

        /**
         * 满分分值
         */
        @Schema(description = "满分分值")
        private Integer maxScore;

        /**
         * 权重
         */
        @Schema(description = "权重")
        private Double weight;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 评标人员进度信息
     */
    @Data
    @Schema(description = "评标人员进度信息")
    public static class EvaluatorProgress implements Serializable {
        /**
         * 用户ID
         */
        @Schema(description = "用户ID")
        private Long userId;

        /**
         * 用户名
         */
        @Schema(description = "用户名")
        private String userName;

        /**
         * 专家姓名
         */
        @Schema(description = "专家姓名")
        private String expertName;

        /**
         * 专家编号
         */
        @Schema(description = "专家编号")
        private String expertCode;

        /**
         * 专家身份证号
         */
        @Schema(description = "专家身份证号")
        private String expertIdNumber;

        /**
         * 专家类别
         */
        @Schema(description = "专家类别")
        private String expertCategory;

        /**
         * 角色
         */
        @Schema(description = "角色")
        private String role;

        /**
         * 角色名称
         */
        @Schema(description = "角色名称")
        private String roleName;

        /**
         * 联系电话
         */
        @Schema(description = "联系电话")
        private String contactPhone;

        /**
         * 各评审节点的完成情况
         * key: 节点名称, value: 完成状态信息
         */
        @Schema(description = "各评审节点的完成情况")
        private Map<String, NodeCompletionStatus> nodeCompletionMap;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 评审节点完成状态
     */
    @Data
    @Schema(description = "评审节点完成状态")
    public static class NodeCompletionStatus implements Serializable {
        /**
         * 评审类型
         */
        @Schema(description = "评审类型")
        private String type;

        /**
         * 节点名称
         */
        @Schema(description = "节点名称")
        private String nodeName;

        /**
         * 该节点下总的评审项数量
         */
        @Schema(description = "该节点下总的评审项数量")
        private Integer totalItems;

        /**
         * 已完成的评审项数量
         */
        @Schema(description = "已完成的评审项数量")
        private Integer completedItems;

        /**
         * 是否全部完成
         */
        @Schema(description = "是否全部完成")
        private Boolean isCompleted;

        /**
         * 完成率
         */
        @Schema(description = "完成率")
        private Double completionRate;

        /**
         * 状态描述
         */
        @Schema(description = "状态描述")
        private String statusDescription;

        /**
         * 是否已汇总
         */
        @Schema(description = "是否已汇总")
        private Boolean isSummarized;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
