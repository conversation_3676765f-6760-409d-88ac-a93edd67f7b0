package com.ylz.saas.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 供应商基本信息详情VO（用于查询回显）
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
@Schema(description = "供应商基本信息详情VO")
public class SupplierBasicInfoDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;
    /**
     * 平台供应商ID
     */
    @Schema(description = "平台供应商ID")
    private Long platformSupplierId;

    /**
     * 租户供应商ID
     */
    @Schema(description = "租户供应商ID")
    private Long tenantSupplierId;

    /**
     * 供应商编号
     */
    @Schema(description = "供应商编号")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    private String supplierName;

    /**
     * 供应商简称
     */
    @Schema(description = "供应商简称")
    private String supplierShortName;

    /**
     * 供应商类型(MANUFACTURER-生产型供应商、TRADER-贸易型供应商、SERVICE-服务型供应商、LOGISTICS-物流承运商、PROJECT-工程建筑供应商、LABOR-劳务外包供应商、RENTAL-租赁服务供应商、OTHER-其他)
     */
    @Schema(description = "供应商类型")
    private String supplierType;

    /**
     * 企业性质(STATE_OWNED-国有企业、PRIVATE-民营企业、FOREIGN-外资企业、JOINT_VENTURE-合资企业、LISTED-上市公司、OTHER-其他)
     */
    @Schema(description = "企业性质")
    private String enterpriseNature;

    /**
     * 供应商来源(PLATFORM_APPLY-平台申请、INVITE_JOIN-邀请加入)
     */
    @Schema(description = "供应商来源")
    private String supplierSource;

    /**
     * 审批状态(DRAFT-草稿、APPROVING-审批中、APPROVED-已审批、REJECTED-已驳回)
     */
    @Schema(description = "审批状态")
    private String approvalStatus;
    /**
     * 审批状态(DRAFT-草稿、APPROVING-审批中、APPROVED-已审批、REJECTED-已驳回)
     */
    @Schema(description = "部门关联审批状态")
    private String deptApprovalStatus;
    /**
     * 统一社会信用代码
     */
    @Schema(description = "统一社会信用代码")
    private String socialCreditCode;

    /**
     * 成立日期
     */
    @Schema(description = "成立日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date establishDate;

    /**
     * 注册资本
     */
    @Schema(description = "注册资本")
    private BigDecimal registeredCapital;

    /**
     * 法人代表
     */
    @Schema(description = "法人代表")
    private String legalPerson;

    /**
     * 法人委托书
     */
    private String letterOfAuthorization;
    /**
     * 法人身份证号
     */
    @Schema(description = "法人身份证号")
    private String legalPersonId;

    /**
     * 法人联系电话
     */
    @Schema(description = "法人联系电话")
    private String legalPersonPhone;

    /**
     * 注册地址
     */
    @Schema(description = "注册地址")
    private String registeredAddress;

    /**
     * 邮政编码
     */
    @Schema(description = "邮政编码")
    private String postalCode;

    /**
     * 传真
     */
    @Schema(description = "传真")
    private String fax;

    /**
     * 年平均营业额
     */
    @Schema(description = "年平均营业额")
    private BigDecimal annualRevenue;

    /**
     * 公司简介
     */
    @Schema(description = "公司简介")
    private String companyProfile;

    /**
     * 经营范围
     */
    @Schema(description = "经营范围")
    private String businessScope;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    // 租户供应商信息相关字段

    /**
     * 供应商分类(COMMON-普通供应商、STRATEGIC-战略供应商)
     */
    @Schema(description = "供应商分类")
    private String supplierCategory;

    /**
     * 供应商状态(QUALIFIED-合格供应商、POTENTIAL-潜在供应商、UNQUALIFIED-不合格供应商、BLACKLIST-黑名单供应商、TERMINATED-终止合作)
     */
    @Schema(description = "供应商状态")
    private String supplierStatus;

    /**
     * 服务开始日期
     */
    @Schema(description = "服务开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate serviceStartDate;

    /**
     * 服务结束日期
     */
    @Schema(description = "服务结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate serviceEndDate;

//    /**
//     * 租户供应商备注
//     */
//    @Schema(description = "租户供应商备注")
//    private String tenantRemark;

    /**
     * 关联部门ID集合
     */
    @Schema(description = "关联部门ID集合")
    private List<Long> deptIds;

    /**
     * 关联部门信息列表
     */
    @Schema(description = "关联部门信息列表")
    private List<DeptRelationInfo> deptRelations;

    /**
     * 是否为所有组织可用（0-否、1-是）
     */
    @Schema(description = "是否为所有组织可用")
    private Boolean isAllDept;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 部门关联信息内部类
     */
    @Data
    @Schema(description = "部门关联信息")
    public static class DeptRelationInfo implements Serializable {

        private static final long serialVersionUID = 1L;
        /**
         * 关联ID
         */
        @Schema(description = "关联ID")
        private Long relationId;

        /**
         * 部门ID
         */
        @Schema(description = "部门ID")
        private Long deptId;

        /**
         * 部门名称
         */
        @Schema(description = "部门名称")
        private String deptName;

        /**
         * 关联类型
         */
        @Schema(description = "关联类型")
        private String relationType;

        /**
         * 审核状态
         */
        @Schema(description = "审核状态")
        private String approvalStatus;

        /**
         * 备注
         */
        @Schema(description = "备注")
        private String remark;
    }
}
