package com.ylz.saas.codegen.srm_procurement_decision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 决策状态
 * <p>
 * 新建待审批  （查看、审批撤销）
 * 新建审批撤回 （查看、编辑、删除）
 * 新建审批不通过（查看、编辑、删除）
 * 已生效 （查看、作废）
 * 作废待审批（查看、审批撤销）
 * 【无】作废审批撤回（查看、删除）   -- 回到已生效
 * 【无】作废审批不通过（查看、删除） --  回到已生效
 * 已作废（查看）
 *
 * <AUTHOR>
 * @date 2025/8/5 16:25
 * @since v0.0.1
 */
@Getter
@AllArgsConstructor
public enum DecisionStatus {
    NEW("新建待审批"),
    NEW_APPROVE_REVOKE("新建审批撤回"),
    NEW_APPROVE_REJECT("新建审批不通过"),
    EFFECT("已生效"),
    INVALID_APPROVE("作废待审批"),
    INVALID("已作废");

    private final String desc;

    private static final Map<String, DecisionStatus> CACHE_MAP = EnumSet.allOf(DecisionStatus.class)
            .stream()
            .collect(Collectors.toMap(DecisionStatus::name, Function.identity()));

    public static DecisionStatus geApprovalTypeByValue(String value) {
        return CACHE_MAP.getOrDefault(value.toUpperCase(), DecisionStatus.INVALID);
    }
}