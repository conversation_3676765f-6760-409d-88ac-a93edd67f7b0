package com.ylz.saas.codegen.base_usage_location.excel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 使用地点信息表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:41:24
 */
@Data
public class BaseUsageLocationReq implements Serializable {


    /**
  	 * 组织名称
   	 */
	@ExcelProperty(value = "组织名称*")
    private String deptName;

    /**
  	 * 牧场名称（使用地点名称）
   	 */
	@ExcelProperty(value = "牧场名称*")
    private String locationName;




    /**
  	 * 省市区
   	 */
	@ExcelProperty(value = "省市区(以/分割)*")
    private String district;


    /**
  	 * 详细地址
   	 */
	@ExcelProperty(value = "详细地址*")
    private String address;




//    /**
//  	 * 状态
//   	 */
//	@ExcelProperty(value = "状态")
//    private String status;

    // 内部字段，用于导入时设置组织ID
//	@ExcelIgnore
//
//    private Long deptId;
}