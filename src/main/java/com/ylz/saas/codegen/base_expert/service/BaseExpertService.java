package com.ylz.saas.codegen.base_expert.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.admin.api.entity.SysUser;
import com.ylz.saas.codegen.base_expert.entity.BaseExpertEntity;
import com.ylz.saas.codegen.base_expert.vo.BaseExpertVo;
import com.ylz.saas.codegen.base_expert.vo.ExpertUserInnerAccountReq;

import java.util.List;

public interface BaseExpertService extends IService<BaseExpertEntity> {
	/**
     * 新增专家信息表
     * @param baseExpertVo 专家信息表
     */
    boolean saveBaseExpert(BaseExpertVo baseExpertVo);
	
	/**
     * 修改专家信息表
     * @param baseExpertVo 专家信息表
     */
    boolean updateBaseExpert(BaseExpertVo baseExpertVo);
	
	/**
     * 查询专家信息表详情
     * @param id
     */
    BaseExpertEntity getByIdBaseExpert(Long id);
	
	/**
	* 通过id删除专家信息表
	*/
	boolean removeByIdsBaseExpert(List<Long> ids);

	/**
	 * 获得内部账号（租户隔离）
	 * @return
	 */
	List<SysUser> getInnerAccount(ExpertUserInnerAccountReq req);


	/**
	 * 开关
	 * @param id
	 * @return
	 */
	Object switchStatus(Long id);
}