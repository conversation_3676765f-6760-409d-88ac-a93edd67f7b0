package com.ylz.saas.req;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

import java.util.List;

@Data
public class SrmTenderContractQueryReq extends Page {
    /**
     * 是否采购方
     */
    private Boolean isPurchase;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 供应商
     */
    private String supplierName;

    /**
     * 采购方
     */
    private String purchaserDeptName;

    /**
     * 签约时间
     */
    private String signDate;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 标段ID
     */
    private Long sectionId;

    /**
     * 供应商ID(多选)
     */
    private List<Long> supplierIds;

    /**
     * 采购方ID
     */
    private Long purchaserDeptId;

    /**
     * 采购立项ID
     */
    private Long projectId;
}
