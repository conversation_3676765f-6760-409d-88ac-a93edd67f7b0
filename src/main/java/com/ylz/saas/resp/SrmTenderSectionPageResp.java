package com.ylz.saas.resp;

import com.ylz.saas.enums.EvaluationSummaryStatusEnum;
import com.ylz.saas.enums.ExpertExtractionStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 标段分页查询响应类
 * <AUTHOR>
 * @createDate 2025-07-11
 */
@Data
@Schema(description = "标段分页查询响应类")
public class SrmTenderSectionPageResp implements Serializable {

    /**
     * 标段ID
     */
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 采购立项ID
     */
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    private String projectName;

    /**
     * 标段编号
     */
    @Schema(description = "标段编号")
    private String sectionCode;

    /**
     * 标段名称
     */
    @Schema(description = "标段名称")
    private String sectionName;

    /**
     * 专家抽取状态
     */
    @Schema(description = "专家抽取状态")
    private ExpertExtractionStatusEnum expertExtractionStatus;

    /**
     * 专家抽取状态描述
     */
    @Schema(description = "专家抽取状态描述")
    private String expertExtractionStatusDesc;

    /**
     * 汇总状态
     */
    @Schema(description = "汇总状态")
    private EvaluationSummaryStatusEnum summaryStatus;

    /**
     * 汇总状态描述
     */
    @Schema(description = "汇总状态描述")
    private String summaryStatusDesc;

    /**
     * 汇总人（进行评标汇总的人员）
     */
    @Schema(description = "汇总人（进行评标汇总的人员）")
    private String summaryByName;

    /**
     * 汇总时间
     */
    @Schema(description = "汇总时间")
    private LocalDateTime summaryTime;

    /**
     * 评标报告内容
     */
    @Schema(description = "评标报告内容")
    private String reportContent;

    /**
     * 报告模板ID
     */
    @Schema(description = "报告模板ID")
    private Long reportTemplateId;

    /**
     * 电子签章评标报告
     */
    @Schema(description = "电子签章评标报告")
    private String signedReportContent;

    /**
     * 签名进度（格式：已签名人数/总人数，如：3/5）
     */
    @Schema(description = "签名进度（格式：已签名人数/总人数，如：3/5）")
    private String signatureProgress;

    /**
     * 操作人（创建评标委员会的人）
     */
    @Schema(description = "操作人（创建评标委员会的人）")
    private String operatorName;

    /**
     * 操作时间（创建评标委员会的时间）
     */
    @Schema(description = "操作时间（创建评标委员会的时间）")
    private LocalDateTime operationTime;

    /**
     * 评标委员会ID
     */
    @Schema(description = "评标委员会ID")
    private Long evaluationId;

    /**
     * 招标公告ID
     */
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 当前报价轮次
     */
    @Schema(description = "当前报价轮次")
    private Integer currentRound;

    /**
     * 评标委员会负责人ID
     */
    @Schema(description = "评标委员会负责人ID")
    private Long evaluationLeaderId;

    /**
     * 评标委员会负责人姓名
     */
    @Schema(description = "评标委员会负责人姓名")
    private String evaluationLeaderName;

    /**
     * 评标委员会负责人专家编码
     */
    @Schema(description = "评标委员会负责人专家编码")
    private String evaluationLeaderCode;

    /**
     * 是否可以抽取专家（只有待抽取状态才可以）
     */
    @Schema(description = "是否可以抽取专家")
    private Boolean canExtract;

    /**
     * 是否可以查看详情（只有已抽取状态才可以）
     */
    @Schema(description = "是否可以查看详情")
    private Boolean canViewDetail;

    /**
     * 是否可以重新抽取（只有已抽取状态才可以）
     */
    @Schema(description = "是否可以重新抽取")
    private Boolean canReExtract;

    /**
     * 当前专家是否已签名
     */
    @Schema(description = "当前专家是否已签名")
    private Boolean hasCurrentUserSigned;

    /**
     * 是否已经评标完成
     */
    @Schema(description = "是否已经评标完成")
    private Boolean isEvaluationCompleted;

    private static final long serialVersionUID = 1L;
}
