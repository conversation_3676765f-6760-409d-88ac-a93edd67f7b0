package com.ylz.saas.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylz.saas.entity.SrmTenderEvaluationStandard;
import com.ylz.saas.resp.SrmTenderEvaluationProgressResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 针对表【srm_tender_evaluation_standard(评标标准)】的数据库操作Mapper
 * <AUTHOR>
 * @createDate 2025-07-10
 */
@Mapper
public interface SrmTenderEvaluationStandardMapper extends BaseMapper<SrmTenderEvaluationStandard> {

    /**
     * 查询评标进度信息
     * @param sectionId 标段ID
     * @param noticeId 招标公告ID
     * @return 评标进度信息
     */
    SrmTenderEvaluationProgressResp.EvaluationCommitteeInfo getCommitteeInfo(@Param("sectionId") Long sectionId, @Param("noticeId") Long noticeId);

    /**
     * 查询评审节点列表
     * @param sectionId 标段ID
     * @param noticeId 招标公告ID
     * @return 评审节点列表
     */
    List<SrmTenderEvaluationProgressResp.EvaluationNode> getEvaluationNodes(@Param("sectionId") Long sectionId, @Param("noticeId") Long noticeId);

    /**
     * 查询评审项详细信息
     * @param sectionId 标段ID
     * @param noticeId 招标公告ID
     * @return 评审项列表
     */
    List<SrmTenderEvaluationProgressResp.EvaluationItem> getEvaluationItems(@Param("sectionId") Long sectionId, @Param("noticeId") Long noticeId);

    /**
     * 查询评标人员进度列表
     * @param evaluationId 评标委员会ID
     * @return 评标人员进度列表
     */
    List<SrmTenderEvaluationProgressResp.EvaluatorProgress> getEvaluatorProgressList(@Param("evaluationId") Long evaluationId);

    /**
     * 查询专家在各评审节点下的完成情况
     * @param sectionId 标段ID
     * @param noticeId 招标公告ID
     * @param userId 用户ID
     * @param currentRound 当前报价轮次
     * @return 完成情况统计
     */
    List<SrmTenderEvaluationProgressResp.NodeCompletionStatus> getEvaluatorNodeCompletion(@Param("sectionId") Long sectionId, @Param("noticeId") Long noticeId, @Param("userId") Long userId, @Param("currentRound") Integer currentRound);

    /**
     * 查询当前用户在项目中的角色权限
     * @param noticeId 招标公告ID
     * @param userId 用户ID
     * @return 用户角色
     */
    String getCurrentUserRole(@Param("noticeId") Long noticeId, @Param("userId") Long userId);
}




