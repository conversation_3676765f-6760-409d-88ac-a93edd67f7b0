package com.ylz.saas.resp;

import com.ylz.saas.enums.OpenStatusEnum;
import com.ylz.saas.enums.QuoteStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 报价明细（供应商）轮次响应类
 * <AUTHOR>
 * @createDate 2025-07-17
 */
@Data
@Schema(description = "报价明细（供应商）轮次响应类")
public class SrmTenderQuoteDetailBySupplierRoundResp implements Serializable {

    /**
     * 报价轮次
     */
    @Schema(description = "报价轮次")
    private Integer roundNo;

    /**
     * 报价截止日期
     */
    @Schema(description = "报价截止日期")
    private LocalDateTime quoteEndTime;

    /**
     * 开标状态
     */
    @Schema(description = "开标状态")
    private OpenStatusEnum openStatus;

    /**
     * 该轮次的供应商报价列表
     */
    @Schema(description = "该轮次的供应商报价列表")
    private List<SupplierQuoteInfo> supplierList;

    /**
     * 供应商报价信息
     */
    @Data
    @Schema(description = "供应商报价信息")
    public static class SupplierQuoteInfo implements Serializable {

        /**
         * 租户供应商ID
         */
        @Schema(description = "租户供应商ID")
        private Long tenantSupplierId;

        /**
         * 供应商名称
         */
        @Schema(description = "供应商名称")
        private String supplierName;

        /**
         * 联系人
         */
        @Schema(description = "联系人")
        private String contactPerson;

        /**
         * 联系电话
         */
        @Schema(description = "联系电话")
        private String contactPhone;

        /**
         * 报价状态
         */
        @Schema(description = "报价状态")
        private QuoteStatusEnum quoteStatus;

        /**
         * 报价IP
         */
        @Schema(description = "报价IP")
        private String quoteIp;

        /**
         * 报价时间
         */
        @Schema(description = "报价时间")
        private LocalDateTime quoteTime;

        /**
         * 报价排名
         */
        @Schema(description = "报价排名")
        private Integer ranking;

        /**
         * 报价总金额（用于排名计算）
         */
        @Schema(description = "报价总金额")
        private BigDecimal totalQuoteAmount;

        /**
         * 报价轮次（用于分组）
         */
        @Schema(description = "报价轮次")
        private Integer roundNo;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
