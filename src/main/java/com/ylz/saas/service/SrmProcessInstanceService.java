package com.ylz.saas.service;

import com.ylz.saas.entity.SrmProcessInstance;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.req.ProcessInstanceStartReq;
import com.ylz.saas.req.ProcessInstanceStopReq;
import com.ylz.saas.resp.ProcessInstanceStartResp;
import com.ylz.saas.resp.ProcessInstanceGroupResp;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 针对表【srm_process_instance(供应商响应表)】的数据库操作Service
 * @createDate 2025-06-12 16:20:53
 */
public interface SrmProcessInstanceService extends IService<SrmProcessInstance> {

    ProcessInstanceStartResp startProcessInstance(ProcessInstanceStartReq req);

    Boolean stopProcessInstance(ProcessInstanceStopReq req);

    /**
     * 通过bizId集合和type集合查询流程实例，返回分组封装对象
     * @param bizIds 业务ID集合
     * @param types 业务类型集合
     * @return 分组封装的流程实例对象
     */
    ProcessInstanceGroupResp getInstancesGroupByBizIdAndType(Set<Long> bizIds, Set<SrmProcessConfigService.BizTypeEnum> types);


    /**
     * 处理审批结果
     * @param bizId
     * @param bizTypeEnum
     * @param awardReportStatus
     */
    void handlerInstanceStatus(Long bizId, SrmProcessConfigService.BizTypeEnum bizTypeEnum, ApproveStatusEnum awardReportStatus);

}
