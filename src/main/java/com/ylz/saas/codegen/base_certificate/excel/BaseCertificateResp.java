package com.ylz.saas.codegen.base_certificate.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 证件信息表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:46
 */
@Data
public class BaseCertificateResp implements Serializable {

    /**
  	 * 主键ID
   	 */
	@ExcelProperty(value = "主键ID")
    private Long id;



    /**
  	 * 证件名称
   	 */
	@ExcelProperty(value = "证件名称")
    private String certName;

    /**
  	 * 补充说明
   	 */
	@ExcelProperty(value = "补充说明")
    private String description;

    /**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
	@ExcelProperty(value = "状态（DISABLED-禁用、ENABLED-启用）")
    private String status;



    /**
  	 * 创建人
   	 */
	@ExcelProperty(value = "创建人")
    private String createBy;

    /**
  	 * 创建时间
   	 */
	@ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
	@ExcelProperty(value = "修改人")
    private String updateBy;

    /**
  	 * 修改时间
   	 */
	@ExcelProperty(value = "修改时间")
    private LocalDateTime updateTime;




}