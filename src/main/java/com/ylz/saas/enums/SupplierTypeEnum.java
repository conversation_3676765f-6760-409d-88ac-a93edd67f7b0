package com.ylz.saas.enums;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/6/11 14:44
 * @Description
 */
@Getter
@AllArgsConstructor
public enum SupplierTypeEnum {


    OTHER("其它"),
    LEASING_SUPPLIER("租赁服务供应商"),
    LABOR_SUPPLIER("劳务外包供应商"),
    PROJECT_SUPPLIER("工程建筑供应商"),
    LOGISTICS_SUPPLIER("物流承运商"),
    SERVICE_SUPPLIER("服务型供应商"),
    TRADER("贸易型供应商"),
    MANUFACTURER("生产型供应商");


    private final String value;
}
