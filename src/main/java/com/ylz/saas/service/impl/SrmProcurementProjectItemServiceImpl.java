package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmProcurementProjectItem;
import com.ylz.saas.service.SrmProcurementProjectItemService;
import com.ylz.saas.mapper.SrmProcurementProjectItemMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【srm_procurement_project_item(采购立项物资明细表)】的数据库操作Service实现
* @createDate 2025-06-11 11:21:09
*/
@Service
public class SrmProcurementProjectItemServiceImpl extends ServiceImpl<SrmProcurementProjectItemMapper, SrmProcurementProjectItem>
    implements SrmProcurementProjectItemService{

}




