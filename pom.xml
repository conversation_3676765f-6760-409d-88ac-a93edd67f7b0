<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2020 canpan Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ylz</groupId>
        <artifactId>saas-monomer-17</artifactId>
        <version>3.1.1</version>
    </parent>

    <artifactId>procurement-platform</artifactId>
    <packaging>jar</packaging>
    <name>procurement-platform</name>
    <description>procurement-platform 单体版本启动</description>

    <properties>
        <knife4j.version>3.0.5</knife4j.version>
        <saas.version>3.1.1</saas.version>
    </properties>

    <dependencies>
        <!--必备：认证中心模块-->
        <dependency>
            <groupId>com.ylz</groupId>
            <artifactId>saas-auth</artifactId>
            <version>${saas.version}</version>
        </dependency>
        <!--必备：用户管理模块-->
        <dependency>
            <groupId>com.ylz</groupId>
            <artifactId>saas-upms-biz</artifactId>
            <version>${saas.version}</version>
        </dependency>
        <!--选配：代码生成模块 -->
        <dependency>
            <groupId>com.ylz</groupId>
            <artifactId>saas-codegen</artifactId>
            <version>${saas.version}</version>
        </dependency>
        <!--选配：app业务模块 -->
        <dependency>
            <groupId>com.ylz</groupId>
            <artifactId>saas-app-server-biz</artifactId>
            <version>${saas.version}</version>
        </dependency>
        <!--选配：定时任务模块 -->
        <dependency>
            <groupId>com.ylz</groupId>
            <artifactId>saas-daemon-quartz</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--选配：审批流引擎模块 -->
        <dependency>
            <groupId>com.ylz</groupId>
            <artifactId>saas-flow-engine-biz</artifactId>
            <version>${saas.version}</version>
        </dependency>
        <!--选配：审批流任务模块 -->
        <dependency>
            <groupId>com.ylz</groupId>
            <artifactId>saas-flow-task-biz</artifactId>
            <version>${saas.version}</version>
        </dependency>
        <!--选配：业务流模块 -->
        <dependency>
            <groupId>com.ylz</groupId>
            <artifactId>saas-flow-work-biz</artifactId>
            <version>${saas.version}</version>
        </dependency>
        <!--选配：支付平台模块 -->
        <dependency>
            <groupId>com.ylz</groupId>
            <artifactId>saas-pay-platform</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--选配：微信公众号管理模块 -->
        <dependency>
            <groupId>com.ylz</groupId>
            <artifactId>saas-mp-platform</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--选配：开放平台模块 -->
        <dependency>
            <groupId>com.ylz</groupId>
            <artifactId>saas-open-biz</artifactId>
            <version>${saas.version}</version>
        </dependency>
        <dependency>
            <groupId>com.ylz</groupId>
            <artifactId>saas-common-job</artifactId>
        </dependency>
        <!-- 接口文档UI  -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springboot</groupId>
            <artifactId>knife4j-boot-openapi3-ui</artifactId>
            <version>${knife4j.version}</version>
        </dependency>
        <!--接口文档-->
        <dependency>
            <groupId>com.ylz</groupId>
            <artifactId>saas-common-swagger</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <!--拼音处理依赖-->
        <!--        <dependency>-->
        <!--            <groupId>com.belerweb</groupId>-->
        <!--            <artifactId>pinyin4j</artifactId>-->
        <!--            <version>2.5.1</version>-->
        <!--        </dependency>-->
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
