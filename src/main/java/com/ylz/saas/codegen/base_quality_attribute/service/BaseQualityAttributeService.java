package com.ylz.saas.codegen.base_quality_attribute.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.base_quality_attribute.entity.BaseQualityAttributeEntity;
import com.ylz.saas.codegen.base_quality_attribute.vo.BaseQualityAttributeVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface BaseQualityAttributeService extends IService<BaseQualityAttributeEntity> {
	/**
     * 新增质量属性表
     * @param baseQualityAttributeVo 质量属性表
     */
    boolean saveBaseQualityAttribute(BaseQualityAttributeVo baseQualityAttributeVo);
	
	/**
     * 修改质量属性表
     * @param baseQualityAttributeVo 质量属性表
     */
    boolean updateBaseQualityAttribute(BaseQualityAttributeVo baseQualityAttributeVo);
	
	/**
     * 查询质量属性表详情
     * @param id
     */
    BaseQualityAttributeEntity getByIdBaseQualityAttribute(Long id);
	
	/**
	* 通过id删除质量属性表
	*/
	boolean removeByIdsBaseQualityAttribute(List<Long> ids);

	/**
	 * 启用禁用
	 * @param id
	 * @return
	 */
	Object switchStatus(Long id);

	/**
	 * 导入模板下载
	 * @param response
	 */
	void template(HttpServletResponse response);

	/**
	 * 数据导入
	 * @param file
	 * @param response
	 */
	void uploadData(MultipartFile file, HttpServletResponse response);

}