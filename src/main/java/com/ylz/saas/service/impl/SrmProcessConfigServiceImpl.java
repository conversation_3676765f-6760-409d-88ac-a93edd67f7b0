package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmProcessConfig;
import com.ylz.saas.service.SrmProcessConfigService;
import com.ylz.saas.mapper.SrmProcessConfigMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【srm_process_config(供应商响应表)】的数据库操作Service实现
* @createDate 2025-06-12 16:41:38
*/
@Service
public class SrmProcessConfigServiceImpl extends ServiceImpl<SrmProcessConfigMapper, SrmProcessConfig>
    implements SrmProcessConfigService{

}




