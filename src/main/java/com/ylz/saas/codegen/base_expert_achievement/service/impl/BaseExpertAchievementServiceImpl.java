package com.ylz.saas.codegen.base_expert_achievement.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.codegen.base_expert_achievement.entity.BaseExpertAchievementEntity;
import com.ylz.saas.codegen.base_expert_achievement.excel.BaseExpertAchievementReq;
import com.ylz.saas.codegen.base_expert_achievement.mapper.BaseExpertAchievementMapper;
import com.ylz.saas.codegen.base_expert_achievement.service.BaseExpertAchievementService;
import com.ylz.saas.codegen.base_expert_achievement.vo.BaseExpertAchievementVo;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.data.excel.ExcelAnalysisUtil;
import com.ylz.saas.common.data.excel.ExcelTemplateGenerator;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Path;
import java.util.List;
/**
 * 专家成果表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:42:08
 */
@Service
@Slf4j
public class BaseExpertAchievementServiceImpl extends ServiceImpl<BaseExpertAchievementMapper, BaseExpertAchievementEntity> implements BaseExpertAchievementService {
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveBaseExpertAchievement(BaseExpertAchievementVo baseExpertAchievementVo) {
		BaseExpertAchievementEntity baseExpertAchievement = new BaseExpertAchievementEntity();
        BeanUtil.copyProperties(baseExpertAchievementVo, baseExpertAchievement);
        return save(baseExpertAchievement);
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateBaseExpertAchievement(BaseExpertAchievementVo baseExpertAchievementVo) {
		BaseExpertAchievementEntity baseExpertAchievement = getById(baseExpertAchievementVo.getId());
		ExceptionUtil.check(baseExpertAchievement == null, "500", "专家成果表不存在");
		BeanUtil.copyProperties(baseExpertAchievementVo, baseExpertAchievement);
        return updateById(baseExpertAchievement);
	}
	
	@Override
    public void template(HttpServletResponse response) {
		// 创建目录及导入模板文件
		isExcelExist();
		// 读取导入模板
		FileInputStream inputStream = null;
		ServletOutputStream outputStream = null;
		try {
			String oriFileName = "专家成果表导入模板";
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding("utf-8");
			String fileName = URLEncoder.encode(oriFileName, "UTF-8").replaceAll("\\+", "%20");
			response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
			Path result = ExcelTemplateGenerator.getResult();
			Path filePath = result.resolve("专家成果表baseExpertAchievement.xlsx");
			// 绝对路径
			File file = new File(filePath.toString());
			inputStream = new FileInputStream(file);

			// 获取输出流
			outputStream = response.getOutputStream();

			// 将输入流的内容写入到输出流中
			byte[] buffer = new byte[1024];
			int bytesRead;
			while ((bytesRead = inputStream.read(buffer)) != -1) {
				outputStream.write(buffer, 0, bytesRead);
			}

			// 确保输出流的数据完全写入
			outputStream.flush();
			log.info("模板下载成功");
		} catch (IOException e) {
			System.out.println("e = " + e.getMessage());
			log.info("模板下载失败");
		} finally {
			// 关闭流
			try {
				if (inputStream != null) {
					inputStream.close();
				}
				if (outputStream != null) {
					outputStream.close();
				}
			} catch (IOException e) {
				log.error("关闭流时发生错误: {}", e.getMessage());
			}
		}
	}

	@Override
	@Transactional
	public void uploadData(MultipartFile file, HttpServletResponse response) {
		try {
			List<BaseExpertAchievementReq> importGoodsList = new ExcelAnalysisUtil<BaseExpertAchievementReq>()
					.readFile(file, BaseExpertAchievementReq.class, 1);
			if (CollectionUtils.isEmpty(importGoodsList)) {
				writeImportResponse(response, 1, "导入数据为空");
				return;
			}
			List<BaseExpertAchievementEntity> baseExpertAchievementEntities = BeanUtil.copyToList(importGoodsList, BaseExpertAchievementEntity.class);
			// 做字段的必传校验
			for (BaseExpertAchievementEntity baseExpertAchievementEntity : baseExpertAchievementEntities) {
				// 默认值，可修改
				if (baseExpertAchievementEntity.getCreateById() == null){
					baseExpertAchievementEntity.setCreateById(1792846774462181377L);
				}
				if (baseExpertAchievementEntity.getUpdateById() == null){
					baseExpertAchievementEntity.setUpdateById(1792846774462181377L);
				}
			}
			// 保存数据
			this.saveOrUpdateBatch(baseExpertAchievementEntities);
		} catch (IOException e) {
			log.info("文件解析失败");
			throw new RuntimeException(e);
		}
	}

	private void writeImportResponse(HttpServletResponse response, int code, String errorMsg) throws IOException {
		JSONObject obj = new JSONObject();
		response.setContentType("text/html;charset=utf-8");
		obj.put("code", code);
		obj.put("msg", errorMsg);
		PrintWriter writer = response.getWriter();
		writer.write(obj.toJSONString());
		writer.close();
	}

	private static void isExcelExist() {
		try {
			Path result = ExcelTemplateGenerator.getResult();

			File file = new File(result.toString());
			if (file.exists()) {
				log.info("excel文件夹路径存在");
			} else {
				// 创建文件夹
				if (file.mkdirs()) {
					log.info("目录已创建: {}" , file);
				} else {
					log.info("创建目录失败: {}", file);
					throw new RuntimeException("创建目录失败: " + file);
				}
			}
			// 设置 Excel 文件的完整路径
			Path filePath = result.resolve("专家成果表baseExpertAchievement.xlsx");
			// 生成模板文件
			ExcelTemplateGenerator.createExcelTemplate(filePath.toString(),BaseExpertAchievementReq.class);
		} catch (UnsupportedEncodingException e) {
			log.info("编码失败");
		}
	}
}