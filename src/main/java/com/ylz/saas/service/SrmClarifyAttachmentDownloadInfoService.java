package com.ylz.saas.service;

import com.ylz.saas.entity.SrmClarifyAttachmentDownloadInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.req.DownloadLogAddReq;
import com.ylz.saas.resp.ClarifyAttachmentDownloadInfoResp;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_clarify_attachment_download_info(澄清公告附件下载信息)】的数据库操作Service
* @createDate 2025-07-10 14:53:29
*/
public interface SrmClarifyAttachmentDownloadInfoService extends IService<SrmClarifyAttachmentDownloadInfo> {

    /**
     * 查询供应商下载信息
     *
     * @param clarifyNoticeId
     * @param supplierName
     * @return
     */
    List<ClarifyAttachmentDownloadInfoResp> querySupplierDownloadInfo(Long clarifyNoticeId, String supplierName);

    /**
     * 供应商下载信息添加
     *
     * @param addReq
     */
    void supplierDownloadInfoAdd(DownloadLogAddReq addReq);
}
