package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

import lombok.Data;
import com.ylz.saas.common.core.util.TenantTable;

/**
 * 租户供应商主营物料表
 * @TableName srm_tenant_supplier_material
 */
@TenantTable
@TableName(value = "srm_tenant_supplier_material")
@Data
public class SrmTenantSupplierMaterial {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 物料ID
     */
    private Long materialId;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 物料名称
     */
    @TableField(exist = false)
    private String materialName;
    /**
     * 物料编码
     */
    @TableField(exist = false)
    private String materialCode;
    /**
     * 物料规格
     */
    @TableField(exist = false)
    private String spec;
    /**
     * 物料类型名称
     */
    @TableField(exist = false)
    private String categoryName;
    /**
     * 物料一级类型名
     */
    @TableField(exist = false)
    private String materialCategoryRoot;
    /**
     * 计量单位
     */
    @TableField(exist = false)
    private String unit;
}
