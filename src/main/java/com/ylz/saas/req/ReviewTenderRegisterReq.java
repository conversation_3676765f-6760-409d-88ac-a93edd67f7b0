package com.ylz.saas.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/6/25 20:07
 * @Description
 */
@Data
public class ReviewTenderRegisterReq {

    /**
     * 公告ID
     */
    @NotNull(message = "公告ID不能为空")
    private Long noticeId;

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    private Long sectionId;

    /**
     * 供应商ID
     */
    @NotNull(message = "供应商ID不能为空")
    private Long tenantSupplierId;

    /**
     * 1:报名 2:保证金 3:邀请回执
     */
    @NotNull(message = "类型不能为空")
    private Integer type;

    /**
     * 审核结果
     */
    @NotNull(message = "审核结果不能为空")
    private Boolean pass;

    /**
     * 拒绝原因
     */
    private String rejectReason;

}
