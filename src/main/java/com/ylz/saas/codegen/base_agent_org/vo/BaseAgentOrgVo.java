package com.ylz.saas.codegen.base_agent_org.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ylz.saas.codegen.base_agent_user.entity.BaseAgentUserEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 代理机构表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:11
 */
@Data
public class BaseAgentOrgVo implements Serializable {

    /**
  	 * 主键ID
   	 */
    private Long id;

    /**
  	 * 组织ID
   	 */
    private Long deptId;

	/**
	 * 用户部门ID
	 */
	private Long deptTypeId;

    /**
  	 * 代理机构编码
   	 */
    private String agentCode;

    /**
  	 * 代理机构名称
   	 */
    private String agentName;
	/**
	 * 社会统一信用代码
	 */
	@Schema(description="社会统一信用代码")
	private String socialCreditCode;
    /**
  	 * 省
   	 */
    private String province;

    /**
  	 * 市
   	 */
    private String city;

    /**
  	 * 区
   	 */
    private String district;

    /**
  	 * 详细地址
   	 */
    private String address;

    /**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
    private String status;


    /**
  	 * 创建人名称
   	 */
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    private String updateByName;

    /**
  	 * 创建人
   	 */
    private String createBy;

    /**
  	 * 创建时间
   	 */
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
    private String updateBy;

    /**
  	 * 修改时间
   	 */
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
    private Long createById;

    /**
  	 * 更新人ID
   	 */
    private Long updateById;
	/**
	* 子表集合:base_agent_user
	*/
	private List<BaseAgentUserEntity> baseAgentUserList;
}