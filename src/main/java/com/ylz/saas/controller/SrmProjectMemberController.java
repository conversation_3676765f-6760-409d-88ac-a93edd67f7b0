package com.ylz.saas.controller;

import com.ylz.saas.common.core.util.R;
import com.ylz.saas.common.security.util.SecurityUtils;
import com.ylz.saas.entity.SrmProjectMember;
import com.ylz.saas.service.SrmProjectMemberService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 项目成员管理
 * <AUTHOR>
 * @Date 2025/7/8 19:08
 * @Description
 */
@RestController
@RequestMapping("/srmProjectMember")
@RequiredArgsConstructor
public class SrmProjectMemberController {

    private final SrmProjectMemberService srmProjectMemberService;


    /**
     * 获取当前用户的项目成员列表
     *
     */
    @RequestMapping("/getMyMemberList/{projectId}")
    public R<List<SrmProjectMember>> getMyMemberList(@PathVariable Long projectId) {
        return R.ok(srmProjectMemberService.getMyMemberList(projectId, SecurityUtils.getUser().getId(), null));
    }


    /**
     * 获取项目成员列表
     *
     */
    @RequestMapping("/getProjectRole/{projectId}/{role}")
    public R<List<SrmProjectMember>> getMyMemberList(@PathVariable Long projectId, @PathVariable String role) {
        return R.ok(srmProjectMemberService.getMyMemberList(projectId, null, role));
    }


}
