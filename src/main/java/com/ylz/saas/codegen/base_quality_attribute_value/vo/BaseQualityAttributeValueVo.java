package com.ylz.saas.codegen.base_quality_attribute_value.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 质量属性值表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:42:43
 */
@Data
public class BaseQualityAttributeValueVo implements Serializable {

    /**
  	 * 主键ID
   	 */
    private Long id;

    /**
  	 * 组织ID
   	 */
    private Long deptId;

    /**
  	 * 质量属性ID
   	 */
    private Long attributeId;

    /**
  	 * 属性值内容
   	 */
    private String valueContent;

    /**
  	 * 适用物料编码
   	 */
    private String materialCode;

    /**
  	 * 拒收标准
   	 */
    private String rejectionStandard;

    /**
  	 * 扣款标准
   	 */
    private String penaltyStandard;

    /**
  	 * 排序
   	 */
    private Integer sortOrder;

    /**
  	 * 创建人名称
   	 */
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    private String updateByName;

    /**
  	 * 创建人
   	 */
    private String createBy;

    /**
  	 * 创建时间
   	 */
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
    private String updateBy;

    /**
  	 * 修改时间
   	 */
    private LocalDateTime updateTime;

    /**
  	 * 租户ID
   	 */
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
    private Long createById;

    /**
  	 * 更新人ID
   	 */
    private Long updateById;
}