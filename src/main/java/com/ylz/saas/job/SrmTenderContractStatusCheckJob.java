package com.ylz.saas.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.ylz.saas.entity.SrmTenderContract;
import com.ylz.saas.enums.ContractStatusEnum;
import com.ylz.saas.service.SrmTenderContractService;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/24 15:53
 * @Description 合同状态失效检查定时任务
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SrmTenderContractStatusCheckJob {

    private final SrmTenderContractService contractService;

    /**
     * 合同状态检查任务
     * 每天凌晨执行一次合同状态检查
     */
    @XxlJob("srmTenderContractStatusCheckHandler")
    public void checkContractStatus() {
        long startTime = System.currentTimeMillis();
        log.info("处理合同状态开始！");
        try {
            contractService.contractStatusCheck();
        } catch (Exception e) {
            log.error("处理项目状态异常：", e);
        }
        log.info("处理合同状态完成！时间：{}", System.currentTimeMillis() - startTime);
    }
}
