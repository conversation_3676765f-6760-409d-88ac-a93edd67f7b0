package com.ylz.saas.codegen.base_expert_education.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 专家教育经历表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:42:08
 */
@Data
public class BaseExpertEducationReq implements Serializable {



    /**
  	 * 证书附件
   	 */
	@ExcelProperty(value = "证书附件")
    private String certificate;



    /**
  	 * 开始时间
   	 */
	@ExcelProperty(value = "开始时间")
    private String startDate;


    /**
  	 * 截止时间
   	 */
	@ExcelProperty(value = "截止时间")
    private String endDate;


    /**
  	 * 毕业院校
   	 */
	@ExcelProperty(value = "毕业院校")
    private String school;


    /**
  	 * 专业
   	 */
	@ExcelProperty(value = "专业")
    private String major;


    /**
  	 * 学位
   	 */
	@ExcelProperty(value = "学位")
    private String degree;










}