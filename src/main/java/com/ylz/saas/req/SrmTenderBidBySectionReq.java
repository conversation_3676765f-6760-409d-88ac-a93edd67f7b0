package com.ylz.saas.req;

import com.ylz.saas.enums.ApproveStatusEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 按标段定标参数
 * 注意：定标审核只能有一个供应商中标
 * <AUTHOR>
 * @createDate 2025-07-15
 */
@Data
public class SrmTenderBidBySectionReq {
    /**
     * 公告id
     */
    @NotNull(message = "公告id不能为空")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    private Long sectionId;

    /**
     * 引用模版ID
     */
    @NotNull(message = "引用模版ID不能为空")
    private Long awardTemplateId;

    /**
     * 定标报告内容
     */
    @NotBlank(message = "定标报告内容不能为空")
    private String awardReportContent;

    /**
     * 附件信息
     */
    private List<AttachmentInfoReq> attachmentInfos;

    /**
     * 定标备注
     */
    private String awardRemark;

    /**
     * 中标供应商信息
     */
    @Valid
    @NotNull(message = "中标供应商信息不能为空")
    @Size(min = 1, message = "中标供应商信息不能为空！")
    private List<BidSupplier> bidSupplierList;

    /**
     * 中标通知书参数
     */
    @Valid
    private List<SrmTenderEditBatchAwardNoticeReq> awardNoticeReqList;

    /**
     * 中标供应商信息
     */
    @Data
    public static class BidSupplier {
        /**
         * 租户供应商ID
         */
        @NotNull(message = "租户供应商ID不能为空")
        private Long tenantSupplierId;

        /**
         * 供应商名称
         */
        private String supplierName;

        /**
         * 是否中标
         */
        @NotNull(message = "是否中标不能为空")
        private Boolean isBid;

        /**
         * 中标金额（供应商对该标段所有物料的总报价金额）
         */
        private BigDecimal bidAmount;

        /**
         * 定标备注
         */
        private String awardRemark;

        /**
         * 支付方式id
         */
        private Long projectPaymentId;

        /**
         * 定标报告状态
         */
        private ApproveStatusEnum awardReportStatus;

        /**
         * 审批备注
         */
        private String examineRemark;

        /**
         * 定标报告内容
         */
        private String awardReportContent;

        /**
         * 定标报告时间
         */
        private LocalDateTime awardReportTime;

        /**
         * 定标报告模板ID
         */
        private Long awardTemplateId;
    }
}
