package com.ylz.saas.resp;

import com.lark.oapi.service.attendance.v1.enums.ApprovalStatusEnum;
import com.ylz.saas.enums.ApprovalType;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.RecruitMemberRoleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 招募结果
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@Schema(description = "招募结果响应类")
public class SrmRecruitResultPageResp implements Serializable {

    /**
     * 供应商id
     */
    @Schema(description = "供应商id")
    private Long supplierId;


    /**
     * 联系人姓名
     */
    @Schema(description = "联系人姓名")
    private String contacts;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String contactsPhone;

    /**
     * 报名资料id
     */
    @Schema(description = "报名资料id")
    private Long signUpId;

    /**
     * 资料审核状态
     */
    @Schema(description = "资料审核状态")
    private ApproveStatusEnum approvalStatus;

    /**
     * 是否入库
     */
    @Schema(description = "是否入库")
    private Integer isStored;

    /**
     * 报名时间
     */
    @Schema(description = "报名时间")
    private LocalDateTime signUpTime;
    @Serial
    private static final long serialVersionUID = 1L;
}
