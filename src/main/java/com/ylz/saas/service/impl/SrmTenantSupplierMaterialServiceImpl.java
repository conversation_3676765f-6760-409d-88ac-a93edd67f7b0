package com.ylz.saas.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmTenantSupplierMaterial;
import com.ylz.saas.service.SrmTenantSupplierMaterialService;
import com.ylz.saas.mapper.SrmTenantSupplierMaterialMapper;
import com.ylz.saas.vo.SupplierMaterialInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【srm_tenant_supplier_material(租户供应商主营物料表)】的数据库操作Service实现
 * @createDate 2025-06-10 18:08:09
 */
@Service
@Slf4j
public class SrmTenantSupplierMaterialServiceImpl extends ServiceImpl<SrmTenantSupplierMaterialMapper, SrmTenantSupplierMaterial>
        implements SrmTenantSupplierMaterialService {



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateSupplierMaterialInfo(SupplierMaterialInfoVo supplierMaterialInfoVo) {
        log.info("开始保存供应商主营物料信息，供应商ID：{}，物料ID：{}",
                supplierMaterialInfoVo.getSupplierId(), supplierMaterialInfoVo.getMaterialId());

        SrmTenantSupplierMaterial material;
        boolean isUpdate = supplierMaterialInfoVo.getId() != null;

        if (isUpdate) {
            // 更新操作
            material = this.getById(supplierMaterialInfoVo.getId());
            if (material == null) {
                log.error("供应商物料关联不存在，ID：{}", supplierMaterialInfoVo.getId());
                return false;
            }
            BeanUtil.copyProperties(supplierMaterialInfoVo, material, "id", "createBy", "createTime");
        } else {
            // 新增操作
            material = new SrmTenantSupplierMaterial();
            BeanUtil.copyProperties(supplierMaterialInfoVo, material);
        }

        // 注意：SrmTenantSupplierMaterial设置租户ID，为租户数据
        // material.setTenantId(null); // 移除这行，让框架自动设置租户ID

        boolean result = this.saveOrUpdate(material);

        if (result) {
            log.info("供应商主营物料信息保存成功，物料关联ID：{}", material.getId());
        } else {
            log.error("供应商主营物料信息保存失败");
        }

        return result;
    }

    @Override
    public List<SrmTenantSupplierMaterial> getMaterialsBySupplierId(Long supplierId) {
//        this.list(new LambdaQueryWrapper<SrmTenantSupplierMaterial>()
//                .eq(SrmTenantSupplierMaterial::getSupplierId, supplierId)
//                .orderByDesc(SrmTenantSupplierMaterial::getCreateTime))

        return this.baseMapper.getMaterialsBySupplierId(supplierId);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSupplierMaterial(Long id) {
        log.info("开始物理删除供应商主营物料信息，物料关联ID：{}", id);

        // 使用物理删除
        int result = this.baseMapper.physicalDeleteById(id);

        if (result > 0) {
            log.info("供应商主营物料信息物理删除成功，物料关联ID：{}", id);
        } else {
            log.error("供应商主营物料信息物理删除失败，物料关联ID：{}", id);
        }

        return result > 0;
    }

    @Override
    public Page<SrmTenantSupplierMaterial> getMaterialsBySupplierIdPage(Long supplierId, Page<SrmTenantSupplierMaterial> page) {
        log.info("开始分页查询供应商主营物料信息，供应商ID：{}，页码：{}，页大小：{}",
                supplierId, page.getCurrent(), page.getSize());

        // 使用自定义mapper方法进行联查，获取物料详细信息
        Page<SrmTenantSupplierMaterial> result = this.baseMapper.getMaterialsBySupplierIdPage(supplierId, page);

        log.info("供应商主营物料信息查询完成，供应商ID：{}，总记录数：{}，当前页记录数：{}",
                supplierId, result.getTotal(), result.getRecords().size());

        return result;
    }
}




