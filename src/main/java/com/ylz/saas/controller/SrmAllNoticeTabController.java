package com.ylz.saas.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.req.SrmAllNoticePageReq;
import com.ylz.saas.req.SrmAppletNoticePageReq;
import com.ylz.saas.resp.SrmAllInvitePageResp;
import com.ylz.saas.resp.SrmAllNoticePageResp;
import com.ylz.saas.resp.SrmAllPublicNoticePageResp;
import com.ylz.saas.resp.SrmAllPublicityPageResp;
import com.ylz.saas.service.SrmAllNoticeTabService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 统一公告
 */
@RestController
@RequestMapping("/noticeTogether")
@AllArgsConstructor
public class SrmAllNoticeTabController {


    private final SrmAllNoticeTabService allNoticeTabService;

    /**
     * 采购公告分页
     *
     * @param req 筛选条件
     * @return 查询结果
     */
    @PostMapping("/noticePage")
    public R<Page<SrmAllNoticePageResp>> tenderNoticePage(@RequestBody SrmAllNoticePageReq req) {
        Page<SrmAllNoticePageResp> noticePage = allNoticeTabService.tenderNoticePage(req);
        return R.ok(noticePage);
    }

    /**
     * 邀请函分页
     *
     * @param req 筛选条件
     * @return 查询结果
     */
    @PostMapping("/invitePage")
    public R<Page<SrmAllInvitePageResp>> invitePage(@RequestBody SrmAllNoticePageReq req) {
        Page<SrmAllInvitePageResp> noticePage = allNoticeTabService.invitePage(req);
        return R.ok(noticePage);
    }

    /**
     * 中标公示分页
     *
     * @param req 筛选条件
     * @return 查询结果
     */
    @PostMapping("/publicityPage")
    public R<Page<SrmAllPublicityPageResp>> publicityPage(@RequestBody SrmAllNoticePageReq req) {
        Page<SrmAllPublicityPageResp> noticePage = allNoticeTabService.publicityPage(req);
        return R.ok(noticePage);
    }

    /**
     * 中标公告分页
     *
     * @param req 筛选条件
     * @return 查询结果
     */
    @PostMapping("/publicNoticePage")
    public R<Page<SrmAllPublicNoticePageResp>> publicNoticePage(@RequestBody SrmAllNoticePageReq req) {
        Page<SrmAllPublicNoticePageResp> noticePage = allNoticeTabService.publicNoticePage(req);
        return R.ok(noticePage);
    }


    /**
     * 采购公告/邀请函分页(小程序使用)
     *
     * @param req 筛选条件
     * @return 查询结果
     */
    @PostMapping("/applet/inviteAndNotice")
    public R<Page<SrmAllNoticePageResp>> inviteAndNoticePage(@RequestBody SrmAppletNoticePageReq req) {
        Page<SrmAllNoticePageResp> noticePage = allNoticeTabService.inviteAndNoticePage(req);
        return R.ok(noticePage);
    }



}

