package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmRecruitSupplierDetail;
import com.ylz.saas.mapper.SrmRecruitSupplierDetailMapper;
import com.ylz.saas.service.SrmRecruitSupplierDetailService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 供应商招募物料/工程表Service实现类
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
@Slf4j
@AllArgsConstructor
public class SrmRecruitSupplierDetailServiceImpl extends ServiceImpl<SrmRecruitSupplierDetailMapper, SrmRecruitSupplierDetail> implements SrmRecruitSupplierDetailService {

}
