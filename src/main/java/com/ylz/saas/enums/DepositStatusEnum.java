package com.ylz.saas.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 保证金状态枚举
 * <AUTHOR>
 * @Date 2025/6/11 16:30
 */
@Getter
@AllArgsConstructor
public enum DepositStatusEnum {

    WITHDRAWN("WITHDRAWN", "已撤回"),
    UNPAID("UNPAID", "未缴纳"),
    PAID("PAID", "已缴纳"),
    VERIFIED("VERIFIED", "已核实"),
    REJECTED("REJECTED", "被拒绝"),
    RETURNED("RETURNED", "已退还");

    @EnumValue
    @JsonValue
    private final String code;
    private final String desc;

} 