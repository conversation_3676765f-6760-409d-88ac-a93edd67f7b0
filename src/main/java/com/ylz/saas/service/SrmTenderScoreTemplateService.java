package com.ylz.saas.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.entity.SrmTenderScoreTemplate;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.req.SrmTenderScoreTemplateSaveReq;
import com.ylz.saas.resp.SrmTenderScoreTemplateDetailResp;

/**
* <AUTHOR>
* @description 针对表【srm_tender_score_template(评分模板表)】的数据库操作Service
* @createDate 2025-07-09 16:30:35
*/
public interface SrmTenderScoreTemplateService extends IService<SrmTenderScoreTemplate> {

    Page<SrmTenderScoreTemplate> query(Page<SrmTenderScoreTemplate> page, String templateName, String templateCode);

    SrmTenderScoreTemplateDetailResp queryDetail(Long id);

    void saveTemplate(SrmTenderScoreTemplateSaveReq req);

    void updateTemplate(SrmTenderScoreTemplateSaveReq req);

    void deleteTemplate(Long id);
}
