package com.ylz.saas.mapper;

import com.ylz.saas.common.data.datascope.annotation.DataPermission;
import com.ylz.saas.entity.SrmTenderRequirement;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【srm_tender_requirement(招标要求表)】的数据库操作Mapper
* @createDate 2025-06-10 14:39:25
* @Entity com.ylz.saas.entity.SrmTenderRequirement
*/
@Mapper
//@DataPermission(userEnabled = true, dataPermissionRuleList = "[{\"dataTypeCode\":\"PROJECT\",\"targetField\":\"project_id\",\"dataSourceField\":\"ID\"}]")
public interface SrmTenderRequirementMapper extends SaasBaseMapper<SrmTenderRequirement> {

}




