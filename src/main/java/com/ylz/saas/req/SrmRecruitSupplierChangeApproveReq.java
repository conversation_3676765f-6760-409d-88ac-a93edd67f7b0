package com.ylz.saas.req;

import com.ylz.saas.enums.ApproveStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 供应商招募公告变更审核请求
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@Schema(description = "供应商招募公告变更审核请求")
public class SrmRecruitSupplierChangeApproveReq {

    /**
     * 变更记录ID
     */
    @NotNull(message = "变更记录ID不能为空")
    @Schema(description = "变更记录ID")
    private Long changeId;

    /**
     * 审核状态
     */
    @NotNull(message = "审核状态不能为空")
    @Schema(description = "审核状态")
    private ApproveStatusEnum approvalStatus;

    /**
     * 审核备注
     */
    @Schema(description = "审核备注")
    private String approveRemark;
}
