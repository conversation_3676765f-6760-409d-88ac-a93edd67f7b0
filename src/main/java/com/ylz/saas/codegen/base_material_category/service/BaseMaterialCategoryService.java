package com.ylz.saas.codegen.base_material_category.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.base_material_category.entity.BaseMaterialCategoryEntity;
import com.ylz.saas.codegen.base_material_category.param.BaseMaterialCategoryParam;
import com.ylz.saas.codegen.base_material_category.vo.BaseMaterialCategoryTreeVo;
import com.ylz.saas.codegen.base_material_category.vo.BaseMaterialCategoryVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface BaseMaterialCategoryService extends IService<BaseMaterialCategoryEntity> {
	/**
     * 新增物料分类表
     * @param baseMaterialCategoryVo 物料分类表
     */
    boolean saveBaseMaterialCategory(BaseMaterialCategoryVo baseMaterialCategoryVo);
	
	/**
     * 修改物料分类表
     * @param baseMaterialCategoryVo 物料分类表
     */
    boolean updateBaseMaterialCategory(BaseMaterialCategoryVo baseMaterialCategoryVo);
	
	/**
     * 导入模板下载
     * @param response
     */
    void template(HttpServletResponse response);

    /**
     * 数据导入
     * @param file
     * @param response
     */
    void uploadData(MultipartFile file, HttpServletResponse response);

    /**
     * 获取物料分类树形结构
     * @return 树形结构列表
     */
    List<BaseMaterialCategoryTreeVo> getTreeList();

    /**
     * 根据状态获取物料分类树形结构
     * @param status 状态（DISABLED-禁用、ENABLED-启用）
     * @return 树形结构列表
     */
    List<BaseMaterialCategoryTreeVo> getTreeListByStatus(String status);

    /**
     * 根据父级编码获取子节点树形结构
     * @param parentCategoryCode 父级分类编码
     * @return 树形结构列表
     */
    List<BaseMaterialCategoryTreeVo> getTreeListByParent(String parentCategoryCode);

    /**
     * 获取所有子节点编码（包括子节点的子节点）
     * @param parentCategoryCode 父级分类编码
     * @return 子节点编码列表
     */
    List<String> getAllChildrenCodes(String parentCategoryCode);

    /**
     * 获取所有父节点编码（包括父节点的父节点）
     * @param categoryCode 分类编码
     * @return 父节点编码列表
     */
    List<String> getAllParentCodes(String categoryCode);

    /**
     * 开关
     * @param id
     * @return
     */
    Object switchStatus(Long id);

    /**
     * 根据分类名称查询物料分类
     * @param categoryName 分类名称
     * @return 物料分类实体
     */
    BaseMaterialCategoryEntity getByCategoryName(String categoryName);

    /**
     * 导出
     * @param param
     * @return
     */
    List<BaseMaterialCategoryEntity> listInfo(BaseMaterialCategoryParam param);
}