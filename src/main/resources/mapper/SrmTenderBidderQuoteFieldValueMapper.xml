<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenderBidderQuoteFieldValueMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenderBidderQuoteFieldValue">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="projectId" column="project_id" jdbcType="BIGINT"/>
            <result property="noticeId" column="notice_id" jdbcType="BIGINT"/>
            <result property="sectionId" column="section_id" jdbcType="BIGINT"/>
            <result property="projectItemId" column="project_item_id" jdbcType="BIGINT"/>
            <result property="tenantSupplierId" column="tenant_supplier_id" jdbcType="BIGINT"/>
            <result property="quoteItemId" column="quote_item_id" jdbcType="BIGINT"/>
            <result property="serviceTypeId" column="service_type_id" jdbcType="BIGINT"/>
            <result property="fieldCode" column="field_code" jdbcType="VARCHAR"/>
            <result property="fieldType" column="field_type" jdbcType="VARCHAR"/>
            <result property="fieldName" column="field_name" jdbcType="VARCHAR"/>
            <result property="enumValues" column="enum_values" jdbcType="VARCHAR"/>
            <result property="fieldValue" column="field_value" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,
        project_id,notice_id,section_id,
        project_item_id,tenant_supplier_id,quote_item_id,
        service_type_id,field_code,field_type,
        field_name,enum_values,field_value,
        del_flag,create_by,create_by_name,
        create_time,update_by,update_by_name,
        update_time
    </sql>
</mapper>
