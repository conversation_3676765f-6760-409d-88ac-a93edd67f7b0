package com.ylz.saas.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 开标记录
 */
@Data
public class SrmTenderOpenInfoReq {
    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    private Long sectionId;

    /**
     * 第几轮次报价
     */
    @NotNull(message = "第几轮次报价不能为空")
    private Integer roundNo;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商ID
     */
    private Long tenantSupplierId;
}