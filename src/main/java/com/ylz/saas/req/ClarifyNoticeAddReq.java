package com.ylz.saas.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 澄清公告
 */
@Data
public class ClarifyNoticeAddReq implements Serializable {

    /**
     * 采购立项id
     */
    @NotNull(message = "采购立项ID不能为空")
    public Long projectId;

    /**
     * 招标公告id
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 澄清公告标题
     */
    @NotBlank(message = "澄清公告标题不能为空")
    private String clarifyTitle;

    /**
     * 澄清公告内容
     */
    @NotBlank(message = "澄清公告内容不能为空")
    private String clarifyContent;

    /**
     * 附件信息
     */
    private List<AttachmentInfoReq> attachmentList;

}
