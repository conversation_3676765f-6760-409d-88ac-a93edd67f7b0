<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmPlatformSupplierInfoMapper">


    <select id="getOwnSupplier" resultType="com.ylz.saas.vo.SupplierBasicInfoDetailVo">

        select si.*
        from srm_tenant_supplier_contact sc
                 left join srm_tenant_supplier_info si on sc.tenant_supplier_id = si.id

        where sc.login_account = #{username}

    </select>
</mapper>
