package com.ylz.saas.resp;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购立项物资明细表
 */
@Data
public class SrmOpenTenderSupplierResp {

    /**
     * 平台供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 供应商编号
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商简称
     */
    private String supplierShortName;

    /**
     * 法人代表
     */
    private String contactName;

    /**
     * 法人电话
     */
    private String contactPhone;

    /**
     * 轮次
     */
    private Integer roundNo;

    /**
     * 报价物料条数
     */
    private Integer materialNum;

    /**
     * 报价总计
     */
    private BigDecimal quoteTotal;

    /**
     * 报价IP
     */
    private String quoteIp;
}