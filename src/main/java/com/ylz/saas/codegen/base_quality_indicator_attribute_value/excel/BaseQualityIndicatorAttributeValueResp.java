package com.ylz.saas.codegen.base_quality_indicator_attribute_value.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 质量指标属性值表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:00
 */
@Data
public class BaseQualityIndicatorAttributeValueResp implements Serializable {

    /**
  	 * 主键ID
   	 */
	@ExcelProperty(value = "主键ID")
    private Long id;


    /**
  	 * 质量指标ID
   	 */
	@ExcelProperty(value = "质量指标ID")
    private Long qualityIndicatorId;

    /**
  	 * 质量属性ID
   	 */
	@ExcelProperty(value = "质量属性ID")
    private String attributeId;

    /**
  	 * 质量属性值ID
   	 */
	@ExcelProperty(value = "质量属性值ID")
    private String attributeValueId;

    /**
  	 * 属性值内容
   	 */
	@ExcelProperty(value = "属性值内容")
    private String valueContent;

    /**
  	 * 拒收标准
   	 */
	@ExcelProperty(value = "拒收标准")
    private String rejectionStandard;

    /**
  	 * 扣款标准
   	 */
	@ExcelProperty(value = "扣款标准")
    private String penaltyStandard;










}