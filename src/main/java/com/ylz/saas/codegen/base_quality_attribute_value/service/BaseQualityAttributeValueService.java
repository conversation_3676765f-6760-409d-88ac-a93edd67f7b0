package com.ylz.saas.codegen.base_quality_attribute_value.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.base_quality_attribute_value.entity.BaseQualityAttributeValueEntity;
import com.ylz.saas.codegen.base_quality_attribute_value.vo.BaseQualityAttributeValueVo;
import com.ylz.saas.codegen.base_quality_attribute_value.vo.BaseQualityAttributeValueWithAttributeVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface BaseQualityAttributeValueService extends IService<BaseQualityAttributeValueEntity> {
	/**
     * 新增质量属性值表
     * @param baseQualityAttributeValueVo 质量属性值表
     */
    Long saveBaseQualityAttributeValue(BaseQualityAttributeValueVo baseQualityAttributeValueVo);
	
	/**
     * 修改质量属性值表
     * @param baseQualityAttributeValueVo 质量属性值表
     */
    boolean updateBaseQualityAttributeValue(BaseQualityAttributeValueVo baseQualityAttributeValueVo);
	
	/**
     * 导入模板下载
     * @param response
     */
    void template(HttpServletResponse response);

    /**
     * 数据导入
     * @param file
     * @param response
     */
    void uploadData(MultipartFile file, HttpServletResponse response);

    /**
     * 根据物料编码查询质量属性值列表（联查质量属性信息）
     * @param materialCode 物料编码
     * @param attributeName 质量属性名称（模糊查询）
     * @return 质量属性值列表（包含质量属性信息）
     */
    List<BaseQualityAttributeValueWithAttributeVo> getBaseQualityAttributeValueListByMaterialCode(String materialCode, String attributeName);
}