<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmClarifyNoticeMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmClarifyNotice">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="projectId" column="project_id" jdbcType="BIGINT"/>
            <result property="noticeId" column="notice_id" jdbcType="BIGINT"/>
            <result property="clarifyTitile" column="clarify_titile" jdbcType="VARCHAR"/>
            <result property="clarifyContent" column="clarify_content" jdbcType="VARCHAR"/>
            <result property="approveStatus" column="approve_status" jdbcType="VARCHAR"/>
            <result property="publishNoticeTime" column="publish_notice_time" jdbcType="TIMESTAMP"/>
            <result property="publishOwner" column="publish_owner" jdbcType="BIGINT"/>
            <result property="publishOwnerId" column="publish_owner_id" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,
        project_id,notice_id,clarify_titile,
        clarify_content,approve_status,publish_notice_time,
        publish_owner,publish_owner_id,del_flag,
        create_by,create_by_name,create_time,
        update_by,update_by_name,update_time
    </sql>
</mapper>
