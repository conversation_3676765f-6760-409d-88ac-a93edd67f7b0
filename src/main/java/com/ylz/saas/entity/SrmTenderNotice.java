package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ylz.saas.common.core.util.TenantTable;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.PublicNoticeStatusEnum;
import com.ylz.saas.enums.TenderWayEnum;
import com.ylz.saas.vo.SectionQuoteRoundVo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 招标公告表
 * @TableName srm_tender_notice
 */
@TenantTable
@TableName(value ="srm_tender_notice")
@Data
public class SrmTenderNotice implements Serializable {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 采购立项ID
     */
    private Long projectId;

    /**
     * 公告标题
     */
    private String noticeTitle;

    /**
     * 引用模版ID
     */
    private Long noticeTemplateId;

    /**
     * 招标公告内容（富文本）
     */
    private String noticeContent;

    /**
     * 招标公告编码
     */
    private String tenderNoticeCode;

    /**
     * 项目所在地区-省
     */
    private String province;

    /**
     * 项目所在地区-市
     */
    private String city;

    /**
     * 项目所在地区-区
     */
    private String district;

    /**
     * 项目详细地址
     */
    private String address;

    /**
     * 报价是否含税（0-否、1-是）
     */
    private Integer includeTax;

    /**
     * 发票要求(SPECIAL-专票、NORMAL-普票、NONE-无要求)
     */
    private String certificateType;

    /**
     * 评审规则(COMPREHENSIVE-综合评标低价法、LOWEST_PRICE-最低价法、COMPREHENSIVE_SCORE-综合评分法、HIGHEST_PRICE-最高价法)
     */
    private String evaluationMethod;

    /**
     * 采购联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 固定电话
     */
    private String contactFixedPhone;

    /**
     * 电子邮件
     */
    private String contactEmail;

    /**
     * 报名截止开始时间
     */
    private LocalDateTime registerStartTime;

    /**
     * 报名截止时间
     */
    private LocalDateTime registerEndTime;

    /**
     * 资格审核开始时间
     */
    private LocalDateTime auditStartTime;

    /**
     * 资格审核截止时间
     */
    private LocalDateTime auditEndTime;

    /**
     * 报价/投标/竞价开始时间
     */
    private LocalDateTime quoteStartTime;

    /**
     * 报价/投标/竞价截止时间
     */
    private LocalDateTime quoteEndTime;

    /**
     * 报价有效期
     */
    private Integer quoteValidity;

    /**
     * 文件递交地址
     */
    private String fileSubmissionAddress;

    /**
     * 文件获取开始时间
     */
    private LocalDateTime fileObtainStartTime;

    /**
     * 文件获取结束时间
     */
    private LocalDateTime fileObtainEndTime;

    /**
     * 标书费是否收取
     */
    private Boolean bidFeeCollection;

    /**
     * 投标缴费开始时间
     */
    private LocalDateTime bidDocPayStartTime;

    /**
     *  投标缴费截止时间
     */
    private LocalDateTime bidDocPayEndTime;

    /**
     * 开标时间
     */
    private LocalDateTime bidOpenTime;

    /**
     * 开标人
     */
    private Long bidOpener;

    /**
     * 开标地点
     */
    private String bidOpeningAddress;

    /**
     * 评标时间
     */
    private LocalDateTime bidEndTime;

    /**
     * 报价须知
     */
    private String quotationNotice;

    /**
     * 状态
     */
    private PublicNoticeStatusEnum status;

    /**
     * 公告开始时间
     */
    private LocalDateTime publishNoticeStartTime;

    /**
     * 公告结束时间
     */
    private LocalDateTime publishNoticeEndTime;

    /**
     * 招标审核状态
     * TO_APPROVE("待审批"),
     *     APPROVING("审批中"),
     *     APPROVE("审批通过"),
     *     APPROVE_REJECT("审批驳回"),
     *     APPROVE_REVOKE("审批撤销"),
     */
    private ApproveStatusEnum noticeStatus;

    /**
     * 招标审核备注信息
     */
    private String approvedRemark;

    /**
     * 付款方式
     */
    private String projectPaymentStr;

    /**
     * 青贮备注信息
     */
    private String ensilageRemark;

    /**
     * 拦标价
     */
    private BigDecimal interceptPrice;

    /**
     * 采购执行人id
     */
    private Long executorId;

    /**
     * 是否牛信支付
     */
    private Boolean isNxPay;

    /**
     * 线下通知供应商
     */
    private Boolean offlineNoticeSupplier;

    /**
     * 供货时间段开始
     */
    private LocalDateTime supplyStartTime;

    /**
     * 供货时间段结束
     */
    private LocalDateTime supplyEndTime;

    /**
     * 浏览次数
     */
    private Integer viewCount;

    /**
     * 开标方式
     */
    private TenderWayEnum tenderWay;

    /**
     * 投标须知
     */
    private String biddingNotice;

    /**
     * 竞谈文件的审核状态
     * TO_APPROVE("待审批"),
     *     APPROVING("审批中"),
     *     APPROVE("审批通过"),
     *     APPROVE_REJECT("审批驳回"),
     *     APPROVE_REVOKE("审批撤销"),
     */
    private ApproveStatusEnum docStatus;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    private Integer delFlag;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createById;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updateById;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 附件列表
     */
    @TableField(exist = false)
    private List<SrmProjectAttachment> attachmentList;

    /**
     * 招标要求列表
     */
    @TableField(exist = false)
    private List<SrmTenderRequirement> requirementList;

    /**
     * 供应商邀请列表
     */
    @TableField(exist = false)
    private List<SrmTenderSupplierInvite> supplierInviteList;

    /**
     * 邀请函标题
     */
    @TableField(exist = false)
    private String inviteTitle;

    /**
     * 物料信息列表
     */
    @TableField(exist = false)
    private List<SrmProcurementProjectItem> projectItemList;

    /**
     * 评价结果列表
     */
    @TableField(exist = false)
    private List<SrmTenderEvaluationResult> evaluationResultList;

    /**
     * 中标通知书表
     */
    @TableField(exist = false)
    private List<SrmTenderAwardNotice> awardNoticeList;

    /**
     * 当前报价轮次列表
     */
    @TableField(exist = false)
    private List<SectionQuoteRoundVo> quoteRoundVoList;

    /**
     * 报价项列表
     */
    @TableField(exist = false)
    private List<SrmTenderBidderQuoteItem> quoteItemList;

    /**
     * 是否缴纳保证金（true-缴纳、false-不缴纳）
     */
    @TableField(exist = false)
    private Boolean needDeposit;


    /**
     * 是否缴纳标书费（true-缴纳、false-不缴纳）
     */
    @TableField(exist = false)
    private Boolean needTenderFee;



}