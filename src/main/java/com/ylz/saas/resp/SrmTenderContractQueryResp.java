package com.ylz.saas.resp;

import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.ContractStatusEnum;
import com.ylz.saas.enums.ContractTypeEnum;
import com.ylz.saas.service.SrmProcessConfigService;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class SrmTenderContractQueryResp {
    /**
     *  ID
     */
    private Long id;

    /**
     * 项目名称
     */
    private String projectName;//project表

    /**
     * 标段名称
     */
    private String sectionName;//section表

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 签约时间
     */
    private LocalDateTime signDate;

    /**
     * 合同含税总金额
     */
    private BigDecimal totalAmount;

    /**
     * 采购方名称
     */
    private String purchaseDeptName;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 合同状态（NEW-新建、PENDING_SUPPLIER-待供应商确认、EFFECTIVE-合同生效、INVALID-已失效、REVOKED-已作废、SUPPLIER_REJECTED-供应商退回）
     */
    private ContractStatusEnum contractStatus;

    /**
     * 审批状态（APPROVING-审批中、APPROVE_REJECT-审批驳回、APPROVE-审批通过）
     */
    private ApproveStatusEnum approvalStatus;

    /**
     * 供方退回且采方修改后保存待确认时合同信息
     */
    private String supplierRejectedContractInfo;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改人名称
     */
    private String updateByName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 业务类型
     */
    private SrmProcessConfigService.BizTypeEnum processInstanceType;
}
