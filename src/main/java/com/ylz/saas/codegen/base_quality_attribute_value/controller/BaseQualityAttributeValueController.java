package com.ylz.saas.codegen.base_quality_attribute_value.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.codegen.base_quality_attribute.service.BaseQualityAttributeService;
import com.ylz.saas.codegen.base_quality_attribute_value.entity.BaseQualityAttributeValueEntity;
import com.ylz.saas.codegen.base_quality_attribute_value.param.BaseQualityAttributeValueParam;
import com.ylz.saas.codegen.base_quality_attribute_value.service.BaseQualityAttributeValueService;
import com.ylz.saas.common.core.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

/**
 * 质量属性值表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:42:43
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/baseQualityAttributeValue")
@Tag(description = "baseQualityAttributeValue", name = "质量属性值表")
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class BaseQualityAttributeValueController {

    private final BaseQualityAttributeValueService baseQualityAttributeValueService;




    /**
     * 质量属性值列表查询（根据物料编码查询，联查质量属性信息）
     * @param param 查询参数，必须包含materialCode
     * @return
     */
    @Operation(summary = "质量属性值列表查询", description = "根据物料编码查询，联查质量属性信息，支持质量属性名称模糊查询，默认返回10条")
    @PostMapping("/list")
    public R getBaseQualityAttributeValueList(@RequestBody BaseQualityAttributeValueParam param) {
        QueryWrapper<BaseQualityAttributeValueEntity> queryWrapper = param.toWrapper();
        return R.ok(baseQualityAttributeValueService.list(queryWrapper));
    }



}