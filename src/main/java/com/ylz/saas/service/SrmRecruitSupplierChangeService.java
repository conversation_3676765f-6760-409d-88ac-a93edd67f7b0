package com.ylz.saas.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.entity.SrmRecruitSupplierChange;
import com.ylz.saas.req.SrmRecruitSupplierChangeApproveReq;
import com.ylz.saas.req.SrmRecruitSupplierChangeCreateReq;
import com.ylz.saas.req.SrmRecruitSupplierChangePageReq;
import com.ylz.saas.resp.SrmRecruitSupplierChangeDetailResp;
import com.ylz.saas.resp.SrmRecruitSupplierChangePageResp;

/**
 * 供应商招募公告变更记录Service
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
public interface SrmRecruitSupplierChangeService extends IService<SrmRecruitSupplierChange> {

    /**
     * 创建招募公告变更记录
     *
     * @param req 变更创建请求
     * @return 变更记录ID
     */
    Long createChange(SrmRecruitSupplierChangeCreateReq req);

    /**
     * 分页查询变更记录
     *
     * @param req  查询条件
     * @param page 分页参数
     * @return 分页结果
     */
    IPage<SrmRecruitSupplierChangePageResp> pageChange(SrmRecruitSupplierChangePageReq req, Page<SrmRecruitSupplierChangePageResp> page);

    /**
     * 查询变更详情
     *
     * @param changeId 变更记录ID
     * @return 变更详情
     */
    SrmRecruitSupplierChangeDetailResp getChangeDetail(Long changeId);

    /**
     * 审核变更记录
     *
     * @param req 审核请求
     */
    void approveChange(SrmRecruitSupplierChangeApproveReq req);

    /**
     * 向已报名供应商发送变更通知
     *
     * @param recruitId 招募公告ID
     */
    void sendChangeNotificationToSuppliers(Long recruitId);
}
