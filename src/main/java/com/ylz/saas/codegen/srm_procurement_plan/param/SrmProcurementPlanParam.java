package com.ylz.saas.codegen.srm_procurement_plan.param;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ylz.saas.codegen.srm_procurement_plan.entity.SrmProcurementPlanEntity;
import com.ylz.saas.common.data.mybatis.helper.Operator;
import com.ylz.saas.common.data.mybatis.helper.QueryWrapperHelper;
import com.ylz.saas.common.data.mybatis.wrapper.CriteriaField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * mybatis-plus的查询帮助类
 *
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SrmProcurementPlanParam {

    /**
     * 主键ID
     */
    @CriteriaField(field = "id", operator = Operator.LIKE)
    private Long id;
    /**
     * 组织ID
     */
    @CriteriaField(field = "deptId", operator = Operator.LIKE)
    private String deptId;
    /**
     * 采购计划编号
     */
    @CriteriaField(field = "planCode", operator = Operator.LIKE)
    private String planCode;
    /**
     * 采购计划名称
     */
    @CriteriaField(field = "planName", operator = Operator.LIKE)
    private String planName;
    /**
     * 服务类型ID
     */
    @CriteriaField(field = "serviceTypeId", operator = Operator.LIKE)
    private String serviceTypeId;
    /**
     * 申请日期_开始
     */
    @CriteriaField(field = "applyDate", operator = Operator.GE, filterBlank = true)
    private LocalDate applyDateStart;
    /**
     * 申请日期_结束
     */
    @CriteriaField(field = "applyDate", operator = Operator.LE, filterBlank = true)
    private LocalDate applyDateEnd;
    /**
     * 申请部门
     */
    @CriteriaField(field = "applyDeptId", operator = Operator.LIKE)
    private String applyDeptId;
    /**
     * 申请人
     */
    @CriteriaField(field = "applicant", operator = Operator.LIKE)
    private String applicant;

    /**
     * 申请部门名
     */
    @CriteriaField(ignore = true)
    private String applyDeptName;

    /**
     * 申请人名
     */
    @CriteriaField(ignore = true)
    private String applicantName;

    /**
     * 申请人电话
     */
    @CriteriaField(field = "applicantPhone", operator = Operator.LIKE)
    private String applicantPhone;

    /**
     * 采购部门
     */
    @CriteriaField(field = "procurementDeptId", operator = Operator.LIKE)
    private String procurementDeptId;
    /**
     * 计划招标方式
     */
    @CriteriaField(field = "planRecruitMethod", operator = Operator.IN)
    private List<String> planRecruitMethodList;
    /**
     * 期限到货日期_开始
     */
    @CriteriaField(field = "periodStartDate", operator = Operator.GE, filterBlank = true)
    private LocalDate periodStartDateStart;
    /**
     * 期限到货日期_结束
     */
    @CriteriaField(field = "periodStartDate", operator = Operator.LE, filterBlank = true)
    private LocalDate periodStartDateEnd;
    /**
     * 采购方式
     */
    @CriteriaField(field = "procurementMethod", operator = Operator.IN)
    private List<String> procurementMethodList;
    /**
     * 资金来源
     */
    @CriteriaField(field = "fundSource", operator = Operator.LIKE)
    private String fundSource;
    /**
     * 预算金额
     */
    @CriteriaField(field = "budgetAmount", operator = Operator.EQ)
    private BigDecimal budgetAmount;
    /**
     * 计划来源方式
     */
    @CriteriaField(field = "planSource", operator = Operator.LIKE)
    private String planSource;
    /**
     * 单据状态
     */
    @CriteriaField(field = "status", operator = Operator.IN)
    private List<String> statusList;
    /**
     * 创建人名称
     */
    @CriteriaField(field = "createByName", operator = Operator.LIKE)
    private String createByName;
    /**
     * 修改人名称
     */
    @CriteriaField(field = "updateByName", operator = Operator.LIKE)
    private String updateByName;
    /**
     * 当前审批人
     */
    @CriteriaField(field = "currentApprover", operator = Operator.LIKE)
    private String currentApprover;
    /**
     * 代理机构表ID
     */
    @CriteriaField(field = "baseAgentDeptId", operator = Operator.LIKE)
    private Long baseAgentDeptId;
    /**
     * 创建人
     */
    @CriteriaField(field = "createBy", operator = Operator.LIKE)
    private String createBy;
    /**
     * 创建时间_开始
     */
    @CriteriaField(field = "createTime", operator = Operator.GE, filterBlank = true)
    private LocalDateTime createTimeStart;
    /**
     * 创建时间_结束
     */
    @CriteriaField(field = "createTime", operator = Operator.LE, filterBlank = true)
    private LocalDateTime createTimeEnd;
    /**
     * 修改人
     */
    @CriteriaField(field = "updateBy", operator = Operator.LIKE)
    private String updateBy;
    /**
     * 修改时间_开始
     */
    @CriteriaField(field = "updateTime", operator = Operator.GE, filterBlank = true)
    private LocalDateTime updateTimeStart;
    /**
     * 修改时间_结束
     */
    @CriteriaField(field = "updateTime", operator = Operator.LE, filterBlank = true)
    private LocalDateTime updateTimeEnd;
    /**
     * 删除标识（0-正常、1-删除）
     */
    @CriteriaField(field = "delFlag", operator = Operator.EQ)
    private int delFlag;
    /**
     * 租户ID
     */
    @CriteriaField(field = "tenantId", operator = Operator.LIKE)
    private Long tenantId;
    /**
     * 创建人ID
     */
    @CriteriaField(field = "createById", operator = Operator.LIKE)
    private Long createById;
    /**
     * 更新人ID
     */
    @CriteriaField(field = "updateById", operator = Operator.LIKE)
    private Long updateById;

    /**
     * 构建查询
     */
    public QueryWrapper<SrmProcurementPlanEntity> toWrapper() {
        QueryWrapper<SrmProcurementPlanEntity> queryWrapper = QueryWrapperHelper.fromBean(this, SrmProcurementPlanEntity.class)
                                                                                .orderByDesc("id");
        return queryWrapper;
    }
}