package com.ylz.saas.controller;

import com.ylz.saas.common.core.util.R;
import com.ylz.saas.entity.SrmProcurementProjectSection;
import com.ylz.saas.service.SrmProcurementProjectSectionService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 标段管理
 * <AUTHOR>
 * @Date 2025/6/25 14:18
 * @Description
 */
@RestController
@RequestMapping("/srmProcurementProjectSelection")
@RequiredArgsConstructor
public class SrmProcurementProjectSelectionController {


    private final SrmProcurementProjectSectionService srmProcurementProjectSectionService;


    /**
     * 获取项目下所有标段
     */
    @GetMapping("/getListByProjectId/{projectId}")
    public R<List<SrmProcurementProjectSection>> getListByNoticeId(@PathVariable("projectId") String projectId) {
        return R.ok(srmProcurementProjectSectionService.getListByNoticeId(projectId));
    }


}
