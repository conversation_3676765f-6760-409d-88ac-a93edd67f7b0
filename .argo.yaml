build_image: acr.ops.yunlizhi.cn/private-img/devops/config-management.git/dc-compile-java:maven3.8.4-openjdk17
builds:  
  - cmds: |-
      rm -rf /caches01/maven/com/ylz/saas-common*
      rm -rf /caches01/maven/com/ylz/saas-upms*
      mvn clean install -am -U -DskipTests -pl . 
    runtime_image_src_artifacts: target/procurement-platform.jar
    runtime_image_dest: /app.jar
    runtime_image_env: allenv
    service_name: procurement-platform