package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ylz.saas.common.core.util.TenantTable;
import com.ylz.saas.enums.ApproveStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 供应商招募公告变更记录表
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@TableName("srm_recruit_supplier_change")
@Schema(description = "供应商招募公告变更记录表")
@TenantTable
public class SrmRecruitSupplierChange {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "部门ID")
    private Long deptId;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "删除标识（0-正常、1-删除）")
    private Integer delFlag;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人ID")
    private Long createById;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人名称")
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人ID")
    private Long updateById;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人名称")
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 招募公告ID
     */
    @Schema(description = "招募公告ID")
    private Long recruitId;

    /**
     * 招募公告编码
     */
    @Schema(description = "招募公告编码")
    private String recruitCode;

    /**
     * 变更人ID
     */
    @Schema(description = "变更人ID")
    private Long changeById;

    /**
     * 变更人
     */
    @Schema(description = "变更人")
    private String changeBy;

    /**
     * 变更人名称
     */
    @Schema(description = "变更人名称")
    private String changeByName;

    /**
     * 变更时间
     */
    @Schema(description = "变更时间")
    private LocalDateTime changeTime;

    /**
     * 变更前整体对象JSON
     */
    @Schema(description = "变更前整体对象JSON")
    private String beforeChangeJson;

    /**
     * 变更后整体对象JSON
     */
    @Schema(description = "变更后整体对象JSON")
    private String afterChangeJson;

    /**
     * 变更审核状态
     */
    @Schema(description = "变更审核状态")
    private ApproveStatusEnum approvalStatus;



    /**
     * 变更说明
     */
    @Schema(description = "变更说明")
    private String changeRemark;
}
