package com.ylz.saas.req;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SrmTenderScoreTemplateSaveReq {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板描述
     */
    private String templateDesc;

    /**
     * 评审项
     */
    private List<TemplateReviewItem> templateReviewItems;

    /**
     * 评分项
     */
    private List<TemplateScoreItem> templateScoreItems;

    @Data
    public static class TemplateReviewItem extends BaseItem{
        /**
         * 不符合否决投标数量(评审)
         */
        private Integer rejectCount;
    }

    @Data
    public static class TemplateScoreItem extends BaseItem{
        /**
         * 总分(评分)
         */
        private BigDecimal totalScore;

        /**
         * 权重(评分)
         */
        private BigDecimal weight;

        /**
         * 分值下限(评分)
         */
        private BigDecimal minScore;

        /**
         * 分值上限(评分)
         */
        private BigDecimal maxScore;
    }
    @Data
    public static class BaseItem {

        /**
         * 名称
         */
        private String name;

        /**
         * 详情
         */
        private String detail;

        /**
         * 节点名称
         */
        private String node;
    }
}
