package com.ylz.saas.codegen.srm_procurement_plan_field_value.mapper;

import com.ylz.saas.codegen.srm_procurement_plan_field_value.entity.SrmProcurementPlanFieldValueEntity;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import com.ylz.saas.common.data.datascope.annotation.DataPermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
//@DataPermission(userEnabled = true, dataPermissionRuleList = "[{\"dataTypeCode\":\"PLAN\",\"targetField\":\"plan_id\",\"dataSourceField\":\"ID\"}]")
public interface SrmProcurementPlanFieldValueMapper extends SaasBaseMapper<SrmProcurementPlanFieldValueEntity> {

    void physicsRemoveByPlanDetailIds(@Param("pdids")List<Long> planDetailIds, @Param("tid")Long tenantId);
}