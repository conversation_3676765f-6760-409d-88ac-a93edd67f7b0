package com.ylz.saas.codegen.srm_procurement_plan_detail.param;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ylz.saas.codegen.srm_procurement_plan_detail.entity.SrmProcurementPlanDetailEntity;
import com.ylz.saas.common.data.mybatis.helper.Operator;
import com.ylz.saas.common.data.mybatis.helper.QueryWrapperHelper;
import com.ylz.saas.common.data.mybatis.wrapper.CriteriaField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * mybatis-plus的查询帮助类
 * 
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SrmProcurementPlanDetailParam {

	/**
  	 * 主键ID
   	 */
 	@CriteriaField(field = "id", operator = Operator.LIKE)
    private Long id;
	/**
  	 * 组织ID
   	 */
 	@CriteriaField(field = "deptId", operator = Operator.LIKE)
    private Long deptId;
	/**
  	 * 采购计划ID
   	 */
 	@CriteriaField(field = "planId", operator = Operator.LIKE)
    private Long planId;
	/**
  	 * 服务类型ID
   	 */
 	@CriteriaField(field = "serviceTypeId", operator = Operator.LIKE)
    private Long serviceTypeId;
	/**
  	 * 物料编码
   	 */
 	@CriteriaField(field = "materialCode", operator = Operator.LIKE)
    private String materialCode;
	/**
  	 * 物料名称
   	 */
 	@CriteriaField(field = "materialName", operator = Operator.LIKE)
    private String materialName;
	/**
  	 * 规格型号
   	 */
 	@CriteriaField(field = "specModel", operator = Operator.LIKE)
    private String specModel;
	/**
  	 * 单位
   	 */
 	@CriteriaField(field = "unit", operator = Operator.LIKE)
    private String unit;
	/**
  	 * 需求数量
   	 */
	@CriteriaField(field = "requiredQuantity", operator = Operator.EQ)
	private BigDecimal requiredQuantity;
	/**
  	 * 计划单价
   	 */
	@CriteriaField(field = "unitPrice", operator = Operator.EQ)
	private BigDecimal unitPrice;
	/**
  	 * 计划总价
   	 */
	@CriteriaField(field = "totalPrice", operator = Operator.EQ)
	private BigDecimal totalPrice;
	/**
  	 * 上单价
   	 */
	@CriteriaField(field = "listPrice", operator = Operator.EQ)
	private BigDecimal listPrice;
	/**
  	 * 预算价
   	 */
	@CriteriaField(field = "budgetPrice", operator = Operator.EQ)
	private BigDecimal budgetPrice;
	/**
  	 * 质量指标ID
   	 */
 	@CriteriaField(field = "qualityIndicatorId", operator = Operator.LIKE)
    private Long qualityIndicatorId;
	/**
  	 * 支付方式ID
   	 */
 	@CriteriaField(field = "paymentMethodId", operator = Operator.LIKE)
    private Long paymentMethodId;
	/**
  	 * 备注
   	 */
 	@CriteriaField(field = "note", operator = Operator.LIKE)
    private String note;
	/**
  	 * 创建人名称
   	 */
 	@CriteriaField(field = "createByName", operator = Operator.LIKE)
    private String createByName;
	/**
  	 * 修改人名称
   	 */
 	@CriteriaField(field = "updateByName", operator = Operator.LIKE)
    private String updateByName;
	/**
  	 * 创建人
   	 */
 	@CriteriaField(field = "createBy", operator = Operator.LIKE)
    private String createBy;
	/**
  	 * 创建时间_开始
   	 */
	@CriteriaField(field = "createTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime createTimeStart;
	/**
  	 * 创建时间_结束
   	 */
    @CriteriaField(field = "createTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime createTimeEnd;
	/**
  	 * 修改人
   	 */
 	@CriteriaField(field = "updateBy", operator = Operator.LIKE)
    private String updateBy;
	/**
  	 * 修改时间_开始
   	 */
	@CriteriaField(field = "updateTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime updateTimeStart;
	/**
  	 * 修改时间_结束
   	 */
    @CriteriaField(field = "updateTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime updateTimeEnd;
	/**
  	 * 删除标识（0-正常、1-删除）
   	 */
	@CriteriaField(field = "delFlag", operator = Operator.EQ)
	private int delFlag;
	/**
  	 * 租户ID
   	 */
 	@CriteriaField(field = "tenantId", operator = Operator.LIKE)
    private Long tenantId;

    /**
    * 构建查询
    */
    public QueryWrapper<SrmProcurementPlanDetailEntity> toWrapper() {
        QueryWrapper<SrmProcurementPlanDetailEntity> queryWrapper = QueryWrapperHelper.fromBean(this, SrmProcurementPlanDetailEntity.class)
            .orderByDesc("id");
       return queryWrapper;
    }
}