<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ylz.saas.codegen.srm_procurement_plan.mapper.SrmProcurementPlanQueryMapper">

    <!-- 根据用户ID列表查询用户信息 -->
    <select id="getUsersByIds" resultType="com.ylz.saas.admin.api.entity.SysUser">
        SELECT user_id, name
        FROM sys_user
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        <if test="applicantName != null and applicantName != ''">
            AND name LIKE CONCAT('%', #{applicantName}, '%')
        </if>
    </select>

    <!-- 根据部门ID列表查询部门信息 -->
    <select id="getDeptsByIds" resultType="com.ylz.saas.admin.api.entity.SysDept">
        SELECT dept_id, name
        FROM sys_dept
        WHERE dept_id IN
        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
        <if test="applyDeptName != null and applyDeptName != ''">
            AND name LIKE CONCAT('%', #{applyDeptName}, '%')
        </if>
    </select>

    <!-- 根据申请人姓名查询用户ID列表（用于过滤） -->
    <select id="getUserIdsByName" resultType="java.lang.String">
        SELECT user_id
        FROM sys_user
        WHERE name LIKE CONCAT('%', #{applicantName}, '%')
    </select>

    <!-- 根据申请部门名称查询部门ID列表（用于过滤） -->
    <select id="getDeptIdsByName" resultType="java.lang.Long">
        SELECT dept_id
        FROM sys_dept
        WHERE name LIKE CONCAT('%', #{applyDeptName}, '%')
    </select>

</mapper>
