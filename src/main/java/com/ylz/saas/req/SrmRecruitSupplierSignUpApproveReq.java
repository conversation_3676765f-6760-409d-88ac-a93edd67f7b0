package com.ylz.saas.req;

import com.ylz.saas.enums.ApproveStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 供应商招募公告审核请求类
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@Schema(description = "供应商招募公告审核请求类")
public class SrmRecruitSupplierSignUpApproveReq {

    /**
     * 招募报名id
     */
    @NotNull(message = "招募报名id不能为空")
    @Schema(description = "招募报名id")
    private Long signUpId;

    /**
     * 审核状态
     */
    @NotNull(message = "审核状态不能为空")
    @Schema(description = "审核状态")
    private ApproveStatusEnum approvalStatus;

    /**
     * 审核备注
     */
    @Schema(description = "审核备注")
    private String approveRemark;
}
