package com.ylz.saas.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/6/18 11:07
 * @Description
 */
@Data
public class QuoteAgainSupplierQueryReq {

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    private Long sectionId;

    /**
     * 当前报价轮次
     */
    @NotNull(message = "当前报价轮次不能为空")
    private Integer currentQuoteRound;

    private String supplierName;

}
