package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;
import com.ylz.saas.common.core.util.TenantTable;

/**
 * 租户供应商资质证书表
 * @TableName srm_tenant_supplier_certificate
 */
@TenantTable
@TableName(value ="srm_tenant_supplier_certificate")
@Data
public class SrmTenantSupplierCertificate {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 组织ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 供应商ID
     */
    private Long supplierId;

    /**
     * 证书类型
     */
    private String certificateType;

    /**
     * 证书名称
     */
    private String certificateName;

    /**
     * 证书号
     */
    private String certificateNo;

    /**
     * 有效期开始日期
     */
    private LocalDate validStartDate;

    /**
     * 有效期结束日期
     */
    private LocalDate validEndDate;

    /**
     * 附件URL
     */
    private String attachmentUrl;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}