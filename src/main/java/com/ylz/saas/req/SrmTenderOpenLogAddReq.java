package com.ylz.saas.req;

import com.ylz.saas.enums.OperationRoleEnum;
import com.ylz.saas.enums.OperationTypeEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 开标记录日志表
 * @TableName srm_tender_open_log
 */
@Data
public class SrmTenderOpenLogAddReq {
    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 标段ID
     */
    private Long sectionId;

    /**
     * 操作类型(LOGIN-登录、LOGOUT-退出、JOIN-进入开标大厅、LEAVE-离开开标大厅、START-开始开标、END-结束开标、SIGN-签到)
     */
    @NotNull(message = "操作类型不能为空")
    private OperationTypeEnum operationType;

    /**
     * 操作角色(SUPPLIER-供应商、BID_OPENER-开标人)
     */
    @NotNull(message = "操作角色不能为空")
    private OperationRoleEnum operationRoleEnum;

    /**
     * 操作人
     */
    @NotNull(message = "操作人不能为空")
    private Long operationById;

    /**
     * 操作人名称
     */
    @NotBlank(message = "操作人名称不能为空")
    private String operationByName;
}