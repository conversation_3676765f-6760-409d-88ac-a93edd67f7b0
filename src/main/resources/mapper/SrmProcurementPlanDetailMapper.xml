<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ylz.saas.codegen.srm_procurement_plan_detail.mapper.SrmProcurementPlanDetailMapper">

    <delete id="physicsRemoveByPlanId">
        DELETE
        FROM srm_procurement_plan_detail
        WHERE plan_id = #{pid}
          AND del_flag = 0
    </delete>
</mapper>