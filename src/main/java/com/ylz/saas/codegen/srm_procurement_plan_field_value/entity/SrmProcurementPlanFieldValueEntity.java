package com.ylz.saas.codegen.srm_procurement_plan_field_value.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * 采购计划字段值表
 *
 * <AUTHOR>
 * @date 2025-06-03 21:00:11
 */
@Data
@TableName("srm_procurement_plan_field_value")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "采购计划字段值表")
@TenantTable
public class SrmProcurementPlanFieldValueEntity extends Model<SrmProcurementPlanFieldValueEntity> {


    /**
  	 * 主键ID
   	 */
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

    /**
  	 * 组织ID
   	 */
    @Schema(description="组织ID")
    private Long deptId;

    /**
  	 * 采购计划明细ID
   	 */
    @Schema(description="采购计划明细ID")
    private Long planDetailId;

    /**
  	 * 服务类型ID
   	 */
    @Schema(description="服务类型ID")
    private Long serviceTypeId;

    /**
     * 字段名
     */
    @Schema(description="字段名")
    private String fieldName;

    /**
  	 * 字段编码
   	 */
    @Schema(description="字段编码")
    private String fieldCode;

    /**
  	 * 字段类型（string-字符串、int-整数、decimal-小数、date-日期、datetime-日期时间、enum-枚举）
   	 */
    @Schema(description="字段类型（string-字符串、int-整数、decimal-小数、date-日期、datetime-日期时间、enum-枚举）")
    private BaseServiceTypeFieldService.FieldTypeEnum fieldType;

    /**
  	 * 字段值
   	 */
    @Schema(description="字段值")
    private String fieldValue;

    /**
     * 枚举值（JSON格式，当field_type为enums时使用）
     */
    @Schema(description="枚举值")
    private String enumValues;

    /**
  	 * 创建人名称
   	 */
    @Schema(description="创建人名称")
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    @Schema(description="修改人名称")
    private String updateByName;

    /**
  	 * 创建人
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
  	 * 创建时间
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

    /**
  	 * 修改时间
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

    /**
  	 * 租户ID
   	 */
    @Schema(description="租户ID")
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人ID")
    private Long createById;

    /**
  	 * 更新人ID
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人ID")
    private Long updateById;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标识（0-正常、1-删除）")
    private int delFlag;

}