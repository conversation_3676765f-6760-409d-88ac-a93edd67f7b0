package com.ylz.saas.codegen.base_quality_indicator_location.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.codegen.base_quality_indicator_location.entity.BaseQualityIndicatorLocationEntity;
import com.ylz.saas.codegen.base_quality_indicator_location.excel.BaseQualityIndicatorLocationReq;
import com.ylz.saas.codegen.base_quality_indicator_location.mapper.BaseQualityIndicatorLocationMapper;
import com.ylz.saas.codegen.base_quality_indicator_location.service.BaseQualityIndicatorLocationService;
import com.ylz.saas.codegen.base_quality_indicator_location.vo.BaseQualityIndicatorLocationVo;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.data.excel.ExcelAnalysisUtil;
import com.ylz.saas.common.data.excel.ExcelTemplateGenerator;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Path;
import java.util.List;
/**
 * 质量指标与部门关联表
 *
 * <AUTHOR>
 * @date 2025-06-05 19:18:27
 */
@Service
@Slf4j
public class BaseQualityIndicatorLocationServiceImpl extends ServiceImpl<BaseQualityIndicatorLocationMapper, BaseQualityIndicatorLocationEntity> implements BaseQualityIndicatorLocationService {
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveBaseQualityIndicatorLocation(BaseQualityIndicatorLocationVo baseQualityIndicatorLocationVo) {
		BaseQualityIndicatorLocationEntity baseQualityIndicatorLocation = new BaseQualityIndicatorLocationEntity();
        BeanUtil.copyProperties(baseQualityIndicatorLocationVo, baseQualityIndicatorLocation);
        return save(baseQualityIndicatorLocation);
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateBaseQualityIndicatorLocation(BaseQualityIndicatorLocationVo baseQualityIndicatorLocationVo) {
		BaseQualityIndicatorLocationEntity baseQualityIndicatorLocation = getById(baseQualityIndicatorLocationVo.getId());
		ExceptionUtil.check(baseQualityIndicatorLocation == null, "500", "质量指标与部门关联表不存在");
		BeanUtil.copyProperties(baseQualityIndicatorLocationVo, baseQualityIndicatorLocation);
        return updateById(baseQualityIndicatorLocation);
	}
	
	@Override
    public void template(HttpServletResponse response) {
		// 创建目录及导入模板文件
		isExcelExist();
		// 读取导入模板
		FileInputStream inputStream = null;
		ServletOutputStream outputStream = null;
		try {
			String oriFileName = "质量指标与场地关联表导入模板";
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding("utf-8");
			String fileName = URLEncoder.encode(oriFileName, "UTF-8").replaceAll("\\+", "%20");
			response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
			Path result = ExcelTemplateGenerator.getResult();
			Path filePath = result.resolve("质量指标与场地关联表baseQualityIndicatorLocation.xlsx");
			// 绝对路径
			File file = new File(filePath.toString());
			inputStream = new FileInputStream(file);

			// 获取输出流
			outputStream = response.getOutputStream();

			// 将输入流的内容写入到输出流中
			byte[] buffer = new byte[1024];
			int bytesRead;
			while ((bytesRead = inputStream.read(buffer)) != -1) {
				outputStream.write(buffer, 0, bytesRead);
			}

			// 确保输出流的数据完全写入
			outputStream.flush();
			log.info("模板下载成功");
		} catch (IOException e) {
			System.out.println("e = " + e.getMessage());
			log.info("模板下载失败");
		} finally {
			// 关闭流
			try {
				if (inputStream != null) {
					inputStream.close();
				}
				if (outputStream != null) {
					outputStream.close();
				}
			} catch (IOException e) {
				log.error("关闭流时发生错误: {}", e.getMessage());
			}
		}
	}

	@Override
	@Transactional
	public void uploadData(MultipartFile file, HttpServletResponse response) {
		try {
			List<BaseQualityIndicatorLocationReq> importGoodsList = new ExcelAnalysisUtil<BaseQualityIndicatorLocationReq>()
					.readFile(file, BaseQualityIndicatorLocationReq.class, 1);
			if (CollectionUtils.isEmpty(importGoodsList)) {
				writeImportResponse(response, 1, "导入数据为空");
				return;
			}
			List<BaseQualityIndicatorLocationEntity> baseQualityIndicatorLocationEntities = BeanUtil.copyToList(importGoodsList, BaseQualityIndicatorLocationEntity.class);
			// 做字段的必传校验
			for (BaseQualityIndicatorLocationEntity baseQualityIndicatorLocationEntity : baseQualityIndicatorLocationEntities) {
				// 默认值，可修改
				if (baseQualityIndicatorLocationEntity.getCreateById() == null){
					baseQualityIndicatorLocationEntity.setCreateById(1792846774462181377L);
				}
				if (baseQualityIndicatorLocationEntity.getUpdateById() == null){
					baseQualityIndicatorLocationEntity.setUpdateById(1792846774462181377L);
				}
			}
			// 保存数据
			this.saveOrUpdateBatch(baseQualityIndicatorLocationEntities);
		} catch (IOException e) {
			log.info("文件解析失败");
			throw new RuntimeException(e);
		}
	}

	private void writeImportResponse(HttpServletResponse response, int code, String errorMsg) throws IOException {
		JSONObject obj = new JSONObject();
		response.setContentType("text/html;charset=utf-8");
		obj.put("code", code);
		obj.put("msg", errorMsg);
		PrintWriter writer = response.getWriter();
		writer.write(obj.toJSONString());
		writer.close();
	}

	private static void isExcelExist() {
		try {
			Path result = ExcelTemplateGenerator.getResult();

			File file = new File(result.toString());
			if (file.exists()) {
				log.info("excel文件夹路径存在");
			} else {
				// 创建文件夹
				if (file.mkdirs()) {
					log.info("目录已创建: {}" , file);
				} else {
					log.info("创建目录失败: {}", file);
					throw new RuntimeException("创建目录失败: " + file);
				}
			}
			// 设置 Excel 文件的完整路径
			Path filePath = result.resolve("质量指标与场地关联表baseQualityIndicatorLocation.xlsx");
			// 生成模板文件
			ExcelTemplateGenerator.createExcelTemplate(filePath.toString(),BaseQualityIndicatorLocationReq.class);
		} catch (UnsupportedEncodingException e) {
			log.info("编码失败");
		}
	}
}