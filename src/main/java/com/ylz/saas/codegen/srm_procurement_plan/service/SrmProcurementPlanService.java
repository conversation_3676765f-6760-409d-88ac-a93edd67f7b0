package com.ylz.saas.codegen.srm_procurement_plan.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.srm_procurement_plan.entity.SrmProcurementPlanEntity;
import com.ylz.saas.codegen.srm_procurement_plan.param.SrmProcurementPlanParam;
import com.ylz.saas.codegen.srm_procurement_plan.vo.BizFlowUpdateVo;
import com.ylz.saas.codegen.srm_procurement_plan.vo.SrmProcurementPlanEntireVo;
import com.ylz.saas.codegen.srm_procurement_plan.vo.SrmProcurementPlanVo;
import com.ylz.saas.codegen.srm_procurement_plan.vo.UpdateSrmProcurementPlanStatusVo;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

public interface SrmProcurementPlanService extends IService<SrmProcurementPlanEntity> {
    /**
     * 新增采购计划表
     *
     * @param srmProcurementPlanVo 采购计划表
     */
    boolean saveSrmProcurementPlan(SrmProcurementPlanVo srmProcurementPlanVo);

    /**
     * 修改采购计划表
     *
     * @param srmProcurementPlanVo 采购计划表
     */
    boolean updateSrmProcurementPlan(SrmProcurementPlanVo srmProcurementPlanVo);

    /**
     * 更新采购计划表单据状态
     *
     * @param updateSrmProcurementPlanStatusVo
     * @return
     */
    boolean updateSrmProcurementStatus(UpdateSrmProcurementPlanStatusVo updateSrmProcurementPlanStatusVo);

    /**
     * 查询采购计划表详情
     *
     * @param id
     */
    SrmProcurementPlanEntity getByIdSrmProcurementPlan(Long id);

    /**
     * 通过id删除采购计划表
     */
    boolean removeByIdsSrmProcurementPlan(List<Long> ids);

    /**
     * 新增采购计划、采购明细及动态字段值
     *
     * @param srmProcurementPlanEntireVo
     * @return
     */
    boolean entireSave(SrmProcurementPlanEntireVo srmProcurementPlanEntireVo);

    /**
     * 仅修改采购计划主表数据
     *
     * @param srmProcurementPlanVo 仅修改采购计划主表数据
     * @return R
     */
    boolean onlyUpdatePlanById(SrmProcurementPlanVo srmProcurementPlanVo);

    boolean bizFlowUpdate(BizFlowUpdateVo bizFlowUpdateVo);

    Page<SrmProcurementPlanEntity> planPage(Page page, SrmProcurementPlanParam param);

    SrmProcurementPlanEntity getByPlanCode(String planCode);


    // 单据状态 枚举
    @Getter
    @AllArgsConstructor
    enum DocumentStatus {

        NEW("新建"),
        EFFECT("计划生效"),
        FINISH("计划完成"),
        CANCEL("计划作废"),
        TO_PROJECT("已转项目");

        private String desc;
    }


    // 计划来源方式 枚举
    @Getter
    @AllArgsConstructor
    enum SourceEnum {

        SYNC("同步"),
        BUILD("直接创建");

        private String desc;
    }
}