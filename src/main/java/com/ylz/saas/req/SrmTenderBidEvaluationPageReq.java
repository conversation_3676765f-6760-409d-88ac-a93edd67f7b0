package com.ylz.saas.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 评标委员会分页查询请求类
 */
@Data
@Schema(description = "评标委员会分页查询请求类")
public class SrmTenderBidEvaluationPageReq implements Serializable {

    /**
     * 当前页
     */
    @Schema(description = "当前页")
    private Long current = 1L;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小")
    private Long size = 10L;

    /**
     * 采购立项ID
     */
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 标段ID
     */
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 评标委员会名称（模糊查询）
     */
    @Schema(description = "评标委员会名称（模糊查询）")
    private String name;

    /**
     * 审核状态
     */
    @Schema(description = "审核状态")
    private String approveStatus;

    private static final long serialVersionUID = 1L;
}
