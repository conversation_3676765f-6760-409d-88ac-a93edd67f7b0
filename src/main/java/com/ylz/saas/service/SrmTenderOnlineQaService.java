package com.ylz.saas.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.entity.SrmTenderOnlineQa;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.req.SrmTenderOnlineQaAddReq;
import com.ylz.saas.req.SrmTenderOnlineQaPageReq;
import com.ylz.saas.req.SrmTenderOnlineQaReplyReq;
import com.ylz.saas.resp.SrmTenderOnlineQaPageResp;

/**
* <AUTHOR>
* @description 针对表【srm_tender_online_qa(在线问答表)】的数据库操作Service
* @createDate 2025-06-10 14:22:17
*/
public interface SrmTenderOnlineQaService extends IService<SrmTenderOnlineQa> {

    /**
     * 添加在线问答
     * @param req
     */
    void addQuestion(SrmTenderOnlineQaAddReq req);

    /**
     * 回复在线问答
     * @param req
     */
    void replyQuestion(SrmTenderOnlineQaReplyReq req);

    /**
     * 查询在线问答
     * @param req
     * @return
     */
    Page<SrmTenderOnlineQaPageResp> findQuestion(SrmTenderOnlineQaPageReq req);
}
