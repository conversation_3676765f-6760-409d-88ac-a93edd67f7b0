package com.ylz.saas.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.entity.BaseTemplates;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.req.BaseAnnouncementTemplateAddReq;
import com.ylz.saas.req.BaseAnnouncementTemplateUpdateReq;
import com.ylz.saas.resp.BaseAnnouncementTemplateQueryResp;

/**
* <AUTHOR>
* @description 针对表【base_templates(模板表)】的数据库操作Service
* @createDate 2025-06-11 13:07:56
*/
public interface BaseTemplatesService extends IService<BaseTemplates> {

    Page<BaseAnnouncementTemplateQueryResp> query(Page<BaseAnnouncementTemplateQueryResp> page, String searchKey, String type);

    boolean logicDeleteTemplate(Long id);

    boolean addTemplate(BaseAnnouncementTemplateAddReq template);

    boolean updateTemplateWithHistory(BaseAnnouncementTemplateUpdateReq req);

    boolean changeTemplateStatus(Long id, String status);
}
