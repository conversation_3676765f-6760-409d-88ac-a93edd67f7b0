package com.ylz.saas.codegen.base_expert.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.codegen.base_expert.entity.BaseExpertEntity;
import com.ylz.saas.codegen.base_expert.param.BaseExpertParam;
import com.ylz.saas.codegen.base_expert.service.BaseExpertService;
import com.ylz.saas.codegen.base_expert.vo.BaseExpertVo;
import com.ylz.saas.codegen.base_expert.vo.ExpertUserInnerAccountReq;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 专家信息表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:42:08
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/baseExpert" )
@Tag(description = "baseExpert" , name = "专家信息表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class BaseExpertController {

    private final  BaseExpertService baseExpertService;






    /**
     * 启用/禁用
     * @return R
     */
    @Operation(summary = "启用/禁用" , description = "启用/禁用" )
    @PostMapping("/switch/{id}")
    public R switchStatus(@PathVariable("id") Long id) {
        return R.ok(baseExpertService.switchStatus(id));
    }

    /**
     * 分页查询
     * @param page 分页对象
     * @param param 专家信息表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @PostMapping("/page" )
    public R getBaseExpertPage(@RequestBody BaseExpertParam param, Page page) {
        QueryWrapper<BaseExpertEntity> queryWrapper = param.toWrapper(); 
        return R.ok(baseExpertService.page(page, queryWrapper));
    }

    /**
     * 通过id查询专家信息表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(baseExpertService.getByIdBaseExpert(id));
    }

    /**
     * 新增专家 选择内部专家 查询内部账号（已绑定不显示）
     * @param req req
     * @return R
     */
    @Operation(summary = "新增专家 选择内部专家 查询内部账号" , description = "新增专家 选择内部专家 查询内部账号" )
    @PostMapping("/getInnerAccount" )
    public R getInnerAccount(@RequestBody ExpertUserInnerAccountReq req) {
        return R.ok(baseExpertService.getInnerAccount(req));
    }

    /**
     * 新增专家信息表
     * @param baseExpertVo 专家信息表
     * @return R
     */
    @Operation(summary = "新增专家信息表" , description = "新增专家信息表" )
    @SysLog("新增专家信息表" )
    @PostMapping("/save")
    public R save(@Valid @RequestBody BaseExpertVo baseExpertVo) {
        return R.ok(baseExpertService.saveBaseExpert(baseExpertVo));
    }

    /**
     * 修改专家信息表
     * @param baseExpertVo 专家信息表
     * @return R
     */
    @Operation(summary = "修改专家信息表" , description = "修改专家信息表" )
    @SysLog("修改专家信息表" )
    @PostMapping("/update")
    public R updateById(@Valid @RequestBody BaseExpertVo baseExpertVo) {
        return R.ok(baseExpertService.updateBaseExpert(baseExpertVo));
    }

    /**
     * 通过id删除专家信息表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除专家信息表" , description = "通过id删除专家信息表" )
    @SysLog("通过id删除专家信息表" )
    @GetMapping("remove")
    public R removeById(@RequestParam List<Long> ids) {
		boolean remove = baseExpertService.removeByIdsBaseExpert(ids);
		return R.ok(remove);
    }

}