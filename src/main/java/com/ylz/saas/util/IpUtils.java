package com.ylz.saas.util;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * <AUTHOR>
 * @Date 2025/6/11 14:04
 * @Description
 */
public class IpUtils {

    /**
     * 获取客户端真实IP地址（兼容代理）
     *
     * @return 客户端IP
     */
    public static String getClientIP() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return "unknown";
        }
        return getClientIP(attributes.getRequest());
    }

    /**
     * 从指定请求对象中获取客户端真实IP地址
     *
     * @param request HttpServletRequest
     * @return 客户端IP
     */
    public static String getClientIP(HttpServletRequest request) {
        String ip;

        // 优先从 X-Forwarded-For 获取
        ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        } else {
            // 多级代理情况下取第一个IP
            int index = ip.indexOf(',');
            if (index > 0) {
                ip = ip.substring(0, index).trim();
            }
        }

        return ip;
    }


}
