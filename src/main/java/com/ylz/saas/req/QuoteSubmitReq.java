package com.ylz.saas.req;

import com.ylz.saas.vo.DynamicFieldVo;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class QuoteSubmitReq {
    /**
     * 供应商ID
     */
    private Long tenantSupplierId;
    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    private Long sectionId;

    /**
     * 报价项
     */
    @NotNull(message = "报价项不能为空")
    private List<QuoteItemReq> quoteItems;

    /**
     * 报价附件
     */
    @NotBlank(message = "报价附件不能为空")
    private String quoteAttachmentPath;

    /**
     * 报价附件名称
     */
    @NotBlank(message = "报价附件名称不能为空")
    private String quoteAttachmentName;

    /**
     * 其他附件
     */
    private String otherAttachmentPath;

    /**
     * 其他附件名称
     */
    private String otherAttachmentName;





    @Data
    @Valid
    public static class QuoteItemReq {
        /**
         * 采购项目条目ID
         */
        @NotNull(message = "采购项目条目ID不能为空")
        private Long projectItemId;

        /**
         * 采购项目支付ID
         */
        @NotNull(message = "采购项目支付ID不能为空")
        private Long projectPaymentId;

        /**
         * 动态字段
         */
        private List<DynamicFieldVo> dynamicFields;
    }
} 