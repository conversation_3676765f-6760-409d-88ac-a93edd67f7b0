package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ylz.saas.common.core.util.TenantTable;
import com.ylz.saas.enums.ReviewEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 评标标准表
 * @TableName srm_tender_evaluation_standard
 */
@TableName(value = "srm_tender_evaluation_standard")
@Data
@TenantTable
@Schema(description = "评标标准")
public class SrmTenderEvaluationStandard implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "部门ID")
    private Long deptId;

    /**
     * 采购立项ID
     */
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 标段ID
     */
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 竞谈文件id
     */
    @Schema(description = "竞谈文件id")
    private Long negotiationId;

    /**
     * 评审类型（评审项、评分项）
     */
    @Schema(description = "评审类型（评审项、评分项）")
    private ReviewEnum type;

    /**
     * 节点名称（评审类型下的子分类）
     */
    @Schema(description = "节点名称（评审类型下的子分类）")
    private String nodeName;


    /**
     * 评审项名称
     */
    @Schema(description = "评审项名称")
    private String itemName;

    /**
     * 评审项描述
     */
    @Schema(description = "评审项描述")
    private String itemDescription;

    /**
     * 总分(评分)
     */
    private BigDecimal totalScore;

    /**
     * 满分分值（仅评分项有效）
     */
    @Schema(description = "满分分值（仅评分项有效）")
    private Integer maxScore;

    /**
     * 分值最小值
     */
    private Integer minScore;

    /**
     * 权重（仅评分项有效）
     */
    @Schema(description = "权重（仅评分项有效）")
    private BigDecimal weight;

    /**
     * 否决投标数量阈值
     */
    private Integer vetoThreshold;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortOrder;

    /**
     * 是否必填
     */
    @Schema(description = "是否必填")
    private Integer isRequired;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @Schema(description = "删除标识（0-正常、1-删除）")
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人名称")
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人名称")
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}