package com.ylz.saas.enums;


import lombok.Getter;

/**
 * 异常处理方式枚举
 */
@Getter
public enum ExceptionHandleTypeEnum {
    // 重新采购，项目终止
    RE_PURCHASE("RE_PURCHASE","重新采购"),
    TERMINATE_PROJECT("TERMINATE_PROJECT","项目终止"),
    ;
    private final String code;
    private final String desc;
    ExceptionHandleTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
