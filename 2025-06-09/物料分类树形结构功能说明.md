# 物料分类树形结构功能说明

## 功能概述

本次开发为物料分类模块添加了完整的树形结构功能，支持多层级的物料分类管理。该功能基于现有的物料分类表结构，通过`parentCategoryCode`字段建立父子关系。

## 新增文件

### 1. 树形VO类
- **文件路径**: `src/main/java/com/ylz/saas/codegen/base_material_category/vo/BaseMaterialCategoryTreeVo.java`
- **功能**: 专门用于树形结构展示的VO类，包含children字段用于存储子节点

### 2. 树形工具类
- **文件路径**: `src/main/java/com/ylz/saas/codegen/base_material_category/util/TreeUtil.java`
- **功能**: 提供树形数据转换的通用方法，可复用于其他模块

### 3. 测试控制器
- **文件路径**: `src/main/java/com/ylz/saas/test/controller/MaterialCategoryTreeTestController.java`
- **功能**: 提供测试接口，方便验证树形结构功能

## 新增接口

### 1. 获取完整树形结构
```
GET /baseMaterialCategory/tree
```
**功能**: 获取所有物料分类的树形结构
**返回**: 树形结构的物料分类列表

### 2. 根据状态获取树形结构
```
GET /baseMaterialCategory/tree/status/{status}
```
**参数**: 
- `status`: 状态（ENABLED-启用、DISABLED-禁用）

**功能**: 根据状态筛选物料分类并返回树形结构

### 3. 根据父级编码获取子树
```
GET /baseMaterialCategory/tree/parent/{parentCategoryCode}
```
**参数**: 
- `parentCategoryCode`: 父级分类编码

**功能**: 获取指定父级下的子树结构

### 4. 获取所有子节点编码
```
GET /baseMaterialCategory/children/{parentCategoryCode}
```
**参数**: 
- `parentCategoryCode`: 父级分类编码

**功能**: 递归获取指定父级下所有子节点的编码（包括子节点的子节点）

### 5. 获取所有父节点编码
```
GET /baseMaterialCategory/parents/{categoryCode}
```
**参数**: 
- `categoryCode`: 分类编码

**功能**: 递归获取指定分类的所有父节点编码（包括父节点的父节点）

## 测试接口

为了方便测试，提供了以下测试接口：

### 1. 测试树形结构
```
GET /test/materialCategoryTree/tree
```

### 2. 测试状态筛选
```
GET /test/materialCategoryTree/tree/status/{status}
```

### 3. 测试子节点查询
```
GET /test/materialCategoryTree/children/{parentCategoryCode}
```

### 4. 测试父节点查询
```
GET /test/materialCategoryTree/parents/{categoryCode}
```

## 数据结构说明

### BaseMaterialCategoryTreeVo 主要字段

```java
public class BaseMaterialCategoryTreeVo {
    private Long id;                           // 主键ID
    private String categoryCode;               // 物料分类编码
    private String categoryName;               // 物料分类名称
    private String parentCategoryCode;         // 上级物料分类编码
    private String parentCategoryName;         // 上级物料分类名称
    private String status;                     // 状态
    private List<BaseMaterialCategoryTreeVo> children;  // 子节点列表
    private Boolean hasChildren;               // 是否有子节点
    private Integer level;                     // 层级深度
    // ... 其他字段
}
```

## 使用示例

### 1. 前端获取树形数据
```javascript
// 获取完整树形结构
fetch('/baseMaterialCategory/tree')
  .then(response => response.json())
  .then(data => {
    console.log('树形数据:', data.data);
  });

// 获取启用状态的树形结构
fetch('/baseMaterialCategory/tree/status/ENABLED')
  .then(response => response.json())
  .then(data => {
    console.log('启用的树形数据:', data.data);
  });
```

### 2. 后端服务调用
```java
@Autowired
private BaseMaterialCategoryService baseMaterialCategoryService;

// 获取树形结构
List<BaseMaterialCategoryTreeVo> treeList = baseMaterialCategoryService.getTreeList();

// 获取子节点编码
List<String> childrenCodes = baseMaterialCategoryService.getAllChildrenCodes("PARENT_CODE");

// 获取父节点编码
List<String> parentCodes = baseMaterialCategoryService.getAllParentCodes("CHILD_CODE");
```

## 技术特点

1. **租户隔离**: 自动支持多租户数据隔离
2. **递归查询**: 支持无限层级的树形结构
3. **性能优化**: 一次查询构建完整树形结构，避免N+1查询问题
4. **灵活筛选**: 支持按状态、父级等条件筛选
5. **工具复用**: TreeUtil工具类可复用于其他树形结构需求

## 注意事项

1. **数据一致性**: 确保parentCategoryCode字段的数据完整性
2. **循环引用**: 避免创建循环引用的父子关系
3. **性能考虑**: 大量数据时建议分页或按条件筛选
4. **权限控制**: 根据业务需要添加相应的权限控制

## 扩展建议

1. **缓存优化**: 可考虑添加Redis缓存提升查询性能
2. **异步加载**: 前端可实现懒加载，按需加载子节点
3. **拖拽排序**: 可扩展支持树形节点的拖拽排序功能
4. **批量操作**: 可扩展支持批量移动、删除等操作
