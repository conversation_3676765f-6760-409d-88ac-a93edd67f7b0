package com.ylz.saas.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.entity.SrmTenderSupplierInvite;
import com.ylz.saas.req.SrmTenderSupplierInviteReq;

/**
* <AUTHOR>
* @description 针对表【srm_tender_supplier_invite(供应商邀请表)】的数据库操作Service
* @createDate 2025-06-11 13:22:39
*/
public interface SrmTenderSupplierInviteService extends IService<SrmTenderSupplierInvite> {

    /**
     * 供应商邀请
     * @param supplierInviteReq
     */
    void supplierInviteList(SrmTenderSupplierInviteReq supplierInviteReq);
}
