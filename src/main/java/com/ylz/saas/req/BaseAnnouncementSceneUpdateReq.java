package com.ylz.saas.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class BaseAnnouncementSceneUpdateReq {
    /**
     * 主键ID
     */
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 模板场景名称
     */
    @NotBlank(message = "模板场景名称不能为空")
    private String sceneName;

    /**
     * 模板场景描述
     */
    private String description;
}
