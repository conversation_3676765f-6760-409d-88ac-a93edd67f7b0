package com.ylz.saas.resp;

import com.ylz.saas.enums.WinnerCandidateOrderEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 评标汇总供应商信息响应类
 * <AUTHOR>
 * @createDate 2025-07-11
 */
@Data
@Schema(description = "评标汇总供应商信息响应类")
public class SrmTenderEvaluationSummarySupplierResp implements Serializable {

    /**
     * 租户供应商ID
     */
    @Schema(description = "租户供应商ID")
    private Long tenantSupplierId;

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    private String supplierName;

    /**
     * 评审项汇总（符合/不符合）
     */
    @Schema(description = "评审项汇总（符合/不符合）")
    private String reviewSummary;

    /**
     * 各节点评分项分数（节点名称 -> 分数）
     */
    @Schema(description = "各节点评分项分数")
    private Map<String, Integer> nodeScores;

    /**
     * 总分
     */
    @Schema(description = "总分")
    private Integer totalScore;

    /**
     * 投标价格
     */
    @Schema(description = "投标价格")
    private BigDecimal bidPrice;

    /**
     * 排名
     */
    @Schema(description = "排名")
    private Integer ranking;

    /**
     * 是否推荐中标(0-否、1-是)
     */
    @Schema(description = "是否推荐中标")
    private Integer isRecommendedWinner;

    /**
     * 中标候选顺序
     */
    @Schema(description = "中标候选顺序")
    private WinnerCandidateOrderEnum winnerCandidateOrder;

    /**
     * 中标候选顺序描述
     */
    @Schema(description = "中标候选顺序描述")
    private String winnerCandidateOrderDesc;

    private static final long serialVersionUID = 1L;
}
