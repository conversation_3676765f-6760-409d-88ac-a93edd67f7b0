<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenderOpenLogMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenderOpenLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="projectId" column="project_id" jdbcType="BIGINT"/>
            <result property="noticeId" column="notice_id" jdbcType="BIGINT"/>
            <result property="sectionId" column="section_id" jdbcType="BIGINT"/>
            <result property="tenantSupplierId" column="tenant_supplier_id" jdbcType="BIGINT"/>
            <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
            <result property="operationType" column="operation_type" jdbcType="VARCHAR"/>
            <result property="operationContent" column="operation_content" jdbcType="VARCHAR"/>
            <result property="operationResult" column="operation_result" jdbcType="VARCHAR"/>
            <result property="operationTime" column="operation_time" jdbcType="TIMESTAMP"/>
            <result property="operationBy" column="operation_by" jdbcType="VARCHAR"/>
            <result property="operationByName" column="operation_by_name" jdbcType="VARCHAR"/>
            <result property="operationIp" column="operation_ip" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,
        project_id,notice_id,section_id,
        tenant_supplier_id,supplier_name,operation_type,
        operation_content,operation_result,operation_time,
        operation_by,operation_by_name,operation_ip,
        remark,del_flag,create_by,
        create_by_name,create_time,update_by,
        update_by_name,update_time
    </sql>
</mapper>
