package com.ylz.saas.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 报价明细（供应商）响应类
 * <AUTHOR>
 * @createDate 2025-07-17
 */
@Data
@Schema(description = "报价明细（供应商）响应类")
public class SrmTenderQuoteDetailBySupplierResp implements Serializable {

    /**
     * 按轮次分组的报价明细列表
     */
    @Schema(description = "按轮次分组的报价明细列表")
    private List<SrmTenderQuoteDetailBySupplierRoundResp> roundList;

    private static final long serialVersionUID = 1L;
}
