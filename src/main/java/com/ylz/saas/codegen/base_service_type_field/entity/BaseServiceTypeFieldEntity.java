package com.ylz.saas.codegen.base_service_type_field.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * 服务类型字段表
 *
 * <AUTHOR>
 * @date 2025-06-03 21:25:49
 */
@Data
@TableName("base_service_type_field")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "服务类型字段表")
@TenantTable
public class BaseServiceTypeFieldEntity extends Model<BaseServiceTypeFieldEntity> {


    /**
  	 * 主键ID
   	 */
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

    /**
  	 * 组织ID
   	 */
    @Schema(description="组织ID")
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
  	 * 服务类型ID
   	 */
    @Schema(description="服务类型ID")
    private Long serviceTypeId;

    /**
  	 * 字段编码
   	 */
    @Schema(description="字段编码")
    private String fieldCode;

    /**
  	 * 字段名称
   	 */
    @Schema(description="字段名称")
    private String fieldName;

    /**
     * 字段说明
     */
    @Schema(description="字段说明")
    private String fieldDesc;

    /**
     * 采招（寻源）方式
     */
    @Schema(description="寻源方式")
    private BaseServiceTypeFieldService.BuyWayEnum buyWay;

    /**
     * 重命名
     */
    @Schema(description="重命名")
    private String fieldRename;

    /**
  	 * 字段类型（string-字符串、int-整数、decimal-小数、date-日期、datetime-日期时间、enum-枚举）
   	 */
    @Schema(description="字段类型（string-字符串、int-整数、decimal-小数、date-日期、datetime-日期时间、enum-枚举）")
    private BaseServiceTypeFieldService.FieldTypeEnum fieldType;

    /**
  	 * 字段长度
   	 */
    @Schema(description="字段长度")
    private Integer fieldLength;

    /**
  	 * 采购计划子表中是否必填（0-否、1-是）
   	 */
    @Schema(description="采购计划子表中是否必填（0-否、1-是）")
    private String planIsRequired;

    /**
     * 采购立项子表中是否必填（0-否、1-是）
     */
    @Schema(description="采购立项子表中是否必填（0-否、1-是）")
    private String projectIsRequired;

    /**
     * 采购报价子表中是否必填（0-否、1-是）
     */
    @Schema(description="采购报价子表中是否必填（0-否、1-是）")
    private String quoteIsRequired;

    /**
  	 * 默认值
   	 */
    @Schema(description="默认值")
    private String defaultValue;

    /**
  	 * 验证规则（JSON格式）
   	 */
    @Schema(description="验证规则（JSON格式）")
    private String validationRule;

    /**
  	 * 枚举值（JSON格式，当field_type为enum时使用）
   	 */
    @Schema(description="枚举值（JSON格式，当field_type为enum时使用）")
    private String enumValues;

    /**
  	 * 占位提示文本
   	 */
    @Schema(description="占位提示文本")
    private String placeholder;

    /**
  	 * 排序
   	 */
    @Schema(description="排序")
    private Integer sort;

    /**
  	 * 是否应用到采购计划子表显示（YES-是、NO-否）
   	 */
    @Schema(description="是否应用到采购计划子表显示（YES-是、NO-否）")
    private String applyToPlan;

    /**
  	 * 是否应用到采购立项子表显示（YES-是、NO-否）
   	 */
    @Schema(description="是否应用到采购立项子表显示（YES-是、NO-否）")
    private String applyToProject;

    /**
  	 * 是否应用到采购报价子表显示（YES-是、NO-否）
   	 */
    @Schema(description="是否应用到采购报价子表显示（YES-是、NO-否）")
    private String applyToQuote;

    /**
     * 是否应用到采购计划子表编辑（YES-是、NO-否）
     */
    @Schema(description="是否应用到采购计划子表编辑（YES-是、NO-否）")
    private String applyToPlanEdit;

    /**
     * 是否应用到采购立项子表编辑（YES-是、NO-否）
     */
    @Schema(description="是否应用到采购立项子表编辑（YES-是、NO-否）")
    private String applyToProjectEdit;

    /**
     * 是否应用到采购报价子表编辑（YES-是、NO-否）
     */
    @Schema(description="是否应用到采购报价子表编辑（YES-是、NO-否）")
    private String applyToQuoteEdit;

    /**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
    @Schema(description="状态（DISABLED-禁用、ENABLED-启用）")
    private String status;

    /**
  	 * 创建人名称
   	 */
    @Schema(description="创建人名称")
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    @Schema(description="修改人名称")
    private String updateByName;

    /**
  	 * 创建人
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
  	 * 创建时间
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

    /**
  	 * 修改时间
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
//    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标识（0-正常、1-删除）")
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    @Schema(description="租户ID")
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人ID")
    private Long createById;

    /**
  	 * 更新人ID
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人ID")
    private Long updateById;

    /**
     * 支付方式是否多选
     */
    @Schema(description="支付方式是否多选（1-是、0-否）")
    private String payIsManyType;

    /**
     * 备用值 backup_value
     * 1. 可用于存储备用数据的用途, 没有固定逻辑
     * 1.1 当 fieldType=LINK_FORM 时, 固定存储 SELECT SQL 语句
     */
    @Schema(description="备用值")
    private String backupValue;
}