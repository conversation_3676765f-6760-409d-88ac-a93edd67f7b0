package com.ylz.saas.test.param;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ylz.saas.common.data.mybatis.wrapper.*;
import com.ylz.saas.common.data.mybatis.helper.*;
import com.ylz.saas.test.entity.SingleTableEntity;

/**
 * mybatis-plus的查询帮助类
 * 
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SingleTableParam {

	/**
  	 * 主键
   	 */
 	@CriteriaField(field = "id", operator = Operator.LIKE)
    private Long id;
	/**
  	 * 名称
   	 */
 	@CriteriaField(field = "name", operator = Operator.LIKE)
    private String name;
	/**
  	 * 编码
   	 */
 	@CriteriaField(field = "code", operator = Operator.LIKE)
    private String code;
	/**
  	 * 创建人
   	 */
 	@CriteriaField(field = "createBy", operator = Operator.LIKE)
    private String createBy;
	/**
  	 * 创建时间_开始
   	 */
	@CriteriaField(field = "createTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime createTimeStart;
	/**
  	 * 创建时间_结束
   	 */
    @CriteriaField(field = "createTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime createTimeEnd;
	/**
  	 * 修改人
   	 */
 	@CriteriaField(field = "updateBy", operator = Operator.LIKE)
    private String updateBy;
	/**
  	 * 修改时间_开始
   	 */
	@CriteriaField(field = "updateTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime updateTimeStart;
	/**
  	 * 修改时间_结束
   	 */
    @CriteriaField(field = "updateTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime updateTimeEnd;
	/**
  	 * 删除标记
   	 */
 	@CriteriaField(field = "delFlag", operator = Operator.LIKE)
    private String delFlag;
	/**
  	 * 租户ID
   	 */
 	@CriteriaField(field = "tenantId", operator = Operator.LIKE)
    private Long tenantId;

    /**
    * 构建查询
    */
    public QueryWrapper<SingleTableEntity> toWrapper() {
        QueryWrapper<SingleTableEntity> queryWrapper = QueryWrapperHelper.fromBean(this, SingleTableEntity.class)
            .orderByDesc("id");
       return queryWrapper;
    }
}