package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmTenderBidderQuoteFieldValue;
import com.ylz.saas.service.SrmTenderBidderQuoteFieldValueService;
import com.ylz.saas.mapper.SrmTenderBidderQuoteFieldValueMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【srm_tender_bidder_quote_field_value(投标报价字段值表)】的数据库操作Service实现
* @createDate 2025-06-13 14:57:59
*/
@Service
public class SrmTenderBidderQuoteFieldValueServiceImpl extends ServiceImpl<SrmTenderBidderQuoteFieldValueMapper, SrmTenderBidderQuoteFieldValue>
    implements SrmTenderBidderQuoteFieldValueService{

}




