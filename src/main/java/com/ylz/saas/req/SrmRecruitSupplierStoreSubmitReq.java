package com.ylz.saas.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 招募公告入库结果提交请求类
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@Schema(description = "招募公告入库结果提交请求类")
public class SrmRecruitSupplierStoreSubmitReq implements Serializable {

    /**
     * 招募公告ID
     */
    @NotNull(message = "招募公告ID不能为空")
    @Schema(description = "招募公告ID", required = true)
    private Long recruitId;

    /**
     * 入库结果列表
     */
    @NotNull(message = "入库结果列表不能为空")
    @Schema(description = "入库结果列表", required = true)
    private List<StoreResultItem> storeResults;

    /**
     * 入库结果项
     */
    @Data
    @Schema(description = "入库结果项")
    public static class StoreResultItem {

        /**
         * 招募报名ID
         */
        @NotNull(message = "招募报名ID不能为空")
        @Schema(description = "招募报名ID", required = true)
        private Long signUpId;

        /**
         * 是否入库 (1-入库, 0-不入库)
         */
        @NotNull(message = "是否入库不能为空")
        @Schema(description = "是否入库 (1-入库, 0-不入库)", required = true)
        private Integer isStored;
    }

    private static final long serialVersionUID = 1L;
}
