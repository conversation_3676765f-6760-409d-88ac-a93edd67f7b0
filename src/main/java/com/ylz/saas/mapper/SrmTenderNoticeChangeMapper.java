package com.ylz.saas.mapper;

import com.ylz.saas.common.data.datascope.annotation.DataPermission;
import com.ylz.saas.entity.SrmTenderNoticeChange;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【srm_tender_notice_change(招标公告变更记录表)】的数据库操作Mapper
* @createDate 2025-06-10 14:21:21
* @Entity com.ylz.saas.entity.SrmTenderNoticeChange
*/
@Mapper
//@DataPermission(userEnabled = true, dataPermissionRuleList = "[{\"dataTypeCode\":\"PROJECT\",\"targetField\":\"project_id\",\"dataSourceField\":\"ID\"}]")
public interface SrmTenderNoticeChangeMapper extends SaasBaseMapper<SrmTenderNoticeChange> {

}




