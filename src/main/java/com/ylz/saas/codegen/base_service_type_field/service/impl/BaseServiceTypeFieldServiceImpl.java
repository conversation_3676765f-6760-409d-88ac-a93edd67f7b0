package com.ylz.saas.codegen.base_service_type_field.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.codegen.base_service_type.mapper.BaseServiceTypeMapper;
import com.ylz.saas.codegen.base_service_type_field.entity.BaseServiceTypeFieldEntity;
import com.ylz.saas.codegen.base_service_type_field.excel.BaseServiceTypeFieldReq;
import com.ylz.saas.codegen.base_service_type_field.mapper.BaseServiceTypeFieldMapper;
import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.codegen.base_service_type_field.vo.BaseServiceTypeFieldVo;
import com.ylz.saas.codegen.srm_procurement_plan_field_value.entity.SrmProcurementPlanFieldValueEntity;
import com.ylz.saas.codegen.srm_procurement_plan_field_value.service.SrmProcurementPlanFieldValueService;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.core.exception.GlobalResultCode;
import com.ylz.saas.common.data.excel.ExcelAnalysisUtil;
import com.ylz.saas.common.data.excel.ExcelTemplateGenerator;
import com.ylz.saas.entity.SrmProcurementProjectFieldValue;
import com.ylz.saas.entity.SrmTenderBidderQuoteFieldValue;
import com.ylz.saas.service.SrmProcurementProjectFieldValueService;
import com.ylz.saas.service.SrmTenderBidderQuoteFieldValueService;
import com.ylz.saas.util.SqlValidator;
import com.ylz.saas.vo.LabelAndValueVo;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务类型字段表
 *
 * <AUTHOR>
 * @date 2025-06-03 21:25:49
 */
@Service
@Slf4j
public class BaseServiceTypeFieldServiceImpl extends ServiceImpl<BaseServiceTypeFieldMapper, BaseServiceTypeFieldEntity> implements BaseServiceTypeFieldService {

    @Autowired
    private SrmProcurementPlanFieldValueService srmProcurementPlanFieldValueService;
    @Autowired
    private SrmProcurementProjectFieldValueService srmProcurementProjectFieldValueService;
    @Autowired
    private SrmTenderBidderQuoteFieldValueService srmTenderBidderQuoteFieldValueService;

    @Autowired
    private BaseServiceTypeMapper baseServiceTypeMapper;
    @Autowired
    private BaseServiceTypeFieldMapper baseServiceTypeFieldMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBaseServiceTypeField(BaseServiceTypeFieldVo baseServiceTypeFieldVo) {
        BaseServiceTypeFieldEntity baseServiceTypeField = new BaseServiceTypeFieldEntity();
        BeanUtil.copyProperties(baseServiceTypeFieldVo, baseServiceTypeField);
        return save(baseServiceTypeField);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBaseServiceTypeField(BaseServiceTypeFieldVo baseServiceTypeFieldVo) {
        BaseServiceTypeFieldEntity baseServiceTypeField = getById(baseServiceTypeFieldVo.getId());
        ExceptionUtil.check(baseServiceTypeField == null, "500", "服务类型字段表不存在");
        BeanUtil.copyProperties(baseServiceTypeFieldVo, baseServiceTypeField);
        return updateById(baseServiceTypeField);
    }

    @Override
    public void template(HttpServletResponse response) {
        // 创建目录及导入模板文件
        isExcelExist();
        // 读取导入模板
        FileInputStream inputStream = null;
        ServletOutputStream outputStream = null;
        try {
            String oriFileName = "服务类型字段表导入模板";
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(oriFileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            Path result = ExcelTemplateGenerator.getResult();
            Path filePath = result.resolve("服务类型字段表baseServiceTypeField.xlsx");
            // 绝对路径
            File file = new File(filePath.toString());
            inputStream = new FileInputStream(file);

            // 获取输出流
            outputStream = response.getOutputStream();

            // 将输入流的内容写入到输出流中
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            // 确保输出流的数据完全写入
            outputStream.flush();
            log.info("模板下载成功");
        } catch (IOException e) {
            System.out.println("e = " + e.getMessage());
            log.info("模板下载失败");
        } finally {
            // 关闭流
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (IOException e) {
                log.error("关闭流时发生错误: {}", e.getMessage());
            }
        }
    }

    @Override
    @Transactional
    public void uploadData(MultipartFile file, HttpServletResponse response) {
        try {
            List<BaseServiceTypeFieldReq> importGoodsList = new ExcelAnalysisUtil<BaseServiceTypeFieldReq>()
                    .readFile(file, BaseServiceTypeFieldReq.class, 1);
            if (CollectionUtils.isEmpty(importGoodsList)) {
                writeImportResponse(response, 1, "导入数据为空");
                return;
            }
            List<BaseServiceTypeFieldEntity> baseServiceTypeFieldEntities = BeanUtil.copyToList(importGoodsList, BaseServiceTypeFieldEntity.class);
            // 做字段的必传校验
            for (BaseServiceTypeFieldEntity baseServiceTypeFieldEntity : baseServiceTypeFieldEntities) {
                // 默认值，可修改
                if (baseServiceTypeFieldEntity.getCreateById() == null) {
                    baseServiceTypeFieldEntity.setCreateById(1792846774462181377L);
                }
                if (baseServiceTypeFieldEntity.getUpdateById() == null) {
                    baseServiceTypeFieldEntity.setUpdateById(1792846774462181377L);
                }
            }
            // 保存数据
            this.saveOrUpdateBatch(baseServiceTypeFieldEntities);
        } catch (IOException e) {
            log.info("文件解析失败");
            throw new RuntimeException(e);
        }
    }


    @Override
    public List<Long> verifyRemoveFieldById(List<Long> ids) {
        final List<BaseServiceTypeFieldEntity> baseServiceTypeFieldEntityList = list(Wrappers.lambdaQuery(BaseServiceTypeFieldEntity.class)
                                                                                             .in(BaseServiceTypeFieldEntity::getId, ids));
        if (CollectionUtils.isNotEmpty(baseServiceTypeFieldEntityList)) {
            final List<String> baseFieldCodeList = baseServiceTypeFieldEntityList
                    .stream()
                    .map(BaseServiceTypeFieldEntity::getFieldCode).collect(Collectors.toList());
            // 校验是否被采购计划引用
            final List<SrmProcurementPlanFieldValueEntity> planFieldValueEntityList = srmProcurementPlanFieldValueService
                    .list(Wrappers
                                  .lambdaQuery(SrmProcurementPlanFieldValueEntity.class)
                                  .in(SrmProcurementPlanFieldValueEntity::getFieldCode, baseFieldCodeList));
            List<SrmProcurementProjectFieldValue> projectFieldValueList = srmProcurementProjectFieldValueService.lambdaQuery()
                    .in(SrmProcurementProjectFieldValue::getFieldCode, baseFieldCodeList).list();
            List<SrmTenderBidderQuoteFieldValue> quoteFieldValueList = srmTenderBidderQuoteFieldValueService.lambdaQuery()
                    .in(SrmTenderBidderQuoteFieldValue::getFieldCode, baseFieldCodeList).list();
            Set<String> planRefCodes = Optional.ofNullable(planFieldValueEntityList).stream()
                    .flatMap(Collection::stream).map(SrmProcurementPlanFieldValueEntity::getFieldCode).collect(Collectors.toSet());
            Set<String> projectRefCodes = Optional.ofNullable(projectFieldValueList).stream()
                    .flatMap(Collection::stream).map(SrmProcurementProjectFieldValue::getFieldCode).collect(Collectors.toSet());
            Set<String> quoteRefCodes = Optional.ofNullable(quoteFieldValueList).stream()
                    .flatMap(Collection::stream).map(SrmTenderBidderQuoteFieldValue::getFieldCode).collect(Collectors.toSet());

            Set<String> codeSet = new HashSet<>();
            codeSet.addAll(planRefCodes);
            codeSet.addAll(projectRefCodes);
            codeSet.addAll(quoteRefCodes);
            // 筛选出已引用的id
            return baseServiceTypeFieldEntityList
                    .stream()
                    .filter(e -> codeSet.contains(e.getFieldCode())).map(BaseServiceTypeFieldEntity::getId)
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private void writeImportResponse(HttpServletResponse response, int code, String errorMsg) throws IOException {
        JSONObject obj = new JSONObject();
        response.setContentType("text/html;charset=utf-8");
        obj.put("code", code);
        obj.put("msg", errorMsg);
        PrintWriter writer = response.getWriter();
        writer.write(obj.toJSONString());
        writer.close();
    }

    private static void isExcelExist() {
        try {
            Path result = ExcelTemplateGenerator.getResult();

            File file = new File(result.toString());
            if (file.exists()) {
                log.info("excel文件夹路径存在");
            } else {
                // 创建文件夹
                if (file.mkdirs()) {
                    log.info("目录已创建: {}", file);
                } else {
                    log.info("创建目录失败: {}", file);
                    throw new RuntimeException("创建目录失败: " + file);
                }
            }
            // 设置 Excel 文件的完整路径
            Path filePath = result.resolve("服务类型字段表baseServiceTypeField.xlsx");
            // 生成模板文件
            ExcelTemplateGenerator.createExcelTemplate(filePath.toString(), BaseServiceTypeFieldReq.class);
        } catch (UnsupportedEncodingException e) {
            log.info("编码失败");
        }
    }

    /**
     * 执行动态查询 SQL
     * 1. 直接原样执行 SQL
     * @param sql 完整的 SQL 查询语句
     * @return 查询结果列表
     */
    @Override
    public List<LabelAndValueVo> executeDynamicSelectQuery(String sql) {

        try {
            ExceptionUtil.check(!SqlValidator.isSelectOnly(sql), GlobalResultCode.INVALID_PARAMS, "只能填写 select 语句!");
            return baseServiceTypeFieldMapper.executeDynamicSelectQuery(sql);
        } catch (Exception e) {
            log.error("SQL 语句查询数据失败!" + e);
            ExceptionUtil.check(true, "500", "SQL 语句查询数据失败!" + e.getMessage());
        }
        return Collections.emptyList();
    }

    /**
     * 根据动态字段查询 SQL 执行结果的数据列表
     * @param field
     * @return
     */
    public List<LabelAndValueVo> convertServiceTypeFieldToSqlDataList(BaseServiceTypeFieldEntity field) {

        if (field == null) {
            return new ArrayList<>();
        }
        if (!BaseServiceTypeFieldService.FieldTypeEnum.LINK_FORM.equals(field.getFieldType())) {
            return new ArrayList<>();
        }
        String backupValue = field.getBackupValue();
        if (StrUtil.isBlank(backupValue)) {
            return new ArrayList<>();
        }

        try {
            return executeDynamicSelectQuery(backupValue);
        } catch (Exception e) {
            log.error("SQL 语句查询数据失败!" + e);
            return new ArrayList<>();
        }
    }
}