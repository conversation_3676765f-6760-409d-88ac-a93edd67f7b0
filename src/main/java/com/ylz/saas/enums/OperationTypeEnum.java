package com.ylz.saas.enums;

import lombok.Getter;

/**
 * 操作类型枚举
 */
@Getter
public enum OperationTypeEnum {
    // LOGIN-登录、LOGOUT-退出、JOIN-进入开标大厅、LEAVE-离开开标大厅、START-开始开标、END-结束开标、SIGN-签到
    LOGIN("LOGIN", "登录"),
    LOGOUT("LOGOUT", "退出"),
    JOIN("JOIN", "进入开标大厅"),
    LEAVE("LEAVE", "离开开标大厅"),
    START("START", "开始开标"),
    END("END", "结束开标"),
    SIGN("SIGN", "签到"),
    ;

    private final String code;
    private final String desc;

    OperationTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
