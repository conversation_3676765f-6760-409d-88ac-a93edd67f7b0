package com.ylz.saas.resp;

import com.ylz.saas.codegen.base_service_type_field.entity.BaseServiceTypeFieldEntity;
import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.entity.SrmTenderBidderQuoteFieldValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 报价明细（物料信息）轮次响应类
 * <AUTHOR>
 * @createDate 2025-07-17
 */
@Data
@Schema(description = "报价明细（物料信息）轮次响应类")
public class SrmTenderQuoteDetailByMaterialRoundResp implements Serializable {

    /**
     * 报价轮次
     */
    @Schema(description = "报价轮次")
    private Integer roundNo;

    /**
     * 报价时间
     */
    @Schema(description = "报价时间")
    private LocalDateTime quoteTime;

    /**
     * 报价IP
     */
    @Schema(description = "报价IP")
    private String quoteIp;

    /**
     * 报价文件
     */
    @Schema(description = "报价文件")
    private List<SrmProjectAttachment> quoteAttachments;

    /**
     * 其他附件
     */
    @Schema(description = "其他附件")
    private List<SrmProjectAttachment> otherAttachments;

    /**
     * 物料报价列表
     */
    @Schema(description = "物料报价列表")
    private List<MaterialQuoteInfo> materialList;

    /**
     * 物料报价信息
     */
    @Data
    @Schema(description = "物料报价信息")
    public static class MaterialQuoteInfo implements Serializable {
        /**
         * 服务类型字段列表
         */
        private List<BaseServiceTypeFieldEntity> baseServiceTypeFieldEntities;

        /**
         * 物料编码
         */
        @Schema(description = "物料编码")
        private String materialCode;

        /**
         * 物料名称
         */
        @Schema(description = "物料名称")
        private String materialName;

        /**
         * 规格型号
         */
        @Schema(description = "规格型号")
        private String specModel;

        /**
         * 单位
         */
        @Schema(description = "单位")
        private String unit;

        /**
         * 需求数量
         */
        @Schema(description = "需求数量")
        private BigDecimal requiredQuantity;

        /**
         * 可供数量
         */
        @Schema(description = "可供数量")
        private BigDecimal availableQuantity;

        /**
         * 报价单价（元）
         */
        @Schema(description = "报价单价（元）")
        private BigDecimal quotePrice;

        /**
         * 报价总价（元）
         */
        @Schema(description = "报价总价（元）")
        private BigDecimal quoteAmount;

        /**
         * 报价项ID
         */
        @Schema(description = "报价项ID")
        private Long quoteItemId;

        /**
         * 报价时间（用于分组）
         */
        @Schema(description = "报价时间")
        private LocalDateTime quoteTime;

        /**
         * 报价IP（用于分组）
         */
        @Schema(description = "报价IP")
        private String quoteIp;

        /**
         * 报价轮次（用于分组）
         */
        @Schema(description = "报价轮次")
        private Integer roundNo;

        /**
         * 报价动态字段值列表
         */
        @Schema(description = "报价动态字段值列表")
        private List<SrmTenderBidderQuoteFieldValue> srmTenderBidderQuoteFieldValues;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
