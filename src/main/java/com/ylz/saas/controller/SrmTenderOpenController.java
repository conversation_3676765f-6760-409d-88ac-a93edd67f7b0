package com.ylz.saas.controller;

import com.ylz.saas.common.core.util.R;
import com.ylz.saas.entity.SrmTenderFailedLog;
import com.ylz.saas.entity.SrmTenderOpen;
import com.ylz.saas.req.*;
import com.ylz.saas.resp.BidOpenConfigResp;
import com.ylz.saas.resp.SrmOpenTenderMaterialResp;
import com.ylz.saas.resp.SrmOpenTenderSupplierResp;
import com.ylz.saas.resp.SrmTenderOpenLogResp;
import com.ylz.saas.service.SrmTenderOpenLogService;
import com.ylz.saas.service.SrmTenderOpenService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 开标控制器
 */
@RestController
@RequestMapping("/tenderOpen")
public class SrmTenderOpenController {

    @Resource
    private SrmTenderOpenService tenderOpenService;

    @Resource
    private SrmTenderOpenLogService tenderOpenLogService;

    /**
     * 开标大厅--消息新增
     *
     * @param req
     * @return
     */
    @PostMapping("/operationLogAdd")
    public R<Void> operationLogAdd(@RequestBody @Validated SrmTenderOpenLogAddReq req) {
        tenderOpenLogService.operationLogAdd(req);
        return R.ok();
    }

    /**
     * 开标大厅--消息记录
     *
     * @param req
     * @return
     */
    @PostMapping("/operationLog")
    public R<SrmTenderOpenLogResp> operationLog(@RequestBody @Validated SrmTenderOpenReq req) {
        SrmTenderOpenLogResp result = tenderOpenLogService.operationLog(req);
        return R.ok(result);
    }

    /**
     * 开标大厅--IP预警
     *
     * @param req
     * @return
     */
    @PostMapping("/ip/warn")
    public R<Integer> ipWarn(@RequestBody @Validated SrmTenderOpenReq req) {
        Integer result = tenderOpenLogService.ipWarn(req);
        return R.ok(result);
    }

    /**
     * 结束开标
     *
     * @param req
     * @return
     */
    @PostMapping("/endOpenBid")
    public R<Void> endOpenBid(@RequestBody @Validated SrmTenderOpenReq req) {
        tenderOpenService.endOpenBid(req);
        return R.ok();
    }

    /**
     * 手动开标
     *
     * @return
     */
    @PostMapping("/openBid")
    public R<Void> openBid(@RequestBody @Validated SrmTenderOpenReq req) {
        tenderOpenService.openBid(req);
        return R.ok();
    }

    /**
     * 流标
     *
     * @return
     */
    @PostMapping("/failedTender")
    public R<Void> failedTender(@RequestBody @Validated SrmTenderFailedLogReq req) {
        tenderOpenService.failedTender(req);
        return R.ok();
    }
    /**
     * 流标详情
     *
     * @return
     */
    @GetMapping("/failedTender/{id}")
    public R<SrmTenderFailedLog> failedTenderDetail(@PathVariable("id")Long id) {
        return R.ok( tenderOpenService.failedTenderDetail(id));
    }

    /**
     * 开标记录--按物料查看
     *
     * @return
     */
    @PostMapping("/openTenderInfo/material")
    public R<List<SrmOpenTenderMaterialResp>> openTenderInfoMaterial(@RequestBody @Validated SrmTenderOpenInfoReq req) {
        List<SrmOpenTenderMaterialResp> resultList = tenderOpenService.openTenderInfoMaterial(req);
        return R.ok(resultList);
    }

    /**
     * 开标记录--按供应商查看
     *
     * @return
     */
    @PostMapping("/openTenderInfo/supplier")
    public R<List<SrmOpenTenderSupplierResp>> openTenderInfoSupplier(@RequestBody @Validated SrmTenderOpenInfoReq req) {
        List<SrmOpenTenderSupplierResp> resultList = tenderOpenService.openTenderInfoSupplier(req);
        return R.ok(resultList);
    }


    /**
     * 查询公告下已再次发起报价的开标信息
     */
    @PostMapping("/getQuoteAgainBidOpenInfo")
    public R<SrmTenderOpen> getQuoteAgainBidOpenInfo(@RequestBody @Validated QuoteAgainBidOpenInfoQueryReq req) {
        return R.ok(tenderOpenService.getQuoteAgainBidOpenInfo(req));
    }


    /**
     * 开标设置
     */
    @PostMapping("/bidOpenConfig")
    public R<Void> bidOpenConfig(@RequestBody @Validated BidOpenConfigReq req){
        tenderOpenService.bidOpenConfig(req);
        return R.ok();
    }


    /**
     * 查询开标设置
     */
    @GetMapping("/getBidOpenConfig/{noticeId}")
    public R<BidOpenConfigResp> getBidOpenConfig(@PathVariable Long noticeId){
        return R.ok(tenderOpenService.getBidOpenConfig(noticeId));
    }

}

