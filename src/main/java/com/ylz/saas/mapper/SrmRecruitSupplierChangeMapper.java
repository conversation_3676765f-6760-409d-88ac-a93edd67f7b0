package com.ylz.saas.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import com.ylz.saas.entity.SrmRecruitSupplierChange;
import com.ylz.saas.req.SrmRecruitSupplierChangePageReq;
import com.ylz.saas.resp.SrmRecruitSupplierChangePageResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 供应商招募公告变更记录Mapper
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Mapper
public interface SrmRecruitSupplierChangeMapper extends SaasBaseMapper<SrmRecruitSupplierChange> {

    /**
     * 分页查询变更记录
     *
     * @param page 分页参数
     * @param req  查询条件
     * @return 分页结果
     */
    IPage<SrmRecruitSupplierChangePageResp> selectChangePage(Page<SrmRecruitSupplierChangePageResp> page, @Param("req") SrmRecruitSupplierChangePageReq req);
}
