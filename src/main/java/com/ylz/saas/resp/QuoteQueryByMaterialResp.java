package com.ylz.saas.resp;

import com.ylz.saas.codegen.base_service_type_field.entity.BaseServiceTypeFieldEntity;
import com.ylz.saas.entity.SrmProcurementProjectFieldValue;
import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.entity.SrmTenderBidderQuoteFieldValue;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/18 13:43
 * @Description
 */
@Data
public class QuoteQueryByMaterialResp {

    /**
     * 项目项ID
     */
    private Long projectItemId;

    /**
     * 需求行号
     */
    private String requireNo;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 规格型号
     */
    private String specModel;

    /**
     * 单位
     */
    private String unit;

    /**
     * 需求数量
     */
    private BigDecimal requiredQuantity;

    /**
     * 牧场id
     */
    private Long usageLocationId;

    /**
     * 标准质量参数ID
     */
    private Long qualityIndicatorId;

    /**
     * 质量指标名称
     */
    private String qualityIndicatorName;

    /**
     * 牧场名称
     */
    private String usageLocationName;

    /**
     * 牧场区域
     */
    private String usageLocationRegion;

    /**
     * 牧场地址
     */
    private String usageLocationAddress;

    /**
     * 服务类型字段列表
     */
    private List<BaseServiceTypeFieldEntity> baseServiceTypeFieldEntities;

    /**
     * 供应商投标报价列表
     */
    private List<SupplierQuote> supplierQuoteList;


    @Data
    public static class SupplierQuote {

        /**
         * 报价项ID
         */
        private Long quoteItemId;

        /**
         * 租户供应商ID
         */
        private Long tenantSupplierId;

        /**
         * 供应商名称
         */
        private String supplierName;

        /**
         * 供应商响应ID
         */
        private Long tenderSupplierResponseId;

        /**
         * 采购立项支付方式ID
         */
        private Long procurementProjectPaymentId;

        /**
         * 轮次
         */
        private Integer roundNo;

        /**
         * 需求数量
         */
        private BigDecimal requiredQuantity;

        /**
         * 报价时间
         */
        private LocalDateTime quoteTime;

        /**
         * 报价IP
         */
        private String quoteIp;

        /**
         * 是否中标(0-否、1-是)
         */
        private Integer awarded;

        /**
         * 中标数量
         */
        private BigDecimal awardedQuantity;

        /**
         * 备注
         */
        private String remark;

        /**
         * 报价数量
         */
        private BigDecimal availableQuantity;

        /**
         * 报价单价
         */
        private BigDecimal quotePrice;

        /**
         * 报价金额
         */
        private BigDecimal quoteAmount;

        /**
         * 报价动态字段值列表
         */
        private List<SrmTenderBidderQuoteFieldValue> srmTenderBidderQuoteFieldValues;

        /**
         * 报价附件列表
         */
        private List<SrmProjectAttachment> attachmentList;

    }

}
