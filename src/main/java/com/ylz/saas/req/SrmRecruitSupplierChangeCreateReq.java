package com.ylz.saas.req;

import com.ylz.saas.req.SrmRecruitSupplierSaveReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 供应商招募公告变更创建请求
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@Schema(description = "供应商招募公告变更创建请求")
public class SrmRecruitSupplierChangeCreateReq {

    /**
     * 招募公告ID
     */
    @NotNull(message = "招募公告ID不能为空")
    @Schema(description = "招募公告ID")
    private Long recruitId;

    /**
     * 变更说明
     */
    @Schema(description = "变更说明")
    private String changeRemark;

    /**
     * 变更后的招募公告数据
     */
    @Valid
    @NotNull(message = "变更后的招募公告数据不能为空")
    @Schema(description = "变更后的招募公告数据")
    private SrmRecruitSupplierSaveReq afterChangeData;
}
