package com.ylz.saas.codegen.base_material_info.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.base_material_info.entity.BaseMaterialInfoEntity;
import com.ylz.saas.codegen.base_material_info.param.BaseMaterialInfoParam;
import com.ylz.saas.codegen.base_material_info.vo.BaseMaterialInfoVo;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

public interface BaseMaterialInfoService extends IService<BaseMaterialInfoEntity> {
    /**
     * 新增物料基础信息表
     * @param baseMaterialInfoVo 物料基础信息表
     */
    boolean saveBaseMaterialInfo(BaseMaterialInfoVo baseMaterialInfoVo);

    /**
     * 修改物料基础信息表
     * @param baseMaterialInfoVo 物料基础信息表
     */
    boolean updateBaseMaterialInfo(BaseMaterialInfoVo baseMaterialInfoVo);

    /**
     * 导入模板下载
     * @param response
     */
    void template(HttpServletResponse response);

    /**
     * 数据导入
     * @param file
     * @param response
     */
    void uploadData(MultipartFile file, HttpServletResponse response);

    /**
     * 启用禁用
     * @param id
     * @return
     */
    Object switchStatus(Long id);

    /**
     * 支持拼音搜索的分页查询
     * @param page 分页对象
     * @param param 查询参数
     * @return 分页结果
     */
    Page<BaseMaterialInfoEntity> pageWithPinyinSearch(Page<BaseMaterialInfoEntity> page, BaseMaterialInfoParam param);

    /**
     * 支持拼音搜索的列表查询（带组织名称）
     * @param param 查询参数
     * @return 查询结果
     */
    List<BaseMaterialInfoEntity> listWithDeptName(BaseMaterialInfoParam param);

    /**
     * 通过ID查询（带组织名称）
     * @param id 主键ID
     * @return 查询结果
     */
    BaseMaterialInfoEntity getByIdWithDeptName(Long id);

    boolean newRemoveByIds(List<Long> idList);

}