package com.ylz.saas.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 在线问答表
 * @TableName srm_tender_online_qa
 */
@Data
public class SrmTenderOnlineQaAddReq {
    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 租户供应商ID
     */
//    @NotNull(message = "租户供应商ID不能为空")
    private Long tenantSupplierId;

    /**
     * 供应商名称
     */
//    @NotBlank(message = "供应商名称不能为空")
    private String supplierName;

    /**
     * 问题标题
     */
    @NotBlank(message = "问题标题不能为空")
    private String questionTitle;

    /**
     * 问题描述
     */
    @NotBlank(message = "问题描述不能为空")
    private String questionContent;

}