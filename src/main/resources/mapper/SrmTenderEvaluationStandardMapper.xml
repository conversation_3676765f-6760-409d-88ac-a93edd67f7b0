<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenderEvaluationStandardMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenderEvaluationStandard">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
        <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
        <result property="projectId" column="project_id" jdbcType="BIGINT"/>
        <result property="noticeId" column="notice_id" jdbcType="BIGINT"/>
        <result property="sectionId" column="section_id" jdbcType="BIGINT"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="nodeName" column="node_name" jdbcType="VARCHAR"/>
        <result property="itemName" column="item_name" jdbcType="VARCHAR"/>
        <result property="itemDescription" column="item_description" jdbcType="VARCHAR"/>
        <result property="maxScore" column="max_score" jdbcType="INTEGER"/>
        <result property="weight" column="weight" jdbcType="DOUBLE"/>
        <result property="sortOrder" column="sort_order" jdbcType="INTEGER"/>
        <result property="isRequired" column="is_required" jdbcType="INTEGER"/>
        <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,tenant_id,dept_id,project_id,notice_id,section_id,
        type,node_name,item_name,item_description,max_score,weight,sort_order,is_required,
        del_flag,create_by,create_by_name,create_time,
        update_by,update_by_name,update_time
    </sql>

    <!-- 查询评标委员会信息 -->
    <select id="getCommitteeInfo"
            resultType="com.ylz.saas.resp.SrmTenderEvaluationProgressResp$EvaluationCommitteeInfo">
        SELECT e.id,
               e.name,
               e.section_id,
               e.notice_id,
               COUNT(DISTINCT m.user_id)                        as totalMembers,
               COUNT(DISTINCT CONCAT(s.type, '-', s.node_name)) as totalEvaluationNodes
        FROM srm_tender_bid_evaluation e
                 LEFT JOIN srm_project_member m ON e.id = m.business_id
            AND m.member_type = 'EVALUATION_MEMBER'
            AND m.del_flag = 0
                 LEFT JOIN srm_tender_evaluation_standard s ON e.section_id = s.section_id
            AND e.notice_id = s.notice_id
            AND s.del_flag = 0
        WHERE e.section_id = #{sectionId}
          AND e.notice_id = #{noticeId}

          AND e.del_flag = 0
        GROUP BY e.id, e.name, e.section_id, e.notice_id
    </select>

    <!-- 查询评审节点列表 -->
    <select id="getEvaluationNodes" resultType="com.ylz.saas.resp.SrmTenderEvaluationProgressResp$EvaluationNode">
        SELECT s.type,
               CASE s.type
                   WHEN 'SCORE' THEN '评分项'
                   WHEN 'REVIEW' THEN '评审项'
                   ELSE s.type
                   END     as typeName,
               s.node_name as nodeName,
               COUNT(*)    as itemCount
        FROM srm_tender_evaluation_standard s
        WHERE s.section_id = #{sectionId}
          AND s.notice_id = #{noticeId}
          AND s.del_flag = 0
        GROUP BY s.type, s.node_name
        ORDER BY s.type, s.node_name
    </select>

    <!-- 查询评审项详细信息 -->
    <select id="getEvaluationItems" resultType="com.ylz.saas.resp.SrmTenderEvaluationProgressResp$EvaluationItem">
        SELECT s.id,
               s.item_name as itemName,
               s.type,
               s.node_name as nodeName,
               s.max_score as maxScore,
               s.weight
        FROM srm_tender_evaluation_standard s
        WHERE s.section_id = #{sectionId}
          AND s.notice_id = #{noticeId}
          AND s.del_flag = 0
        ORDER BY s.type, s.node_name, s.sort_order, s.create_time
    </select>

    <!-- 查询评标人员进度列表 -->
    <select id="getEvaluatorProgressList"
            resultType="com.ylz.saas.resp.SrmTenderEvaluationProgressResp$EvaluatorProgress">
        SELECT m.user_id,
               u.username      as userName,
               be.name         as expertName,
               be.expert_code,
               be.id_number    as expertIdNumber,
               be.expert_category,
               m.role,
               CASE m.role
                   WHEN 'EVALUATION_LEADER' THEN '评标组长'
                   WHEN 'EVALUATION_MEMBER' THEN '评标成员'
                   ELSE m.role
                   END         as roleName,
               m.contact_phone as contactPhone
        FROM srm_project_member m
                 LEFT JOIN sys_user u ON m.user_id = u.user_id
                 LEFT JOIN base_expert be ON u.user_id = be.user_id
        WHERE m.business_id = #{evaluationId}
          AND m.member_type = 'EVALUATION_MEMBER'
          AND m.del_flag = 0
          and be.del_flag = 0
        ORDER BY m.role, m.create_time
    </select>

    <!-- 查询专家在各评审节点下的完成情况 -->
    <select id="getEvaluatorNodeCompletion"
            resultType="com.ylz.saas.resp.SrmTenderEvaluationProgressResp$NodeCompletionStatus">
        SELECT s.type,
        s.node_name as nodeName,
        COUNT(DISTINCT s.id) as totalItems,
        COUNT(DISTINCT CASE
        WHEN sc.status = 'SUBMITTED' OR sc.status = 'SUMMARIZED' THEN sc.scoring_detail_id
        ELSE NULL
        END) as completedItems,
        CASE
        WHEN COUNT(DISTINCT s.id) = COUNT(DISTINCT CASE
        WHEN sc.status = 'SUBMITTED' OR sc.status = 'SUMMARIZED'
        THEN sc.scoring_detail_id
        ELSE NULL
        END) THEN 1
        ELSE 0
        END as isCompleted,
        CASE
        WHEN COUNT(DISTINCT s.id) = 0 THEN 0
        ELSE ROUND(COUNT(DISTINCT CASE
        WHEN sc.status = 'SUBMITTED' OR sc.status = 'SUMMARIZED'
        THEN sc.scoring_detail_id
        ELSE NULL
        END) * 100.0 / COUNT(DISTINCT s.id), 2)
        END as completionRate,
        CASE
        WHEN COUNT(DISTINCT s.id) = COUNT(DISTINCT CASE
        WHEN sc.status = 'SUBMITTED' OR sc.status = 'SUMMARIZED'
        THEN sc.scoring_detail_id
        ELSE NULL
        END) THEN '已完成'
        WHEN COUNT(DISTINCT CASE
        WHEN sc.status = 'SUBMITTED' OR sc.status = 'SUMMARIZED'
        THEN sc.scoring_detail_id
        ELSE NULL
        END) = 0 THEN '未开始'
        ELSE CONCAT(COUNT(DISTINCT CASE
        WHEN sc.status = 'SUBMITTED' OR sc.status = 'SUMMARIZED'
        THEN sc.scoring_detail_id
        ELSE NULL
        END), '/', COUNT(DISTINCT s.id))
        END as statusDescription,
        CASE
        WHEN e.summary_status = 'SUMMARIZED' THEN 1
        ELSE 0
        END
        as isSummarized
        FROM srm_tender_evaluation_standard s
        LEFT JOIN srm_tender_bid_evaluation_scoring sc ON s.id = sc.scoring_detail_id
        AND sc.user_id = #{userId}
        AND sc.section_id = #{sectionId}
        AND sc.notice_id = #{noticeId}
        AND sc.del_flag = 0
        LEFT JOIN srm_tender_bid_evaluation e ON sc.evaluation_id = e.id
        AND e.del_flag = 0
        WHERE s.section_id = #{sectionId}
          and sc.current_round = #{currentRound}
        AND s.notice_id = #{noticeId}
        AND s.del_flag = 0


        GROUP BY s.type, s.node_name
        ORDER BY s.type, s.node_name
    </select>

    <!-- 查询当前用户在项目中的角色权限 -->
    <select id="getCurrentUserRole" resultType="java.lang.String">
        SELECT pm.role
        FROM srm_project_member pm
        WHERE pm.business_id = #{noticeId}
          AND pm.user_id = #{userId}
          AND pm.del_flag = 0
          and pm.member_type = 'EVALUATION_MEMBER' LIMIT 1
    </select>

</mapper>
