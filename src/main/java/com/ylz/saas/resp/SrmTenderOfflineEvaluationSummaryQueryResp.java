package com.ylz.saas.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 线下评标汇总查询响应类
 * <AUTHOR>
 * @createDate 2025-07-14
 */
@Data
@Schema(description = "线下评标汇总查询响应类")
public class SrmTenderOfflineEvaluationSummaryQueryResp implements Serializable {

    /**
     * 评标委员会id
     */
    @Schema(description = "评标委员会id")
    private Long evaluationId;

    /**
     * 供应商汇总列表
     */
    @Schema(description = "供应商汇总列表")
    private List<SrmTenderOfflineEvaluationSummarySupplierResp> supplierList;

    /**
     * 评标报告文件地址
     */
    @Schema(description = "评标报告文件地址")
    private String reportContent;

    /**
     * 汇总状态
     */
    @Schema(description = "汇总状态")
    private String summaryStatus;

    /**
     * 汇总人
     */
    @Schema(description = "汇总人")
    private String summaryBy;

    /**
     * 汇总人名称
     */
    @Schema(description = "汇总人名称")
    private String summaryByName;

    /**
     * 汇总时间
     */
    @Schema(description = "汇总时间")
    private LocalDateTime summaryTime;

    private static final long serialVersionUID = 1L;
}
