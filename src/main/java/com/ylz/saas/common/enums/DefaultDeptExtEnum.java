package com.ylz.saas.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/6/13 10:07
 * @Description
 */
@Getter
@AllArgsConstructor
public enum DefaultDeptExtEnum {

    EXPERT_ROLE("专家默认角色"),
    ORG_ROLE("代理机构默认角色"),
    SUPPLIER_CONTACT("租户供应商联系人角色"),


    ORG_DEPT("招标代理（外部）"),
    EXPERT_DEPT("外部专家"),
    SUPPLIER_CONTACT_DEPT("供应商联系人"),


    EXTERNAL_POST("外部岗位");


    private final String value;

}
