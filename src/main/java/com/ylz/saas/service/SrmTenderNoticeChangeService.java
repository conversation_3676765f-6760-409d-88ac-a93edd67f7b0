package com.ylz.saas.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.entity.SrmTenderNoticeChange;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.req.SrmTenderNoticeChangeAddReq;
import com.ylz.saas.req.SrmTenderNoticeChangeDetailReq;
import com.ylz.saas.req.SrmTenderNoticeChangePageReq;
import com.ylz.saas.req.SrmTenderNoticeChangeReviewReq;
import com.ylz.saas.resp.SrmTenderNoticeChangePageResp;

/**
* <AUTHOR>
* @description 针对表【srm_tender_notice_change(招标公告变更记录表)】的数据库操作Service
* @createDate 2025-06-10 14:21:21
*/
public interface SrmTenderNoticeChangeService extends IService<SrmTenderNoticeChange> {

    /**
     * 添加招标公告变更记录
     * @param req
     */
    void addTenderNoticeChange(SrmTenderNoticeChangeAddReq req);

    /**
     * 招标公告变更记录分页
     * @param req
     * @return
     */
    Page<SrmTenderNoticeChangePageResp> tenderNoticeChangePage(SrmTenderNoticeChangePageReq req);

    /**
     * 招标公告变更记录审核
     * @param req
     */
    void tenderNoticeChangeReview(SrmTenderNoticeChangeReviewReq req);

    /**
     * 招标公告变更记录审批通过
     * @param changeId
     * @param projectId
     * @param noticeId
     */
    void tenderNoticeChangeReviewPass(Long changeId, Long projectId, Long noticeId);

    /**
     * 招标公告变更记录审批拒绝
     * @param changeId
     * @param projectId
     * @param noticeId
     */
    void tenderNoticeChangeReviewReject(Long changeId, Long projectId, Long noticeId);

    /**
     * 变更详情
     * @param req
     * @return
     */
    SrmTenderNoticeChangeAddReq tenderNoticeChangeDetail(SrmTenderNoticeChangeDetailReq req);

    /**
     * 变更详情（给业务流使用）
     * @param changeId
     * @return
     */
    SrmTenderNoticeChangeAddReq tenderNoticeChangeDetailForBase(String changeId);

    /**
     * 招标变更审批修改为审批中
     * @param changeId
     */
    void changeApprovingHook(String changeId);
}
