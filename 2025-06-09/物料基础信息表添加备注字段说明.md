# 物料基础信息表添加备注字段说明

## 修改概述

为物料基础信息表(`base_material_info`)添加`remark`备注字段，用于存储物料的额外说明信息。

## 修改时间

2025-06-10

## 修改内容

### 1. 数据库表结构修改

**SQL文件**: `db/add_material_remark_field.sql`

```sql
ALTER TABLE `base_material_info` 
ADD COLUMN `remark` varchar(500) NULL DEFAULT NULL COMMENT '备注' AFTER `status`;
```

**字段说明**:
- 字段名: `remark`
- 数据类型: `varchar(500)`
- 是否为空: `NULL`
- 默认值: `NULL`
- 注释: `备注`
- 位置: 在`status`字段之后

### 2. Java代码修改

#### 2.1 实体类修改

**文件**: `src/main/java/com/ylz/saas/codegen/base_material_info/entity/BaseMaterialInfoEntity.java`

```java
/**
 * 备注
 */
@Schema(description="备注")
private String remark;
```

#### 2.2 VO类修改

**文件**: `src/main/java/com/ylz/saas/codegen/base_material_info/vo/BaseMaterialInfoVo.java`

```java
/**
 * 备注
 */
private String remark;
```

#### 2.3 Excel导入类修改

**文件**: `src/main/java/com/ylz/saas/codegen/base_material_info/excel/BaseMaterialInfoReq.java`

```java
/**
 * 备注
 */
@ExcelProperty(value = "备注")
private String remark;
```

#### 2.4 Excel导出类修改

**文件**: `src/main/java/com/ylz/saas/codegen/base_material_info/excel/BaseMaterialInfoResp.java`

```java
/**
 * 备注
 */
@ExcelProperty(value = "备注")
private String remark;
```

#### 2.5 查询参数类修改

**文件**: `src/main/java/com/ylz/saas/codegen/base_material_info/param/BaseMaterialInfoParam.java`

```java
/**
 * 备注
 */
@CriteriaField(field = "remark", operator = Operator.LIKE)
private String remark;
```

## 功能特性

### 1. 数据存储
- 支持最多500个字符的备注信息
- 字段可为空，不影响现有数据

### 2. 查询功能
- 支持按备注内容进行模糊查询
- 在分页查询接口中可以通过remark参数进行筛选

### 3. Excel导入导出
- Excel导入时支持备注字段
- Excel导出时包含备注信息
- Excel模板会自动包含备注列

### 4. API接口
现有的所有API接口都会自动支持备注字段：

- **新增物料**: `POST /baseMaterialInfo/save`
- **修改物料**: `POST /baseMaterialInfo/update`
- **分页查询**: `POST /baseMaterialInfo/page`
- **详情查询**: `GET /baseMaterialInfo/{id}`
- **Excel导入**: `POST /baseMaterialInfo/import`
- **Excel导出**: `GET /baseMaterialInfo/export`

## 使用示例

### 1. 新增物料时包含备注

```json
{
  "materialCode": "MAT001",
  "materialName": "测试物料",
  "materialCategory": "CAT001",
  "remark": "这是一个测试物料的备注信息"
}
```

### 2. 查询时按备注筛选

```json
{
  "remark": "测试",
  "page": 1,
  "size": 10
}
```

### 3. Excel导入格式

Excel文件需要包含"备注"列，可以填写相关的说明信息。

## 兼容性说明

### 1. 向后兼容
- 现有数据不受影响，remark字段默认为NULL
- 现有API接口保持兼容，不传remark字段不会报错
- 前端可以选择性地显示和编辑备注字段

### 2. 数据迁移
- 无需数据迁移，新字段允许为空
- 可以通过UPDATE语句为现有数据添加备注信息

## 部署说明

### 1. 数据库更新
执行SQL文件：`db/add_material_remark_field.sql`

### 2. 应用部署
重新编译并部署应用程序

### 3. 验证步骤
1. 检查数据库表结构是否正确添加了remark字段
2. 测试新增物料接口是否支持备注字段
3. 测试查询接口是否支持备注筛选
4. 测试Excel导入导出是否包含备注列

## 注意事项

1. **字段长度限制**: 备注字段最大长度为500字符，前端需要做相应的长度校验
2. **查询性能**: 如果备注字段经常用于查询，建议考虑添加索引
3. **数据安全**: 备注字段可能包含敏感信息，需要注意权限控制
4. **前端适配**: 前端需要相应地添加备注字段的显示和编辑功能

## 后续优化建议

1. **富文本支持**: 如果需要支持格式化文本，可以考虑将字段类型改为TEXT
2. **多语言支持**: 如果系统需要支持多语言，可以考虑备注字段的国际化
3. **历史记录**: 可以考虑添加备注修改的历史记录功能
4. **模板功能**: 可以为常用的备注内容提供模板选择功能
