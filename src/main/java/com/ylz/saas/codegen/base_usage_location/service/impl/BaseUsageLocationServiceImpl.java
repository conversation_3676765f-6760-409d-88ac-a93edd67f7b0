package com.ylz.saas.codegen.base_usage_location.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.admin.api.entity.SysDept;
import com.ylz.saas.admin.service.SysDeptService;
import com.ylz.saas.codegen.base_usage_location.entity.BaseUsageLocationEntity;
import com.ylz.saas.codegen.base_usage_location.enums.LocationTypeEnum;
import com.ylz.saas.codegen.base_usage_location.excel.BaseUsageLocationReq;
import com.ylz.saas.codegen.base_usage_location.mapper.BaseUsageLocationMapper;
import com.ylz.saas.codegen.base_usage_location.param.BaseUsageLocationParam;
import com.ylz.saas.codegen.base_usage_location.service.BaseUsageLocationService;
import com.ylz.saas.codegen.base_usage_location.vo.BaseUsageLocationVo;
import com.ylz.saas.common.enums.CodeGeneratorPrefixEnum;
import com.ylz.saas.common.enums.CommonSwitchEnum;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.data.excel.ExcelAnalysisUtil;
import com.ylz.saas.common.data.excel.ExcelTemplateGenerator;
import com.ylz.saas.common.sequence.generator.impl.DefaultCodeGenerator;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 使用地点信息表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:41:24
 */
@Service
@Slf4j
@AllArgsConstructor
public class BaseUsageLocationServiceImpl extends ServiceImpl<BaseUsageLocationMapper, BaseUsageLocationEntity> implements BaseUsageLocationService {

    private final DefaultCodeGenerator codeGenerator;
    private final SysDeptService sysDeptService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBaseUsageLocation(BaseUsageLocationVo baseUsageLocationVo) {
        BaseUsageLocationEntity baseUsageLocation = new BaseUsageLocationEntity();
        BeanUtil.copyProperties(baseUsageLocationVo, baseUsageLocation);
        Long count = this.lambdaQuery().eq(BaseUsageLocationEntity::getLocationName, baseUsageLocationVo.getLocationName()).count();
        ExceptionUtil.check(count > 0, "500", "使用地点名称已存在，不可重复提交");
        baseUsageLocation.setLocationType(LocationTypeEnum.RANCH.name());
        baseUsageLocation.setLocationCode(codeGenerator.generate(CodeGeneratorPrefixEnum.MC.name(), CodeGeneratorPrefixEnum.MC.getLength()));
        return save(baseUsageLocation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBaseUsageLocation(BaseUsageLocationVo baseUsageLocationVo) {
        BaseUsageLocationEntity baseUsageLocation = getById(baseUsageLocationVo.getId());
        ExceptionUtil.check(baseUsageLocation == null, "500", "使用地点信息表不存在");
        BeanUtil.copyProperties(baseUsageLocationVo, baseUsageLocation);
        return updateById(baseUsageLocation);
    }

    @Override
    public void template(HttpServletResponse response) {
        // 创建目录及导入模板文件
        isExcelExist();
        // 读取导入模板
        FileInputStream inputStream = null;
        ServletOutputStream outputStream = null;
        try {
            String oriFileName = "牧场信息导入模板";
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(oriFileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            Path result = ExcelTemplateGenerator.getResult();
            Path filePath = result.resolve("使用地点信息表baseUsageLocation.xlsx");
            // 绝对路径
            File file = new File(filePath.toString());
            inputStream = new FileInputStream(file);

            // 获取输出流
            outputStream = response.getOutputStream();

            // 将输入流的内容写入到输出流中
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            // 确保输出流的数据完全写入
            outputStream.flush();
            log.info("模板下载成功");
        } catch (IOException e) {
            System.out.println("e = " + e.getMessage());
            log.info("模板下载失败");
        } finally {
            // 关闭流
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (IOException e) {
                log.error("关闭流时发生错误: {}", e.getMessage());
            }
        }
    }

    @Override
    @Transactional
    public void uploadData(MultipartFile file, HttpServletResponse response) {
        try {
            List<BaseUsageLocationReq> importGoodsList = new ExcelAnalysisUtil<BaseUsageLocationReq>()
                    .readFile(file, BaseUsageLocationReq.class, 1);
            if (CollectionUtils.isEmpty(importGoodsList)) {
                writeImportResponse(response, 1, "导入数据为空");
                return;
            }

            // 1. 先做必填项校验
            for (BaseUsageLocationReq req : importGoodsList) {
                if (StrUtil.isBlank(req.getLocationName())) {
                    writeImportResponse(response, 1, "牧场名称不能为空");
                    return;
                }
                if (StrUtil.isBlank(req.getDeptName())) {
                    writeImportResponse(response, 1, "组织名称不能为空");
                    return;
                }
                if (StrUtil.isBlank(req.getDistrict())) {
                    writeImportResponse(response, 1, "省市区不能为空");
                    return;
                }
                if (StrUtil.isBlank(req.getAddress())) {
                    writeImportResponse(response, 1, "详细地址不能为空");
                    return;
                }
            }

            // 2. 检查导入数据中是否有重复的牧场名称
            List<String> importLocationNames = importGoodsList.stream()
                    .map(BaseUsageLocationReq::getLocationName)
                    .filter(StrUtil::isNotBlank)
                    .toList();
            if (importLocationNames.size() != importLocationNames.stream().distinct().count()) {
                writeImportResponse(response, 1, "导入数据中存在重复的牧场名称");
                return;
            }

            // 3. 检查与数据库中已有牧场名称的重复性
            if (CollectionUtils.isNotEmpty(importLocationNames)) {
                LambdaQueryWrapper<BaseUsageLocationEntity> nameQueryWrapper = new LambdaQueryWrapper<>();
                nameQueryWrapper.in(BaseUsageLocationEntity::getLocationName, importLocationNames);
                List<BaseUsageLocationEntity> existingLocations = this.list(nameQueryWrapper);
                if (CollectionUtils.isNotEmpty(existingLocations)) {
                    List<String> existingNames = existingLocations.stream()
                            .map(BaseUsageLocationEntity::getLocationName)
                            .toList();
                    writeImportResponse(response, 1, "牧场名称已存在：" + String.join(",", existingNames));
                    return;
                }
            }

            List<BaseUsageLocationEntity> baseUsageLocationEntities = BeanUtil.copyToList(importGoodsList, BaseUsageLocationEntity.class);
            // 处理组织名称到组织ID的映射
            processDeptNameToId(baseUsageLocationEntities);
            // 做字段的必传校验（已提前做，这里可省略）
            for (BaseUsageLocationEntity baseUsageLocationEntity : baseUsageLocationEntities) {
                // 设置默认值
                baseUsageLocationEntity.setLocationType(LocationTypeEnum.RANCH.name());
                baseUsageLocationEntity.setLocationCode(codeGenerator.generate(CodeGeneratorPrefixEnum.MC.name(), CodeGeneratorPrefixEnum.MC.getLength()));
                baseUsageLocationEntity.setStatus(CommonSwitchEnum.ENABLED.name());
            }
            // 保存数据
            this.saveOrUpdateBatch(baseUsageLocationEntities);
            writeImportResponse(response, 0, "导入成功，共导入 " + baseUsageLocationEntities.size() + " 条数据");
        } catch (IOException e) {
            log.info("文件解析失败");
            throw new RuntimeException(e);
        }
    }

    /**
     * 处理组织名称到组织ID的映射
     * @param importList 导入数据列表
     */
    private void processDeptNameToId(List<BaseUsageLocationEntity> importList) {
        if (CollectionUtils.isEmpty(importList)) {
            return;
        }

        // 收集所有的组织名称

        Set<String> deptNames = importList.stream()
            .map(BaseUsageLocationEntity::getDeptName)
            .filter(StrUtil::isNotBlank)
            .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(deptNames)) {
            return;
        }

        // 批量查询组织信息
        List<SysDept> deptList = sysDeptService.lambdaQuery()
            .in(SysDept::getName, deptNames)
            .list();

        Map<String, Long> deptNameToIdMap = deptList.stream()
            .collect(Collectors.toMap(SysDept::getName, SysDept::getDeptId, (existing, replacement) -> existing));

        // 设置组织ID
        importList.forEach(item -> {
            if (StrUtil.isNotBlank(item.getDeptName())) {
                Long deptId = deptNameToIdMap.get(item.getDeptName());
                ExceptionUtil.check(deptId == null, "500", String.format("组织名称不存在：%s",item.getDeptName()));
                item.setDeptId(deptId);
            }
        });
    }

    private void writeImportResponse(HttpServletResponse response, int code, String errorMsg) throws IOException {
        JSONObject obj = new JSONObject();
        response.setContentType("text/html;charset=utf-8");
        obj.put("code", code);
        obj.put("msg", errorMsg);
        PrintWriter writer = response.getWriter();
        writer.write(obj.toJSONString());
        writer.close();
    }

    private static void isExcelExist() {
        try {
            Path result = ExcelTemplateGenerator.getResult();

            File file = new File(result.toString());
            if (file.exists()) {
                log.info("excel文件夹路径存在");
            } else {
                // 创建文件夹
                if (file.mkdirs()) {
                    log.info("目录已创建: {}", file);
                } else {
                    log.info("创建目录失败: {}", file);
                    throw new RuntimeException("创建目录失败: " + file);
                }
            }
            // 设置 Excel 文件的完整路径
            Path filePath = result.resolve("使用地点信息表baseUsageLocation.xlsx");
            // 生成模板文件
            ExcelTemplateGenerator.createExcelTemplate(filePath.toString(), BaseUsageLocationReq.class);
        } catch (UnsupportedEncodingException e) {
            log.info("编码失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean switchStatus(Long id) {
        BaseUsageLocationEntity original = getById(id);
        ExceptionUtil.check(original == null, "500", "使用地点不存在");
        if (CommonSwitchEnum.ENABLED.name().equals(original.getStatus())) {
            original.setStatus(CommonSwitchEnum.DISABLED.name());
        } else {
            original.setStatus(CommonSwitchEnum.ENABLED.name());
        }
        this.updateById(original);
        return true;
    }

    @Override
    public List<BaseUsageLocationEntity> listWithDeptName(BaseUsageLocationParam param) {
        QueryWrapper<BaseUsageLocationEntity> queryWrapper = param.toWrapper();
        List<BaseUsageLocationEntity> result = list(queryWrapper);

        // 设置组织名称
        setDeptNames(result);

        return result;
    }

    /**
     * 批量设置组织名称
     * @param records 使用地点信息列表
     */
    private void setDeptNames(List<BaseUsageLocationEntity> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        // 收集所有的组织ID
        Set<Long> deptIds = records.stream()
            .map(BaseUsageLocationEntity::getDeptId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(deptIds)) {
            return;
        }

        // 批量查询组织信息
        List<SysDept> deptList = sysDeptService.listByIds(deptIds);
        Map<Long, String> deptIdToNameMap = deptList.stream()
            .collect(Collectors.toMap(SysDept::getDeptId, SysDept::getName, (existing, replacement) -> existing));

        // 设置组织名称
        records.forEach(record -> {
            if (record.getDeptId() != null) {
                String deptName = deptIdToNameMap.get(record.getDeptId());
                record.setDeptName(deptName);
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIdsBaseUsageLocation(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }

        List<BaseUsageLocationEntity> baseUsageLocationList = lambdaQuery()
                .in(BaseUsageLocationEntity::getId, ids)
                .list();

        if (CollectionUtils.isNotEmpty(baseUsageLocationList)) {
            // 物理删除：直接从数据库中删除记录
            this.baseMapper.removeByIdsBaseUsageLocation(ids);
            return true;
        }
        return true;
    }

}