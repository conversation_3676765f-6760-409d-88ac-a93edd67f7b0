package com.ylz.saas.resp;

import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.ChangeTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 变更查询结果
 */
@Data
public class SrmTenderNoticeChangePageResp {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 采购立项ID
     */
    private Long projectId;

    /**
     * 采购立项名称
     */
    private String projectName;

    /**
     * 变更人
     */
    private String changeBy;

    /**
     * 变更人名称
     */
    private String changeByName;

    /**
     * 变更时间
     */
    private LocalDateTime changeTime;

    /**
     * 变更类型
     */
    private ChangeTypeEnum changeType;

    /**
     * 变更审核状态
     * TO_APPROVE("待审批"),
     *     APPROVING("审批中"),
     *     APPROVE("审批通过"),
     *     APPROVE_REJECT("审批驳回"),
     *     APPROVE_REVOKE("审批撤销"),
     */
    private ApproveStatusEnum approvedStatus;
}