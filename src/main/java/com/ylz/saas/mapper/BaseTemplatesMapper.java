package com.ylz.saas.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.entity.BaseTemplates;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylz.saas.resp.BaseAnnouncementTemplateQueryResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【base_templates(模板表)】的数据库操作Mapper
* @createDate 2025-06-11 13:07:56
* @Entity com.ylz.saas.entity.BaseTemplates
*/
@Mapper
public interface BaseTemplatesMapper extends BaseMapper<BaseTemplates> {
    Page<BaseAnnouncementTemplateQueryResp> selectByType(
            Page<BaseAnnouncementTemplateQueryResp> page,
            @Param("searchKey") String searchKey,
            @Param("type") String type);
}




