package com.ylz.saas.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.ylz.saas.admin.api.dto.MessageSmsDTO;
import com.ylz.saas.admin.service.SysMessageService;
import com.ylz.saas.codegen.base_quality_indicator.entity.BaseQualityIndicatorEntity;
import com.ylz.saas.codegen.base_quality_indicator.service.BaseQualityIndicatorService;
import com.ylz.saas.codegen.base_service_type_field.entity.BaseServiceTypeFieldEntity;
import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.codegen.base_usage_location.entity.BaseUsageLocationEntity;
import com.ylz.saas.codegen.base_usage_location.service.BaseUsageLocationService;
import com.ylz.saas.common.SmsBizCodeConstant;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.core.exception.GlobalResultCode;
import com.ylz.saas.common.core.util.SpringContextHolder;
import com.ylz.saas.entity.*;
import com.ylz.saas.enums.*;
import com.ylz.saas.mapper.SrmTenderBidderQuoteItemMapper;
import com.ylz.saas.req.*;
import com.ylz.saas.resp.*;
import com.ylz.saas.service.*;
import com.ylz.saas.util.IpUtils;
import com.ylz.saas.vo.*;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【srm_tender_bidder_quote_item(投标报价明细表)】的数据库操作Service实现
 * @createDate 2025-06-13 14:57:59
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SrmTenderBidderQuoteItemServiceImpl extends ServiceImpl<SrmTenderBidderQuoteItemMapper, SrmTenderBidderQuoteItem>
        implements SrmTenderBidderQuoteItemService {

    private final SrmProcurementProjectService srmProcurementProjectService;
    private final SrmProcurementProjectSectionService srmProcurementProjectSectionService;
    private final SrmProcurementProjectItemService srmProcurementProjectItemService;
    private final SrmTenderSupplierResponseService srmTenderSupplierResponseService;
    private final SrmTenderRequirementService srmTenderRequirementService;
    private final SrmTenderBidderQuoteFieldValueService srmTenderBidderQuoteFieldValueService;
    private final SrmProjectAttachmentService srmProjectAttachmentService;
    private final SrmTenderOpenService srmTenderOpenService;
    private final BaseServiceTypeFieldService baseServiceTypeFieldService;
    private final BaseUsageLocationService baseUsageLocationService;
    private final BaseQualityIndicatorService baseQualityIndicatorService;
    private final SrmTenderNoticeService srmTenderNoticeService;
    private final SrmTenderBidderSupplierQuoteAgainService srmTenderBidderSupplierQuoteAgainService;
    private final SysMessageService sysMessageService;
    private final SrmTenderAwardNoticeService awardNoticeService;
    @Resource
    private AsyncSmsService asyncSmsService;

    @Value("${app.api-url:https://ayn.canpanscp.com}")
    private String appApiUrl;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitQuote(QuoteSubmitReq req) {
        ExceptionUtil.checkNotEmpty(req.getQuoteItems(), "报价项不能为空");
        // 1. 获取当前登录用户
        ValidateTenderResult validateTenderResult = srmTenderSupplierResponseService.validateSupplierAndProject(req.getNoticeId(), req.getTenantSupplierId());
        SrmProcurementProject project = validateTenderResult.getProject();
        SrmTenderNotice tenderNotice = validateTenderResult.getTenderNotice();
        ExceptionUtil.check((project.getSourcingType() == BaseServiceTypeFieldService.BuyWayEnum.ZB ||
                project.getSourcingType() == BaseServiceTypeFieldService.BuyWayEnum.JZTP)
                && tenderNotice.getDocStatus() != ApproveStatusEnum.APPROVE, GlobalResultCode.INVALID_STATE, "招标/竞谈文件未审批通过");

        final LocalDateTime now = LocalDateTime.now();

        // 校验标段信息
        SrmProcurementProjectSection section = srmProcurementProjectSectionService.getById(req.getSectionId());
        ExceptionUtil.checkNonNull(section, "标段不存在");

        final boolean selfQuote = Objects.isNull(req.getTenantSupplierId());
        final Long tenantSupplierId = validateTenderResult.getTenantSupplierInfo().getId();

        // 2. 查询供应商响应表
        SrmTenderSupplierResponse supplierResponse = srmTenderSupplierResponseService.lambdaQuery()
                .eq(SrmTenderSupplierResponse::getNoticeId, tenderNotice.getId())
                .eq(SrmTenderSupplierResponse::getSectionId, section.getId())
                .eq(SrmTenderSupplierResponse::getTenantSupplierId, tenantSupplierId)
                .one();

        // 3. 获取开标信息
        SrmTenderOpen tenderOpen = srmTenderOpenService.lambdaQuery().eq(SrmTenderOpen::getNoticeId, tenderNotice.getId())
                .eq(SrmTenderOpen::getSectionId, section.getId()).one();

        // 校验校验资格预审
        if (Objects.equals(YesNoEnum.YES.getCode(), project.getPreQualification()) && selfQuote) {
            ExceptionUtil.check(supplierResponse == null
                            || !Objects.equals(TenderSupplierRegisterStatusEnum.PASSED, supplierResponse.getRegisterStatus()),
                    GlobalResultCode.INVALID_PARAMS, "供应商未报名或报名信息未审核通过");
        }

        if (supplierResponse == null) {
            supplierResponse = srmTenderSupplierResponseService.getOrCreateSupplierResponse(validateTenderResult, section.getId(), TenderSupplierResponseStageEnum.QUOTE);
        }

        if (tenderOpen == null) {
            // 还没有开标，第一次报价
            if (tenderNotice.getQuoteStartTime() != null && now.isBefore(tenderNotice.getQuoteStartTime())) {
                ExceptionUtil.checkNonNull(null, "报价尚未开始");
            }
            if (tenderNotice.getQuoteEndTime() != null && now.isAfter(tenderNotice.getQuoteEndTime())) {
                ExceptionUtil.checkNonNull(null, "报价已结束");
            }
        } else {
            if (tenderOpen.getLatestQuoteStartTime() != null && now.isBefore(tenderOpen.getLatestQuoteStartTime())) {
                ExceptionUtil.checkNonNull(null, "报价尚未开始");
            }
            if (tenderOpen.getLatestQuoteEndTime() != null && now.isAfter(tenderOpen.getLatestQuoteEndTime())) {
                ExceptionUtil.checkNonNull(null, "报价已结束");
            }
        }

        // 校验保证金
        boolean needDeposit = srmTenderRequirementService.isNeedEffInfo(tenderNotice.getId(), section.getId());
        if (selfQuote && needDeposit) {
            ExceptionUtil.check(!Objects.equals(DepositStatusEnum.VERIFIED, supplierResponse.getDepositStatus()),
                    GlobalResultCode.INVALID_PARAMS, "未缴纳保证金或保证金审核不通过");
        }
        Map<Long, Boolean> needTenderFeeMap = srmTenderRequirementService.isNeedEffInfoBySectionIds(tenderNotice.getId(), Lists.newArrayList(section.getId()), BidsSegmentTypeEnum.QUALIFICATION);
        if (selfQuote && needTenderFeeMap.get(section.getId())) {
            ExceptionUtil.check(!Objects.equals(TenderFeeStatusEnum.VERIFIED, supplierResponse.getTenderFeeStatus()),
                    GlobalResultCode.INVALID_PARAMS, "未缴纳标书费或标书费审核不通过");
        }


        final int roundNo = tenderOpen == null ? 1 : tenderOpen.getCurrentRound();

        if (roundNo > 1) {
            SrmTenderBidderSupplierQuoteAgain supplierQuoteAgain = srmTenderBidderSupplierQuoteAgainService.lambdaQuery()
                    .eq(SrmTenderBidderSupplierQuoteAgain::getNoticeId, tenderNotice.getId())
                    .eq(SrmTenderBidderSupplierQuoteAgain::getSectionId, section.getId())
                    .eq(SrmTenderBidderSupplierQuoteAgain::getTenantSupplierId, tenantSupplierId)
                    .eq(SrmTenderBidderSupplierQuoteAgain::getQuoteAgainRound, roundNo)
                    .one();
            ExceptionUtil.checkNonNull(supplierQuoteAgain, "供应商未邀请参加新一轮报价");
        }


        List<Long> projectItemIds = req.getQuoteItems().stream().map(QuoteSubmitReq.QuoteItemReq::getProjectItemId).toList();
        List<SrmProcurementProjectItem> srmProcurementProjectItems = srmProcurementProjectItemService.listByIds(projectItemIds);
        ExceptionUtil.checkNotEmpty(srmProcurementProjectItems, "采购项目条目不存在");
        Map<Long, SrmProcurementProjectItem> projectItemMap = srmProcurementProjectItems.stream().collect(Collectors.toMap(SrmProcurementProjectItem::getId, Function.identity()));

        // 先删除
        this.lambdaUpdate()
                .eq(SrmTenderBidderQuoteItem::getNoticeId, tenderNotice.getId())
                .eq(SrmTenderBidderQuoteItem::getSectionId, section.getId())
                .eq(SrmTenderBidderQuoteItem::getTenantSupplierId, tenantSupplierId)
                .in(SrmTenderBidderQuoteItem::getProjectItemId, projectItemIds)
                .eq(SrmTenderBidderQuoteItem::getRoundNo, roundNo)
                .remove();
        // 4. 遍历报价明细，保存/更新报价
        Long supplierResponseId = supplierResponse.getId();
        List<BaseServiceTypeFieldEntity> baseServiceTypeFields = baseServiceTypeFieldService.lambdaQuery()
                .eq(BaseServiceTypeFieldEntity::getServiceTypeId, project.getServiceTypeId())
                .eq(BaseServiceTypeFieldEntity::getBuyWay, project.getSourcingType()).list();
        Map<String, BaseServiceTypeFieldEntity> baseServiceTypeFieldMap = Optional.ofNullable(baseServiceTypeFields).stream().flatMap(Collection::stream)
                .collect(Collectors.toMap(BaseServiceTypeFieldEntity::getFieldCode, Function.identity(), (v1, v2) -> v1));
        List<SrmTenderBidderQuoteItem> bidderQuoteItems = req.getQuoteItems().stream().map(item -> {
            SrmProcurementProjectItem projectItem = projectItemMap.get(item.getProjectItemId());
            ExceptionUtil.checkNonNull(projectItem, "采购项目条目不存在");
            SrmTenderBidderQuoteItem quoteItem = new SrmTenderBidderQuoteItem();
            quoteItem.setProjectId(project.getId());
            quoteItem.setNoticeId(tenderNotice.getId());
            quoteItem.setSectionId(section.getId());
            quoteItem.setTenantSupplierId(tenantSupplierId);
            quoteItem.setTenderSupplierResponseId(supplierResponseId);
            quoteItem.setProjectItemId(item.getProjectItemId());
            quoteItem.setProcurementProjectPaymentId(item.getProjectPaymentId());
            quoteItem.setRequireNo(projectItem.getRequireNo());
            quoteItem.setMaterialCode(projectItem.getMaterialCode());
            quoteItem.setMaterialName(projectItem.getMaterialName());
            quoteItem.setSpecModel(projectItem.getSpecModel());
            quoteItem.setUnit(projectItem.getUnit());
            quoteItem.setRequiredQuantity(projectItem.getRequiredQuantity());
            quoteItem.setUsageLocationId(projectItem.getUsageLocationId());
            quoteItem.setQualityIndicatorId(projectItem.getQualityIndicatorId());
            quoteItem.setRoundNo(roundNo);
            quoteItem.setQuoteTime(now);
            quoteItem.setQuoteIp(IpUtils.getClientIP());
            quoteItem.setDelFlag(YesNoEnum.NO.getCode());
            List<SrmTenderBidderQuoteFieldValue> quoteFieldValues = Lists.newArrayList();
            for (DynamicFieldVo dynamicFieldVo : item.getDynamicFields()) {
                BaseServiceTypeFieldEntity baseServiceTypeFieldEntity = baseServiceTypeFieldMap.get(dynamicFieldVo.getCode());
                ExceptionUtil.checkNonNull(baseServiceTypeFieldEntity, "业务类型字段不存在");
                FixedFieldEnum fixedFieldEnum = FixedFieldEnum.getByCode(baseServiceTypeFieldEntity.getFieldCode());
                if (fixedFieldEnum != null) {
                    if (FixedFieldEnum.isQuoteRequireField(fixedFieldEnum)
                            && Objects.equals(com.ylz.saas.common.core.constant.enums.YesNoEnum.YES.getCode() ,baseServiceTypeFieldEntity.getQuoteIsRequired())) {
                        ExceptionUtil.check(StringUtils.isBlank(dynamicFieldVo.getValue()), GlobalResultCode.INVALID_PARAMS, fixedFieldEnum.getDesc() + "不能为空");
                    }
                    if (StringUtils.isNotBlank(dynamicFieldVo.getValue())) {
                        switch (fixedFieldEnum) {
                            case AVAILABLE_QUANTITY:
                                quoteItem.setAvailableQuantity(new BigDecimal(dynamicFieldVo.getValue()));
                                break;
                            case QUOTE_PRICE:
                                quoteItem.setQuotePrice(new BigDecimal(dynamicFieldVo.getValue()));
                                break;
                            case QUOTE_AMOUNT:
                                quoteItem.setQuoteAmount(new BigDecimal(dynamicFieldVo.getValue()));
                                break;
                        }
                    }
                } else {
                    ExceptionUtil.check(Objects.equals(com.ylz.saas.common.core.constant.enums.YesNoEnum.YES.getCode(), baseServiceTypeFieldEntity.getPlanIsRequired())
                                    && StringUtils.isBlank(String.valueOf(dynamicFieldVo.getValue())), GlobalResultCode.INVALID_PARAMS,
                            baseServiceTypeFieldEntity.getFieldName() + "不能为空");
                    SrmTenderBidderQuoteFieldValue fieldValue = new SrmTenderBidderQuoteFieldValue();
                    fieldValue.setProjectId(project.getId());
                    fieldValue.setNoticeId(tenderNotice.getId());
                    fieldValue.setSectionId(section.getId());
                    fieldValue.setProjectItemId(item.getProjectItemId());
                    fieldValue.setTenantSupplierId(tenantSupplierId);
                    fieldValue.setServiceTypeId(project.getServiceTypeId());
                    fieldValue.setFieldCode(baseServiceTypeFieldEntity.getFieldCode());
                    fieldValue.setFieldType(baseServiceTypeFieldEntity.getFieldType());
                    fieldValue.setFieldName(baseServiceTypeFieldEntity.getFieldName());
                    if (baseServiceTypeFieldEntity.getFieldType() == BaseServiceTypeFieldService.FieldTypeEnum.LINK_FORM) {
                        fieldValue.setEnumValues(null);
                    } else {
                        fieldValue.setEnumValues(baseServiceTypeFieldEntity.getEnumValues());
                    }
                    fieldValue.setFieldValue(dynamicFieldVo.getValue());
                    fieldValue.setDelFlag(YesNoEnum.NO.getCode());
                    quoteFieldValues.add(fieldValue);
                }
            }
            quoteItem.setSrmTenderBidderQuoteFieldValueList(quoteFieldValues);
            return quoteItem;
        }).toList();
        this.saveBatch(bidderQuoteItems);

        // 5. 批量保存动态字段
        List<SrmTenderBidderQuoteFieldValue> allQuoteFieldValues = Lists.newArrayList();
        for (SrmTenderBidderQuoteItem bidderQuoteItem : bidderQuoteItems) {
            if (CollectionUtils.isNotEmpty(bidderQuoteItem.getSrmTenderBidderQuoteFieldValueList())) {
                bidderQuoteItem.getSrmTenderBidderQuoteFieldValueList().forEach(item -> item.setQuoteItemId(bidderQuoteItem.getId()));
                allQuoteFieldValues.addAll(bidderQuoteItem.getSrmTenderBidderQuoteFieldValueList());
            }
        }
        List<Long> quoteItemIds = bidderQuoteItems.stream().map(SrmTenderBidderQuoteItem::getId).toList();
        srmTenderBidderQuoteFieldValueService.lambdaUpdate()
                .eq(SrmTenderBidderQuoteFieldValue::getNoticeId, tenderNotice.getId())
                .eq(SrmTenderBidderQuoteFieldValue::getSectionId, section.getId())
                .eq(SrmTenderBidderQuoteFieldValue::getTenantSupplierId, tenantSupplierId)
                .in(SrmTenderBidderQuoteFieldValue::getQuoteItemId, quoteItemIds)
                .remove();
        if (CollectionUtils.isNotEmpty(allQuoteFieldValues)) {
            srmTenderBidderQuoteFieldValueService.saveBatch(allQuoteFieldValues);
        }

        // 6. 批量保存附件
        List<SrmProjectAttachment> attachments = Lists.newArrayList();
        SrmProjectAttachment quoteAttachment = new SrmProjectAttachment();
        quoteAttachment.setProjectId(project.getId());
        quoteAttachment.setBusinessId(supplierResponse.getId());
        quoteAttachment.setBusinessGroup(String.valueOf(roundNo));
        quoteAttachment.setBusinessType(AttachmentTypeEnum.QUOTE_ATTACHMENT);
        quoteAttachment.setFileName(req.getQuoteAttachmentPath());
        quoteAttachment.setFilePath(req.getQuoteAttachmentPath());
        quoteAttachment.setDelFlag(YesNoEnum.NO.getCode());
        attachments.add(quoteAttachment);
        if (StringUtils.isNotBlank(req.getOtherAttachmentPath())) {
            SrmProjectAttachment otherAttachment = new SrmProjectAttachment();
            otherAttachment.setProjectId(project.getId());
            otherAttachment.setBusinessId(supplierResponse.getId());
            otherAttachment.setBusinessGroup(String.valueOf(roundNo));
            otherAttachment.setBusinessType(AttachmentTypeEnum.QUOTE_OTHER_ATTACHMENT);
            otherAttachment.setFileName(req.getOtherAttachmentName());
            otherAttachment.setFilePath(req.getOtherAttachmentPath());
            otherAttachment.setDelFlag(YesNoEnum.NO.getCode());
            attachments.add(otherAttachment);
        }
        srmProjectAttachmentService.lambdaUpdate()
                .eq(SrmProjectAttachment::getBusinessId, tenderNotice.getId())
                .eq(SrmProjectAttachment::getBusinessGroup, String.valueOf(roundNo))
                .in(SrmProjectAttachment::getBusinessType, Lists.newArrayList(AttachmentTypeEnum.QUOTE_ATTACHMENT, AttachmentTypeEnum.QUOTE_OTHER_ATTACHMENT))
                .remove();
        srmProjectAttachmentService.saveBatch(attachments);

        // 5. 更新供应商响应表的轮次和状态
        supplierResponse.setQuoteStatus(QuoteStatusEnum.COMPLETED);
        supplierResponse.setCurrentRound(roundNo);
        supplierResponse.setNewestQuoteTime(LocalDateTime.now());
        supplierResponse.setNewestQuoteIp(IpUtils.getClientIP());
        srmTenderSupplierResponseService.updateById(supplierResponse);

        // 6. 更新项目进度状态
        srmProcurementProjectService.updateProgressStatus(project.getProjectCode(), ProjectProgressStatusEnum.QUOTING, false);
    }


    @Override
    public List<Pair<String, String>> getQuoteAgainSupplierList(QuoteAgainSupplierQueryReq req) {
        List<SrmTenderSupplierResponse> quoteCompleteResponse = srmTenderSupplierResponseService.lambdaQuery()
                .eq(SrmTenderSupplierResponse::getQuoteStatus, QuoteStatusEnum.COMPLETED)
                .eq(SrmTenderSupplierResponse::getNoticeId, req.getNoticeId())
                .eq(SrmTenderSupplierResponse::getSectionId, req.getSectionId())
                .eq(SrmTenderSupplierResponse::getCurrentRound, req.getCurrentQuoteRound())
                .like(StringUtils.isNotBlank(req.getSupplierName()), SrmTenderSupplierResponse::getSupplierName, req.getSupplierName())
                .list();
        if (CollectionUtils.isEmpty(quoteCompleteResponse)) {
            return Lists.newArrayList();
        }
        return quoteCompleteResponse.stream().map(response ->
                new Pair<>(String.valueOf(response.getTenantSupplierId()), response.getSupplierName())).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void quoteAgain(QuoteAgainReq req) {
        final LocalDateTime now = LocalDateTime.now();
        ExceptionUtil.check(req.getLatestQuoteEndTime().isBefore(now)
                || req.getBidOpenTime().isBefore(now), GlobalResultCode.INVALID_STATE, "报价结束时间或开标时间不能早于当前时间");
        ExceptionUtil.checkNotEmpty(req.getSectionAndTenantSuppliersList(), "请选择标段和供应商");
        SrmTenderNotice tenderNotice = srmTenderNoticeService.getById(req.getNoticeId());
        ExceptionUtil.checkNonNull(tenderNotice, "招标公告不存在");
        final SrmProcurementProject project = srmProcurementProjectService.getById(tenderNotice.getProjectId());
        ExceptionUtil.checkNonNull(project, "项目不存在");
        ExceptionUtil.check(project.getSourcingType() == BaseServiceTypeFieldService.BuyWayEnum.ZB, GlobalResultCode.INVALID_STATE, "招标项目无法多次报价");

        List<SrmTenderOpen> tenderOpenList = srmTenderOpenService.lambdaQuery()
                .eq(SrmTenderOpen::getNoticeId, tenderNotice.getId())
                .list();
        ExceptionUtil.checkNotEmpty(tenderOpenList, "开标信息为空");
        OptionalInt maxRoundOpt = tenderOpenList.stream().mapToInt(SrmTenderOpen::getCurrentRound).max();
        ExceptionUtil.check(maxRoundOpt.isEmpty(), GlobalResultCode.INVALID_STATE, "招标项目无开标信息");

        // 再次报价的标段，必须为已开标
        List<Long> selectionIds = req.getSectionAndTenantSuppliersList().stream().map(QuoteAgainReq.SectionAndTenantSuppliers::getSectionId).toList();
        List<SrmTenderOpen> tenderOpenAgainList = tenderOpenList.stream()
                .filter(item -> selectionIds.contains(item.getSectionId()) && item.getOpenStatus() == OpenStatusEnum.OPENED)
                .toList();
        ExceptionUtil.check(CollectionUtils.isEmpty(tenderOpenAgainList) || tenderOpenAgainList.size() != selectionIds.size(), GlobalResultCode.INVALID_STATE, "未开标或开标已结束，无法再次报价");
        final int newRound = tenderOpenAgainList.get(0).getCurrentRound() + 1;
        ExceptionUtil.check(newRound <= maxRoundOpt.getAsInt(), GlobalResultCode.INVALID_STATE, "当前轮次已结束，无法再次报价");

        List<SrmTenderSupplierResponse> allSupplierResponse = Lists.newArrayList();
        for (QuoteAgainReq.SectionAndTenantSuppliers sectionAndTenantSuppliers : req.getSectionAndTenantSuppliersList()) {
            ExceptionUtil.checkNotEmpty(sectionAndTenantSuppliers.getTenantSupplierIds(), "未请选择供应商");
            List<SrmTenderSupplierResponse> quoteCompleteResponse = srmTenderSupplierResponseService.lambdaQuery()
                    .eq(SrmTenderSupplierResponse::getQuoteStatus, QuoteStatusEnum.COMPLETED)
                    .eq(SrmTenderSupplierResponse::getNoticeId, req.getNoticeId())
                    .eq(SrmTenderSupplierResponse::getSectionId, sectionAndTenantSuppliers.getSectionId())
                    .eq(SrmTenderSupplierResponse::getCurrentRound, (newRound -1))
                    .in(SrmTenderSupplierResponse::getTenantSupplierId, sectionAndTenantSuppliers.getTenantSupplierIds())
                    .list();
            ExceptionUtil.checkNotEmpty(quoteCompleteResponse, "供应商未完成过上一轮报价，无法再次报价");
            allSupplierResponse.addAll(quoteCompleteResponse);
            List<Long> quoteCompleteSupplierIds = quoteCompleteResponse.stream().map(SrmTenderSupplierResponse::getTenantSupplierId).distinct().toList();
            ExceptionUtil.check(!CollectionUtils.isEqualCollection(sectionAndTenantSuppliers.getTenantSupplierIds(), quoteCompleteSupplierIds), GlobalResultCode.INVALID_PARAMS, "部分供应商未完成上一轮过报价，无法再次报价");
            quoteCompleteResponse.forEach(item -> item.setQuoteStatus(QuoteStatusEnum.WAITING));
            srmTenderSupplierResponseService.updateBatchById(quoteCompleteResponse);

            List<SrmTenderBidderSupplierQuoteAgain> quoteAgainList = sectionAndTenantSuppliers.getTenantSupplierIds().stream().map(item -> {
                SrmTenderBidderSupplierQuoteAgain quoteAgain = new SrmTenderBidderSupplierQuoteAgain();
                quoteAgain.setProjectId(tenderNotice.getProjectId());
                quoteAgain.setNoticeId(tenderNotice.getId());
                quoteAgain.setSectionId(sectionAndTenantSuppliers.getSectionId());
                quoteAgain.setTenantSupplierId(item);
                quoteAgain.setQuoteAgainRound(newRound);
                quoteAgain.setQuoteEndTime(req.getLatestQuoteEndTime());
                quoteAgain.setDelFlag(YesNoEnum.NO.getCode());
                return quoteAgain;
            }).toList();
            srmTenderBidderSupplierQuoteAgainService.saveBatch(quoteAgainList);
        }

        QuoteAgainBidOpenInfoQueryReq againBidOpenInfoQueryReq = new QuoteAgainBidOpenInfoQueryReq();
        againBidOpenInfoQueryReq.setNoticeId(req.getNoticeId());
        againBidOpenInfoQueryReq.setRoundNo(newRound);


        for (SrmTenderOpen tenderOpen : tenderOpenAgainList) {
            tenderOpen.setLatestQuoteStartTime(now);
            tenderOpen.setLatestQuoteEndTime(req.getLatestQuoteEndTime());
            tenderOpen.setOpenTime(req.getBidOpenTime());
            tenderOpen.setCurrentRound(newRound);
            tenderOpen.setOpenStatus(OpenStatusEnum.TO_BID_OPEN);
        }
        srmTenderOpenService.updateBatchById(tenderOpenAgainList);
        tenderNotice.setQuoteStartTime(now);
        tenderNotice.setQuoteEndTime(req.getLatestQuoteEndTime());
        tenderNotice.setBidOpenTime(req.getBidOpenTime());
        srmTenderNoticeService.updateById(tenderNotice);

        srmProcurementProjectService.updateProgressStatus(project.getProjectCode(), ProjectProgressStatusEnum.QUOTING, false);

        /*
        try {
            LinkedHashMap<String, String> params = Maps.newLinkedHashMap();
            params.put("noticeTitle", tenderNotice.getNoticeTitle());
            params.put("newRound", String.valueOf(newRound));
            params.put("quoteEndTime", req.getLatestQuoteEndTime().format(DatePattern.NORM_DATETIME_FORMATTER));

            LinkedHashMap<String, String> testParams = Maps.newLinkedHashMap();
            testParams.put("code", "2212");
            // 发送通知短信
            List<String> mobileList = allSupplierResponse.stream().map(SrmTenderSupplierResponse::getContactPhone).distinct().toList();
            MessageSmsDTO messageSmsDTO = MessageSmsDTO.builder()
                    .biz(SmsBizCodeConstant.QUOTE_AGAIN_NOTIFY)
                    .build();
            messageSmsDTO.setMobiles(mobileList);
            messageSmsDTO.setParams(testParams);
            sysMessageService.sendSms(messageSmsDTO);
        } catch (Exception e) {
            log.error("再次报价发送通知短信失败", e);
        }
         */

    }

    @Override
    public Page<QuoteQueryByMaterialResp> queryQuoteListByMaterial(BidderQuoteQueryReq req, Page<QuoteQueryByMaterialResp> page) {
        // 验证开标状态 - 基于noticeId和projectId查询
        final boolean couldLookQuote = this.canLookQuote(req);

        // 1. 查询项目物料 - 基于noticeId和projectId
        Page<SrmProcurementProjectItem> projectItemPage = srmProcurementProjectItemService.lambdaQuery()
                .eq(SrmProcurementProjectItem::getProjectId, req.getProjectId())
                .eq(Objects.nonNull(req.getSectionId()), SrmProcurementProjectItem::getSectionId, req.getSectionId())
                .like(StringUtils.isNotBlank(req.getMaterialName()), SrmProcurementProjectItem::getMaterialName, req.getMaterialName())
                .like(StringUtils.isNotBlank(req.getMaterialCode()), SrmProcurementProjectItem::getMaterialCode, req.getMaterialCode())
                .in(CollectionUtils.isNotEmpty(req.getMaterialCodeList()), SrmProcurementProjectItem::getMaterialCode, req.getMaterialCodeList())
                .orderByAsc(SrmProcurementProjectItem::getMaterialCode)
                .page(new Page<>(page.getCurrent(), page.getSize()));
        Page<QuoteQueryByMaterialResp> respPage = new Page<>();
        respPage.setTotal(projectItemPage.getTotal());
        respPage.setCurrent(projectItemPage.getCurrent());
        respPage.setSize(projectItemPage.getSize());
        if (CollectionUtils.isEmpty(projectItemPage.getRecords())) {
            return respPage;
        }
        List<QuoteQueryByMaterialResp> respRecords = projectItemPage.getRecords().stream().map(item -> {
            QuoteQueryByMaterialResp quoteQueryByMaterialResp = new QuoteQueryByMaterialResp();
            BeanUtils.copyProperties(item, quoteQueryByMaterialResp);
            quoteQueryByMaterialResp.setProjectItemId(item.getId());
            return quoteQueryByMaterialResp;
        }).toList();

        // 2. 查询项目物料的报价（使用SQL联查获取供应商名称）
        List<Long> projectItemIds = projectItemPage.getRecords().stream().map(SrmProcurementProjectItem::getId).toList();
        List<SrmTenderBidderQuoteItem> quoteItemList = baseMapper.queryQuoteItemsByNoticeAndProject(
                req.getNoticeId(),
                req.getProjectId(),
                req.getSectionId(),
                req.getRoundNo(),
                req.getAwarded(),
                req.getTenantSupplierId(),
                projectItemIds);
        if (CollectionUtils.isNotEmpty(quoteItemList)) {
            // 报价的动态字段
            List<Long> quoteItemIds = quoteItemList.stream().map(SrmTenderBidderQuoteItem::getId).toList();
            List<SrmTenderBidderQuoteFieldValue> quoteFieldValues = srmTenderBidderQuoteFieldValueService.lambdaQuery()
                    .eq(SrmTenderBidderQuoteFieldValue::getNoticeId, req.getNoticeId())
                    .eq(SrmTenderBidderQuoteFieldValue::getProjectId, req.getProjectId())
                    .in(SrmTenderBidderQuoteFieldValue::getQuoteItemId, quoteItemIds)
                    .list();
            Map<Long, List<SrmTenderBidderQuoteFieldValue>> quoteFieldValueQuoteIdGroup = Optional.ofNullable(quoteFieldValues).stream()
                    .flatMap(Collection::stream).collect(Collectors.groupingBy(SrmTenderBidderQuoteFieldValue::getQuoteItemId));
            // 附件
            List<Long> tenantSupplierResponseIds = quoteItemList.stream().map(SrmTenderBidderQuoteItem::getTenderSupplierResponseId).distinct().toList();
            List<SrmProjectAttachment> attachmentList = srmProjectAttachmentService.lambdaQuery().in(SrmProjectAttachment::getBusinessId, tenantSupplierResponseIds)
                    .in(SrmProjectAttachment::getBusinessType, Lists.newArrayList(AttachmentTypeEnum.QUOTE_ATTACHMENT, AttachmentTypeEnum.QUOTE_OTHER_ATTACHMENT))
                    .list();

            Map<Long, List<SrmTenderBidderQuoteItem>> quoteItemMap = quoteItemList.stream()
                    .collect(Collectors.groupingBy(SrmTenderBidderQuoteItem::getProjectItemId));
            respRecords.forEach(item -> {
                // 单条物料的所有供应商报价
                List<SrmTenderBidderQuoteItem> quoteItems = quoteItemMap.get(item.getProjectItemId());
                if (CollectionUtils.isNotEmpty(quoteItems)) {
                    item.setSupplierQuoteList(quoteItems.stream().map(quoteItem -> {
                        QuoteQueryByMaterialResp.SupplierQuote supplierQuote = new QuoteQueryByMaterialResp.SupplierQuote();
                        BeanUtils.copyProperties(quoteItem, supplierQuote);
                        if (couldLookQuote) {
                            supplierQuote.setQuoteItemId(quoteItem.getId());
                            supplierQuote.setSrmTenderBidderQuoteFieldValues(quoteFieldValueQuoteIdGroup.get(quoteItem.getId()));

                            // 附件
                            List<SrmProjectAttachment> subAttatchmentList = Optional.ofNullable(attachmentList).stream().flatMap(Collection::stream)
                                    .filter(attachment -> attachment.getBusinessId().equals(quoteItem.getTenderSupplierResponseId())
                                            && Objects.equals(attachment.getBusinessGroup(), String.valueOf(quoteItem.getRoundNo())))
                                    .toList();
                            supplierQuote.setAttachmentList(subAttatchmentList);
                        }
                        return supplierQuote;
                    }).sorted(Comparator.comparing(QuoteQueryByMaterialResp.SupplierQuote::getTenantSupplierId)
                            .thenComparing(QuoteQueryByMaterialResp.SupplierQuote::getRoundNo)).toList());
                }
            });
        }

        Map<Long, BaseUsageLocationEntity> locationEntityMap = this.getLocationMap(projectItemPage.getRecords());
        Map<Long, String> qualityIndicatorEntityMap = this.getQualityIndicatorMap(projectItemPage.getRecords());
        Map<Long, List<BaseServiceTypeFieldEntity>> baseServiceTypeFieldServiceTypeIdGroup = this.getServiceTypeFieldMap(projectItemPage.getRecords());

        for (QuoteQueryByMaterialResp e : respRecords) {
            BaseUsageLocationEntity baseUsageLocationEntity = locationEntityMap.get(e.getUsageLocationId());
            if (Objects.nonNull(baseUsageLocationEntity)) {
                e.setUsageLocationName(baseUsageLocationEntity.getLocationName());
                e.setUsageLocationRegion(baseUsageLocationEntity.getProvince() + "/" + baseUsageLocationEntity.getCity() + "/" + baseUsageLocationEntity.getDistrict());
                e.setUsageLocationAddress(baseUsageLocationEntity.getAddress());
            }
            e.setQualityIndicatorName(qualityIndicatorEntityMap.get(e.getQualityIndicatorId()));
            e.setBaseServiceTypeFieldEntities(baseServiceTypeFieldServiceTypeIdGroup.get(e.getProjectItemId()));
        }
        respPage.setRecords(respRecords);
        return respPage;
    }


    @Override
    public List<QuoteQueryBySupplierResp> queryQuoteListBySupplier(BidderQuoteQueryReq req) {
        // 验证开标状态 - 基于noticeId和projectId查询
        final boolean couldLookQuote = this.canLookQuote(req);

        // 查询招标公告获取评标方式
        SrmTenderNotice tenderNotice = srmTenderNoticeService.getById(req.getNoticeId());
        ExceptionUtil.checkNonNull(tenderNotice, "招标公告不存在");
        TenderWayEnum tenderWay = tenderNotice.getTenderWay();

        // 线下评标时不校验标段物料，因为可能没有登记物料信息
        if (tenderWay == TenderWayEnum.ONLINE) {
            // 线上评标时校验物料总数
            Long totalMaterialCount = srmProcurementProjectItemService.lambdaQuery()
                    .eq(SrmProcurementProjectItem::getProjectId, req.getProjectId())
                    .eq(SrmProcurementProjectItem::getSectionId, req.getSectionId())
                    .count();
            ExceptionUtil.checkNonNull(totalMaterialCount, "该标段没有物料");
        }

        // 参与报价总轮次
        List<QuoteRoundCountVo> quoteRoundCountVos = couldLookQuote ?
                baseMapper.queryQuoteRoundCountByNoticeAndProject(req.getProjectId(), req.getNoticeId(), req.getSectionId())
                : Lists.newArrayList();
        if (CollectionUtils.isEmpty(quoteRoundCountVos)) {
            return Lists.newArrayList();
        }
        Map<Long, Integer> quoteRoundCountVoMap = quoteRoundCountVos.stream().collect(Collectors.toMap(QuoteRoundCountVo::getTenantSupplierId, QuoteRoundCountVo::getRoundCount));
        List<Long> tenantSupplierIds = Objects.nonNull(req.getTenantSupplierId()) ? Lists.newArrayList(req.getTenantSupplierId())
                : quoteRoundCountVos.stream().map(QuoteRoundCountVo::getTenantSupplierId).toList();

        // 报价统计
        List<QuoteStatisticsVo> quoteStatisticsVos = couldLookQuote ? baseMapper.queryQuoteStatisticsByNoticeAndProject(req.getProjectId(),
                req.getNoticeId(), req.getSectionId(), req.getRoundNo(), tenantSupplierIds) : Lists.newArrayList();
        Map<Long, QuoteStatisticsVo> quoteStatisticsVoMap = Optional.ofNullable(quoteStatisticsVos).stream().flatMap(Collection::stream)
                .collect(Collectors.toMap(QuoteStatisticsVo::getTenantSupplierId, Function.identity()));

        // 查询供应商响应 - 基于noticeId和projectId
        List<SrmTenderSupplierResponse> supplierResponseList = srmTenderSupplierResponseService.lambdaQuery()
                .eq(SrmTenderSupplierResponse::getNoticeId, req.getNoticeId())
                .eq(SrmTenderSupplierResponse::getProjectId, req.getProjectId())
                .eq(SrmTenderSupplierResponse::getSectionId, req.getSectionId())
                .in(SrmTenderSupplierResponse::getTenantSupplierId, tenantSupplierIds)
                .like(StringUtils.isNotBlank(req.getSupplierName()), SrmTenderSupplierResponse::getSupplierName, req.getSupplierName())
                .orderByAsc(SrmTenderSupplierResponse::getSupplierName)
//                只显示推荐中标的
                .eq(Objects.nonNull(req.getIsRecommendedWinner()), SrmTenderSupplierResponse::getIsRecommendedWinner, req.getIsRecommendedWinner())
                .list();
        if (CollectionUtils.isEmpty(supplierResponseList)) {
            return Lists.newArrayList();
        }

        List<SrmProcurementProjectItem> projectItems = srmProcurementProjectItemService.lambdaQuery()
                .eq(SrmProcurementProjectItem::getProjectId, req.getProjectId())
                .eq(SrmProcurementProjectItem::getSectionId, req.getSectionId()).list();
        List<Long> projectItemIds = projectItems.stream().map(SrmProcurementProjectItem::getId).toList();

        List<SrmTenderBidderQuoteItem> quoteItemList = Lists.newArrayList();
        List<SrmProjectAttachment> attachmentList = Lists.newArrayList();
        SrmProcurementProject project = srmProcurementProjectService.getById(req.getProjectId());
        if (project.getSourcingType() == BaseServiceTypeFieldService.BuyWayEnum.QZXJ){
            Map<Long, BaseUsageLocationEntity> locationEntityMap = this.getLocationMap(projectItems);
            Map<Long, String> qualityIndicatorEntityMap = this.getQualityIndicatorMap(projectItems);
            Map<Long, List<BaseServiceTypeFieldEntity>> baseServiceTypeFieldServiceTypeIdGroup = this.getServiceTypeFieldMap(projectItems);
            for (SrmProcurementProjectItem e : projectItems) {
                BaseUsageLocationEntity baseUsageLocationEntity = locationEntityMap.get(e.getUsageLocationId());
                if (Objects.nonNull(baseUsageLocationEntity)) {
                    e.setUsageLocationName(baseUsageLocationEntity.getLocationName());
                    e.setUsageLocationRegion(baseUsageLocationEntity.getProvince() + "/" + baseUsageLocationEntity.getCity() + "/" + baseUsageLocationEntity.getDistrict());
                    e.setUsageLocationAddress(baseUsageLocationEntity.getAddress());
                }
                e.setQualityIndicatorName(qualityIndicatorEntityMap.get(e.getQualityIndicatorId()));
                e.setBaseServiceTypeFieldEntityList(baseServiceTypeFieldServiceTypeIdGroup.get(e.getId()));
            }
            // 报价情况
            quoteItemList = baseMapper.queryQuoteItemsByNoticeAndProject(
                    req.getNoticeId(),
                    req.getProjectId(),
                    req.getSectionId(),
                    req.getRoundNo(),
                    req.getAwarded(),
                    req.getTenantSupplierId(),
                    projectItemIds);
            if (CollectionUtils.isNotEmpty(quoteItemList)) {
                // 报价的动态字段
                List<Long> quoteItemIds = quoteItemList.stream().map(SrmTenderBidderQuoteItem::getId).toList();
                List<SrmTenderBidderQuoteFieldValue> quoteFieldValues = srmTenderBidderQuoteFieldValueService.lambdaQuery()
                        .eq(SrmTenderBidderQuoteFieldValue::getNoticeId, req.getNoticeId())
                        .eq(SrmTenderBidderQuoteFieldValue::getProjectId, req.getProjectId())
                        .in(SrmTenderBidderQuoteFieldValue::getQuoteItemId, quoteItemIds)
                        .list();
                Map<Long, List<SrmTenderBidderQuoteFieldValue>> quoteFieldValueQuoteIdGroup = Optional.ofNullable(quoteFieldValues).stream()
                        .flatMap(Collection::stream).collect(Collectors.groupingBy(SrmTenderBidderQuoteFieldValue::getQuoteItemId));
                // 附件
                List<Long> tenantSupplierResponseIds = quoteItemList.stream().map(SrmTenderBidderQuoteItem::getTenderSupplierResponseId).distinct().toList();
                attachmentList = srmProjectAttachmentService.lambdaQuery().in(SrmProjectAttachment::getBusinessId, tenantSupplierResponseIds)
                        .in(SrmProjectAttachment::getBusinessType, Lists.newArrayList(AttachmentTypeEnum.QUOTE_ATTACHMENT, AttachmentTypeEnum.QUOTE_OTHER_ATTACHMENT))
                        .list();

                quoteItemList.forEach(item -> item.setSrmTenderBidderQuoteFieldValueList(quoteFieldValueQuoteIdGroup.get(item.getId())));
            }
        }

        Map<Long, List<SrmTenderBidderQuoteItem>> quoteSupplierGroup = quoteItemList.stream().collect(Collectors.groupingBy(SrmTenderBidderQuoteItem::getTenantSupplierId));
        Map<Long, List<SrmProjectAttachment>> attachmentSupplierGroup = attachmentList.stream().collect(Collectors.groupingBy(SrmProjectAttachment::getBusinessId));
        return supplierResponseList.stream().map(item -> {
            QuoteQueryBySupplierResp quoteQueryBySupplierResp = new QuoteQueryBySupplierResp();
            BeanUtils.copyProperties(item, quoteQueryBySupplierResp);

            // 设置报价轮次
            quoteQueryBySupplierResp.setQuoteRoundCount(quoteRoundCountVoMap.get(item.getTenantSupplierId()));

            // 设置报价物料数
            quoteQueryBySupplierResp.setTotalMaterialCount(projectItems.size());
            if (project.getSourcingType() == BaseServiceTypeFieldService.BuyWayEnum.QZXJ) {
                quoteQueryBySupplierResp.setProjectItemList(projectItems);
                quoteQueryBySupplierResp.setQuoteItemList(quoteSupplierGroup.get(item.getTenantSupplierId()));
                quoteQueryBySupplierResp.setQuoteAttachmentList(attachmentSupplierGroup.get(item.getId()));
            }
            // 设置报价统计信息
            QuoteStatisticsVo quoteStatisticsVo = quoteStatisticsVoMap.get(item.getTenantSupplierId());
            if (Objects.nonNull(quoteStatisticsVo)) {
                quoteQueryBySupplierResp.setQuoteMaterialCount(quoteStatisticsVo.getQuoteMaterialCount());
                quoteQueryBySupplierResp.setTotalQuoteAmount(quoteStatisticsVo.getTotalQuoteAmount());
                quoteQueryBySupplierResp.setQuoteIp(quoteStatisticsVo.getQuoteIp());
            }

            // 设置中标相关信息
            quoteQueryBySupplierResp.setWinnerCandidateOrder(
                    item.getWinnerCandidateOrder() != null ? item.getWinnerCandidateOrder().getDesc() : null
            );
            quoteQueryBySupplierResp.setIsRecommendedWinner(item.getIsRecommendedWinner());
            quoteQueryBySupplierResp.setIsWin(item.getIsWin());

            return quoteQueryBySupplierResp;
        }).toList();
    }

    private boolean canLookQuote(BidderQuoteQueryReq req) {
        boolean canLookQuote = true;
//        List<SrmTenderOpen> tenderOpens = srmTenderOpenService.lambdaQuery()
//                .eq(SrmTenderOpen::getNoticeId, req.getNoticeId())
//                .eq(SrmTenderOpen::getProjectId, req.getProjectId())
//                .eq(Objects.nonNull(req.getSectionId()), SrmTenderOpen::getSectionId, req.getSectionId())
//                .list();
//        if(req.getAwarded() == null || !req.getAwarded()){
//            ExceptionUtil.checkNonNull(req.getSectionId(), "标段不能为空！");
//            ExceptionUtil.checkNonNull(req.getRoundNo(), "请选择报价轮次！");
//            Optional<SrmTenderOpen> tenderOpen = tenderOpens.stream().filter(item ->
//                    (Objects.equals(item.getCurrentRound(), req.getRoundNo()) && item.getOpenStatus() == OpenStatusEnum.OPENED)
//                            || item.getCurrentRound() > req.getRoundNo()).findAny();
//            canLookQuote = tenderOpen.isPresent();
//        }
        return canLookQuote;
    }


    private Map<Long, BaseUsageLocationEntity> getLocationMap(List<SrmProcurementProjectItem> projectItems){
        // 牧场
        List<Long> locationIds = projectItems.stream().map(SrmProcurementProjectItem::getUsageLocationId).distinct().toList();
        Map<Long, BaseUsageLocationEntity> locationEntityMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(locationIds)) {
            List<BaseUsageLocationEntity> baseUsageLocationEntities = baseUsageLocationService.listByIds(locationIds);
            locationEntityMap = Optional.ofNullable(baseUsageLocationEntities).stream().flatMap(Collection::stream)
                    .collect(Collectors.toMap(BaseUsageLocationEntity::getId, Function.identity()));
        }
        return locationEntityMap;
    }

    private Map<Long, String> getQualityIndicatorMap(List<SrmProcurementProjectItem> projectItems){
        // 质量指标
        List<Long> qualityIds = projectItems.stream().map(SrmProcurementProjectItem::getQualityIndicatorId).distinct().toList();
        Map<Long, String> qualityIndicatorEntityMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(qualityIds)) {
            List<BaseQualityIndicatorEntity> baseQualityIndicatorEntities = baseQualityIndicatorService.listByIds(qualityIds);
            qualityIndicatorEntityMap = Optional.ofNullable(baseQualityIndicatorEntities).stream().flatMap(Collection::stream)
                    .collect(Collectors.toMap(BaseQualityIndicatorEntity::getId, BaseQualityIndicatorEntity::getIndicatorName));
        }
        return qualityIndicatorEntityMap;
    }

    private Map<Long, List<BaseServiceTypeFieldEntity>> getServiceTypeFieldMap(List<SrmProcurementProjectItem> projectItems){
        //动态字段配置
        List<Long> serviceTypeIds = projectItems.stream().map(SrmProcurementProjectItem::getServiceTypeId).distinct().toList();
        List<BaseServiceTypeFieldService.BuyWayEnum> sourcingTypes = projectItems.stream().map(SrmProcurementProjectItem::getSourcingType).distinct().toList();
        List<BaseServiceTypeFieldEntity> baseServiceTypeFieldEntities = baseServiceTypeFieldService.lambdaQuery()
                .in(BaseServiceTypeFieldEntity::getServiceTypeId, serviceTypeIds)
                .in(BaseServiceTypeFieldEntity::getBuyWay, sourcingTypes)
                .eq(BaseServiceTypeFieldEntity::getDelFlag, 0).list();
        Map<Long, List<BaseServiceTypeFieldEntity>> baseServiceTypeFieldServiceTypeIdGroup = Maps.newHashMap();
        for (SrmProcurementProjectItem record : projectItems) {
            List<BaseServiceTypeFieldEntity> subTypeFields = baseServiceTypeFieldEntities.stream().filter(
                    field -> field.getServiceTypeId().equals(record.getServiceTypeId())
                            && field.getBuyWay().equals(record.getSourcingType())).toList();
            baseServiceTypeFieldServiceTypeIdGroup.put(record.getId(), subTypeFields);
        }
        return baseServiceTypeFieldServiceTypeIdGroup;
    }


    @Override
    public TenderBidderItemsResp queryAwardedItems(com.ylz.saas.req.AwardedItemsQueryReq req) {
        TenderBidderItemsResp resp = new TenderBidderItemsResp();

        // 1. 查询中标明细（使用SQL联查获取供应商名称）
        List<SrmTenderBidderQuoteItem> awardedItems = baseMapper.queryAwardedItemsWithSupplierName(
                req.getNoticeId(),
                req.getProjectId(),
                req.getMaterialCode(),
                req.getMaterialName(),
                req.getTenantSupplierId(),
                req.getSectionId()
        );

        if (CollectionUtils.isNotEmpty(awardedItems)) {
            // 2. 查询动态字段值
            List<Long> quoteItemIds = awardedItems.stream().map(SrmTenderBidderQuoteItem::getId).toList();
            List<SrmTenderBidderQuoteFieldValue> quoteFieldValues = srmTenderBidderQuoteFieldValueService.lambdaQuery()
                    .eq(SrmTenderBidderQuoteFieldValue::getNoticeId, req.getNoticeId())
                    .eq(SrmTenderBidderQuoteFieldValue::getProjectId, req.getProjectId())
                    .in(SrmTenderBidderQuoteFieldValue::getQuoteItemId, quoteItemIds)
                    .list();

            // 3. 按报价明细ID分组动态字段
            Map<Long, List<SrmTenderBidderQuoteFieldValue>> fieldValueMap = quoteFieldValues.stream()
                    .collect(Collectors.groupingBy(SrmTenderBidderQuoteFieldValue::getQuoteItemId));

            // 4. 为每个报价明细设置动态字段
            awardedItems.forEach(item -> {
                List<SrmTenderBidderQuoteFieldValue> fieldValues = fieldValueMap.get(item.getId());
                if (CollectionUtils.isNotEmpty(fieldValues)) {
                    item.setSrmTenderBidderQuoteFieldValueList(fieldValues);
                }
            });


        }
        //5. 查询附件
        SrmTenderOpen tenderOpen = srmTenderOpenService.lambdaQuery()
                .eq(SrmTenderOpen::getSectionId, req.getSectionId())
                .eq(SrmTenderOpen::getProjectId, req.getProjectId())
                .eq(SrmTenderOpen::getNoticeId, req.getNoticeId())
                .one();
        final int roundNo = tenderOpen == null ? 1 : tenderOpen.getCurrentRound();
        SrmTenderSupplierResponse supplierResponse = srmTenderSupplierResponseService.lambdaQuery()
                .eq(SrmTenderSupplierResponse::getNoticeId, req.getNoticeId())
                .eq(SrmTenderSupplierResponse::getSectionId, req.getSectionId())
                .eq(SrmTenderSupplierResponse::getTenantSupplierId, req.getTenantSupplierId())
                .one();
        List<SrmProjectAttachment> attachmentList = srmProjectAttachmentService.lambdaQuery()
                .eq(SrmProjectAttachment::getBusinessId, supplierResponse.getId())
                .eq(SrmProjectAttachment::getBusinessGroup, String.valueOf(roundNo))
                .in(SrmProjectAttachment::getBusinessType, Lists.newArrayList(AttachmentTypeEnum.QUOTE_ATTACHMENT, AttachmentTypeEnum.QUOTE_OTHER_ATTACHMENT))
                .list();


//        srmProjectAttachmentService.
        resp.setSrmProjectAttachments(attachmentList.stream().filter(e -> AttachmentTypeEnum.QUOTE_ATTACHMENT == e.getBusinessType()).toList());
        resp.setOtherSrmProjectAttachments(attachmentList.stream().filter(e -> AttachmentTypeEnum.QUOTE_OTHER_ATTACHMENT == e.getBusinessType()).toList());
        resp.setSrmTenderBidderQuoteItems(awardedItems);


        return resp;
    }

    @Override
    public List<com.ylz.saas.resp.AwardedSupplierResp> queryAwardedSuppliers(com.ylz.saas.req.AwardedSuppliersQueryReq req) {
        // 查询中标供应商列表（包含统计信息）
        Long noticeId = req.getNoticeId();
        List<AwardedSupplierResp> awardedSupplierResps = baseMapper.queryAwardedSuppliers(noticeId, req.getProjectId(), req.getSupplierName());
        List<SrmTenderAwardNotice> awardNoticeList = awardNoticeService.lambdaQuery().eq(SrmTenderAwardNotice::getNoticeId, noticeId).list();
        Map<Long, SrmTenderAwardNotice> tenantSupplerIdMapVo = awardNoticeList.stream().collect(
                Collectors.toMap(SrmTenderAwardNotice::getTenantSupplierId, Function.identity(), (v1, v2) -> v1));
        List<Long> resultIds = awardedSupplierResps.stream().map(AwardedSupplierResp::getResultId).filter(Objects::nonNull).toList();
        Map<Long, SrmTenderEvaluationResult> resultIdMapVo = new HashMap<>();
        if(CollectionUtils.isNotEmpty(resultIds)){
            SrmTenderEvaluationResultService resultService = SpringContextHolder.getBean(SrmTenderEvaluationResultService.class);
            List<SrmTenderEvaluationResult> evaluationResultList = resultService.lambdaQuery().in(SrmTenderEvaluationResult::getId, resultIds).list();
            resultIdMapVo.putAll(evaluationResultList.stream().collect(
                    Collectors.toMap(SrmTenderEvaluationResult::getId, Function.identity())));
        }
        awardedSupplierResps.forEach(item -> {
            SrmTenderAwardNotice srmTenderAwardNotice = tenantSupplerIdMapVo.get(item.getTenantSupplierId());
            if(srmTenderAwardNotice != null){
                item.setAwardNotice(srmTenderAwardNotice);
            }
            // 根据resultId，设置定标审批状态
            SrmTenderEvaluationResult evaluationResult = resultIdMapVo.get(item.getResultId());
            if(evaluationResult != null){
                item.setAwardReportStatus(evaluationResult.getAwardReportStatus());
            }
        });
        return awardedSupplierResps;
    }


    @Override
    public List<SrmProcurementProjectItem> queryMaterialList(QuoteMaterialQueryReq req) {
        ValidateTenderResult validateTenderResult = srmTenderSupplierResponseService.validateSupplierAndProject(req.getNoticeId(), req.getTenantSupplierId());
        SrmProcurementProject project = validateTenderResult.getProject();
        List<SrmProcurementProjectItem> projectItems = srmProcurementProjectItemService.lambdaQuery()
                .eq(SrmProcurementProjectItem::getProjectId, project.getId())
                .eq(SrmProcurementProjectItem::getSectionId, req.getSectionId())
                .like(StringUtils.isNotBlank(req.getMaterialCode()), SrmProcurementProjectItem::getMaterialCode, req.getMaterialCode())
                .like(StringUtils.isNotBlank(req.getMaterialName()), SrmProcurementProjectItem::getMaterialName, req.getMaterialName())
                .list();
        if (CollectionUtils.isEmpty(projectItems)) {
            return Lists.newArrayList();
        }
        // 查询服务类型字段
        List<Long> serviceTypeIds = projectItems.stream().map(SrmProcurementProjectItem::getServiceTypeId).toList();
        List<BaseServiceTypeFieldService.BuyWayEnum> buyWayList = projectItems.stream().map(SrmProcurementProjectItem::getSourcingType).toList();
        List<BaseServiceTypeFieldEntity> serviceTypeFields = baseServiceTypeFieldService.lambdaQuery()
                .in(BaseServiceTypeFieldEntity::getServiceTypeId, serviceTypeIds)
                .in(BaseServiceTypeFieldEntity::getBuyWay, buyWayList)
                .list();

        // 牧场
        List<Long> locationIds = projectItems.stream().map(SrmProcurementProjectItem::getUsageLocationId).distinct().toList();
        Map<Long, BaseUsageLocationEntity> locationEntityMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(locationIds)) {
            List<BaseUsageLocationEntity> baseUsageLocationEntities = baseUsageLocationService.listByIds(locationIds);
            locationEntityMap = Optional.ofNullable(baseUsageLocationEntities).stream().flatMap(Collection::stream)
                    .collect(Collectors.toMap(BaseUsageLocationEntity::getId, Function.identity()));
        }
        // 质量指标
        List<Long> qualityIds = projectItems.stream().map(SrmProcurementProjectItem::getQualityIndicatorId).distinct().toList();
        Map<Long, String> qualityIndicatorEntityMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(qualityIds)) {
            List<BaseQualityIndicatorEntity> baseQualityIndicatorEntities = baseQualityIndicatorService.listByIds(qualityIds);
            qualityIndicatorEntityMap = Optional.ofNullable(baseQualityIndicatorEntities).stream().flatMap(Collection::stream)
                    .collect(Collectors.toMap(BaseQualityIndicatorEntity::getId, BaseQualityIndicatorEntity::getIndicatorName));
        }
        for (SrmProcurementProjectItem e : projectItems) {
            BaseUsageLocationEntity baseUsageLocationEntity = locationEntityMap.get(e.getUsageLocationId());
            if (Objects.nonNull(baseUsageLocationEntity)) {
                e.setUsageLocationName(baseUsageLocationEntity.getLocationName());
                e.setUsageLocationRegion(baseUsageLocationEntity.getProvince() + "/" + baseUsageLocationEntity.getCity() + "/" + baseUsageLocationEntity.getDistrict());
                e.setUsageLocationAddress(baseUsageLocationEntity.getAddress());
            }
            e.setQualityIndicatorName(qualityIndicatorEntityMap.get(e.getQualityIndicatorId()));
            if (CollectionUtils.isNotEmpty(serviceTypeFields)) {
                List<BaseServiceTypeFieldEntity> serviceTypeFieldList = serviceTypeFields.stream()
                        .filter(item -> item.getServiceTypeId().equals(e.getServiceTypeId())
                                && item.getBuyWay().equals(e.getSourcingType()))
                        .peek(item -> {
                            if (item.getFieldType() == BaseServiceTypeFieldService.FieldTypeEnum.LINK_FORM) {
                                item.setEnumValues(JSONObject.toJSONString(baseServiceTypeFieldService.convertServiceTypeFieldToSqlDataList(item)));
                            }
                        }).toList();
                e.setBaseServiceTypeFieldEntityList(serviceTypeFieldList);
            }
        }

        return projectItems;
    }

    @Override
    public Integer quoteCount(QuoteCountReq req) {
        Long noticeId = req.getNoticeId();
        SrmTenderBidderQuoteItem quoteItem = lambdaQuery()
                .eq(SrmTenderBidderQuoteItem::getNoticeId, noticeId)
                .orderByDesc(SrmTenderBidderQuoteItem::getRoundNo)
                .last(" limit 1")
                .one();
        ExceptionUtil.checkNonNull(quoteItem, "未找到报价信息！");
        return quoteItem.getRoundNo();
    }

    @Override
    public List<String> getQuoteLessThanThreeMaterialCodeList(QuoteLessThanThresholdSupplierQueryReq req) {
        return baseMapper.getQuoteLessThanThresholdSupplierList(req);
    }

    @Override
    public List<QuoteDetailQueryResp> queryQuoteDetail(QuoteDetailQueryReq req, Page page) {
        List<SectionQuoteRoundVo> sectionQuoteRound = srmTenderOpenService.getSectionQuoteRound(req.getNoticeId());
        if (CollectionUtils.isEmpty(sectionQuoteRound)) {
            return Lists.newArrayList();
        }
        Optional<SectionQuoteRoundVo> sectionQuoteRoundOpt = sectionQuoteRound.stream()
                .filter(item -> Objects.equals(req.getSectionId(), item.getSectionId())).findFirst();
        if (sectionQuoteRoundOpt.isEmpty()) {
            return Lists.newArrayList();
        }
        SectionQuoteRoundVo sectionQuoteRoundVo = sectionQuoteRoundOpt.get();
        List<QuoteDetailQueryResp> result = Lists.newArrayList();
        for (int i = sectionQuoteRoundVo.getCurrentQuoteRound(); i >= 1; i--) {
            BidderQuoteQueryReq queryReq = new BidderQuoteQueryReq();
            queryReq.setNoticeId(req.getNoticeId());
            queryReq.setProjectId(req.getSectionId());
            queryReq.setTenantSupplierId(req.getTenantSupplierId());
            queryReq.setRoundNo(i);

            Page<QuoteQueryByMaterialResp> materialRespPage = this.queryQuoteListByMaterial(queryReq, new Page<>(page.getCurrent(), page.getSize()));
            if (CollectionUtils.isNotEmpty(materialRespPage.getRecords())) {
                QuoteDetailQueryResp resultResp = new QuoteDetailQueryResp();
                resultResp.setRoundNo(i);
                resultResp.setQuoteQueryByMaterialPage(materialRespPage);
            }
        }
        return result;
    }

    /**
     * 获取有效的供应商ID列表（与queryQuoteDetailBySupplier使用相同的逻辑）
     */
    private List<Long> getValidSupplierIds(Long projectId, Long sectionId, Long noticeId) {
        // 验证开标状态
        BidderQuoteQueryReq tempReq = new BidderQuoteQueryReq();
        tempReq.setProjectId(projectId);
        tempReq.setSectionId(sectionId);
        tempReq.setNoticeId(noticeId);
        final boolean couldLookQuote = this.canLookQuote(tempReq);

        // 参与报价总轮次
        List<QuoteRoundCountVo> quoteRoundCountVos = couldLookQuote ?
                baseMapper.queryQuoteRoundCountByNoticeAndProject(projectId, noticeId, sectionId)
                : Lists.newArrayList();
        if (CollectionUtils.isEmpty(quoteRoundCountVos)) {
            return Lists.newArrayList();
        }

        List<Long> tenantSupplierIds = quoteRoundCountVos.stream().map(QuoteRoundCountVo::getTenantSupplierId).toList();

        // 查询供应商响应 - 基于noticeId和projectId，只显示推荐中标的
        List<SrmTenderSupplierResponse> supplierResponseList = srmTenderSupplierResponseService.lambdaQuery()
                .eq(SrmTenderSupplierResponse::getNoticeId, noticeId)
                .eq(SrmTenderSupplierResponse::getProjectId, projectId)
                .eq(SrmTenderSupplierResponse::getSectionId, sectionId)
                .in(SrmTenderSupplierResponse::getTenantSupplierId, tenantSupplierIds)
                .eq(SrmTenderSupplierResponse::getIsRecommendedWinner, 1)  // 只显示推荐中标的
                .list();

        return supplierResponseList.stream().map(SrmTenderSupplierResponse::getTenantSupplierId).toList();
    }

    /**
     * 查询指定轮次的报价附件
     */
    private List<SrmProjectAttachment> queryQuoteAttachmentsByRound(Long noticeId, Long sectionId, Integer roundNo) {
        // 查询供应商响应记录
        List<SrmTenderSupplierResponse> supplierResponses = srmTenderSupplierResponseService.lambdaQuery()
                .eq(SrmTenderSupplierResponse::getNoticeId, noticeId)
                .eq(SrmTenderSupplierResponse::getSectionId, sectionId)
                .list();

        if (CollectionUtils.isEmpty(supplierResponses)) {
            return Lists.newArrayList();
        }

        List<Long> businessIds = supplierResponses.stream().map(SrmTenderSupplierResponse::getId).toList();

        // 查询附件
        return srmProjectAttachmentService.lambdaQuery()
                .in(SrmProjectAttachment::getBusinessId, businessIds)
                .eq(SrmProjectAttachment::getBusinessGroup, String.valueOf(roundNo))
                .in(SrmProjectAttachment::getBusinessType, Lists.newArrayList(AttachmentTypeEnum.QUOTE_ATTACHMENT, AttachmentTypeEnum.QUOTE_OTHER_ATTACHMENT))
                .eq(SrmProjectAttachment::getDelFlag, 0)
                .list();
    }

    @Override
    public com.ylz.saas.resp.SrmTenderQuoteDetailBySupplierResp queryQuoteDetailBySupplier(com.ylz.saas.req.SrmTenderQuoteDetailBySupplierQueryReq req) {
        // 查询所有轮次的供应商报价信息
        List<com.ylz.saas.resp.SrmTenderQuoteDetailBySupplierRoundResp.SupplierQuoteInfo> allQuoteInfos =
                baseMapper.queryQuoteDetailBySupplier(req.getProjectId(), req.getSectionId(), req.getNoticeId(), req.getSupplierName());

        if (CollectionUtils.isEmpty(allQuoteInfos)) {
            com.ylz.saas.resp.SrmTenderQuoteDetailBySupplierResp resp = new com.ylz.saas.resp.SrmTenderQuoteDetailBySupplierResp();
            resp.setRoundList(Lists.newArrayList());
            return resp;
        }

        // 查询开标信息（每个标段只有一条记录，包含当前轮次信息）
        SrmTenderOpen tenderOpen = srmTenderOpenService.lambdaQuery()
                .eq(SrmTenderOpen::getProjectId, req.getProjectId())
                .eq(SrmTenderOpen::getSectionId, req.getSectionId())
                .eq(SrmTenderOpen::getNoticeId, req.getNoticeId())
                .one();

        // 查询招标公告信息（获取第一轮次的截止时间）
        SrmTenderNotice tenderNotice = srmTenderNoticeService.getById(req.getNoticeId());

        // 查询历史轮次的截止时间信息
        List<SrmTenderBidderSupplierQuoteAgain> quoteAgainList = srmTenderBidderSupplierQuoteAgainService.lambdaQuery()
                .eq(SrmTenderBidderSupplierQuoteAgain::getProjectId, req.getProjectId())
                .eq(SrmTenderBidderSupplierQuoteAgain::getSectionId, req.getSectionId())
                .eq(SrmTenderBidderSupplierQuoteAgain::getNoticeId, req.getNoticeId())
                .groupBy(SrmTenderBidderSupplierQuoteAgain::getQuoteAgainRound)
                .list();

        // 按轮次分组截止时间信息（安全处理null值）
        Map<Integer, LocalDateTime> roundEndTimeMap = new HashMap<>();
        for (SrmTenderBidderSupplierQuoteAgain item : quoteAgainList) {
            if (item.getQuoteAgainRound() != null) {
                Integer roundNo = item.getQuoteAgainRound();
                LocalDateTime endTime = item.getQuoteEndTime();

                // 如果Map中还没有这个轮次，或者Map中的值为null但当前值不为null，则更新
                if (!roundEndTimeMap.containsKey(roundNo) ||
                        (roundEndTimeMap.get(roundNo) == null && endTime != null)) {
                    roundEndTimeMap.put(roundNo, endTime);
                }
            }
        }

        // 按轮次分组
        Map<Integer, List<com.ylz.saas.resp.SrmTenderQuoteDetailBySupplierRoundResp.SupplierQuoteInfo>> roundGroupMap =
                allQuoteInfos.stream().collect(Collectors.groupingBy(info -> info.getRoundNo()));

        // 构建响应数据
        List<com.ylz.saas.resp.SrmTenderQuoteDetailBySupplierRoundResp> roundList = Lists.newArrayList();

        for (Map.Entry<Integer, List<com.ylz.saas.resp.SrmTenderQuoteDetailBySupplierRoundResp.SupplierQuoteInfo>> entry : roundGroupMap.entrySet()) {
            Integer roundNo = entry.getKey();
            List<com.ylz.saas.resp.SrmTenderQuoteDetailBySupplierRoundResp.SupplierQuoteInfo> supplierInfos = entry.getValue();

            // 按报价总金额排序并设置排名
            supplierInfos.sort((s1, s2) -> {
                if (s1.getTotalQuoteAmount() == null && s2.getTotalQuoteAmount() == null) {
                    return 0;
                }
                if (s1.getTotalQuoteAmount() == null) {
                    return 1;
                }
                if (s2.getTotalQuoteAmount() == null) {
                    return -1;
                }
                return s1.getTotalQuoteAmount().compareTo(s2.getTotalQuoteAmount());
            });

            // 设置排名
            for (int i = 0; i < supplierInfos.size(); i++) {
                supplierInfos.get(i).setRanking(i + 1);
            }

            com.ylz.saas.resp.SrmTenderQuoteDetailBySupplierRoundResp roundResp = new com.ylz.saas.resp.SrmTenderQuoteDetailBySupplierRoundResp();
            roundResp.setRoundNo(roundNo);
            roundResp.setSupplierList(supplierInfos);

            // 设置报价截止时间和开标状态
            if (tenderOpen != null) {
                Integer currentRound = tenderOpen.getCurrentRound();

                if (roundNo == 1) {
                    // 第一轮次的截止时间来自招标公告
                    if (tenderNotice != null) {
                        roundResp.setQuoteEndTime(tenderNotice.getQuoteEndTime());
                    }
                } else if (roundNo.equals(currentRound)) {
                    // 当前轮次的截止时间来自开标表
                    roundResp.setQuoteEndTime(tenderOpen.getLatestQuoteEndTime());
                } else {
                    // 历史轮次的截止时间来自再次报价表
                    LocalDateTime endTime = roundEndTimeMap.get(roundNo);
                    roundResp.setQuoteEndTime(endTime);
                }

                // 设置开标状态
                if (roundNo.equals(currentRound)) {
                    // 当前轮次使用实际状态
                    roundResp.setOpenStatus(tenderOpen.getOpenStatus());
                } else if (roundNo < currentRound) {
                    // 历史轮次都是已结束状态
                    roundResp.setOpenStatus(OpenStatusEnum.END_OPENED);
                }
                // 未来轮次（理论上不应该存在）保持null
            } else {
                // 没有开标信息的情况，只有第一轮次
                if (roundNo == 1 && tenderNotice != null) {
                    roundResp.setQuoteEndTime(tenderNotice.getQuoteEndTime());
                    // 没有开标信息时，状态为null或根据业务需要设置默认状态
                }
            }

            roundList.add(roundResp);
        }

        // 按轮次排序
        roundList.sort(Comparator.comparing(com.ylz.saas.resp.SrmTenderQuoteDetailBySupplierRoundResp::getRoundNo));

        com.ylz.saas.resp.SrmTenderQuoteDetailBySupplierResp resp = new com.ylz.saas.resp.SrmTenderQuoteDetailBySupplierResp();
        resp.setRoundList(roundList);
        return resp;
    }


    @Override
    public List<SrmProcurementProjectSection> getQuoteAgainSection(QuoteAgainSectionQueryReq req) {
        List<SrmTenderOpen> openList = srmTenderOpenService.lambdaQuery().eq(SrmTenderOpen::getNoticeId, req.getNoticeId())
                .eq(SrmTenderOpen::getCurrentRound, req.getCurrentRound())
                .list();
        if (CollectionUtils.isEmpty(openList)) {
            return Lists.newArrayList();
        }
        List<Long> sectionList = openList.stream().map(SrmTenderOpen::getSectionId).toList();
        return srmProcurementProjectSectionService.listByIds(sectionList);

    }
    /**
     * 发送多轮报价通知短信给供应商
     *
     * @param supplierResponses 供应商响应列表
     * @param tenderNotice 招标公告
     * @param newRound 新报价轮次
     * @param quoteEndTime 报价截止时间
     */
    private void sendQuoteAgainSms(List<SrmTenderSupplierResponse> supplierResponses, SrmTenderNotice tenderNotice,
                                   int newRound, LocalDateTime quoteEndTime) {
        try {
            SrmProcurementProject project = srmProcurementProjectService.getById(tenderNotice.getProjectId());

            for (SrmTenderSupplierResponse response : supplierResponses) {
                // 获取供应商联系人电话
                String contactPhone = response.getContactPhone();
                if (StringUtils.isBlank(contactPhone)) {
                    log.warn("供应商 {} 未设置联系电话，无法发送短信通知", response.getSupplierName());
                    continue;
                }

                // 构造短信参数
                MessageSmsDTO messageSmsDTO = MessageSmsDTO.builder()
                        .mobile(contactPhone)
                        .biz("AYN_MULTI_ROUND_QUOTE")
                        .param("company_name", response.getSupplierName() != null ? response.getSupplierName() : "供应商")
                        .param("project_name", project != null ? project.getProjectName() : "")
                        .param("project_code", project != null ? project.getProjectCode() : "")
                        .param("quote_end_time", quoteEndTime != null ? quoteEndTime.toString() : "")
                        .param("login_url", "https://ayn.canpanscp.com")
                        .build();

                // 异步发送短信
                asyncSmsService.sendSmsAsync(messageSmsDTO);
            }
            log.info("已发送多轮报价通知短信给 {} 个供应商", supplierResponses.size());
        } catch (Exception e) {
            log.error("发送多轮报价通知短信失败", e);
        }
    }

}




