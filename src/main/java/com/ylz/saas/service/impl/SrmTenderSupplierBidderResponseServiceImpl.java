package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmTenderSupplierBidderResponse;
import com.ylz.saas.service.SrmTenderSupplierBidderResponseService;
import com.ylz.saas.mapper.SrmTenderSupplierBidderResponseMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【srm_tender_supplier_bidder_response(投标响应表)】的数据库操作Service实现
* @createDate 2025-06-11 16:03:22
*/
@Service
public class SrmTenderSupplierBidderResponseServiceImpl extends ServiceImpl<SrmTenderSupplierBidderResponseMapper, SrmTenderSupplierBidderResponse>
    implements SrmTenderSupplierBidderResponseService{

}




