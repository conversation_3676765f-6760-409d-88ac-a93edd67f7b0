package com.ylz.saas.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.ProjectProgressStatusEnum;
import com.ylz.saas.req.SrmEnsilagePageForAppletReq;
import com.ylz.saas.req.SrmProcurementProjectCreateReq;
import com.ylz.saas.req.SrmProcurementProjectPageQueryReq;
import com.ylz.saas.resp.ProcessInstanceStartResp;
import com.ylz.saas.resp.ProjectArchiveResp;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【srm_procurement_project(采购立项主表)】的数据库操作Service
* @createDate 2025-06-11 11:21:09
*/
public interface SrmProcurementProjectService extends IService<SrmProcurementProject> {

    /**
     * 创建采购立项及其相关明细
     */
    ProcessInstanceStartResp upsertAndStartFlow(SrmProcurementProjectCreateReq req);

    /**
     * 创建采购立项及其相关明细
     *
     * @param req
     * @param documentStatus
     * @param approveStatus
     * @param progressStatusEnum
     * @return
     */
    SrmProcurementProject upsertSrmProcurementProject(SrmProcurementProjectCreateReq req
            , DocumentStatus documentStatus, ApproveStatusEnum approveStatus, ProjectProgressStatusEnum progressStatusEnum);

    void insertProjectUserIdentityDataPermission(SrmProcurementProject project, Set<Long> newUserIds, Set<Long> existingUserIds);

    /**
     * 分页查询（不查询nodeStatus，性能较快）
     * @param req 查询请求
     * @param page 分页参数
     * @return 分页结果
     */
    Page<SrmProcurementProject> pageQuery(SrmProcurementProjectPageQueryReq req, Page<SrmProcurementProject> page);

    /**
     * 分页查询（使用审批流实例查询nodeStatus）
     * @param req 查询请求
     * @param page 分页参数
     * @return 分页结果
     */
    Page<SrmProcurementProject> pageQueryWithProcessInstance(SrmProcurementProjectPageQueryReq req, Page<SrmProcurementProject> page);

    /**
     * 供应商分页查询（使用审批流实例查询nodeStatus，禁用数据权限控制）
     * @param req 查询请求
     * @param page 分页参数
     * @return 分页结果
     */
    Page<SrmProcurementProject> pageQueryWithProcessInstanceForSupplier(SrmProcurementProjectPageQueryReq req, Page<SrmProcurementProject> page);

    /**
     * 小程序分页查询（关联多表查询）
     * @param req 查询请求
     * @param page 分页参数
     * @return 分页结果
     */
    Page<SrmProcurementProject> pageForApplet(SrmEnsilagePageForAppletReq req, Page<SrmProcurementProject> page);

    SrmProcurementProject detail(String projectCode, boolean simple);

    void delete(Long id);

    boolean checkStatus(String projectCode, DocumentStatus newDocStatus, ApproveStatusEnum newApproveStatus, ProjectProgressStatusEnum newProgressStatus, boolean throwEx);

    /**
     * 审批拒绝业务流会调用
     */
    void updateApproveStatus(String projectCode, ApproveStatusEnum newApproveStatus, boolean throwEx);

    void updateProgressStatus(String projectCode, ProjectProgressStatusEnum newProgressStatus, boolean throwEx);

    /**
     * 审批通过，业务流调用
     */
    void approvePassed(String projectCode);

    void autoUpdateProgressStatus();

    ProjectArchiveResp getProjectArchive(String projectCode);


    @Getter
    @AllArgsConstructor
    enum DocumentStatus {

        NEW("新建"),
        EFFECT("项目生效"),
        CANCEL("项目作废")
         ;

        private String desc;
    }

}
