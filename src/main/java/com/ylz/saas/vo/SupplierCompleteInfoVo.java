package com.ylz.saas.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 供应商完整信息VO（用于一次性保存所有供应商信息）
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
@Schema(description = "供应商完整信息VO")
public class SupplierCompleteInfoVo {
    /**
     * 企业性质
     */
    private String enterpriseNature;
    /**
     * 租户供应商ID（更新时必填，新增时为空）
     */
    @Schema(description = "租户供应商ID（更新时必填，新增时为空）")
    private Long id;

    /**
     * 平台供应商ID（更新时必填，新增时为空）
     */
    @Schema(description = "平台供应商ID（更新时必填，新增时为空）")
    private Long platformSupplierId;

    // ========== 基本信息 ==========

    /**
     * 供应商编号（必填）
     */
//    @NotBlank(message = "供应商编号不能为空")
    @Schema(description = "供应商编号", required = true)
    private String supplierCode;

    /**
     * 供应商名称（必填）
     */
    @NotBlank(message = "供应商名称不能为空")
    @Schema(description = "供应商名称", required = true)
    private String supplierName;

    /**
     * 供应商简称
     */
    @Schema(description = "供应商简称")
    private String supplierShortName;

    /**
     * 供应商类型
     */
    @Schema(description = "供应商类型")
    private String supplierType;

    /**
     * 供应商来源
     */
    @Schema(description = "供应商来源")
    private String supplierSource;

    /**
     * 审批状态
     */
    @Schema(description = "审批状态")
    private String approvalStatus;

    /**
     * 统一社会信用代码
     */
    @Schema(description = "统一社会信用代码")
    private String socialCreditCode;

    /**
     * 成立日期
     */
    @Schema(description = "成立日期")
    private LocalDate establishDate;

    /**
     * 注册资本
     */
    @Schema(description = "注册资本")
    private BigDecimal registeredCapital;

    /**
     * 法人代表
     */
    @Schema(description = "法人代表")
    private String legalPerson;

    /**
     * 法人身份证号
     */
    @Schema(description = "法人身份证号")
    private String legalPersonId;

    /**
     * 法人联系电话
     */
    @Schema(description = "法人联系电话")
    private String legalPersonPhone;

    /**
     * 注册地址
     */
    @Schema(description = "注册地址")
    private String registeredAddress;

    /**
     * 邮政编码
     */
    @Schema(description = "邮政编码")
    private String postalCode;

    /**
     * 传真
     */
    @Schema(description = "传真")
    private String fax;

    /**
     * 年平均营业额
     */
    @Schema(description = "年平均营业额")
    private BigDecimal annualRevenue;

    /**
     * 公司简介
     */
    @Schema(description = "公司简介")
    private String companyProfile;

    /**
     * 经营范围
     */
    @Schema(description = "经营范围")
    private String businessScope;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 法人委托书
     */
    @Schema(description = "法人委托书")
    private String letterOfAuthorization;

    // ========== 租户相关信息 ==========

    /**
     * 供应商分类（必填）
     */
    @NotBlank(message = "供应商分类不能为空")
    @Schema(description = "供应商分类", required = true)
    private String supplierCategory;

    /**
     * 供应商状态（必填）
     */
    @NotBlank(message = "供应商状态不能为空")
    @Schema(description = "供应商状态", required = true)
    private String supplierStatus;

    /**
     * 服务开始日期
     */
    @Schema(description = "服务开始日期")
    private LocalDate serviceStartDate;

    /**
     * 服务结束日期
     */
    @Schema(description = "服务结束日期")
    private LocalDate serviceEndDate;

//    /**
//     * 租户供应商备注
//     */
//    @Schema(description = "租户供应商备注")
//    private String tenantRemark;

    /**
     * 关联部门ID集合
     */
    @Schema(description = "关联部门ID集合")
    private List<Long> deptIds;

    /**
     * 是否为所有组织可用（0-否、1-是）
     */
    @Schema(description = "是否为所有组织可用")
    private Boolean isAllDept;

    // ========== 子表信息 ==========

    /**
     * 联系人信息列表
     */
    @Schema(description = "联系人信息列表")
    @Valid
    private List<SupplierContactInfoVo> contactList;

    /**
     * 资质信息列表
     */
    @Schema(description = "资质信息列表")
    @Valid
    private List<SupplierCertificateInfoVo> certificateList;

    /**
     * 银行账户信息列表
     */
    @Schema(description = "银行账户信息列表")
    @Valid
    private List<SupplierBankAccountInfoVo> bankAccountList;

    /**
     * 主营物料信息列表
     */
    @Schema(description = "主营物料信息列表")
    @Valid
    private List<SupplierMaterialInfoVo> materialList;

    // ========== 删除标识 ==========

    /**
     * 需要删除的联系人ID列表
     */
    @Schema(description = "需要删除的联系人ID列表")
    private List<Long> deleteContactIds;

    /**
     * 需要删除的资质ID列表
     */
    @Schema(description = "需要删除的资质ID列表")
    private List<Long> deleteCertificateIds;

    /**
     * 需要删除的银行账户ID列表
     */
    @Schema(description = "需要删除的银行账户ID列表")
    private List<Long> deleteBankAccountIds;

    /**
     * 需要删除的物料关联ID列表
     */
    @Schema(description = "需要删除的物料关联ID列表")
    private List<Long> deleteMaterialIds;
}
