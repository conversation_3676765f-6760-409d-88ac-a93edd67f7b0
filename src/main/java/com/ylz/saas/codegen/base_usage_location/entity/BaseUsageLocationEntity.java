package com.ylz.saas.codegen.base_usage_location.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ylz.saas.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * 使用地点信息表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:41:24
 */
@Data
@TableName("base_usage_location")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "使用地点信息表")
@TenantTable
public class BaseUsageLocationEntity extends Model<BaseUsageLocationEntity> {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 组织ID
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "组织ID")
    private Long deptId;

    /**
     * 使用地点名称
     */
    @Schema(description = "使用地点名称")
    private String locationName;

    /**
     * 使用地点编码
     */
    @Schema(description = "使用地点编码")
    private String locationCode;

    /**
     * 地点类型（工厂、仓库、办公室、牧场、工地等）
     */
    @Schema(description = "地点类型（工厂、仓库、办公室、牧场、工地等）")
    private String locationType;

    /**
     * 省
     */
    @Schema(description = "省")
    private String province;

    /**
     * 市
     */
    @Schema(description = "市")
    private String city;

    /**
     * 区
     */
    @Schema(description = "区")
    private String district;

    /**
     * 详细地址
     */
    @Schema(description = "详细地址")
    private String address;

    /**
     * 联系人
     */
    @Schema(description = "联系人")
    private String contactPerson;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String contactPhone;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    /**
     * 创建人名称
     */
    @Schema(description = "创建人名称")
    private String createByName;

    /**
     * 修改人名称
     */
    @Schema(description = "修改人名称")
    private String updateByName;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "删除标识（0-正常、1-删除）")
    private int delFlag;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人ID")
    private Long createById;

    /**
     * 更新人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新人ID")
    private Long updateById;

    /**
     * 组织名称（关联查询字段）
     */
    @TableField(exist = false)
    @Schema(description = "组织名称")
    private String deptName;
}