package com.ylz.saas.service;

import com.ylz.saas.entity.BaseTemplates;
import com.ylz.saas.entity.BaseTemplatesHistory;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【base_templates_history(历史模板记录表)】的数据库操作Service
* @createDate 2025-06-11 13:07:56
*/
public interface BaseTemplatesHistoryService extends IService<BaseTemplatesHistory> {
    void saveToHistory(BaseTemplates template);

    List<BaseTemplatesHistory> getHistoryByTemplateId(Long templateId);
}
