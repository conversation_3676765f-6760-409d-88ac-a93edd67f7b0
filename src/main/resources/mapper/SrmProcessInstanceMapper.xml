<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmProcessInstanceMapper">




    <select id="getInstancesByBizIdAndType" resultType="com.ylz.saas.entity.SrmProcessInstance">
        select
        spi.*
        from srm_process_instance spi
        where 1=1
        <if test="bizIds !=null and bizIds.size() !=0">
            and spi.biz_id in
            <foreach item="item" collection="bizIds" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="types !=null and types.size() !=0">
            and spi.type in
            <foreach item="item" collection="types" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        order by spi.biz_id, spi.type, spi.create_time desc
    </select>




</mapper>
