package com.ylz.saas.resp;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ylz.saas.entity.BaseSceneFields;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class SceneWithFieldsResp {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 模板场景名称
     */
    private String sceneName;

    /**
     * 模板场景描述
     */
    private String description;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    private Long createById;

    /**
     * 更新人ID
     */
    private Long updateById;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改人名称
     */
    private String updateByName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 字段信息
     */
    private List<BaseSceneFields> fields;
}