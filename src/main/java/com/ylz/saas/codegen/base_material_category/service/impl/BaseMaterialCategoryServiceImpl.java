package com.ylz.saas.codegen.base_material_category.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.codegen.base_material_category.entity.BaseMaterialCategoryEntity;
import com.ylz.saas.codegen.base_material_category.excel.BaseMaterialCategoryReq;
import com.ylz.saas.codegen.base_material_category.mapper.BaseMaterialCategoryMapper;
import com.ylz.saas.codegen.base_material_category.param.BaseMaterialCategoryParam;
import com.ylz.saas.codegen.base_material_category.service.BaseMaterialCategoryService;
import com.ylz.saas.codegen.base_material_category.util.TreeUtil;
import com.ylz.saas.codegen.base_material_category.vo.BaseMaterialCategoryTreeVo;
import com.ylz.saas.codegen.base_material_category.vo.BaseMaterialCategoryVo;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.data.excel.ExcelAnalysisUtil;
import com.ylz.saas.common.data.excel.ExcelTemplateGenerator;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;
/**
 * 物料分类表
 *
 * <AUTHOR>
 * @date 2025-06-03 17:25:41
 */
@Service
@Slf4j
public class BaseMaterialCategoryServiceImpl extends ServiceImpl<BaseMaterialCategoryMapper, BaseMaterialCategoryEntity> implements BaseMaterialCategoryService {
	// 状态和数据来源映射
	private static final Map<String, String> STATUS_MAP = Map.of(
		"ENABLED", "启用",
		"DISABLED", "禁用"
	);
	private static final Map<String, String> DATA_SOURCE_MAP = Map.of(
		"BATCH_IMPORT", "批量导入",
		"MANUAL", "手工录入"
		// 可根据实际情况补充
	);

	private void setStatusAndDataSourceNames(List<BaseMaterialCategoryEntity> records) {
		if (records == null) return;
		for (BaseMaterialCategoryEntity entity : records) {
			if (entity == null) continue;
			entity.setStatus(STATUS_MAP.getOrDefault(entity.getStatus(), entity.getStatus()));
			entity.setDataSource(DATA_SOURCE_MAP.getOrDefault(entity.getDataSource(), entity.getDataSource()));
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveBaseMaterialCategory(BaseMaterialCategoryVo baseMaterialCategoryVo) {
		// 1. 必填校验
		ExceptionUtil.check(StrUtil.isBlank(baseMaterialCategoryVo.getCategoryCode()), "500", "物料分类编码不能为空");
		ExceptionUtil.check(StrUtil.isBlank(baseMaterialCategoryVo.getCategoryName()), "500", "物料分类名称不能为空");
		
		// 2. 编码格式校验（编码不能包含特殊字符，只能包含字母、数字、下划线）
		if (!baseMaterialCategoryVo.getCategoryCode().matches("^[a-zA-Z0-9_]+$")) {
			ExceptionUtil.check(true, "500", "物料分类编码只能包含字母、数字、下划线");
		}
		
		// 3. 编码长度校验
		if (baseMaterialCategoryVo.getCategoryCode().length() > 30) {
			ExceptionUtil.check(true, "500", "物料分类编码长度不能超过30个字符");
		}
		
		// 4. 名称长度校验
		if (baseMaterialCategoryVo.getCategoryName().length() > 30) {
			ExceptionUtil.check(true, "500", "物料分类名称长度不能超过30个字符");
		}
		
		// 5. 编码唯一性校验
		Long codeCount = this.lambdaQuery()
				.eq(BaseMaterialCategoryEntity::getCategoryCode, baseMaterialCategoryVo.getCategoryCode())
				.count();
		ExceptionUtil.check(codeCount > 0, "500", "物料分类编码已存在，不可重复提交");
		
		// 6. 名称唯一性校验
		Long nameCount = this.lambdaQuery()
				.eq(BaseMaterialCategoryEntity::getCategoryName, baseMaterialCategoryVo.getCategoryName())
				.count();
		ExceptionUtil.check(nameCount > 0, "500", "物料分类名称已存在，不可重复提交");
		
		// 7. 父级编码存在性校验
		if (StrUtil.isNotBlank(baseMaterialCategoryVo.getParentCategoryCode())) {
			Long parentCount = this.lambdaQuery()
					.eq(BaseMaterialCategoryEntity::getCategoryCode, baseMaterialCategoryVo.getParentCategoryCode())
					.count();
			ExceptionUtil.check(parentCount == 0, "500", "父级物料分类编码不存在");
		}
		
		BaseMaterialCategoryEntity baseMaterialCategory = new BaseMaterialCategoryEntity();
        BeanUtil.copyProperties(baseMaterialCategoryVo, baseMaterialCategory);
        return save(baseMaterialCategory);
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateBaseMaterialCategory(BaseMaterialCategoryVo baseMaterialCategoryVo) {
		BaseMaterialCategoryEntity baseMaterialCategory = getById(baseMaterialCategoryVo.getId());
		ExceptionUtil.check(baseMaterialCategory == null, "500", "物料分类表不存在");
		
		// 1. 必填校验
		ExceptionUtil.check(StrUtil.isBlank(baseMaterialCategoryVo.getCategoryCode()), "500", "物料分类编码不能为空");
		ExceptionUtil.check(StrUtil.isBlank(baseMaterialCategoryVo.getCategoryName()), "500", "物料分类名称不能为空");
		
		// 2. 编码格式校验（编码不能包含特殊字符，只能包含字母、数字、下划线）
		if (!baseMaterialCategoryVo.getCategoryCode().matches("^[a-zA-Z0-9_]+$")) {
			ExceptionUtil.check(true, "500", "物料分类编码只能包含字母、数字、下划线");
		}
		
		// 3. 编码长度校验
		if (baseMaterialCategoryVo.getCategoryCode().length() > 30) {
			ExceptionUtil.check(true, "500", "物料分类编码长度不能超过30个字符");
		}
		
		// 4. 名称长度校验
		if (baseMaterialCategoryVo.getCategoryName().length() > 30) {
			ExceptionUtil.check(true, "500", "物料分类名称长度不能超过30个字符");
		}
		
		// 5. 编码唯一性校验（排除当前记录）
		Long codeCount = this.lambdaQuery()
				.eq(BaseMaterialCategoryEntity::getCategoryCode, baseMaterialCategoryVo.getCategoryCode())
				.ne(BaseMaterialCategoryEntity::getId, baseMaterialCategoryVo.getId())
				.count();
		ExceptionUtil.check(codeCount > 0, "500", "物料分类编码已存在，不可重复提交");
		
		// 6. 名称唯一性校验（排除当前记录）
		Long nameCount = this.lambdaQuery()
				.eq(BaseMaterialCategoryEntity::getCategoryName, baseMaterialCategoryVo.getCategoryName())
				.ne(BaseMaterialCategoryEntity::getId, baseMaterialCategoryVo.getId())
				.count();
		ExceptionUtil.check(nameCount > 0, "500", "物料分类名称已存在，不可重复提交");
		
		// 7. 父级编码存在性校验
		if (StrUtil.isNotBlank(baseMaterialCategoryVo.getParentCategoryCode())) {
			// 不能将自己设为父级
			ExceptionUtil.check(baseMaterialCategoryVo.getCategoryCode().equals(baseMaterialCategoryVo.getParentCategoryCode()), 
					"500", "不能将自己设为父级分类");
			
			Long parentCount = this.lambdaQuery()
					.eq(BaseMaterialCategoryEntity::getCategoryCode, baseMaterialCategoryVo.getParentCategoryCode())
					.count();
			ExceptionUtil.check(parentCount == 0, "500", "父级物料分类编码不存在");
		}
		
		BeanUtil.copyProperties(baseMaterialCategoryVo, baseMaterialCategory);
        return updateById(baseMaterialCategory);
	}
	
	@Override
    public void template(HttpServletResponse response) {
		// 创建目录及导入模板文件
		isExcelExist();
		// 读取导入模板
		FileInputStream inputStream = null;
		ServletOutputStream outputStream = null;
		try {
			String oriFileName = "物料分类表导入模板";
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding("utf-8");
			String fileName = URLEncoder.encode(oriFileName, "UTF-8").replaceAll("\\+", "%20");
			response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
			Path result = ExcelTemplateGenerator.getResult();
			Path filePath = result.resolve("物料分类表baseMaterialCategory.xlsx");
			// 绝对路径
			File file = new File(filePath.toString());
			inputStream = new FileInputStream(file);

			// 获取输出流
			outputStream = response.getOutputStream();

			// 将输入流的内容写入到输出流中
			byte[] buffer = new byte[1024];
			int bytesRead;
			while ((bytesRead = inputStream.read(buffer)) != -1) {
				outputStream.write(buffer, 0, bytesRead);
			}

			// 确保输出流的数据完全写入
			outputStream.flush();
			log.info("模板下载成功");
		} catch (IOException e) {
			System.out.println("e = " + e.getMessage());
			log.info("模板下载失败");
		} finally {
			// 关闭流
			try {
				if (inputStream != null) {
					inputStream.close();
				}
				if (outputStream != null) {
					outputStream.close();
				}
			} catch (IOException e) {
				log.error("关闭流时发生错误: {}", e.getMessage());
			}
		}
	}

	@Override
	@Transactional
	public void uploadData(MultipartFile file, HttpServletResponse response) {
		try {
			List<BaseMaterialCategoryReq> importGoodsList = new ExcelAnalysisUtil<BaseMaterialCategoryReq>()
					.readFile(file, BaseMaterialCategoryReq.class, 1);
			if (CollectionUtils.isEmpty(importGoodsList)) {
				writeImportResponse(response, 1, "导入数据为空");
				return;
			}
			
			// 做字段的必传校验和业务校验
			StringBuilder errorMsg = new StringBuilder();
			for (int i = 0; i < importGoodsList.size(); i++) {
				BaseMaterialCategoryReq req = importGoodsList.get(i);
				int rowNum = i + 2; // Excel行号从2开始（第1行是标题）
				
				// 1. 必填校验
				if (StrUtil.isBlank(req.getCategoryCode())) {
					errorMsg.append("第").append(rowNum).append("行：物料分类编码不能为空；");
				}
				if (StrUtil.isBlank(req.getCategoryName())) {
					errorMsg.append("第").append(rowNum).append("行：物料分类名称不能为空；");
				}
				
				// 2. 编码格式校验（编码不能包含特殊字符，只能包含字母、数字、下划线）
				if (StrUtil.isNotBlank(req.getCategoryCode()) && !req.getCategoryCode().matches("^[a-zA-Z0-9_]+$")) {
					errorMsg.append("第").append(rowNum).append("行：物料分类编码只能包含字母、数字、下划线；");
				}
				
				// 3. 编码长度校验
				if (StrUtil.isNotBlank(req.getCategoryCode()) && req.getCategoryCode().length() > 50) {
					errorMsg.append("第").append(rowNum).append("行：物料分类编码长度不能超过50个字符；");
				}
				
				// 4. 名称长度校验
				if (StrUtil.isNotBlank(req.getCategoryName()) && req.getCategoryName().length() > 100) {
					errorMsg.append("第").append(rowNum).append("行：物料分类名称长度不能超过100个字符；");
				}
			}
			
			// 如果有校验错误，直接返回
			if (!errorMsg.isEmpty()) {
				writeImportResponse(response, 1, errorMsg.toString());
				return;
			}
			
			// 5. 编码唯一性校验（与数据库中的数据比较）
			List<String> importCodes = importGoodsList.stream()
					.map(BaseMaterialCategoryReq::getCategoryCode)
					.filter(StrUtil::isNotBlank)
					.toList();
			
			// 检查导入数据中是否有重复编码
			if (importCodes.size() != importCodes.stream().distinct().count()) {
				writeImportResponse(response, 1, "导入数据中存在重复的物料分类编码");
				return;
			}
			
			// 检查与数据库中已有编码的重复性
			LambdaQueryWrapper<BaseMaterialCategoryEntity> codeQueryWrapper = new LambdaQueryWrapper<>();
			codeQueryWrapper.in(BaseMaterialCategoryEntity::getCategoryCode, importCodes);
			List<BaseMaterialCategoryEntity> existingCategories = this.list(codeQueryWrapper);
			if (CollectionUtils.isNotEmpty(existingCategories)) {
				List<String> existingCodes = existingCategories.stream()
						.map(BaseMaterialCategoryEntity::getCategoryCode)
						.toList();
				writeImportResponse(response, 1, "物料分类编码已存在：" + String.join(",", existingCodes));
				return;
			}
			
			// 6. 名称唯一性校验
			List<String> importNames = importGoodsList.stream()
					.map(BaseMaterialCategoryReq::getCategoryName)
					.filter(StrUtil::isNotBlank)
					.toList();
			
			// 检查导入数据中是否有重复名称
			if (importNames.size() != importNames.stream().distinct().count()) {
				writeImportResponse(response, 1, "导入数据中存在重复的物料分类名称");
				return;
			}
			
			// 检查与数据库中已有名称的重复性
			LambdaQueryWrapper<BaseMaterialCategoryEntity> nameQueryWrapper = new LambdaQueryWrapper<>();
			nameQueryWrapper.in(BaseMaterialCategoryEntity::getCategoryName, importNames);
			List<BaseMaterialCategoryEntity> existingCategoriesByName = this.list(nameQueryWrapper);
			if (CollectionUtils.isNotEmpty(existingCategoriesByName)) {
				List<String> existingNames = existingCategoriesByName.stream()
						.map(BaseMaterialCategoryEntity::getCategoryName)
						.toList();
				writeImportResponse(response, 1, "物料分类名称已存在：" + String.join(",", existingNames));
				return;
			}
			
			// 7. 父级编码存在性校验
			List<String> parentCodes = importGoodsList.stream()
					.map(BaseMaterialCategoryReq::getParentCategoryCode)
					.filter(StrUtil::isNotBlank)
					.distinct()
					.toList();
			
			if (CollectionUtils.isNotEmpty(parentCodes)) {
				LambdaQueryWrapper<BaseMaterialCategoryEntity> parentQueryWrapper = new LambdaQueryWrapper<>();
				parentQueryWrapper.in(BaseMaterialCategoryEntity::getCategoryCode, parentCodes);
				List<BaseMaterialCategoryEntity> existingParentCategories = this.list(parentQueryWrapper);
				
				if (existingParentCategories.size() != parentCodes.size()) {
					List<String> existingParentCodes = existingParentCategories.stream()
							.map(BaseMaterialCategoryEntity::getCategoryCode)
							.toList();
					List<String> missingParentCodes = parentCodes.stream()
							.filter(code -> !existingParentCodes.contains(code))
							.toList();
					writeImportResponse(response, 1, "父级物料分类编码不存在：" + String.join(",", missingParentCodes));
					return;
				}
			}
			
			// 8. 检查是否存在循环引用（子分类的编码不能是父分类的父级编码）
			for (BaseMaterialCategoryReq req : importGoodsList) {
				if (StrUtil.isNotBlank(req.getParentCategoryCode())) {
					for (BaseMaterialCategoryReq otherReq : importGoodsList) {
						if (!req.getCategoryCode().equals(otherReq.getCategoryCode()) && 
							req.getCategoryCode().equals(otherReq.getParentCategoryCode()) &&
							req.getParentCategoryCode().equals(otherReq.getCategoryCode())) {
							writeImportResponse(response, 1, "存在循环引用：" + req.getCategoryCode() + " 和 " + otherReq.getCategoryCode());
							return;
						}
					}
				}
			}
			
			List<BaseMaterialCategoryEntity> baseMaterialCategoryEntities = BeanUtil.copyToList(importGoodsList, BaseMaterialCategoryEntity.class);
			// 保存数据
			this.saveOrUpdateBatch(baseMaterialCategoryEntities);
			writeImportResponse(response, 0, "导入成功，共导入 " + baseMaterialCategoryEntities.size() + " 条数据");
		} catch (IOException e) {
			log.info("文件解析失败");
			throw new RuntimeException(e);
		}
	}

	private void writeImportResponse(HttpServletResponse response, int code, String errorMsg) throws IOException {
		JSONObject obj = new JSONObject();
		response.setContentType("text/html;charset=utf-8");
		obj.put("code", code);
		obj.put("msg", errorMsg);
		PrintWriter writer = response.getWriter();
		writer.write(obj.toJSONString());
		writer.close();
	}

	private static void isExcelExist() {
		try {
			Path result = ExcelTemplateGenerator.getResult();

			File file = new File(result.toString());
			if (file.exists()) {
				log.info("excel文件夹路径存在");
			} else {
				// 创建文件夹
				if (file.mkdirs()) {
					log.info("目录已创建: {}" , file);
				} else {
					log.info("创建目录失败: {}", file);
					throw new RuntimeException("创建目录失败: " + file);
				}
			}
			// 设置 Excel 文件的完整路径
			Path filePath = result.resolve("物料分类表baseMaterialCategory.xlsx");
			// 生成模板文件
			ExcelTemplateGenerator.createExcelTemplate(filePath.toString(),BaseMaterialCategoryReq.class);
		} catch (UnsupportedEncodingException e) {
			log.info("编码失败");
		}
	}

	@Override
	public List<BaseMaterialCategoryTreeVo> getTreeList() {
		List<BaseMaterialCategoryEntity> categoryList = list();
		return TreeUtil.buildMaterialCategoryTree(categoryList);
	}

	@Override
	public List<BaseMaterialCategoryTreeVo> getTreeListByStatus(String status) {
		LambdaQueryWrapper<BaseMaterialCategoryEntity> queryWrapper = new LambdaQueryWrapper<>();
		if (StrUtil.isNotBlank(status)) {
			queryWrapper.eq(BaseMaterialCategoryEntity::getStatus, status);
		}
		List<BaseMaterialCategoryEntity> categoryList = list(queryWrapper);
		return TreeUtil.buildMaterialCategoryTree(categoryList);
	}

	@Override
	public List<BaseMaterialCategoryTreeVo> getTreeListByParent(String parentCategoryCode) {
		LambdaQueryWrapper<BaseMaterialCategoryEntity> queryWrapper = new LambdaQueryWrapper<>();
		if (StrUtil.isNotBlank(parentCategoryCode)) {
			queryWrapper.eq(BaseMaterialCategoryEntity::getParentCategoryCode, parentCategoryCode);
		} else {
			// 如果父级编码为空，则查询根节点
			queryWrapper.and(wrapper -> wrapper.isNull(BaseMaterialCategoryEntity::getParentCategoryCode)
					.or().eq(BaseMaterialCategoryEntity::getParentCategoryCode, ""));
		}
		List<BaseMaterialCategoryEntity> categoryList = list(queryWrapper);
		return TreeUtil.buildMaterialCategoryTree(categoryList);
	}

	@Override
	public List<String> getAllChildrenCodes(String parentCategoryCode) {
		List<BaseMaterialCategoryEntity> categoryList = list();
		return TreeUtil.getAllChildrenCodes(parentCategoryCode, categoryList);
	}

	@Override
	public List<String> getAllParentCodes(String categoryCode) {
		List<BaseMaterialCategoryEntity> categoryList = list();
		return TreeUtil.getAllParentCodes(categoryCode, categoryList);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean switchStatus(Long id) {
		BaseMaterialCategoryEntity baseMaterialCategory = getById(id);
		ExceptionUtil.check(baseMaterialCategory == null, "500", "物料分类表不存在");
		if ("ENABLED".equals(baseMaterialCategory.getStatus())) {
			baseMaterialCategory.setStatus("DISABLED");
		} else {
			baseMaterialCategory.setStatus("ENABLED");
		}
		this.updateById(baseMaterialCategory);
		return true;
	}

	@Override
	public BaseMaterialCategoryEntity getByCategoryName(String categoryName) {
		if (StrUtil.isBlank(categoryName)) {
			return null;
		}
		LambdaQueryWrapper<BaseMaterialCategoryEntity> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(BaseMaterialCategoryEntity::getCategoryName, categoryName.trim());
		return this.getOne(queryWrapper);
	}

	@Override
	public List<BaseMaterialCategoryEntity> listInfo(BaseMaterialCategoryParam param) {
		QueryWrapper<BaseMaterialCategoryEntity> queryWrapper = param.toWrapper();
		List<BaseMaterialCategoryEntity> entityList = list(queryWrapper);
		setStatusAndDataSourceNames(entityList);
		return entityList;
	}
}