package com.ylz.saas.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.req.SrmTenderOnlineQaAddReq;
import com.ylz.saas.req.SrmTenderOnlineQaPageReq;
import com.ylz.saas.req.SrmTenderOnlineQaReplyReq;
import com.ylz.saas.resp.SrmTenderOnlineQaPageResp;
import com.ylz.saas.service.SrmTenderOnlineQaService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 在线问答控制层
 */
@RestController
@RequestMapping("/onlineQa")
public class SrmTenderOnlineQaController {


    @Resource
    private SrmTenderOnlineQaService onlineQaService;

    /**
     * 查询在线问答
     *
     * @param req
     * @return
     */
    @PostMapping("/findQuestion")
    public R<Page<SrmTenderOnlineQaPageResp>> findQuestion(@RequestBody SrmTenderOnlineQaPageReq req) {
        Page<SrmTenderOnlineQaPageResp> respPage = onlineQaService.findQuestion(req);
        return R.ok(respPage);
    }

    /**
     * 添加在线问答
     *
     * @param req
     * @return
     */
    @PostMapping("/addQuestion")
    public R<Void> addQuestion(@RequestBody @Validated SrmTenderOnlineQaAddReq req) {
        onlineQaService.addQuestion(req);
        return R.ok();
    }

    /**
     * 回复在线问答
     *
     * @param req
     * @return
     */
    @PostMapping("/replyQuestion")
    public R<Void> replyQuestion(@RequestBody @Validated SrmTenderOnlineQaReplyReq req) {
        onlineQaService.replyQuestion(req);
        return R.ok();
    }


}

