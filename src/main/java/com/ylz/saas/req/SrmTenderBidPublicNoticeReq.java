package com.ylz.saas.req;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 定标公告
 */
@Data
public class SrmTenderBidPublicNoticeReq {
    /**
     * 公告id
     */
    @NotNull(message = "公告id不能为空")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 邀请供应商
     */
    @Valid
    @NotNull(message = "邀请供应商不能为空")
    @Size(min = 1, message = "请选择邀请供应商！")
    private List<InviteSupplierReq> inviteSupplierList;

    /**
     * 变更中标公告--是否发布中标公告
     */
    @NotNull(message = "请选择是否发布中标公告！")
    private Boolean isPublishNotice;

    /**
     * 公告标题
     */
    private String noticeTitle;

    /**
     * 引用模版ID
     */
    private Long noticeTemplateId;

    /**
     * 公告内容
     */
    private String noticeContent;

    /**
     * 附件信息
     */
    private List<AttachmentInfoReq> attachmentInfos;


}