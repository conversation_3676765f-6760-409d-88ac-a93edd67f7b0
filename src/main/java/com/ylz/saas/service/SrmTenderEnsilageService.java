package com.ylz.saas.service;

import com.ylz.saas.req.SrmEnsilageAddReq;

/**
 * 青贮服务
 */
public interface SrmTenderEnsilageService {

    /**
     * 新增
     * @param req
     */
    void upsertEnsilage(SrmEnsilageAddReq req);

    /**
     *  处理青贮信息
     * @param req
     * @return
     */
    Long handlerEnsilageNotice(SrmEnsilageAddReq req);

    /**
     * 详情
     * @param noticeId
     * @return
     */
    SrmEnsilageAddReq detailEnsilage(Long noticeId);

    /**
     * 青贮审批锁定修改为审批中
     *
     * @param noticeId
     */
    void noticeApprovingHook(String noticeId);

    /**
     *  通过
     * @param noticeId
     */
    void ensilageApprovePass(String noticeId);

    /**
     * 驳回
     * @param noticeId
     */
    void ensilageApproveReject(String noticeId);
}
