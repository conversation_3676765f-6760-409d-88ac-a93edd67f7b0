package com.ylz.saas.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.entity.SrmTenderOpenLog;
import com.ylz.saas.req.SrmTenderOpenLogAddReq;
import com.ylz.saas.req.SrmTenderOpenReq;
import com.ylz.saas.resp.SrmTenderOpenLogResp;

/**
* <AUTHOR>
* @description 针对表【srm_tender_open_log(开标记录日志表)】的数据库操作Service
* @createDate 2025-06-17 19:14:59
*/
public interface SrmTenderOpenLogService extends IService<SrmTenderOpenLog> {

    /**
     * 查询开标记录日志
     *
     * @param req
     * @return
     */
    SrmTenderOpenLogResp operationLog(SrmTenderOpenReq req);

    /**
     * 添加开标记录日志
     *
     * @param req
     */
    void operationLogAdd(SrmTenderOpenLogAddReq req);

    /**
     * 添加IP预警
     *
     * @param req
     * @return
     */
    Integer ipWarn(SrmTenderOpenReq req);
}
