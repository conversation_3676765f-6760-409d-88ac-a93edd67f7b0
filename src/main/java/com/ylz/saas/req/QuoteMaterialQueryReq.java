package com.ylz.saas.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/6/26 13:45
 * @Description
 */
@Data
public class QuoteMaterialQueryReq {

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购标段ID不能为空")
    private Long sectionId;

    /**
     * 供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 采购物料编码
     */
    private String materialCode;

    /**
     * 采购物料名称
     */
    private String materialName;

}
