<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.BaseTemplatesMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.BaseTemplates">
            <id property="id" column="id" />
            <result property="tenantId" column="tenant_id" />
            <result property="deptId" column="dept_id" />
            <result property="templateName" column="template_name" />
            <result property="templateSceneId" column="template_scene_id" />
            <result property="type" column="type" />
            <result property="code" column="code" />
            <result property="version" column="version" />
            <result property="usageCount" column="usage_count" />
            <result property="status" column="status" />
            <result property="description" column="description" />
            <result property="iconUrl" column="icon_url" />
            <result property="content" column="content" />
            <result property="isDefault" column="is_default" />
            <result property="delFlag" column="del_flag" />
            <result property="createBy" column="create_by" />
            <result property="createByName" column="create_by_name" />
            <result property="createTime" column="create_time" />
            <result property="createById" column="create_by_id" />
            <result property="updateById" column="update_by_id" />
            <result property="updateBy" column="update_by" />
            <result property="updateByName" column="update_by_name" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,template_name,template_scene_id,type,
        code,version,usage_count,status,description,icon_url,content,is_default,del_flag,
        create_by,create_by_name,create_time,create_by_id,update_by_id,
        update_by,update_by_name,update_time
    </sql>
<!--    <select id="selectWithSceneName" resultType="com.ylz.saas.resp.BaseAnnouncementTemplateQueryResp">-->
<!--    SELECT-->
<!--        t.id,-->
<!--        t.tenant_id AS tenantId,-->
<!--        t.dept_id AS deptId,-->
<!--        t.template_name AS templateName,-->
<!--        s.scene_name AS templateSceneName,-->
<!--        t.type,-->
<!--        t.code,-->
<!--        t.version,-->
<!--        t.usage_count AS usageCount,-->
<!--        t.status,-->
<!--        t.description,-->
<!--        t.icon_url AS iconUrl,-->
<!--        t.content,-->
<!--        t.del_flag AS delFlag,-->
<!--        t.create_by AS createBy,-->
<!--        t.create_by_name AS createByName,-->
<!--        t.create_time AS createTime,-->
<!--        t.create_by_id AS createById,-->
<!--        t.update_by_id AS updateById,-->
<!--        t.update_by AS updateBy,-->
<!--        t.update_by_name AS updateByName,-->
<!--        t.update_time AS updateTime-->
<!--        FROM base_templates t-->
<!--        LEFT JOIN base_template_scenes s ON t.template_scene_id = s.id-->
<!--        WHERE t.del_flag = 0-->
<!--        <if test="templateSceneId != null and templateSceneId != ''">-->
<!--            AND t.type = #{type}-->
<!--        </if>-->
<!--        <if test="searchKey != null and searchKey != ''">-->
<!--            AND (t.code LIKE CONCAT('%', #{searchKey}, '%')-->
<!--            OR t.template_name LIKE CONCAT('%', #{searchKey}, '%'))-->
<!--        </if>ORDER BY t.create_time DESC-->
<!--        </select>-->
    <select id="selectByType" resultType="com.ylz.saas.resp.BaseAnnouncementTemplateQueryResp">
        SELECT
        bt.id,
        bt.template_name,
        bt.type,
        bt.code,
        bt.version,
        bt.status,
        bt.description,
        bt.icon_url,
        bt.create_time,
        bt.update_time,
        bt.usage_count,
        bt.is_default
        FROM base_templates bt
        WHERE bt.del_flag = 0
        <if test="type != null and type != ''">
            AND bt.type = #{type}
        </if>
        <if test="searchKey != null and searchKey != ''">
            AND (bt.code LIKE CONCAT('%', #{searchKey}, '%') OR bt.template_name LIKE CONCAT('%', #{searchKey}, '%'))
        </if>
    </select>

</mapper>
