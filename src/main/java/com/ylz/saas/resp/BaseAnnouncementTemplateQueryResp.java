package com.ylz.saas.resp;

import lombok.Data;

import java.time.LocalDateTime;
@Data
public class BaseAnnouncementTemplateQueryResp {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 组织ID
     */
    private Long deptId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板场景ID
     */
    private Long templateSceneId;

    /**
     * 模板场景名称
     */
    private String templateSceneName;


    /**
     * 类型
     */
    private String type;

    /**
     * 编号
     */
    private String code;

    /**
     * 版本号
     */
    private String version;

    /**
     * 使用量
     */
    private Integer usageCount;

    /**
     * 状态（ENABLE-启用、DISABLE-禁用）
     */
    private String status;

    /**
     * 模板说明
     */
    private String description;

    /**
     * 图标
     */
    private String iconUrl;

    /**
     * 模板内容
     */
    private String content;

    /**
     * 是否默认（0-否、1-是）
     */
    private Integer isDefault;

    /**
     * 删除标识（0-正常、1-删除）
     */
    private Integer delFlag;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    private Long createById;

    /**
     * 更新人ID
     */
    private Long updateById;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改人名称
     */
    private String updateByName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
}
