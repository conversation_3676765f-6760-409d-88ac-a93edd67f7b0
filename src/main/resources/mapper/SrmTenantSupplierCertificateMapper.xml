<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenantSupplierCertificateMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenantSupplierCertificate">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="supplierId" column="supplier_id" jdbcType="BIGINT"/>
            <result property="certificateType" column="certificate_type" jdbcType="VARCHAR"/>
            <result property="certificateName" column="certificate_name" jdbcType="VARCHAR"/>
            <result property="certificateNo" column="certificate_no" jdbcType="VARCHAR"/>
            <result property="validStartDate" column="valid_start_date" jdbcType="DATE"/>
            <result property="validEndDate" column="valid_end_date" jdbcType="DATE"/>
            <result property="attachmentUrl" column="attachment_url" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateById" column="update_by_id" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,
        supplier_id,certificate_type,certificate_name,
        certificate_no,valid_start_date,valid_end_date,
        attachment_url,remark,del_flag,
        create_by_id,create_by,create_by_name,
        create_time,update_by_id,update_by,
        update_by_name,update_time
    </sql>

    <delete id="physicalDeleteById">
        DELETE FROM srm_tenant_supplier_certificate WHERE id = #{id}
    </delete>
</mapper>
