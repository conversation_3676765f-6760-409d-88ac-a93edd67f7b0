package com.ylz.saas.codegen.base_material_info_dept.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.codegen.base_material_info_dept.entity.BaseMaterialInfoDeptEntity;
import com.ylz.saas.codegen.base_material_info_dept.mapper.BaseMaterialInfoDeptMapper;
import com.ylz.saas.codegen.base_material_info_dept.service.BaseMaterialInfoDeptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 物料基础信息组织关联表Service实现类
 *
 * <AUTHOR>
 * @date 2025-06-12
 */
@Service
@Slf4j
public class BaseMaterialInfoDeptServiceImpl extends ServiceImpl<BaseMaterialInfoDeptMapper, BaseMaterialInfoDeptEntity> 
        implements BaseMaterialInfoDeptService {

    @Override
    public List<BaseMaterialInfoDeptEntity> getDeptsByMaterialId(Long materialInfoId) {
        if (materialInfoId == null) {
            return new ArrayList<>();
        }
        return baseMapper.selectDeptsByMaterialId(materialInfoId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveMaterialDeptRelations(Long materialInfoId, List<Long> deptIds) {
        if (materialInfoId == null || CollectionUtil.isEmpty(deptIds)) {
            return true;
        }

        // 构建关联关系实体列表
        List<BaseMaterialInfoDeptEntity> relationList = deptIds.stream()
                .distinct() // 去重
                .map(deptId -> {
                    BaseMaterialInfoDeptEntity relation = new BaseMaterialInfoDeptEntity();
                    relation.setMaterialInfoId(materialInfoId);
                    relation.setDeptId(deptId);
                    return relation;
                })
                .collect(Collectors.toList());

        // 批量保存
        return saveBatch(relationList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMaterialDeptRelations(Long materialInfoId, List<Long> deptIds) {
        if (materialInfoId == null) {
            return false;
        }

        // 先删除原有关联关系
        deleteByMaterialId(materialInfoId);

        // 如果有新的组织ID，则保存新的关联关系
        if (CollectionUtil.isNotEmpty(deptIds)) {
            return saveMaterialDeptRelations(materialInfoId, deptIds);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByMaterialId(Long materialInfoId) {
        if (materialInfoId == null) {
            return true;
        }
        return baseMapper.deleteByMaterialId(materialInfoId) >= 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByMaterialIds(List<Long> materialInfoIds) {
        if (CollectionUtil.isEmpty(materialInfoIds)) {
            return true;
        }

        // 批量删除
        for (Long materialInfoId : materialInfoIds) {
            deleteByMaterialId(materialInfoId);
        }
        return true;
    }
}
