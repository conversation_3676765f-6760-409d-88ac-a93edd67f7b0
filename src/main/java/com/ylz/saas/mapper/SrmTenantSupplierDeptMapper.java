package com.ylz.saas.mapper;

import com.ylz.saas.entity.SrmTenantSupplierDept;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【srm_tenant_supplier_dept(租户供应商部门关联表)】的数据库操作Mapper
 * @createDate 2025-06-20
 * @Entity com.ylz.saas.entity.SrmTenantSupplierDept
 */
@Mapper
public interface SrmTenantSupplierDeptMapper extends SaasBaseMapper<SrmTenantSupplierDept> {

    /**
     * 根据供应商ID查询关联的部门列表
     * @param tenantSupplierId 租户供应商ID
     * @return 部门关联列表
     */
    List<SrmTenantSupplierDept> getDeptsByTenantSupplierId(@Param("tenantSupplierId") Long tenantSupplierId);

    /**
     * 根据部门ID查询关联的供应商ID列表
     * @param deptId 部门ID
     * @return 供应商ID列表
     */
    List<Long> getTenantSupplierIdsByDeptId(@Param("deptId") Long deptId);

    /**
     * 物理删除供应商部门关联
     * @param id 关联ID
     * @return 删除的记录数
     */
    int physicalDeleteById(@Param("id") Long id);

    /**
     * 根据供应商ID物理删除所有关联
     * @param tenantSupplierId 租户供应商ID
     * @return 删除的记录数
     */
    int physicalDeleteByTenantSupplierId(@Param("tenantSupplierId") Long tenantSupplierId);
}
