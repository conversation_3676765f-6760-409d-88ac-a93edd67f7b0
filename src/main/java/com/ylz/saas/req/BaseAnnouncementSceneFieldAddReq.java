package com.ylz.saas.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class BaseAnnouncementSceneFieldAddReq {
    /**
     * 模板场景ID
     */
    @NotNull(message = "模板场景id不能为空")
    private Long templateSceneId;

    /**
     * 字段名称
     */
    @NotBlank(message = "字段名称不能为空")
    private String fieldName;
    /**
     * 显示名称
     */
    private String displayName;

    /**
     * 字段类型
     */
    private String fieldType;

    /**
     * 字段长度
     */
    private Integer fieldLength;

    /**
     * 是否必须
     */
    private Integer isRequired;

    /**
     * 默认值
     */
    private String defaultValue;

    /**
     * 字段描述
     */
    private String description;


}
