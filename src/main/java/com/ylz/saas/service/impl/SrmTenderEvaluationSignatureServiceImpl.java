package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.admin.api.entity.SysUser;
import com.ylz.saas.admin.service.SysUserService;
import com.ylz.saas.entity.SrmProjectMember;
import com.ylz.saas.entity.SrmTenderBidEvaluation;
import com.ylz.saas.entity.SrmTenderEvaluationSignature;
import com.ylz.saas.enums.ProjectMemberTypeEnum;
import com.ylz.saas.enums.SignatureStatusEnum;
import com.ylz.saas.mapper.SrmTenderEvaluationSignatureMapper;
import com.ylz.saas.mapper.SrmTenderBidEvaluationMapper;
import com.ylz.saas.service.SrmProjectMemberService;
import com.ylz.saas.service.SrmTenderEvaluationSignatureService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 针对表【srm_tender_evaluation_signature(评标汇总签名进度表)】的数据库操作Service实现
 * <AUTHOR>
 * @createDate 2025-07-11
 */
@Service
@Slf4j
@AllArgsConstructor
public class SrmTenderEvaluationSignatureServiceImpl extends ServiceImpl<SrmTenderEvaluationSignatureMapper, SrmTenderEvaluationSignature>
        implements SrmTenderEvaluationSignatureService {


    private final SrmTenderBidEvaluationMapper srmTenderBidEvaluationMapper;
    private final SrmProjectMemberService srmProjectMemberService;
    private final SysUserService sysUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean signEvaluation(Long evaluationId, Long userId, String signatureComment) {
        // 查询签名记录
        SrmTenderEvaluationSignature signature = baseMapper.getByEvaluationIdAndUserId(evaluationId, userId);
        if (signature == null) {
            log.error("签名记录不存在，评标委员会ID: {}, 用户ID: {}", evaluationId, userId);
            return false;
        }

        // 检查是否已经签名
        if (SignatureStatusEnum.SIGNED.equals(signature.getSignatureStatus())) {
            log.warn("用户已经签名，评标委员会ID: {}, 用户ID: {}", evaluationId, userId);
            return true;
        }

        // 更新签名状态
        signature.setSignatureStatus(SignatureStatusEnum.SIGNED);
        signature.setSignatureTime(LocalDateTime.now());
        signature.setSignatureComment(signatureComment);

        return updateById(signature);
    }

    @Override
    public int[] getSignatureProgress(Long evaluationId) {
        int signedCount = baseMapper.countSignedByEvaluationId(evaluationId);
        int totalCount = baseMapper.countTotalByEvaluationId(evaluationId);
        return new int[]{signedCount, totalCount};
    }

    @Override
    public boolean isAllSigned(Long evaluationId) {
        int[] progress = getSignatureProgress(evaluationId);
        return progress[0] > 0 && progress[0] == progress[1];
    }

    @Override
    public boolean hasUserSigned(Long evaluationId, Long userId) {
        SrmTenderEvaluationSignature signature = baseMapper.getByEvaluationIdAndUserId(evaluationId, userId);
        return signature != null && SignatureStatusEnum.SIGNED.equals(signature.getSignatureStatus());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createSignatureRecords(Long evaluationId) {
        // 查询评标委员会信息
        SrmTenderBidEvaluation evaluation = srmTenderBidEvaluationMapper.selectById(evaluationId);
        if (evaluation == null) {
            log.error("评标委员会不存在，ID: {}", evaluationId);
            return false;
        }

        // 查询评标委员会成员
        List<SrmProjectMember> members = srmProjectMemberService.lambdaQuery()
                .eq(SrmProjectMember::getBusinessId, evaluationId)
                .eq(SrmProjectMember::getMemberType, ProjectMemberTypeEnum.EVALUATION_MEMBER)
                .eq(SrmProjectMember::getDelFlag, 0)
                .list();

        if (members.isEmpty()) {
            log.warn("评标委员会没有成员，ID: {}", evaluationId);
            return true;
        }

        // 检查是否已存在签名记录
        List<SrmTenderEvaluationSignature> existingSignatures = this.lambdaQuery()
                .eq(SrmTenderEvaluationSignature::getEvaluationId, evaluationId)
                .eq(SrmTenderEvaluationSignature::getDelFlag, 0)
                .list();

        Set<Long> existingUserIds = existingSignatures.stream()
                .map(SrmTenderEvaluationSignature::getUserId)
                .collect(Collectors.toSet());

        // 获取新成员的用户ID列表
        List<Long> newMemberUserIds = members.stream()
                .map(SrmProjectMember::getUserId)
                .filter(userId -> !existingUserIds.contains(userId))
                .collect(Collectors.toList());

        if (newMemberUserIds.isEmpty()) {
            return true;
        }

        // 批量查询用户信息获取用户姓名
        List<SysUser> users = sysUserService.lambdaQuery()
                .in(SysUser::getUserId, newMemberUserIds)
                .eq(SysUser::getDelFlag, 0)
                .list();

        Map<Long, String> userNameMap = users.stream()
                .collect(Collectors.toMap(SysUser::getUserId, SysUser::getName));

        // 为新成员创建签名记录
        List<SrmTenderEvaluationSignature> newSignatures = new ArrayList<>();
        for (SrmProjectMember member : members) {
            if (!existingUserIds.contains(member.getUserId())) {
                SrmTenderEvaluationSignature signature = new SrmTenderEvaluationSignature();
                signature.setProjectId(evaluation.getProjectId());
                signature.setNoticeId(evaluation.getNoticeId());
                signature.setSectionId(evaluation.getSectionId());
                signature.setEvaluationId(evaluationId);
                signature.setUserId(member.getUserId());
                // 从用户表中获取真实姓名，如果查不到则使用member中的用户名作为备用
                signature.setUserName(userNameMap.getOrDefault(member.getUserId(),""));
                signature.setSignatureStatus(SignatureStatusEnum.UNSIGNED);
                signature.setDelFlag(0);
                newSignatures.add(signature);
            }
        }

        if (!newSignatures.isEmpty()) {
            return saveBatch(newSignatures);
        }

        return true;
    }

    @Override
    public int[] getSignatureProgressBySectionId(Long sectionId) {
        int signedCount = baseMapper.countSignedBySectionId(sectionId);
        int totalCount = baseMapper.countTotalBySectionId(sectionId);
        return new int[]{signedCount, totalCount};
    }

    @Override
    public boolean hasUserSignedBySectionId(Long sectionId, Long userId) {
        SrmTenderEvaluationSignature signature = baseMapper.getBySectionIdAndUserId(sectionId, userId);
        return signature != null && SignatureStatusEnum.SIGNED.equals(signature.getSignatureStatus());
    }
}
