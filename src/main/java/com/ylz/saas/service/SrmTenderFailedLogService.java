package com.ylz.saas.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.entity.SrmTenderFailedLog;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tender_failed_log(流标记录表)】的数据库操作Service
* @createDate 2025-06-24 20:17:56
*/
public interface SrmTenderFailedLogService extends IService<SrmTenderFailedLog> {

    /**
     * 流标处理通过
     * @param projectAndNoticeId
     * @param projectAndNoticeId
     */
    List<SrmTenderFailedLog> tenderFailedLogDetail(String projectAndNoticeId);

    /**
     * 流标处理通过
     * @param projectId
     * @param noticeId
     */
    void tenderFailedLogPass(String projectId, String noticeId);

    /**
     * 流标处理不通过
     * @param projectId
     * @param noticeId
     */
    void tenderFailedLogReject(String projectId, String noticeId);

    /**
     * 流标审批锁定修改为审批中
     *
     * @param noticeId
     */
    void failedBidApprovingHook(String noticeId);
}
