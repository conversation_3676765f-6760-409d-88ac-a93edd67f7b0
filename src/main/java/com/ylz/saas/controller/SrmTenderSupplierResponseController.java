package com.ylz.saas.controller;

import com.ylz.saas.common.core.util.R;
import com.ylz.saas.entity.SrmTenderSupplierInvite;
import com.ylz.saas.entity.SrmTenderSupplierResponse;
import com.ylz.saas.req.*;
import com.ylz.saas.resp.SrmTenderRegisterInfoListResp;
import com.ylz.saas.service.SrmTenderSupplierResponseService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 投标报名
 * <AUTHOR>
 * @Date 2025/6/10 15:56
 * @Description
 */
@RestController
@RequestMapping("/srmTenderSupplierResponse")
@RequiredArgsConstructor
public class SrmTenderSupplierResponseController {

    private final SrmTenderSupplierResponseService srmTenderSupplierResponseService;


    /**
     * 投标报名
     */
    @PostMapping("/tenderRegister")
    public R<Void> tenderRegister(@RequestBody @Validated SrmTenderSupplierResponseApplyReq req){
        srmTenderSupplierResponseService.tenderRegister(req);
        return R.ok();
    }


    /**
     * 保证金响应
     */
    @PostMapping("/payDeposit")
    public R<Void> payDeposit(@RequestBody @Validated PayDepositReq req){
        srmTenderSupplierResponseService.payDeposit(req);
        return R.ok();
    }

    /**
     * 撤销报名/报价
     */
    @PostMapping("/withdrawn")
    public R<Void> withdrawn(@RequestBody @Validated WithdrawnTenderRegisterReq req){
        srmTenderSupplierResponseService.withdrawn(req);
        return R.ok();
    }


    /**
     * 供方获取标段报名信息
     */
    @PostMapping("/getSelectionRegisterInfo")
    public R<List<SrmTenderRegisterInfoListResp>> getSelectionRegisterInfo(@RequestBody SrmTenderRegisterInfoQueryReq req){
        return R.ok(srmTenderSupplierResponseService.getSelectionRegisterInfo(req));
    }


    /**
     * 获取已报名信息
     */
    @PostMapping("/getRegisteredInfo")
    public R<SrmTenderSupplierResponse> getRegisteredInfo(@RequestBody NoticeAndSectionIdReq req){
        return R.ok(srmTenderSupplierResponseService.getRegisteredInfo(req));
    }


    /**
     * 采方获取已报名的供方列表
     */
    @PostMapping("/getRegisteredSupplierList")
    public R<List<SrmTenderSupplierResponse>> getRegisteredSupplierList(@RequestBody RegisteredSupplierQueryReq req){
        return R.ok(srmTenderSupplierResponseService.getRegisteredSupplierList(req));
    }


    /**
     * 审核供应报名/保证金
     * @param req
     * @return
     */
    @PostMapping("/review")
    public R<Void> reviewRegisterInfo(@RequestBody @Validated ReviewTenderRegisterReq req){
        srmTenderSupplierResponseService.review(req);
        return R.ok();
    }


    /**
     * 供方回执
     */
    @PostMapping("/inviteResponse")
    public R<Void> inviteResponse(@RequestBody @Validated InviteResponseReq req){
        srmTenderSupplierResponseService.inviteResponse(req);
        return R.ok();
    }


    /**
     * 采方查询回执详情
     */
    @PostMapping("/inviteDetail")
    public R<SrmTenderSupplierInvite> inviteDetail(@RequestBody @Validated InviteDetailQueryReq req){
        return R.ok(srmTenderSupplierResponseService.inviteDetail(req));
    }


    /**
     * 已下载标书文件
     */
    @PostMapping("/downloadedBidFileDownloadStatus/{noticeId}/{sectionId}")
    public R<Void> downloadedBidFileDownloadStatus(@PathVariable Long noticeId, @PathVariable Long sectionId){
        srmTenderSupplierResponseService.downloadedBidFileDownloadStatus(noticeId, sectionId);
        return R.ok();
    }


}
