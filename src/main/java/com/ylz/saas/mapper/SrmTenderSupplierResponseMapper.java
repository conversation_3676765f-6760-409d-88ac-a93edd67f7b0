package com.ylz.saas.mapper;

import com.ylz.saas.common.data.datascope.annotation.DataPermission;
import com.ylz.saas.entity.SrmTenderSupplierResponse;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【srm_tender_supplier_response(供应商响应表)】的数据库操作Mapper
* @createDate 2025-06-10 15:49:11
* @Entity com.ylz.saas.entity.SrmTenderSupplierResponse
*/
@Mapper
//@DataPermission(userEnabled = true, dataPermissionRuleList = "[{\"dataTypeCode\":\"PROJECT\",\"targetField\":\"project_id\",\"dataSourceField\":\"ID\"}]")
public interface SrmTenderSupplierResponseMapper extends SaasBaseMapper<SrmTenderSupplierResponse> {

}




