package com.ylz.saas.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @<PERSON> huanghong
 * @Date 2025/7/4 17:37
 * @Description
 */
@Getter
@AllArgsConstructor
public enum TenderSupplierResponseStageEnum {

    REGISTER("报名"),
    PAY_DEPOSIT("保证金支付"),
    PAY_TENDER_FEE("标书费用支付"),
    DOWNLOAD_BID_FILE("下载标书"),
    INVITE("邀请"),
    INVITE_RESPONSE("邀请回执"),
    QUOTE("报价"),
    ;

    private final String desc;

}
