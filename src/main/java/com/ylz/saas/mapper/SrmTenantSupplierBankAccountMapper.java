package com.ylz.saas.mapper;

import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import com.ylz.saas.entity.SrmTenantSupplierBankAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【srm_tenant_supplier_bank_account(租户供应商银行账户表)】的数据库操作Mapper
* @createDate 2025-06-10 18:08:09
* @Entity com.ylz.saas.entity.SrmTenantSupplierBankAccount
*/
@Mapper
public interface SrmTenantSupplierBankAccountMapper extends SaasBaseMapper<SrmTenantSupplierBankAccount> {

    /**
     * 物理删除供应商银行账户
     * @param id 银行账户ID
     * @return 删除的记录数
     */
    int physicalDeleteById(@Param("id") Long id);

}




