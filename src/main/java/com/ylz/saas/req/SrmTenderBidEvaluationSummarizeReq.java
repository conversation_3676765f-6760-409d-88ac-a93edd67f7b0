package com.ylz.saas.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 评标汇总请求类
 */
@Data
@Schema(description = "评标汇总请求类")
public class SrmTenderBidEvaluationSummarizeReq implements Serializable {

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 评标委员会ID
     */
    @NotNull(message = "评标委员会ID不能为空")
    @Schema(description = "评标委员会ID")
    private Long evaluationId;

    /**
     * 节点名称
     */
    @NotNull(message = "节点名称不能为空")
    @Schema(description = "节点名称")
    private String nodeName;

    /**
     * 评审类型（评分项、评审项）
     */
    @NotNull(message = "评审类型不能为空")
    @Schema(description = "评审类型（评分项、评审项）")
    private String type;

    private static final long serialVersionUID = 1L;
}
