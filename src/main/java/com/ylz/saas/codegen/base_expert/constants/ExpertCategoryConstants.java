package com.ylz.saas.codegen.base_expert.constants;

/**
 * 专家分类常量
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
public class ExpertCategoryConstants {

    /**
     * 内部专家
     * 内部专家新增时关联已有的用户账号，来源于SysUser表
     */
    public static final String INTERNAL = "INTERNAL";

    /**
     * 外部专家
     * 外部专家新增时会新增一个用户账号
     */
    public static final String EXTERNAL = "EXTERNAL";





    /**
     * 判断是否为内部专家
     *
     * @param expertCategory 专家分类
     * @return true-内部专家，false-非内部专家
     */
    public static boolean isInternal(String expertCategory) {
        return INTERNAL.equals(expertCategory);
    }

    /**
     * 判断是否为外部专家
     *
     * @param expertCategory 专家分类
     * @return true-外部专家，false-非外部专家
     */
    public static boolean isExternal(String expertCategory) {
        return EXTERNAL.equals(expertCategory);
    }

    /**
     * 验证专家分类是否有效
     *
     * @param expertCategory 专家分类
     * @return true-有效，false-无效
     */
    public static boolean isValid(String expertCategory) {
        return INTERNAL.equals(expertCategory) || EXTERNAL.equals(expertCategory);
    }
}
