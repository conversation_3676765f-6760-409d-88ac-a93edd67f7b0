package com.ylz.saas.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.admin.api.entity.SysUser;
import com.ylz.saas.admin.service.SysUserService;
import com.ylz.saas.codegen.base_agent_org.entity.BaseAgentOrgEntity;
import com.ylz.saas.codegen.base_agent_user.entity.BaseAgentUserEntity;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.data.mybatis.helper.QueryWrapperHelper;
import com.ylz.saas.common.enums.CommonSwitchEnum;
import com.ylz.saas.common.security.util.SecurityUtils;
import com.ylz.saas.entity.SrmTenantSupplierContact;
import com.ylz.saas.entity.SrmTenantSupplierDept;
import com.ylz.saas.entity.SrmTenantSupplierInfo;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.SupplierApprovalStatus;
import com.ylz.saas.enums.SupplierSourceEnum;
import com.ylz.saas.service.*;
import com.ylz.saas.mapper.SrmTenantSupplierInfoMapper;
import com.ylz.saas.vo.SrmTenantSupplierInfoVo;
import com.ylz.saas.vo.SupplierBizFlowUpdateVo;
import com.ylz.saas.vo.SupplierCompleteInfoVo;
import com.ylz.saas.vo.SupplierInviteRequestVo;
import com.ylz.saas.enums.SupplierDeptRelationType;
import com.ylz.saas.vo.SupplierQueryVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【srm_tenant_supplier_info(租户供应商信息表)】的数据库操作Service实现
 * @createDate 2025-06-10 18:08:09
 */
@Service
@AllArgsConstructor
@Slf4j
public class SrmTenantSupplierInfoServiceImpl extends ServiceImpl<SrmTenantSupplierInfoMapper, SrmTenantSupplierInfo>
        implements SrmTenantSupplierInfoService {

    private final SrmTenantSupplierContactService srmTenantSupplierContactService;
    private final SrmTenantSupplierDeptService srmTenantSupplierDeptService;
    private final SysUserService sysUserService;
    private final SrmProcessInstanceService srmProcessInstanceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean switchStatus(Long id) {
        SrmTenantSupplierInfo original = getById(id);
        ExceptionUtil.check(original == null, "500", "供应商不存在");
        // 用户 0未锁定，9已锁定
        String newStatus;
        if (CommonSwitchEnum.ENABLED.name().equals(original.getStatus())) {
            original.setStatus(CommonSwitchEnum.DISABLED.name());
            newStatus = "9";
        } else {
            original.setStatus(CommonSwitchEnum.ENABLED.name());
            newStatus = "0";
        }
        this.updateById(original);

        // 联动修改该机构下所有用户的状态
        List<SrmTenantSupplierContact> userList = srmTenantSupplierContactService.lambdaQuery().eq(SrmTenantSupplierContact::getTenantSupplierId, id).list();
        if (CollectionUtils.isNotEmpty(userList)) {

            // 批量更新sys_user表的status
            List<String> userAccounts = userList.stream().map(SrmTenantSupplierContact::getLoginAccount).filter(Objects::nonNull).toList();
            if (CollectionUtils.isNotEmpty(userAccounts)) {
                sysUserService.lambdaUpdate()
                        .in(SysUser::getUserId, userAccounts)
                        .set(SysUser::getLockFlag, newStatus)
                        .update();
            }
        }
        return true;
    }

    @Override
    public IPage<SrmTenantSupplierInfoVo> getMyTenantSupplierPage(SupplierQueryVo queryVo, Page<SrmTenantSupplierInfo> pageParams) {
        log.info("开始查询我的租户供应商信息，查询条件：{}", queryVo);

        // 获取当前用户的部门ID
        Long currentUserDeptId = null;
        try {
            currentUserDeptId = SecurityUtils.getUser().getDeptId();
            if (currentUserDeptId == null) {
                log.warn("当前用户没有部门信息，返回空结果");
                return createEmptyPage(pageParams);
            }
        } catch (Exception e) {
            log.error("获取当前用户部门信息失败", e);
            return createEmptyPage(pageParams);
        }

        // 构建分页参数
        Page<SrmTenantSupplierInfoVo> voPageParams = new Page<>();
        voPageParams.setCurrent(pageParams.getCurrent());
        voPageParams.setSize(pageParams.getSize());

        // 使用自定义SQL查询，联查联系人信息
        IPage<SrmTenantSupplierInfoVo> result = this.baseMapper.getMyTenantSupplierPageWithContact(
                voPageParams, queryVo.getSupplierName(), queryVo.getApprovalStatus(), currentUserDeptId,
                queryVo.getSupplierType(), queryVo.getBusinessNature(), queryVo.getStatus());

        log.info("查询我的租户供应商信息完成，总记录数：{}", result.getTotal());
        return result;
    }

    @Override
    public IPage<SrmTenantSupplierInfoVo> getRegisterTenantSupplierPage(SupplierQueryVo queryVo, Page<SrmTenantSupplierInfo> pageParams) {
        log.info("开始查询注册租户供应商信息，查询条件：{}", queryVo);

        // 构建查询条件
        QueryWrapper<SrmTenantSupplierInfo> wrapper = new QueryWrapper<>();
        wrapper.eq("del_flag", 0);
        wrapper.eq("supplier_source", SupplierSourceEnum.SUPPLIER_REGISTER.name());
        wrapper.orderByDesc("id");

        // 添加供应商名称模糊查询
        if (StrUtil.isNotBlank(queryVo.getSupplierName())) {
            wrapper.like("supplier_name", queryVo.getSupplierName());
        }

        // 添加供应商类型查询（多选）
        if (queryVo.getSupplierType() != null && !queryVo.getSupplierType().isEmpty()) {
            wrapper.in("supplier_type", queryVo.getSupplierType());
        }

        // 添加企业性质查询（多选）
        if (queryVo.getBusinessNature() != null && !queryVo.getBusinessNature().isEmpty()) {
            wrapper.in("enterprise_nature", queryVo.getBusinessNature());
        }

        // 添加启用状态查询
        if (StrUtil.isNotBlank(queryVo.getStatus())) {
            wrapper.eq("status", queryVo.getStatus());
        }

        // 添加审批状态查询
        if (StrUtil.isNotBlank(queryVo.getApprovalStatus())) {
            wrapper.eq("approval_status", queryVo.getApprovalStatus());
        }

        // 执行分页查询
        Page<SrmTenantSupplierInfo> tenantSupplierInfoPage = this.page(pageParams, wrapper);

        // 转换为VO并返回
        return convertToVoPage(tenantSupplierInfoPage);
    }

    /**
     * 创建空的分页结果
     *
     * @param pageParams 分页参数
     * @return 空的分页结果
     */
    private IPage<SrmTenantSupplierInfoVo> createEmptyPage(Page<SrmTenantSupplierInfo> pageParams) {
        Page<SrmTenantSupplierInfoVo> emptyPage = new Page<>();
        emptyPage.setRecords(List.of());
        emptyPage.setTotal(0);
        emptyPage.setSize(pageParams.getSize());
        emptyPage.setCurrent(pageParams.getCurrent());
        return emptyPage;
    }

    /**
     * 将实体分页结果转换为VO分页结果
     *
     * @param entityPage 实体分页结果
     * @return VO分页结果
     */
    private IPage<SrmTenantSupplierInfoVo> convertToVoPage(Page<SrmTenantSupplierInfo> entityPage) {
        // 转换为VO
        List<SrmTenantSupplierInfoVo> supplierInfoVoList = entityPage.getRecords().stream()
                .map(item -> BeanUtil.toBean(item, SrmTenantSupplierInfoVo.class))
                .toList();

        Page<SrmTenantSupplierInfoVo> resultPage = new Page<>();
        resultPage.setRecords(supplierInfoVoList);
        resultPage.setTotal(entityPage.getTotal());
        resultPage.setSize(entityPage.getSize());
        resultPage.setCurrent(entityPage.getCurrent());
        return resultPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean inviteSupplier(SupplierCompleteInfoVo supplierInviteRequestVo) {
        log.info("开始邀请供应商建立部门关联关系，供应商ID：{}，部门关联数量：{}",
                supplierInviteRequestVo.getId(),
                supplierInviteRequestVo.getDeptIds().size());

        if (CollectionUtils.isEmpty(supplierInviteRequestVo.getDeptIds())) {
            return true;
        }
        Long tenantSupplierId = supplierInviteRequestVo.getId();

        // 验证供应商是否存在
        SrmTenantSupplierInfo supplierInfo = this.getById(tenantSupplierId);
        if (supplierInfo == null) {
            log.error("供应商不存在，ID：{}", tenantSupplierId);
            throw new RuntimeException("供应商不存在");
        }

        // 验证供应商是否已审核通过
        if (!SupplierApprovalStatus.APPROVED.name().equals(supplierInfo.getApprovalStatus())) {
            log.error("供应商未审核通过，无法邀请，供应商ID：{}，当前状态：{}", tenantSupplierId, supplierInfo.getApprovalStatus());
            throw new RuntimeException("只能邀请已审核通过的供应商");
        }
        // 验证供应商是否来源于供应商注册
        if (!SupplierSourceEnum.SUPPLIER_REGISTER.name().equals(supplierInfo.getSupplierSource())) {
            log.error("只能邀请来源于供应商注册的供应商，供应商ID：{}，当前来源：{}", tenantSupplierId, supplierInfo.getSupplierSource());
            throw new RuntimeException("只能邀请来源于供应商注册的供应商");
        }
        // 批量创建邀请关联关系
        List<Long> deptIds = supplierInviteRequestVo.getDeptIds();

        // 使用邀请关联类型和邀请审核中状态创建关联关系
        boolean result = srmTenantSupplierDeptService.createSupplierDeptRelations(
                tenantSupplierId,
                deptIds,
                SupplierDeptRelationType.INVITE.getCode(),
                SupplierApprovalStatus.INVITE_APPROVING.name()
        );

        if (result) {
            log.info("供应商邀请成功，供应商ID：{}，邀请部门数量：{}", tenantSupplierId, deptIds.size());
        } else {
            log.error("供应商邀请失败，供应商ID：{}", tenantSupplierId);
            throw new RuntimeException("供应商邀请失败");
        }

        return result;
    }

    @Override
    public IPage<SrmTenantSupplierInfoVo> getTenantSupplierPageWithInviteStatus(SupplierQueryVo queryVo, Page<SrmTenantSupplierInfo> pageParams) {
        log.info("开始查询租户供应商信息（含邀请状态），查询条件：{}", queryVo);

        // 获取当前用户的部门ID
        Long currentUserDeptId = null;
        try {
            currentUserDeptId = SecurityUtils.getUser().getDeptId();
            if (currentUserDeptId == null) {
                log.warn("当前用户没有部门信息，返回空结果");
                return createEmptyPage(pageParams);
            }
        } catch (Exception e) {
            log.error("获取当前用户部门信息失败", e);
            return createEmptyPage(pageParams);
        }

        // 构建分页参数
        Page<SrmTenantSupplierInfoVo> voPageParams = new Page<>();
        voPageParams.setCurrent(pageParams.getCurrent());
        voPageParams.setSize(pageParams.getSize());

        // 使用自定义SQL查询，包含邀请状态信息
        IPage<SrmTenantSupplierInfoVo> result = this.baseMapper.getTenantSupplierPageWithInviteStatus(
                voPageParams, queryVo.getSupplierName(), currentUserDeptId,
                queryVo.getSupplierType(), queryVo.getBusinessNature(), queryVo.getStatus());

        log.info("查询租户供应商信息（含邀请状态）完成，总记录数：{}", result.getTotal());
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean supplierAuditPass(String supplierCode, Long supplierDeptId) {
        SrmTenantSupplierInfo one = this.lambdaQuery().eq(SrmTenantSupplierInfo::getSupplierCode, supplierCode).one();
        if (supplierDeptId != null) {
            SrmTenantSupplierDept byId = srmTenantSupplierDeptService.getById(supplierDeptId);
            String relationType = byId.getRelationType();
            if (SupplierDeptRelationType.INVITE.name().equals(relationType)) {
//                邀请的
                byId.setApprovalStatus(SupplierApprovalStatus.INVITE_APPROVED.name());
            } else if (SupplierDeptRelationType.CREATE.name().equals(relationType)) {
//                创建的
                byId.setApprovalStatus(SupplierApprovalStatus.APPROVED.name());
                one.setApprovalStatus(SupplierApprovalStatus.APPROVED.name());
            }
            srmTenantSupplierDeptService.updateById(byId);
        }else{
            one.setApprovalStatus(SupplierApprovalStatus.APPROVED.name());
        }

//        srmTenantSupplierContactService.createSupplierUser(supplierContactInfoVo, tenantId);
        srmProcessInstanceService.handlerInstanceStatus(one.getId(), SrmProcessConfigService.BizTypeEnum.SRM_SUPPLIER_AUDIT, ApproveStatusEnum.APPROVE);
        return this.updateById(one);
    }

    @Override
    public boolean supplierAuditUnpass(String supplierCode, Long supplierDeptId) {
        SrmTenantSupplierInfo one = this.lambdaQuery().eq(SrmTenantSupplierInfo::getSupplierCode, supplierCode).one();
        if (supplierDeptId != null) {
            SrmTenantSupplierDept byId = srmTenantSupplierDeptService.getById(supplierDeptId);
            String relationType = byId.getRelationType();
            if (SupplierDeptRelationType.INVITE.name().equals(relationType)) {
//                邀请的
                byId.setApprovalStatus(SupplierApprovalStatus.INVITE_REJECTED.name());


            } else if (SupplierDeptRelationType.CREATE.name().equals(relationType)) {
//                创建的
                byId.setApprovalStatus(SupplierApprovalStatus.REJECTED.name());
                one.setApprovalStatus(SupplierApprovalStatus.REJECTED.name());
            }
            srmTenantSupplierDeptService.updateById(byId);
        }else{
            one.setApprovalStatus(SupplierApprovalStatus.REJECTED.name());
        }

        srmProcessInstanceService.handlerInstanceStatus(one.getId(), SrmProcessConfigService.BizTypeEnum.SRM_SUPPLIER_AUDIT, ApproveStatusEnum.APPROVE_REJECT);

        return this.updateById(one);
    }

    @Override
    public boolean bizFlowUpdate(SupplierBizFlowUpdateVo supplierBizFlowUpdateVo) {
        log.info("开始更新供应商审批状态，供应商ID：{}，审批状态：{}",
                supplierBizFlowUpdateVo.getId(), supplierBizFlowUpdateVo.getApprovalStatus());

        final SrmTenantSupplierInfo srmTenantSupplierInfo = getById(supplierBizFlowUpdateVo.getId());
        if (srmTenantSupplierInfo == null) {
            log.error("供应商信息不存在，ID：{}", supplierBizFlowUpdateVo.getId());
            throw new RuntimeException("供应商信息不存在");
        }

        // 复制属性，将审批状态转换为字符串
        if (supplierBizFlowUpdateVo.getApprovalStatus() != null) {
            srmTenantSupplierInfo.setApprovalStatus(supplierBizFlowUpdateVo.getApprovalStatus().name());
        }
//        if (supplierBizFlowUpdateVo.getCurrentApprover() != null) {
//            srmTenantSupplierInfo.setCurrentApprover(supplierBizFlowUpdateVo.getCurrentApprover());
//        }

        boolean result = updateById(srmTenantSupplierInfo);
        log.info("供应商审批状态更新完成，结果：{}", result);
        return result;
    }
}




