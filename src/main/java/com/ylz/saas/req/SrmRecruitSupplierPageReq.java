package com.ylz.saas.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 供应商招募分页查询请求类
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@Schema(description = "供应商招募分页查询请求类")
public class SrmRecruitSupplierPageReq implements Serializable {

    /**
     * 采购部门id
     */
    @Schema(description = "采购部门id")
    private Long purchaseDeptId;

    /**
     * 招募参与人员id
     */
    @Schema(description = "招募参与人员id")
    private Long memberId;

    /**
     * 招募编码（模糊查询）
     */
    @Schema(description = "招募编码（模糊查询）")
    private String recruitCode;

    /**
     * 招募公告标题（模糊查询）
     */
    @Schema(description = "招募公告标题（模糊查询）")
    private String recruitTitle;

    /**
     * 招募负责人（名称模糊查询）
     */
    @Schema(description = "招募负责人（名称模糊查询）")
    private String recruitLeaderName;

    /**
     * 采购部门（名称模糊查询）
     */
    @Schema(description = "采购部门（名称模糊查询）")
    private String purchaseDeptName;

    /**
     * 报名开始时间
     */
    @Schema(description = "报名开始时间")
    private LocalDateTime signUpStartTime;

    /**
     * 报名结束时间
     */
    @Schema(description = "报名结束时间")
    private LocalDateTime signUpEndTime;

    /**
     * 招募进程（枚举）
     */
    @Schema(description = "招募进程（枚举）")
    private String process;

    private static final long serialVersionUID = 1L;
}
