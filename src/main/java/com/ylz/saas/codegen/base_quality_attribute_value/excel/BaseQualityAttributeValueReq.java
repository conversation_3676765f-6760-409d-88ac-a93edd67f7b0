package com.ylz.saas.codegen.base_quality_attribute_value.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 质量属性值表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:42:43
 */
@Data
public class BaseQualityAttributeValueReq implements Serializable {




    /**
  	 * 属性值内容
   	 */
	@ExcelProperty(value = "属性值内容")
    private String valueContent;


    /**
  	 * 适用物料编码
   	 */
	@ExcelProperty(value = "适用物料编码")
    private String materialCode;


    /**
  	 * 拒收标准
   	 */
	@ExcelProperty(value = "拒收标准")
    private String rejectionStandard;


    /**
  	 * 扣款标准
   	 */
	@ExcelProperty(value = "扣款标准")
    private String penaltyStandard;











}