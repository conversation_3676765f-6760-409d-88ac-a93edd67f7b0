package com.ylz.saas.req;

import com.ylz.saas.service.SrmProcessConfigService;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Set;

/**
 * 流程实例分组查询请求参数
 * <AUTHOR>
 * @Date 2025-07-08
 * @Description
 */
@Data
@Schema(description = "流程实例分组查询请求参数")
public class ProcessInstanceGroupQueryReq {

    /**
     * 业务ID集合
     */
    @Schema(description = "业务ID集合", required = true)
    private Set<Long> bizIds;

    /**
     * 业务类型集合
     */
    @Schema(description = "业务类型集合，为空时查询所有类型")
    private Set<SrmProcessConfigService.BizTypeEnum> types;
}
