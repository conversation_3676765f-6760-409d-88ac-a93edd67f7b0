package com.ylz.saas.service;

import com.ylz.saas.entity.SrmTenderRequirement;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.enums.BidsSegmentTypeEnum;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【srm_tender_requirement(招标要求表)】的数据库操作Service
* @createDate 2025-06-10 14:39:25
*/
public interface SrmTenderRequirementService extends IService<SrmTenderRequirement> {

    /**
     * 是否需要保证金
     *
     * @param noticeId
     * @param sectionId
     * @return
     */
    boolean isNeedEffInfo(Long noticeId, Long sectionId);

    /**
     * 是否需要保证金
     *
     * @param noticeId
     * @param sectionIds
     * @return
     */
    Map<Long,Boolean> isNeedEffInfoBySectionIds(Long noticeId, List<Long> sectionIds, BidsSegmentTypeEnum requirementType);


    /**
     * 是否需要标书费
     *
     * @param noticeIds
     * @return
     */
    Map<Long, Boolean> isNeedRequirement(List<Long> noticeIds, BidsSegmentTypeEnum requirementType);
}
