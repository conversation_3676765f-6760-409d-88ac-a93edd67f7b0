package com.ylz.saas.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ylz.saas.admin.api.dto.MessageSmsDTO;
import com.ylz.saas.codegen.base_service_type.entity.BaseServiceTypeEntity;
import com.ylz.saas.codegen.base_service_type.service.BaseServiceTypeService;
import com.ylz.saas.codegen.base_service_type_field.entity.BaseServiceTypeFieldEntity;
import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.enums.CodeGeneratorPrefixEnum;
import com.ylz.saas.common.security.service.SaasUser;
import com.ylz.saas.common.security.util.SecurityUtils;
import com.ylz.saas.common.sequence.generator.CodeGenerator;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.entity.SrmProcurementProjectItem;
import com.ylz.saas.entity.SrmProcurementProjectSection;
import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.entity.SrmProjectMember;
import com.ylz.saas.entity.SrmTenderNotice;
import com.ylz.saas.entity.SrmTenderOpen;
import com.ylz.saas.entity.SrmTenderRequirement;
import com.ylz.saas.entity.SrmTenderSupplierInvite;
import com.ylz.saas.entity.*;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.AttachmentTypeEnum;
import com.ylz.saas.enums.BidsSegmentTypeEnum;
import com.ylz.saas.enums.FixedFieldEnum;
import com.ylz.saas.enums.InviteMethodEnum;
import com.ylz.saas.enums.InviteStatusEnum;
import com.ylz.saas.enums.OpenStatusEnum;
import com.ylz.saas.enums.ProjectMemberRoleEnum;
import com.ylz.saas.enums.ProjectMemberTypeEnum;
import com.ylz.saas.enums.ProjectProgressStatusEnum;
import com.ylz.saas.enums.PublicNoticeStatusEnum;
import com.ylz.saas.enums.TenderWayEnum;
import com.ylz.saas.req.AttachmentInfoReq;
import com.ylz.saas.req.ProcessInstanceStartReq;
import com.ylz.saas.req.SrmEnsilageAddReq;
import com.ylz.saas.req.SrmProcurementProjectCreateReq;
import com.ylz.saas.req.SrmTenderNoticeAddReq;
import com.ylz.saas.req.SrmTenderOpenAddReq;
import com.ylz.saas.service.ApproveRejectHookService;
import com.ylz.saas.service.SrmProcessConfigService;
import com.ylz.saas.service.SrmProcessInstanceService;
import com.ylz.saas.service.SrmProcurementProjectItemService;
import com.ylz.saas.service.SrmProcurementProjectSectionService;
import com.ylz.saas.service.SrmProcurementProjectService;
import com.ylz.saas.service.SrmProjectAttachmentService;
import com.ylz.saas.service.SrmProjectMemberService;
import com.ylz.saas.service.SrmTenderEnsilageService;
import com.ylz.saas.service.SrmTenderNoticeService;
import com.ylz.saas.service.SrmTenderOpenService;
import com.ylz.saas.service.SrmTenderRequirementService;
import com.ylz.saas.service.SrmTenderSupplierInviteService;
import com.ylz.saas.service.*;
import com.ylz.saas.vo.DynamicFieldVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SrmTenderEnsilageServiceImpl implements SrmTenderEnsilageService, ApproveRejectHookService {

    @Value("${saas.serviceTypeCode}")
    private String serviceTypeCode;

    @Resource
    private SrmProcurementProjectService projectService;

    @Resource
    private SrmTenderNoticeService noticeService;

    @Resource
    private BaseServiceTypeService serverTypeService;

    @Resource
    private SrmTenderRequirementService requirementService;

    @Resource
    private SrmProjectAttachmentService attachmentService;

    @Resource
    private SrmTenderSupplierInviteService invoiceService;

    @Resource
    private CodeGenerator codeGenerator;

    @Resource
    private SrmProcessInstanceService srmProcessInstanceService;

    @Resource
    private SrmTenderOpenService srmTenderOpenService;

    @Resource
    private SrmProcurementProjectSectionService srmProcurementProjectSectionService;

    @Resource
    private SrmProcurementProjectItemService srmProcurementProjectItemService;

    @Resource
    private BaseServiceTypeFieldService baseServiceTypeFieldService;

    @Resource
    private SrmProjectMemberService srmProjectMemberService;

    @Resource
    private SrmTenantSupplierInfoService srmTenantSupplierInfoService;

    @Resource
    private AsyncSmsService asyncSmsService;

    @Value("${app.api-url:https://ayn.canpanscp.com}")
    private String appApiUrl;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void upsertEnsilage(SrmEnsilageAddReq req) {
        // 校验入参
        checkRequest(req);
        LocalDateTime bidOpenTime = LocalDateTime.now().plusMinutes(10L);
        // 创建项目
        Long projectId = upsertProject(req,bidOpenTime);

        // 创建公告信息
        Long noticeId = upsertTenderNotice(req, projectId,bidOpenTime);

        // 设置附件信息
        upsertAttachments(req.getAttachmentInfos(), noticeId, projectId);

        // 创建流程
        startWorkflow(noticeId,projectId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long handlerEnsilageNotice(SrmEnsilageAddReq req) {
        // 校验入参
        checkRequest(req);
        LocalDateTime bidOpenTime = LocalDateTime.now().plusMinutes(10L);
        // 创建项目
        Long projectId = upsertProject(req, bidOpenTime);

        // 创建公告信息
        Long noticeId = upsertTenderNotice(req, projectId, bidOpenTime);

        // 设置附件信息
        upsertAttachments(req.getAttachmentInfos(), noticeId, projectId);
        return noticeId;
    }

    private void checkRequest(SrmEnsilageAddReq req) {
        String demandName = req.getDemandName();
        Long noticeId = req.getNoticeId();
        // 需求名称不能重复
        Long count;
        if(noticeId != null){
            count = noticeService.lambdaQuery().eq(SrmTenderNotice::getNoticeTitle, demandName).ne(SrmTenderNotice::getId, noticeId).count();
        } else {
            count = noticeService.lambdaQuery().eq(SrmTenderNotice::getNoticeTitle, demandName).count();
        }
        if (count > 0L){
            ExceptionUtil.checkNonNull(null, "需求名称已存在!");
        }
        // 是否收取保证金
        Boolean needDeposit = req.getNeedDeposit();
        if (needDeposit) {
            // 获取保证金金额
            BigDecimal guaranteeAmount = req.getGuaranteeAmount();
            String payAccount = req.getPayAccount();
            if(guaranteeAmount.compareTo(BigDecimal.ZERO) == 0 || StringUtils.isBlank(payAccount)){
                ExceptionUtil.checkNonNull(null, "保证金设置-保证金金额不能为空！");
            }
        }
        // 联系人信息校验
        String contactPerson = req.getContactPerson();
        if(StringUtils.isBlank(contactPerson)){
            ExceptionUtil.checkNonNull(null, "联系人信息-联系人人不能为空！");
        }
        String contactPhone = req.getContactPhone();
        if(StringUtils.isBlank(contactPhone)){
            ExceptionUtil.checkNonNull(null, "联系人信息-联系电话不能为空！");
        }
    }

    private void startWorkflow(Long noticeId, Long projectId) {
        // 业务流程发起
        ProcessInstanceStartReq startReq = new ProcessInstanceStartReq();
        startReq.setBizKey(String.valueOf(noticeId));
        startReq.setBizId(projectId);
        startReq.setArgs(List.of(noticeId, noticeId));
        startReq.setBizType(SrmProcessConfigService.BizTypeEnum.SRM_TENDER_ENSILAGE_AUDIT);
        srmProcessInstanceService.startProcessInstance(startReq);
        log.info("业务流程发起完成！");
        // 生成开标信息
        SrmTenderOpenAddReq openReq = new SrmTenderOpenAddReq();
        openReq.setNoticeId(noticeId);
        openReq.setProjectId(projectId);
        srmTenderOpenService.addTenderOpen(openReq);
        log.info("生成开标信息完成！");
    }

    private void upsertAttachments(List<AttachmentInfoReq> attachmentInfos, Long noticeId, Long projectId) {
        if (CollectionUtils.isEmpty(attachmentInfos)) {
            return;
        }
        // 先删除已存在附件
        attachmentService.lambdaUpdate()
                .eq(SrmProjectAttachment::getBusinessId, noticeId)
                .in(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.ENSILAGE_QUALITY_STANDARD, AttachmentTypeEnum.ENSILAGE_PRICE_SYSTEM)
                .remove();
        SrmTenderNotice tenderNotice = noticeService.getById(noticeId);
        // 保存附件信息
        List<SrmProjectAttachment> attachments = attachmentInfos.stream().map(info -> {
            SrmProjectAttachment attachment = new SrmProjectAttachment();
            BeanUtils.copyProperties(info, attachment);
            attachment.setProjectId(projectId);
            attachment.setBusinessId(noticeId);
            attachment.setDeptId(tenderNotice.getDeptId());
            attachment.setTenantId(tenderNotice.getTenantId());
            return attachment;
        }).toList();
        attachmentService.saveBatch(attachments);
    }

    private Long upsertTenderNotice(SrmEnsilageAddReq req, Long projectId, LocalDateTime bidOpenTime) {
        SrmTenderNotice srmTenderNotice = new SrmTenderNotice();
        srmTenderNotice.setNoticeTitle(req.getDemandName());
        srmTenderNotice.setProjectId(projectId);
        srmTenderNotice.setProvince(req.getProvince());
        srmTenderNotice.setCity(req.getCity());
        srmTenderNotice.setDistrict(req.getDistrict());
        srmTenderNotice.setAddress(req.getAddress());
        srmTenderNotice.setInterceptPrice(req.getInterceptPrice());
        srmTenderNotice.setOfflineNoticeSupplier(req.getOfflineNoticeSupplier());
        srmTenderNotice.setSupplyStartTime(req.getSupplyStartTime());
        srmTenderNotice.setSupplyEndTime(req.getSupplyEndTime());
        srmTenderNotice.setExecutorId(req.getExecutorId());
        srmTenderNotice.setQuoteStartTime(req.getQuoteStartTime());
        srmTenderNotice.setQuoteEndTime(req.getQuoteEndTime());
        srmTenderNotice.setQuoteValidity(req.getQuoteValidity());
        srmTenderNotice.setProjectPaymentStr(req.getProjectPaymentStr());
        srmTenderNotice.setEnsilageRemark(req.getRemark());
        srmTenderNotice.setIsNxPay(req.getIsNxPay());
        srmTenderNotice.setNeedDeposit(req.getNeedDeposit());
        if(req.getNoticeId() == null){
            String tenderNoticeCode = codeGenerator.generateWithDate(CodeGeneratorPrefixEnum.ZBGG.name());
            srmTenderNotice.setTenderNoticeCode(tenderNoticeCode);
        }else {
            srmTenderNotice.setId(req.getNoticeId());
        }
        srmTenderNotice.setTenderWay(TenderWayEnum.ONLINE);
        srmTenderNotice.setStatus(PublicNoticeStatusEnum.UNPUBLISHED);
        SaasUser user = SecurityUtils.getUser();
        if(user != null){
            srmTenderNotice.setBidOpener(user.getId());
        }
        srmTenderNotice.setBidOpenTime(bidOpenTime); // 设置开标时间
        srmTenderNotice.setCertificateType("NONE");

        // 设置联系人信息
        srmTenderNotice.setContactPerson(req.getContactPerson());
        srmTenderNotice.setContactPhone(req.getContactPhone());
        srmTenderNotice.setContactFixedPhone(req.getContactFixedPhone());
        srmTenderNotice.setContactEmail(req.getContactEmail());
        // 审批状态
        ApproveStatusEnum noticeStatus = req.getNoticeStatus();
        if(noticeStatus != null){
            srmTenderNotice.setNoticeStatus(noticeStatus);
        } else {
            srmTenderNotice.setNoticeStatus(ApproveStatusEnum.TO_APPROVE);
        }
        noticeService.saveOrUpdate(srmTenderNotice);
        Long noticeId = srmTenderNotice.getId();

        // 先删除 requirement
        requirementService.lambdaUpdate()
                .eq(SrmTenderRequirement::getNoticeId, noticeId)
                .remove();
        // 设置保证金信息
        SrmTenderNoticeAddReq.FeeInfo feeInfo = new SrmTenderNoticeAddReq.FeeInfo();
        feeInfo.setGuaranteeAmount(req.getGuaranteeAmount());
        feeInfo.setPayAccount(req.getPayAccount());
        feeInfo.setPayBank(req.getPayBank());
        feeInfo.setOpenAccountBank(req.getOpenAccountBank());
        // 创建 requirement
        SrmTenderRequirement requirement = new SrmTenderRequirement();
        // 获取标段信息
        List<SrmProcurementProjectSection> sectionList = srmProcurementProjectSectionService.lambdaQuery()
                .eq(SrmProcurementProjectSection::getProjectId, projectId).list();
        SrmProcurementProjectSection section = sectionList.get(0);
        Long sectionId = section.getId();
        requirement.setSectionId(sectionId); // 标段获取
        requirement.setRequirementName("保证金");
        requirement.setRequirementContent(JSONObject.toJSONString(feeInfo));
        requirement.setRequirementType(BidsSegmentTypeEnum.FEE);
        requirement.setNoticeId(noticeId);
        Long tenantId = section.getTenantId();
        Long deptId = section.getDeptId();
        requirement.setTenantId(tenantId);
        requirement.setDeptId(deptId);
        requirementService.save(requirement);
        // 邀请供应商信息
        List<SrmTenderNoticeAddReq.InviteSuppliers> inviteSuppliers = req.getInviteSuppliers();
        if (CollectionUtils.isNotEmpty(inviteSuppliers)) {
            invoiceService.lambdaUpdate()
                    .eq(SrmTenderSupplierInvite::getNoticeId, noticeId)
                    .remove();
            List<SrmTenderSupplierInvite> invoices = inviteSuppliers.stream().map(invite -> {
                SrmTenderSupplierInvite invoice = new SrmTenderSupplierInvite();
                invoice.setNoticeId(noticeId);
                invoice.setProjectId(projectId);
                invoice.setSectionId(sectionId);
                invoice.setTenantSupplierId(invite.getTenantSupplierId());
                invoice.setSupplierName(invite.getSupplierName());
                invoice.setInviteStatus(InviteStatusEnum.PENDING);
                invoice.setConcatName(invite.getContactName());
                invoice.setConcatPhone(invite.getContactPhone());
                invoice.setDeptId(deptId);
                invoice.setTenantId(tenantId);
                return invoice;
            }).toList();
            invoiceService.saveBatch(invoices);
        }
        return noticeId;
    }

    private Long upsertProject(SrmEnsilageAddReq req, LocalDateTime bidOpenTime) {
        SrmProcurementProjectCreateReq projectReq = new SrmProcurementProjectCreateReq();
        projectReq.setId(req.getProjectId());
        projectReq.setProjectName(req.getDemandName());
        BaseServiceTypeEntity baseServiceType = serverTypeService.lambdaQuery().eq(BaseServiceTypeEntity::getTypeCode, serviceTypeCode).one();
        Long serviceTypeId = baseServiceType.getId();
        projectReq.setServiceTypeId(serviceTypeId);
        projectReq.setSourcingType(BaseServiceTypeFieldService.BuyWayEnum.QZXJ);
        projectReq.setInviteMethod(InviteMethodEnum.INVITE);
        // 省市区
        String province = req.getProvince();
        projectReq.setProvince(province);
        String city = req.getCity();
        projectReq.setCity(city);
        String district = req.getDistrict();
        projectReq.setDistrict(district);
        String address = req.getAddress();
        projectReq.setAddress(address);
        projectReq.setBudgetAmount(BigDecimal.ONE);
        projectReq.setPurchaseReason("采购意图");
        projectReq.setSupplierRequirement("供应商要求");
        projectReq.setProjectDesc("项目概况");
        projectReq.setQuoteStartTime(req.getQuoteStartTime());
        projectReq.setQuoteEndTime(req.getQuoteEndTime());
        projectReq.setSourcingMethod("MCZC");
        // 部门id
        SaasUser user = SecurityUtils.getUser();
        Long deptId;
        // 项目小组成员
        List<SrmProcurementProjectCreateReq.ProjectMember> projectMembers = new ArrayList<>();
        SrmProcurementProjectCreateReq.ProjectMember projectMember = new SrmProcurementProjectCreateReq.ProjectMember();
        projectMember.setRole(ProjectMemberRoleEnum.PROJECT_LEADER);
        if(user != null){
            deptId= user.getDeptId();
            projectMember.setUserId(user.getId());
            projectMember.setContactPhone(user.getPhone());
        } else {
            List<SrmProjectMember> srmProjectMembers = srmProjectMemberService.lambdaQuery()
                    .eq(SrmProjectMember::getBusinessId, req.getProjectId())
                    .eq(SrmProjectMember::getRole, ProjectMemberRoleEnum.PROJECT_LEADER)
                    .eq(SrmProjectMember::getMemberType, ProjectMemberTypeEnum.PROJECT_MEMBER)
                    .list();
            SrmProjectMember srmProjectMember = srmProjectMembers.get(0);
            deptId = srmProjectMember.getDeptId();
            projectMember.setUserId(srmProjectMember.getUserId());
            projectMember.setContactPhone(srmProjectMember.getContactPhone());
            projectMember.setDeptId(deptId);
            projectMember.setTenantId(srmProjectMember.getTenantId());
        }
        projectReq.setPurchaseDeptId(String.valueOf(deptId));
        projectReq.setBuyerDeptId(deptId);
        // 项目成员
        projectMembers.add(projectMember);
        projectReq.setProjectMembers(projectMembers);
        // 标段信息
        List<SrmProcurementProjectCreateReq.Section> sections = new ArrayList<>();
        SrmProcurementProjectCreateReq.Section section = new SrmProcurementProjectCreateReq.Section();
        section.setSectionName("青贮标段");
        section.setSectionCode(codeGenerator.generateWithDate(CodeGeneratorPrefixEnum.QZBD.name()));
        List<SrmProcurementProjectCreateReq.ProjectItem> projectItems = new ArrayList<>();
        List<DynamicFieldVo> dynamicFieldList = new ArrayList<>();
        SrmProcurementProjectCreateReq.ProjectItem projectItem = new SrmProcurementProjectCreateReq.ProjectItem();
        List<BaseServiceTypeFieldEntity> baseServiceTypeFields = baseServiceTypeFieldService.lambdaQuery()
                .eq(BaseServiceTypeFieldEntity::getServiceTypeId, serviceTypeId)
                .eq(BaseServiceTypeFieldEntity::getBuyWay, BaseServiceTypeFieldService.BuyWayEnum.QZXJ).list();
        baseServiceTypeFields.stream().map(BaseServiceTypeFieldEntity::getFieldCode).toList()
                .forEach(fieldCode -> {
            DynamicFieldVo dynamicFieldVo = new DynamicFieldVo();
            dynamicFieldVo.setCode(fieldCode);
            if (Objects.equals(fieldCode, FixedFieldEnum.MATERIAL_CODE.getCode())) {
                dynamicFieldVo.setValue(req.getMaterialCode());
            } else if (Objects.equals(fieldCode, FixedFieldEnum.MATERIAL_NAME.getCode())) {
                dynamicFieldVo.setValue(req.getMaterialName());
            } else if (Objects.equals(fieldCode, FixedFieldEnum.REQUIRED_QUANTITY.getCode())) {
                dynamicFieldVo.setValue(req.getRequiredQuantity().toString());
            } else if (Objects.equals(fieldCode, FixedFieldEnum.USAGE_LOCATION_ID.getCode())) {
                dynamicFieldVo.setValue(req.getUsageLocationId().toString());
            } else if (Objects.equals(fieldCode, FixedFieldEnum.QUALITY_INDICATOR_ID.getCode())) {
                dynamicFieldVo.setValue("");
            } else if (Objects.equals(fieldCode, FixedFieldEnum.UNIT.getCode())) {
                dynamicFieldVo.setValue(req.getUnit());
            }
            dynamicFieldList.add(dynamicFieldVo);
        });
        projectItem.setDynamicFieldList(dynamicFieldList);
        projectItems.add(projectItem);
        section.setProjectItems(projectItems);
        sections.add(section);
        projectReq.setSections(sections);
        // 支付信息
        List<SrmProcurementProjectCreateReq.PaymentInfo> paymentInfoList = new ArrayList<>();
        SrmProcurementProjectCreateReq.PaymentInfo paymentInfo = new SrmProcurementProjectCreateReq.PaymentInfo();
        paymentInfo.setPaymentMethodId(-1L); // todo 默认值
        paymentInfo.setPaymentPeriodId(-1L); // todo 默认值
        paymentInfoList.add(paymentInfo);
        projectReq.setPaymentInfoList(paymentInfoList);
        projectReq.setBidOpenTime(bidOpenTime); // 时间10分钟后开标

        SrmProcurementProject srmProcurementProject = projectService.upsertSrmProcurementProject(projectReq, SrmProcurementProjectService.DocumentStatus.EFFECT
                , ApproveStatusEnum.APPROVE, ProjectProgressStatusEnum.TO_NOTICE);
        return srmProcurementProject.getId();
    }

    @Override
    public SrmEnsilageAddReq detailEnsilage(Long noticeId) {
        SrmTenderNotice notice = noticeService.getById(noticeId);
        ExceptionUtil.checkNonNull(notice, "青贮信息不存在！");

        Long projectId = notice.getProjectId();
        SrmEnsilageAddReq resp = new SrmEnsilageAddReq();
        resp.setNoticeId(noticeId);
        resp.setProjectId(projectId);
        resp.setDemandName(notice.getNoticeTitle());
        resp.setProvince(notice.getProvince());
        resp.setCity(notice.getCity());
        resp.setDistrict(notice.getDistrict());
        resp.setAddress(notice.getAddress());
        resp.setInterceptPrice(notice.getInterceptPrice());
        resp.setOfflineNoticeSupplier(notice.getOfflineNoticeSupplier());
        resp.setSupplyStartTime(notice.getSupplyStartTime());
        resp.setSupplyEndTime(notice.getSupplyEndTime());
        resp.setExecutorId(notice.getExecutorId());
        resp.setQuoteStartTime(notice.getQuoteStartTime());
        resp.setQuoteEndTime(notice.getQuoteEndTime());
        resp.setQuoteValidity(notice.getQuoteValidity());
        resp.setProjectPaymentStr(notice.getProjectPaymentStr());
        resp.setRemark(notice.getEnsilageRemark());
        resp.setIsNxPay(notice.getIsNxPay());
        resp.setContactPerson(notice.getContactPerson());
        resp.setContactPhone(notice.getContactPhone());
        resp.setContactFixedPhone(notice.getContactFixedPhone());
        resp.setContactEmail(notice.getContactEmail());

        // 保证金信息
        SrmTenderRequirement requirement = requirementService.lambdaQuery()
                .eq(SrmTenderRequirement::getNoticeId, noticeId)
                .eq(SrmTenderRequirement::getRequirementType, BidsSegmentTypeEnum.FEE)
                .one();
        if (requirement != null) {
            String requirementContent = requirement.getRequirementContent();
            SrmTenderNoticeAddReq.FeeInfo feeInfo = JSONObject.parseObject(requirementContent, SrmTenderNoticeAddReq.FeeInfo.class);
            if (feeInfo != null) {
                resp.setGuaranteeAmount(feeInfo.getGuaranteeAmount());
                resp.setPayAccount(feeInfo.getPayAccount());
                resp.setPayBank(feeInfo.getPayBank());
                resp.setOpenAccountBank(feeInfo.getOpenAccountBank());
                resp.setNeedDeposit(true);
            }else {
                resp.setNeedDeposit(false);
            }
        }

        // 附件信息
        List<SrmProjectAttachment> attachments = attachmentService.lambdaQuery()
                .eq(SrmProjectAttachment::getBusinessId, noticeId)
                .in(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.ENSILAGE_QUALITY_STANDARD,AttachmentTypeEnum.ENSILAGE_PRICE_SYSTEM)
                .list();
        if (CollectionUtils.isNotEmpty(attachments)) {
            List<AttachmentInfoReq> attachmentInfos = new ArrayList<>();
            attachments.forEach(attachment -> {
                AttachmentInfoReq info = new AttachmentInfoReq();
                BeanUtils.copyProperties(attachment, info);
                attachmentInfos.add(info);
            });
            resp.setAttachmentInfos(attachmentInfos);
        }

        // 标段信息（获取物料相关）
        List<SrmProcurementProjectItem> projectItems = srmProcurementProjectItemService.lambdaQuery()
                .eq(SrmProcurementProjectItem::getProjectId, projectId).list();
        if (CollectionUtils.isNotEmpty(projectItems)) {
            for (SrmProcurementProjectItem projectItem : projectItems) {
                resp.setMaterialCode(projectItem.getMaterialCode());
                resp.setMaterialName(projectItem.getMaterialName());
                resp.setRequiredQuantity(projectItem.getRequiredQuantity());
                resp.setUsageLocationId(projectItem.getUsageLocationId());
                resp.setUnit(projectItem.getUnit());
            }
        }
        // 设置供应商信息
        List<SrmTenderSupplierInvite> supplierInvites = invoiceService.lambdaQuery()
                .eq(SrmTenderSupplierInvite::getNoticeId, noticeId)
                .list();
        if (CollectionUtils.isNotEmpty(supplierInvites)) {
            List<SrmTenderNoticeAddReq.InviteSuppliers> inviteSuppliers = supplierInvites.stream()
                    .map(supplier -> {
                        SrmTenderNoticeAddReq.InviteSuppliers inviteSupplier = new SrmTenderNoticeAddReq.InviteSuppliers();
                        BeanUtils.copyProperties(supplier, inviteSupplier);
                        inviteSupplier.setContactName(supplier.getConcatName());
                        inviteSupplier.setContactPhone(supplier.getConcatPhone());
                        return inviteSupplier;
                    }).collect(Collectors.toList());
            resp.setInviteSuppliers(inviteSuppliers);
        }
        return resp;
    }

    @Override
    public void noticeApprovingHook(String noticeId) {
        noticeService.lambdaUpdate().set(SrmTenderNotice::getNoticeStatus, ApproveStatusEnum.APPROVING)
                .eq(SrmTenderNotice::getId, noticeId).update();
    }

    @Override
    public void ensilageApprovePass(String noticeId) {
        noticeService.lambdaUpdate().set(SrmTenderNotice::getNoticeStatus, ApproveStatusEnum.APPROVE)
                // 设置招标公告状态，审批通过就发布
                .set(SrmTenderNotice::getStatus, PublicNoticeStatusEnum.PUBLISHED)
                .eq(SrmTenderNotice::getId, noticeId)
                .update();

        SrmTenderNotice tenderNotice = noticeService.getById(noticeId);
        Long projectId = tenderNotice.getProjectId();
        // 修改开标状态为待开标
        srmTenderOpenService.lambdaUpdate().set(SrmTenderOpen::getOpenStatus, OpenStatusEnum.TO_BID_OPEN)
                .eq(SrmTenderOpen::getNoticeId, noticeId)
                .update();
        SrmProcurementProject procurementProject = projectService.getById(projectId);
        projectService.updateProgressStatus(procurementProject.getProjectCode(), ProjectProgressStatusEnum.NOTICE, true);
        srmProcessInstanceService.handlerInstanceStatus(projectId,SrmProcessConfigService.BizTypeEnum.SRM_TENDER_ENSILAGE_AUDIT,ApproveStatusEnum.APPROVE);

        // 1.发送青贮公告更改短信
        // 获取项目信息用于短信内容变量
        SrmProcurementProject project = projectService.getById(projectId);
        // 判断是新增还是修改 - 通过检查是否已存在流程实例
        List<SrmProcessInstance> processInstances = srmProcessInstanceService.lambdaQuery()
                .eq(SrmProcessInstance::getBizId, noticeId)
                .eq(SrmProcessInstance::getType, SrmProcessConfigService.BizTypeEnum.SRM_TENDER_ENSILAGE_AUDIT)
                .list();
        if( processInstances.size() > 1) { // 如果有超过一条实例为修改
            LambdaQueryWrapper<SrmTenantSupplierInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SrmTenantSupplierInfo::getDelFlag, 0) // 未删除
                    .eq(SrmTenantSupplierInfo::getApprovalStatus, "APPROVED") // 审批状态为已审批
                    .isNotNull(SrmTenantSupplierInfo::getLegalPersonPhone); // 确保法人电话不为空

            List<SrmTenantSupplierInfo> suppliers = srmTenantSupplierInfoService.list(queryWrapper);

            // 为每个供应商发送单独的短信
            for (SrmTenantSupplierInfo tenantSupplier : suppliers) {
                if (tenantSupplier.getLegalPersonPhone() != null) {
                    MessageSmsDTO messageSmsDTO = MessageSmsDTO.builder()
                            .mobile(tenantSupplier.getLegalPersonPhone())
                            .biz("AYN_NOTICE_UPDATE")
                            .param("company_name", tenantSupplier.getSupplierName() != null ?
                                    tenantSupplier.getSupplierName() : "供应商")
                            .param("project_name", project != null ? project.getProjectName() : "")
                            .param("project_code", project != null ? project.getProjectCode() : "")
                            .param("login_url", appApiUrl != null ? appApiUrl : "https://ayn.canpanscp.com")
                            .build();

                    // 调用发送短信的服务方法
                    asyncSmsService.sendSmsAsync(messageSmsDTO);
                }
            }
        }

        // 2.发送青贮邀请供应商短信
        // 获取被邀请的供应商列表
        List<SrmTenderSupplierInvite> invitedSuppliers = invoiceService.lambdaQuery()
                .eq(SrmTenderSupplierInvite::getNoticeId, Long.valueOf(noticeId))
                .eq(SrmTenderSupplierInvite::getProjectId, projectId)
                .list();

            // 为每个被邀请的供应商发送单独的短信
            for (SrmTenderSupplierInvite invite : invitedSuppliers) {
                // 获取供应商详细信息
                SrmTenantSupplierInfo supplierInfo = srmTenantSupplierInfoService.getById(invite.getTenantSupplierId());

                if (supplierInfo != null && supplierInfo.getLegalPersonPhone() != null) {
                    MessageSmsDTO messageSmsDTO = MessageSmsDTO.builder()
                            .mobile(supplierInfo.getLegalPersonPhone())
                            .biz("AYN_INVITE_ISSUE")
                            .param("company_name", supplierInfo.getSupplierName() != null ?
                                    supplierInfo.getSupplierName() : "供应商")
                            .param("project_name", project != null ? project.getProjectName() : "")
                            .param("project_code", project != null ? project.getProjectCode() : "")
                            .param("sourcing_type", String.valueOf(BaseServiceTypeFieldService.BuyWayEnum.QZXJ))
                            .param("start_time", tenderNotice.getQuoteStartTime() != null ?
                                    tenderNotice.getQuoteStartTime().toString() : "")
                            .param("end_time", tenderNotice.getQuoteEndTime() != null ?
                                    tenderNotice.getQuoteEndTime().toString() : "")
                            .param("login_url", appApiUrl != null ? appApiUrl : "https://ayn.canpanscp.com")
                            .build();

                    // 调用发送短信的服务方法
                    asyncSmsService.sendSmsAsync(messageSmsDTO);
                }

        }
    }

    @Override
    public void ensilageApproveReject(String noticeId) {
        noticeService.lambdaUpdate().set(SrmTenderNotice::getNoticeStatus, ApproveStatusEnum.APPROVE_REJECT)
                // 设置招标公告状态，审批通过就发布
                .set(SrmTenderNotice::getStatus, PublicNoticeStatusEnum.UNPUBLISHED)
                .eq(SrmTenderNotice::getId, noticeId)
                .update();

        SrmTenderNotice tenderNotice = noticeService.getById(noticeId);
        Long projectId = tenderNotice.getProjectId();
        // 修改采购项目状态
        srmProcessInstanceService.handlerInstanceStatus(projectId,SrmProcessConfigService.BizTypeEnum.SRM_TENDER_ENSILAGE_AUDIT,ApproveStatusEnum.APPROVE_REJECT);
    }

    @Override
    public boolean match(SrmProcessConfigService.BizTypeEnum bizType) {
        return bizType == SrmProcessConfigService.BizTypeEnum.SRM_TENDER_ENSILAGE_AUDIT;
    }

    @Override
    public void approveRejectHook(String bizKey) {
        noticeService.lambdaUpdate().set(SrmTenderNotice::getNoticeStatus, ApproveStatusEnum.APPROVE_REVOKE)
                .eq(SrmTenderNotice::getId, bizKey)
                .update();
    }
}
