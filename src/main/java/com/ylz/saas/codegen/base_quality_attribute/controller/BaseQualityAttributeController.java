package com.ylz.saas.codegen.base_quality_attribute.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.codegen.base_quality_attribute.entity.BaseQualityAttributeEntity;
import com.ylz.saas.codegen.base_quality_attribute.excel.BaseQualityAttributeResp;
import com.ylz.saas.codegen.base_quality_attribute.param.BaseQualityAttributeParam;
import com.ylz.saas.codegen.base_quality_attribute.service.BaseQualityAttributeService;
import com.ylz.saas.codegen.base_quality_attribute.vo.BaseQualityAttributeVo;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.common.excel.annotation.ResponseExcel;
import com.ylz.saas.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 质量属性表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:42:43
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/baseQualityAttribute" )
@Tag(description = "baseQualityAttribute" , name = "质量属性表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class BaseQualityAttributeController {

    private final  BaseQualityAttributeService baseQualityAttributeService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param param 质量属性表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @PostMapping("/page" )
    public R getBaseQualityAttributePage(@RequestBody BaseQualityAttributeParam param, Page page) {
        QueryWrapper<BaseQualityAttributeEntity> queryWrapper = param.toWrapper(); 
        return R.ok(baseQualityAttributeService.page(page, queryWrapper));
    }
    /**
     * 列表查询
     * param
     * @return
     */
    @Operation(summary = "列表查询" , description = "列表查询" )
    @PostMapping("/list" )
    public R getBaseQualityAttributeList(@RequestBody BaseQualityAttributeParam param) {
        QueryWrapper<BaseQualityAttributeEntity> queryWrapper = param.toWrapper();
        return R.ok(baseQualityAttributeService.list( queryWrapper));
    }
    /**
     * 启用/禁用
     * @return R
     */
    @Operation(summary = "启用/禁用" , description = "启用/禁用" )
    @PostMapping("/switch/{id}")
    public R switchStatus(@PathVariable("id") Long id) {
        return R.ok(baseQualityAttributeService.switchStatus(id));
    }
    /**
     * 通过id查询质量属性表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(baseQualityAttributeService.getByIdBaseQualityAttribute(id));
    }

    /**
     * 新增质量属性表
     * @param baseQualityAttributeVo 质量属性表
     * @return R
     */
    @Operation(summary = "新增质量属性表" , description = "新增质量属性表" )
    @SysLog("新增质量属性表" )
    @PostMapping("/save")
    public R save(@Valid @RequestBody BaseQualityAttributeVo baseQualityAttributeVo) {
        return R.ok(baseQualityAttributeService.saveBaseQualityAttribute(baseQualityAttributeVo));
    }

    /**
     * 修改质量属性表
     * @param baseQualityAttributeVo 质量属性表
     * @return R
     */
    @Operation(summary = "修改质量属性表" , description = "修改质量属性表" )
    @SysLog("修改质量属性表" )
    @PostMapping("/update")
    public R updateById(@Valid @RequestBody BaseQualityAttributeVo baseQualityAttributeVo) {
        return R.ok(baseQualityAttributeService.updateBaseQualityAttribute(baseQualityAttributeVo));
    }

    /**
     * 通过id删除质量属性表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除质量属性表" , description = "通过id删除质量属性表" )
    @SysLog("通过id删除质量属性表" )
    @GetMapping("remove")
    public R removeById(@RequestParam List<Long> ids) {
		boolean remove = baseQualityAttributeService.removeByIdsBaseQualityAttribute(ids);
		return R.ok(remove);
    }

    /**
     * 导出excel 表格
     * @param param 查询条件
     * @return excel 文件流
     */
    @ResponseExcel
    @PostMapping("/export")
    @Operation(summary = "导出excel" , description = "导出excel" )
    public List<BaseQualityAttributeResp> export(@RequestBody BaseQualityAttributeParam param) {
        QueryWrapper<BaseQualityAttributeEntity> queryWrapper = param.toWrapper();
        List<BaseQualityAttributeEntity> entityList = baseQualityAttributeService.list(queryWrapper);
        return BeanUtil.copyToList(entityList, BaseQualityAttributeResp.class);
    }

    /**
     * 导入excel 表格
     * @param file
     * @param response
     * @return
     */
    @PostMapping("/import")
    @Operation(summary = "导入excel" , description = "导入excel" )
    public R uploadData(@RequestParam("file") MultipartFile file, HttpServletResponse response) {
        baseQualityAttributeService.uploadData(file,response);
        return R.ok();
    }

    /**
     * excel模板下载
     * @param response
     */
    @GetMapping("/template")
    @Operation(summary = "excel模板下载" , description = "excel模板下载" )
    public void template(HttpServletResponse response) {
        baseQualityAttributeService.template(response);
    }

}