<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenderOpenMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenderOpen">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="projectId" column="project_id" jdbcType="BIGINT"/>
            <result property="noticeId" column="notice_id" jdbcType="BIGINT"/>
            <result property="sectionId" column="section_id" jdbcType="BIGINT"/>
            <result property="currentRound" column="current_round" jdbcType="INTEGER"/>
            <result property="latestQuoteStartTime" column="latest_quote_start_time" jdbcType="TIMESTAMP"/>
            <result property="latestQuoteEndTime" column="latest_quote_end_time" jdbcType="TIMESTAMP"/>
            <result property="openTime" column="open_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,
        project_id,notice_id,section_id,
        current_round,latest_quote_start_time,latest_quote_end_time,
        open_time,del_flag,create_by,
        create_by_name,create_time,update_by,
        update_by_name,update_time
    </sql>
    <select id="selectOpenTenderMaterials" resultType="com.ylz.saas.resp.SrmOpenTenderMaterialResp">
        SELECT
        spi.material_code,
        spi.material_name,
        sqi.spec_model,
        sqi.unit,
        sqi.required_quantity,
        ssi.id tenantSupplierId,
        ssi.supplier_code,
        ssi.supplier_name,
        ssi.supplier_short_name,
        sqi.available_quantity quoteQuantity,
        sqi.quote_price,
        sqi.quote_amount,
        sqi.quote_time,
        sqi.quote_ip,
        sqi.awarded_quantity
        FROM
        srm_procurement_project_item spi
        LEFT JOIN
        srm_tender_bidder_quote_item sqi on spi.material_code = sqi.material_code and spi.section_id = sqi.section_id
        AND spi.project_id = sqi.project_id
        LEFT JOIN
        srm_tenant_supplier_info ssi ON sqi.tenant_supplier_id = ssi.id
        WHERE
            spi.project_id = #{projectId}
            AND sqi.notice_id = #{noticeId}
            AND spi.section_id = #{sectionId}
            AND sqi.round_no = #{roundNo}
        <if test="materialName != null and materialName != ''">
            AND spi.material_name LIKE CONCAT('%', #{materialName}, '%')
        </if>
        <if test="tenantSupplierId != null">
            AND sqi.tenant_supplier_id = #{tenantSupplierId}
        </if>
    </select>
    <select id="selectOpenTenderSuppliers" resultType="com.ylz.saas.resp.SrmOpenTenderSupplierResp">
        SELECT
        ssi.id tenantSupplierId,
        ssi.supplier_code,
        ssi.supplier_name,
        ssi.supplier_short_name,
        ssi.legal_person contactName,
        ssi.legal_person_phone contactPhone,
        sqi.round_no,
        count(sqi.material_code) materialNum,
        SUM(sqi.quote_amount) quoteTotal,
        sqi.quote_ip
        FROM
        srm_tender_bidder_quote_item sqi
        LEFT JOIN
        srm_tender_open sto ON sqi.project_id = sto.project_id AND sqi.notice_id = sto.notice_id AND sqi.section_id = sto.section_id
        LEFT JOIN
        srm_tenant_supplier_info ssi ON sqi.tenant_supplier_id = ssi.id
        WHERE
        sqi.project_id = #{projectId}
        AND sqi.notice_id = #{noticeId}
        AND sqi.section_id = #{sectionId}
        AND sqi.round_no = #{roundNo}
        <if test="supplierName != null and supplierName != ''">
            AND ssi.supplier_name LIKE CONCAT('%', #{supplierName}, '%')
        </if>
        <if test="tenantSupplierId != null">
            AND sqi.tenant_supplier_id = #{tenantSupplierId}
        </if>
        GROUP BY
        ssi.id, ssi.supplier_code, ssi.supplier_name, ssi.supplier_short_name,ssi.legal_person,ssi.legal_person_phone,sqi.round_no
    </select>
</mapper>
