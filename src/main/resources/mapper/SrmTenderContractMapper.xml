<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenderContractMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenderContract">
            <id property="id" column="id" />
            <result property="tenantId" column="tenant_id" />
            <result property="deptId" column="dept_id" />
            <result property="projectId" column="project_id" />
            <result property="noticeId" column="notice_id" />
            <result property="sectionId" column="section_id" />
            <result property="evaluationResultId" column="evaluation_result_id" />
            <result property="contractCode" column="contract_code" />
            <result property="contractName" column="contract_name" />
            <result property="purchaserDeptId" column="purchaser_dept_id" />
            <result property="purchaserDeptName" column="purchaser_dept_name" />
            <result property="purchaseContactId" column="purchase_contact_id" />
            <result property="purchaseContactPhone" column="purchase_contact_phone" />
            <result property="effectStartDate" column="effect_start_date" />
            <result property="effectEndDate" column="effect_end_date" />
            <result property="signDate" column="sign_date" />
            <result property="signLocation" column="sign_location" />
            <result property="performanceBond" column="performance_bond" />
            <result property="totalAmount" column="total_amount" />
            <result property="electronicSeal" column="electronic_seal" />
            <result property="tenantSupplierId" column="tenant_supplier_id" />
            <result property="supplierName" column="supplier_name" />
            <result property="supplierSalesPrincipal" column="supplier_sales_principal" />
            <result property="supplierSalesPrincipalPhone" column="supplier_sales_principal_phone" />
            <result property="supplierBankCardId" column="supplier_bank_card_id" />
            <result property="supplierBankName" column="supplier_bank_name" />
            <result property="supplierBankAccount" column="supplier_bank_account" />
            <result property="templateId" column="template_id" />
            <result property="contractBody" column="contract_body" />
            <result property="contractStatus" column="contract_status" />
            <result property="approvalStatus" column="approval_status" />
            <result property="supplierRejectedContractInfo" column="supplier_rejected_contract_info" />
            <result property="delFlag" column="del_flag" />
            <result property="createBy" column="create_by" />
            <result property="createByName" column="create_by_name" />
            <result property="createTime" column="create_time" />
            <result property="createById" column="create_by_id" />
            <result property="updateById" column="update_by_id" />
            <result property="updateBy" column="update_by" />
            <result property="updateByName" column="update_by_name" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,project_id,notice_id,section_id,
        evaluation_result_id,contract_code,contract_name,purchaser_dept_id,purchaser_dept_name,purchase_contact_id,
        purchase_contact_phone,effect_start_date,effect_end_date,sign_date,sign_location,
        performance_bond,total_amount,electronic_seal,
        tenant_supplier_id,supplier_name,supplier_sales_principal,supplier_sales_principal_phone,supplier_bank_card_id,supplier_bank_name,
        supplier_bank_account,template_id,contract_body,contract_status,approval_status,supplier_rejected_contract_info,del_flag,create_by,
        create_by_name,create_time,create_by_id,update_by_id,update_by,
        update_by_name,update_time
    </sql>

    <select id="selectContractPage" resultType="com.ylz.saas.resp.SrmTenderContractQueryResp">
        SELECT
        tc.id,
        tc.contract_code AS contractCode,
        tc.contract_name AS contractName,
        tc.sign_date AS signDate,
        tc.total_amount AS totalAmount,
        tc.purchaser_dept_name AS purchaseDeptName,
        tc.supplier_name AS supplierName,
        tc.contract_status AS contractStatus,
        tc.approval_status AS approvalStatus,
        tc.create_by AS createBy,
        tc.create_by_name AS createByName,
        tc.create_time AS createTime,
        tc.update_by AS updateBy,
        tc.update_by_name AS updateByName,
        tc.update_time AS updateTime,
        tc.supplier_rejected_contract_info AS supplierRejectedContractInfo,
        p.project_name AS projectName,
        s.section_name AS sectionName
        FROM
        srm_tender_contract tc
        LEFT JOIN srm_procurement_project p ON tc.project_id = p.id
        LEFT JOIN srm_procurement_project_section s ON tc.section_id = s.id
        <where>
            tc.del_flag = 0
            <!-- 合同名称 模糊搜索 -->
            <if test="req.contractName != null and req.contractName != ''">
                AND tc.contract_name LIKE CONCAT('%', #{req.contractName}, '%')
            </if>

            <!-- 供应商 模糊搜索 -->
            <if test="req.supplierName != null and req.supplierName != ''">
                AND tc.supplier_name LIKE CONCAT('%', #{req.supplierName}, '%')
            </if>

            <!-- 采购方 模糊搜索 -->
            <if test="req.purchaserDeptName != null and req.purchaserDeptName != ''">
                AND tc.purchaser_dept_name LIKE CONCAT('%', #{req.purchaserDeptName}, '%')
            </if>

            <!-- 签约时间 模糊搜索 -->
            <if test="req.signDate != null and req.signDate != ''">
                AND DATE_FORMAT(tc.sign_date, '%Y-%m-%d') LIKE CONCAT('%', #{req.signDate}, '%')
            </if>

            <!-- 项目名称 模糊搜索 -->
            <if test="req.projectName != null and req.projectName != ''">
                AND p.project_name LIKE CONCAT('%', #{req.projectName}, '%')
            </if>
            <!-- 标段ID精确查询 -->
            <if test="req.sectionId != null">
                AND tc.section_id = #{req.sectionId}
            </if>

            <!-- 供应商ID多选查询 -->
            <if test="req.supplierIds != null and !req.supplierIds.isEmpty()">
                AND tc.tenant_supplier_id IN
                <foreach collection="req.supplierIds" item="supplierId" open="(" separator="," close=")">
                    #{supplierId}
                </foreach>
            </if>
            <!-- 采购方ID精确查询 -->
            <if test="req.purchaserDeptId != null">
                AND tc.purchaser_dept_id = #{req.purchaserDeptId}
            </if>
            <!-- 采购立项ID精确查询 -->
            <if test="req.projectId != null">
                AND tc.project_id = #{req.projectId}
            </if>
        </where>
        ORDER BY tc.create_time DESC
    </select>
    <select id="selectContractPageBySupplier" resultType="com.ylz.saas.resp.SrmTenderContractQueryResp">
        SELECT
        tc.id,
        tc.contract_code AS contractCode,
        tc.contract_name AS contractName,
        tc.sign_date AS signDate,
        tc.total_amount AS totalAmount,
        tc.purchaser_dept_name AS purchaseDeptName,
        tc.supplier_name AS supplierName,
        tc.contract_status AS contractStatus,
        tc.approval_status AS approvalStatus,
        tc.create_by AS createBy,
        tc.create_by_name AS createByName,
        tc.create_time AS createTime,
        tc.update_by AS updateBy,
        tc.update_by_name AS updateByName,
        tc.update_time AS updateTime,
        tc.supplier_rejected_contract_info AS supplierRejectedContractInfo,
        p.project_name AS projectName,
        s.section_name AS sectionName
        FROM
        srm_tender_contract tc
        LEFT JOIN srm_procurement_project p ON tc.project_id = p.id
        LEFT JOIN srm_procurement_project_section s ON tc.section_id = s.id
        <where>
            tc.del_flag = 0
            AND (tc.approval_status = 'APPROVE' OR
            (tc.approval_status IN ('APPROVING', 'APPROVE_REJECT') AND tc.contract_status = 'SUPPLIER_REJECTED'))
            <!-- 合同名称 模糊搜索 -->
            <if test="req.contractName != null and req.contractName != ''">
                AND tc.contract_name LIKE CONCAT('%', #{req.contractName}, '%')
            </if>

            <!-- 供应商 模糊搜索 -->
            <if test="req.supplierName != null and req.supplierName != ''">
                AND tc.supplier_name LIKE CONCAT('%', #{req.supplierName}, '%')
            </if>

            <!-- 采购方 模糊搜索 -->
            <if test="req.purchaserDeptName != null and req.purchaserDeptName != ''">
                AND tc.purchaser_dept_name LIKE CONCAT('%', #{req.purchaserDeptName}, '%')
            </if>

            <!-- 签约时间 模糊搜索 -->
            <if test="req.signDate != null and req.signDate != ''">
                AND DATE_FORMAT(tc.sign_date, '%Y-%m-%d') LIKE CONCAT('%', #{req.signDate}, '%')
            </if>

            <!-- 项目名称 模糊搜索 -->
            <if test="req.projectName != null and req.projectName != ''">
                AND p.project_name LIKE CONCAT('%', #{req.projectName}, '%')
            </if>
            <!-- 标段ID精确查询 -->
            <if test="req.sectionId != null">
                AND tc.section_id = #{req.sectionId}
            </if>

            <!-- 供应商ID多选查询 -->
            <if test="req.supplierIds != null and !req.supplierIds.isEmpty()">
                AND tc.tenant_supplier_id IN
                <foreach collection="req.supplierIds" item="supplierId" open="(" separator="," close=")">
                    #{supplierId}
                </foreach>
            </if>
            <!-- 采购方ID精确查询 -->
            <if test="req.purchaserDeptId != null">
                AND tc.purchaser_dept_id = #{req.purchaserDeptId}
            </if>
            <!-- 采购立项ID精确查询 -->
            <if test="req.projectId != null">
                AND tc.project_id = #{req.projectId}
            </if>
        </where>
        ORDER BY tc.create_time DESC
    </select>

    <select id="selectMaxContractSequence" resultType="java.lang.Integer">
        SELECT MAX(CAST(SUBSTRING(contract_code, 12) AS UNSIGNED)) AS maxSequence
        FROM srm_tender_contract
        WHERE contract_code LIKE CONCAT('HT-', #{datePrefix}, '%')
    </select>

</mapper>