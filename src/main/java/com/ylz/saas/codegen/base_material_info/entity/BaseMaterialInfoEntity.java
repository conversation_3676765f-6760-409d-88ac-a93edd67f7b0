package com.ylz.saas.codegen.base_material_info.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ylz.saas.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 物料基础信息表
 *
 * <AUTHOR>
 * @date 2025-06-03 18:00:37
 */
@Data
@TableName("base_material_info")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "物料基础信息表")
@TenantTable
public class BaseMaterialInfoEntity extends Model<BaseMaterialInfoEntity> {


    /**
  	 * 主键ID
   	 */
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

    /**
  	 * 创建人ID
   	 */
    @Schema(description="创建人ID")
    private Long createById;

    /**
  	 * 组织ID
   	 */
    @Schema(description="组织ID")
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
  	 * 更新人ID
   	 */
    @Schema(description="更新人ID")
    private Long updateById;

    /**
  	 * 物料编码
   	 */
    @Schema(description="物料编码")
    private String materialCode;

    /**
  	 * 物料名称
   	 */
    @Schema(description="物料名称")
    private String materialName;

    /**
  	 * 物料简称
   	 */
    @Schema(description="物料简称")
    private String materialSimpleName;

    /**
  	 * 规格/型号
   	 */
    @Schema(description="规格/型号")
    private String spec;

    /**
  	 * 物料所属分类
   	 */
    @Schema(description="物料所属分类")
    private Long materialCategoryId;

    /**
  	 * 计量单位
   	 */
    @Schema(description="计量单位")
    private String unit;

    /**
  	 * 来源标识
   	 */
    @Schema(description="来源标识")
    private String dataSource;

    /**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
    @Schema(description="状态（DISABLED-禁用、ENABLED-启用）")
    private String status;

    /**
  	 * 备注
   	 */
    @Schema(description="备注")
    private String remark;

    /**
  	 * 创建人名称
   	 */
    @Schema(description="创建人名称")
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    @Schema(description="修改人名称")
    private String updateByName;

    /**
  	 * 创建人
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
  	 * 创建时间
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

    /**
  	 * 修改时间
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标识（0-正常、1-删除）")
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    @Schema(description="租户ID")
    private Long tenantId;

    /**
  	 * 组织名称（关联查询字段）
   	 */
    @TableField(exist = false)
    @Schema(description="组织名称")
    private String deptName;

    /**
     * 物料分类名称（关联查询字段）
     */
    @TableField(exist = false)
    @Schema(description="物料分类名称")
    private String materialCategoryName;

    /**
     * 物料分类顶级名称（关联查询字段）
     */
    @TableField(exist = false)
    @Schema(description="物料分类顶级名称")
    private String materialCategoryRoot;

    /**
     * 关联的组织列表（用于回显）
     */
    @TableField(exist = false)
    @Schema(description="关联的组织列表")
    private List<DeptInfo> deptList;

    /**
     * 组织信息内部类
     */
    @Data
    public static class DeptInfo implements java.io.Serializable {
        /**
         * 组织ID
         */
        private Long deptId;

        /**
         * 组织名称
         */
        private String deptName;
    }
}