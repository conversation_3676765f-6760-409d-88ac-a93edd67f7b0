package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmProcurementProjectSection;
import com.ylz.saas.enums.YesNoEnum;
import com.ylz.saas.service.SrmProcurementProjectSectionService;
import com.ylz.saas.mapper.SrmProcurementProjectSectionMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_procurement_project_section(采购立项标段表)】的数据库操作Service实现
* @createDate 2025-06-11 11:21:09
*/
@Service
public class SrmProcurementProjectSectionServiceImpl extends ServiceImpl<SrmProcurementProjectSectionMapper, SrmProcurementProjectSection>
    implements SrmProcurementProjectSectionService{


    @Override
    public List<SrmProcurementProjectSection> getListByNoticeId(String noticeId) {
        return this.lambdaQuery()
                .eq(SrmProcurementProjectSection::getProjectId, noticeId)
                .eq(SrmProcurementProjectSection::getDelFlag, YesNoEnum.NO.getCode())
                .list();
    }
}




