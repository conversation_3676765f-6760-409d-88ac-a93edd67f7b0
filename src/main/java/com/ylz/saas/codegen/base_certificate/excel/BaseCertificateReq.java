package com.ylz.saas.codegen.base_certificate.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 证件信息表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:46
 */
@Data
public class BaseCertificateReq implements Serializable {




    /**
  	 * 证件名称
   	 */
	@ExcelProperty(value = "证件名称")
    private String certName;


    /**
  	 * 补充说明
   	 */
	@ExcelProperty(value = "补充说明")
    private String description;


//    /**
//  	 * 状态（DISABLED-禁用、ENABLED-启用）
//   	 */
//	@ExcelProperty(value = "状态（DISABLED-禁用、ENABLED-启用）")
//    private String status;











}