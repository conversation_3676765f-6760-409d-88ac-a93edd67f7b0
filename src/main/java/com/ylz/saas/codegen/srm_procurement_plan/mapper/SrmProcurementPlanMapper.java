package com.ylz.saas.codegen.srm_procurement_plan.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.codegen.srm_procurement_plan.entity.SrmProcurementPlanEntity;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import com.ylz.saas.common.data.datascope.annotation.DataPermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DataPermission(userEnabled = true, dataPermissionRuleList = "[{\"dataTypeCode\":\"PLAN\",\"targetField\":\"plan_id\",\"dataSourceField\":\"ID\"}]")
public interface SrmProcurementPlanMapper extends SaasBaseMapper<SrmProcurementPlanEntity> {

    /**
     * 主表分页查询（受数据权限控制）
     */
    Page<SrmProcurementPlanEntity> planPage(Page<SrmProcurementPlanEntity> page,
                                            @Param("an") String applicantName,
                                            @Param("adn") String applyDeptName,
                                            @Param("ew") QueryWrapper<SrmProcurementPlanEntity> queryWrapper);


    @DataPermission(roleEnabled = false, userEnabled = false)
    List<SrmProcurementPlanEntity> queryIgnorePermission(@Param("ew") LambdaQueryWrapper<SrmProcurementPlanEntity> queryWrapper);
}