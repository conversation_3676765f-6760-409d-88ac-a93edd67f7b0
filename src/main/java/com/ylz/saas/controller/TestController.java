//package com.ylz.saas.controller;
//
//import com.ylz.saas.common.core.constant.SecurityConstants;
//import com.ylz.saas.common.core.util.R;
//import com.ylz.saas.open.feign.RemoteOutboundService;
//import com.ylz.saas.open.req.OpenCallReq;
//import io.swagger.v3.oas.annotations.security.SecurityRequirement;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import jakarta.annotation.Resource;
//import lombok.RequiredArgsConstructor;
//import org.springframework.http.HttpHeaders;
//import org.springframework.web.bind.annotation.*;
//
///**
// * className: TestController
// * title:
// * author: grape
// * data: 2025/4/27 星期日 17:16
// * description:
// * version: 1.0
// */
//@RestController
//@RequiredArgsConstructor
//@RequestMapping("/test")
//@Tag(description = "test", name = "测试")
//@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
//public class TestController {
//
//    @Resource
//    private RemoteOutboundService openApiFeign;
//
//    @PostMapping("/demo")
//    public R getById(@RequestBody OpenCallReq openCallReq) {
//        return R.ok(openApiFeign.send(openCallReq, SecurityConstants.FROM_IN));
//    }
//
//    @PostMapping("/demo1")
//    public R send(@RequestBody OpenCallReq openCallReq) {
//        return R.ok(openApiFeign.sdkSave(openCallReq, SecurityConstants.FROM_IN));
//    }
//
//    @GetMapping
//    public R test() {
//        return R.ok("调用成功");
//    }
//
//}
