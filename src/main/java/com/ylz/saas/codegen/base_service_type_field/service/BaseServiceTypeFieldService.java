package com.ylz.saas.codegen.base_service_type_field.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.base_service_type_field.entity.BaseServiceTypeFieldEntity;
import com.ylz.saas.codegen.base_service_type_field.vo.BaseServiceTypeFieldVo;
import com.ylz.saas.vo.LabelAndValueVo;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface BaseServiceTypeFieldService extends IService<BaseServiceTypeFieldEntity> {
	/**
     * 新增服务类型字段表
     * @param baseServiceTypeFieldVo 服务类型字段表
     */
    boolean saveBaseServiceTypeField(BaseServiceTypeFieldVo baseServiceTypeFieldVo);
	
	/**
     * 修改服务类型字段表
     * @param baseServiceTypeFieldVo 服务类型字段表
     */
    boolean updateBaseServiceTypeField(BaseServiceTypeFieldVo baseServiceTypeFieldVo);
	
	/**
     * 导入模板下载
     * @param response
     */
    void template(HttpServletResponse response);



    /**
     * 数据导入
     * @param file
     * @param response
     */
    void uploadData(MultipartFile file, HttpServletResponse response);

    List<Long> verifyRemoveFieldById(List<Long> ids);

    /**
     * 执行动态查询 SQL
     * 1. 直接原样执行 SQL
     * @param sql 完整的 SQL 查询语句
     * @return 查询结果列表
     */
    List<LabelAndValueVo> executeDynamicSelectQuery(String sql);

    /**
     * 根据动态字段 id 查询 SQL 执行结果的数据列表
     * @param field
     * @return
     */
    List<LabelAndValueVo> convertServiceTypeFieldToSqlDataList(BaseServiceTypeFieldEntity field);

    // 寻源方式/采购方式 枚举
    @Getter
    @AllArgsConstructor
    enum BuyWayEnum {
        NULL("无"),
        XJCG("询价采购"),
        JZTP("竞争性谈判"),
        ZJWT("直接委托"),
        JJCG("竞价采购"),
        KSXJ("快速询价"),
        QZXJ("青贮询价"),
        ZB("招标");

        private String desc;
    }

    // 动态表单字段类型 枚举
    @Getter
    @AllArgsConstructor
    enum FieldTypeEnum {

        TEXT("文本型"),
        NUM("数值型"),
        NUM_CALC("数值计算型"),
        ENUM("枚举型"),
        LINK_FORM("链接表单"),
        ATTACH("附件");

        private String desc;
    }

}