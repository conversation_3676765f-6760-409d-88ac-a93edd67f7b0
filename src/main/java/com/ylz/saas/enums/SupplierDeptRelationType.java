package com.ylz.saas.enums;

/**
 * 供应商部门关联类型枚举
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
public enum SupplierDeptRelationType {

    /**
     * 创建关联 - 供应商创建时与创建人部门的关联
     */
    CREATE("CREATE", "创建关联"),

    /**
     * 管理关联 - 后续可能添加的管理权限关联
     */
    MANAGE("MANAGE", "管理关联"),

    /**
     * 邀请关联 - 通过邀请功能建立的关联
     */
    INVITE("INVITE", "邀请关联");

    private final String code;
    private final String description;

    SupplierDeptRelationType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 代码
     * @return 枚举值
     */
    public static SupplierDeptRelationType fromCode(String code) {
        for (SupplierDeptRelationType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的供应商部门关联类型: " + code);
    }
}
