package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.common.security.util.SecurityUtils;
import com.ylz.saas.entity.SrmProjectMember;
import com.ylz.saas.enums.ProjectMemberTypeEnum;
import com.ylz.saas.service.SrmProjectMemberService;
import com.ylz.saas.mapper.SrmProjectMemberMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
* <AUTHOR>
* @description 针对表【srm_project_member(成员表)】的数据库操作Service实现
* @createDate 2025-06-11 11:21:09
*/
@Service
public class SrmProjectMemberServiceImpl extends ServiceImpl<SrmProjectMemberMapper, SrmProjectMember>
    implements SrmProjectMemberService{


    @Override
    public List<SrmProjectMember> getMyMemberList(long projectId, Long userId, String role) {
        return this.lambdaQuery()
                .eq(SrmProjectMember::getProjectId, projectId)
                .eq(Objects.nonNull(userId), SrmProjectMember::getUserId, userId)
                .eq(StringUtils.isNotBlank(role), SrmProjectMember::getRole, role)
                .eq(SrmProjectMember::getDelFlag, 0)
                .list();
    }
}




