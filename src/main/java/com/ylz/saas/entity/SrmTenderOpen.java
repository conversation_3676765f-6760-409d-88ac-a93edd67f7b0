package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ylz.saas.common.core.util.TenantTable;
import com.ylz.saas.enums.OpenStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 开标表
 * @TableName srm_tender_open
 */
@TableName(value ="srm_tender_open")
@Data
@TenantTable
public class SrmTenderOpen {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 采购立项ID
     */
    private Long projectId;

    /**
     * 招标公告ID
     */
    private Long noticeId;

    /**
     * 标段ID
     */
    private Long sectionId;

    /**
     * 当前报价轮次
     */
    private Integer currentRound;

    /**
     * 最新报价开始时间
     */
    private LocalDateTime latestQuoteStartTime;

    /**
     * 最新报价截止时间
     */
    private LocalDateTime latestQuoteEndTime;

    /**
     * 开标时间
     */
    private LocalDateTime openTime;

    /**
     * 开标状态
     */
    private OpenStatusEnum openStatus;

    /**
     * 是否公开报价
     */
    private Integer publicQuote;

    /**
     * 删除标识（0-正常、1-删除）
     */
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
}