package com.ylz.saas.codegen.srm_procurement_plan.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.ylz.saas.admin.api.dto.BatchCreateUserIdentityDataPermissionDTO;
import com.ylz.saas.admin.api.dto.DataScopeDataDTO;
import com.ylz.saas.admin.api.entity.SysDept;
import com.ylz.saas.admin.api.entity.SysIdentity;
import com.ylz.saas.admin.api.entity.SysUser;
import com.ylz.saas.admin.mapper.SysIdentityMapper;
import com.ylz.saas.admin.service.SysDeptService;
import com.ylz.saas.admin.service.SysUserIdentityDataPermissionRelationService;
import com.ylz.saas.admin.service.SysUserService;
import com.ylz.saas.codegen.base_agent_user.entity.BaseAgentUserEntity;
import com.ylz.saas.codegen.base_agent_user.mapper.BaseAgentUserMapper;
import com.ylz.saas.codegen.base_quality_indicator.entity.BaseQualityIndicatorEntity;
import com.ylz.saas.codegen.base_quality_indicator.service.BaseQualityIndicatorService;
import com.ylz.saas.codegen.base_service_type.entity.BaseServiceTypeEntity;
import com.ylz.saas.codegen.base_service_type.service.impl.BaseServiceTypeServiceImpl;
import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.codegen.base_usage_location.entity.BaseUsageLocationEntity;
import com.ylz.saas.codegen.base_usage_location.service.BaseUsageLocationService;
import com.ylz.saas.codegen.srm_procurement_plan.entity.SrmProcurementPlanEntity;
import com.ylz.saas.codegen.srm_procurement_plan.mapper.SrmProcurementPlanMapper;
import com.ylz.saas.codegen.srm_procurement_plan.mapper.SrmProcurementPlanQueryMapper;
import com.ylz.saas.codegen.srm_procurement_plan.param.SrmProcurementPlanParam;
import com.ylz.saas.codegen.srm_procurement_plan.service.SrmProcurementPlanService;
import com.ylz.saas.codegen.srm_procurement_plan.vo.BizFlowUpdateVo;
import com.ylz.saas.codegen.srm_procurement_plan.vo.EnhanceBaseServiceTypeFieldVo;
import com.ylz.saas.codegen.srm_procurement_plan.vo.SrmProcurementPlanEntireVo;
import com.ylz.saas.codegen.srm_procurement_plan.vo.SrmProcurementPlanVo;
import com.ylz.saas.codegen.srm_procurement_plan.vo.UpdateSrmProcurementPlanStatusVo;
import com.ylz.saas.codegen.srm_procurement_plan_detail.entity.SrmProcurementPlanDetailEntity;
import com.ylz.saas.codegen.srm_procurement_plan_detail.service.SrmProcurementPlanDetailService;
import com.ylz.saas.codegen.srm_procurement_plan_field_value.entity.SrmProcurementPlanFieldValueEntity;
import com.ylz.saas.codegen.srm_procurement_plan_field_value.service.SrmProcurementPlanFieldValueService;
import com.ylz.saas.common.core.constant.enums.YesNoEnum;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.core.exception.GlobalResultCode;
import com.ylz.saas.common.data.tenant.TenantContextHolder;
import com.ylz.saas.common.enums.CodeGeneratorPrefixEnum;
import com.ylz.saas.common.sequence.generator.impl.DefaultCodeGenerator;
import com.ylz.saas.enums.FixedFieldEnum;
import com.ylz.saas.req.ProcessInstanceStartReq;
import com.ylz.saas.service.SrmProcessConfigService;
import com.ylz.saas.service.SrmProcessInstanceService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.PURE_DATE_PATTERN;
import static com.ylz.saas.enums.DataPermissionTypeEnum.PLAN;
import static com.ylz.saas.enums.DefaultRoleEnum.IdentifyCode.BID_AGENCY_IDENTITY;

/**
 * 采购计划表
 *
 * <AUTHOR>
 * @date 2025-06-03 20:39:40
 */
@Service
@Slf4j
public class SrmProcurementPlanServiceImpl extends ServiceImpl<SrmProcurementPlanMapper, SrmProcurementPlanEntity> implements SrmProcurementPlanService {

    @Autowired
    private SrmProcurementPlanDetailService srmProcurementPlanDetailService;
    @Autowired
    private SrmProcurementPlanFieldValueService srmProcurementPlanFieldValueService;
    @Autowired
    private SrmProcurementPlanMapper srmProcurementPlanMapper;
    @Autowired
    private SrmProcurementPlanQueryMapper srmProcurementPlanQueryMapper;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysDeptService sysDeptService;
    @Autowired
    private SysUserIdentityDataPermissionRelationService dataPermissionRelationService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    @Lazy
    private SrmProcessInstanceService srmProcessInstanceService;
    @Autowired
    private DefaultCodeGenerator defaultCodeGenerator;
    @Autowired
    private BaseUsageLocationService baseUsageLocationService;
    @Autowired
    private BaseQualityIndicatorService baseQualityIndicatorService;
    @Resource
    private BaseAgentUserMapper baseAgentUserMapper;
    @Resource
    private SysIdentityMapper identityMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveSrmProcurementPlan(SrmProcurementPlanVo srmProcurementPlanVo) {
        SrmProcurementPlanEntity srmProcurementPlan = new SrmProcurementPlanEntity();
        BeanUtil.copyProperties(srmProcurementPlanVo, srmProcurementPlan);
        save(srmProcurementPlan);
        if (CollectionUtils.isNotEmpty(srmProcurementPlanVo.getSrmProcurementPlanDetailList())) {
            srmProcurementPlanVo.getSrmProcurementPlanDetailList().forEach(srmProcurementPlanDetail -> {
                srmProcurementPlanDetail.setPlanId(srmProcurementPlan.getId());
            });
            srmProcurementPlanDetailService.saveBatch(srmProcurementPlanVo.getSrmProcurementPlanDetailList());
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSrmProcurementPlan(SrmProcurementPlanVo srmProcurementPlanVo) {
        SrmProcurementPlanEntity srmProcurementPlan = getById(srmProcurementPlanVo.getId());
        ExceptionUtil.check(srmProcurementPlan == null, "500", "采购计划表不存在");
        // 采购计划明细子表更新
        List<SrmProcurementPlanDetailEntity> oldSrmProcurementPlanDetailList = Optional.ofNullable(srmProcurementPlanDetailService.list(Wrappers.<SrmProcurementPlanDetailEntity>lambdaQuery().eq(SrmProcurementPlanDetailEntity::getPlanId, srmProcurementPlan.getId()))).orElse(Collections.emptyList());
        List<SrmProcurementPlanDetailEntity> newSrmProcurementPlanDetailList = Optional.ofNullable(srmProcurementPlanVo.getSrmProcurementPlanDetailList()).orElse(Collections.emptyList());
        Set<Long> newSrmProcurementPlanDetailIds = newSrmProcurementPlanDetailList.stream().map(SrmProcurementPlanDetailEntity::getId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<Long> idsSrmProcurementPlanDetailToDelete = oldSrmProcurementPlanDetailList.stream().filter(old -> !newSrmProcurementPlanDetailIds.contains(old.getId())).map(SrmProcurementPlanDetailEntity::getId).collect(Collectors.toList());
        if (!idsSrmProcurementPlanDetailToDelete.isEmpty()) {
            srmProcurementPlanDetailService.removeByIds(idsSrmProcurementPlanDetailToDelete);
        }
        for (SrmProcurementPlanDetailEntity srmProcurementPlanDetail : newSrmProcurementPlanDetailList) {
            srmProcurementPlanDetail.setPlanId(srmProcurementPlan.getId());
        }
        if (CollectionUtils.isNotEmpty(srmProcurementPlanVo.getSrmProcurementPlanDetailList())) {
            // upsert采购明细数据
            srmProcurementPlanDetailService.saveOrUpdateBatch(srmProcurementPlanVo.getSrmProcurementPlanDetailList());
            // 采购计划字段值数据更新
            srmProcurementPlanDetailService.batchUpdatePlanFieldByPlanDetail(srmProcurementPlanVo.getSrmProcurementPlanDetailList());
        }
        // 更新主表数据
        BeanUtil.copyProperties(srmProcurementPlanVo, srmProcurementPlan);
        return updateById(srmProcurementPlan);
    }


    @Override
    public boolean onlyUpdatePlanById(SrmProcurementPlanVo srmProcurementPlanVo) {
        SrmProcurementPlanEntity srmProcurementPlan = getById(srmProcurementPlanVo.getId());
        ExceptionUtil.check(srmProcurementPlan == null, "500", "采购计划表不存在");
        // 更新主表数据
        BeanUtil.copyProperties(srmProcurementPlanVo, srmProcurementPlan);
        // 插入招标代理机构身份数据权限关联
        insertAgentOrgUserIdentityDataPermissionRelation(srmProcurementPlan, srmProcurementPlanVo.getBaseAgentDeptId());
        return updateById(srmProcurementPlan);
    }

    /**
     * 插入招标代理机构身份数据权限关联
     *
     * @param srmProcurementPlan 采购计划表
     * @param baseAgentDeptId
     */
    private void insertAgentOrgUserIdentityDataPermissionRelation(SrmProcurementPlanEntity srmProcurementPlan, Long baseAgentDeptId) {
        if (Objects.isNull(srmProcurementPlan.getBaseAgentDeptId())) {
            return;
        }
        List<Long> userIdList =
                baseAgentUserMapper.selectList(Wrappers.<BaseAgentUserEntity>lambdaQuery()
                        .select(BaseAgentUserEntity::getUserId)
                        .eq(BaseAgentUserEntity::getAgentOrgId, srmProcurementPlan.getBaseAgentDeptId()))
                        .stream().map(BaseAgentUserEntity::getUserId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }
        // 此处默认以招标代理机构身份创建数据权限规则
        SysIdentity sysIdentity = identityMapper.selectOne(Wrappers.<SysIdentity>lambdaQuery()
                .select(SysIdentity::getId)
                .eq(SysIdentity::getIdentityCode, BID_AGENCY_IDENTITY)
                .eq(SysIdentity::getIsActive, Boolean.TRUE));
        if (Objects.isNull(sysIdentity)) {
            return;
        }
        BatchCreateUserIdentityDataPermissionDTO paramDTO =
                assembleCreateDataPermissionParamDTO(srmProcurementPlan, userIdList, sysIdentity);
        dataPermissionRelationService.batchSaveUserIdentityDataPermission(paramDTO);
    }

    /**
     * 组装创建数据权限参数DTO
     *
     * @param srmProcurementPlan 采购计划
     * @param userIdList 用户ID列表
     * @param sysIdentity 身份
     * @return 创建数据权限参数DTO
     */
    private static BatchCreateUserIdentityDataPermissionDTO assembleCreateDataPermissionParamDTO(
            SrmProcurementPlanEntity srmProcurementPlan, List<Long> userIdList, SysIdentity sysIdentity) {
        BatchCreateUserIdentityDataPermissionDTO paramDTO = new BatchCreateUserIdentityDataPermissionDTO();
        BatchCreateUserIdentityDataPermissionDTO.UserIdentityCombination userIdentityCombination =
                new BatchCreateUserIdentityDataPermissionDTO.UserIdentityCombination();
        userIdentityCombination.setUserIdList(userIdList);
        userIdentityCombination.setIdentityIdList(List.of(sysIdentity.getId()));
        paramDTO.setUserIdentityCombinations(List.of(userIdentityCombination));
        paramDTO.setTypeCode(PLAN.name());
        DataScopeDataDTO dataPermission = new DataScopeDataDTO();
        dataPermission.setId(srmProcurementPlan.getId());
        dataPermission.setName(srmProcurementPlan.getPlanName());
        dataPermission.setCode(srmProcurementPlan.getPlanCode());
        paramDTO.setDataPermission(dataPermission);
        return paramDTO;
    }


    @Override
    public Page<SrmProcurementPlanEntity> planPage(Page page, SrmProcurementPlanParam param) {
        QueryWrapper<SrmProcurementPlanEntity> queryWrapper = param.toWrapper();

        // 如果有申请人姓名或申请部门名称的过滤条件，需要先查询对应的ID列表
        if (StringUtils.isNotBlank(param.getApplicantName())) {
            List<String> userIds = srmProcurementPlanQueryMapper.getUserIdsByName(param.getApplicantName());
            if (CollectionUtils.isEmpty(userIds)) {
                // 如果没有找到匹配的用户，返回空结果
                return new Page<>(page.getCurrent(), page.getSize(), 0);
            }
            queryWrapper.in("applicant", userIds);
        }

        if (StringUtils.isNotBlank(param.getApplyDeptName())) {
            List<Long> deptIds = srmProcurementPlanQueryMapper.getDeptIdsByName(param.getApplyDeptName());
            if (CollectionUtils.isEmpty(deptIds)) {
                // 如果没有找到匹配的部门，返回空结果
                return new Page<>(page.getCurrent(), page.getSize(), 0);
            }
            queryWrapper.in("apply_dept_id", deptIds);
        }

        // 查询主表数据（受数据权限控制）
        Page<SrmProcurementPlanEntity> resultPage = srmProcurementPlanMapper.planPage(page, null, null, queryWrapper);

        if (CollectionUtils.isNotEmpty(resultPage.getRecords())) {
            List<SrmProcurementPlanEntity> records = resultPage.getRecords();

            // 收集所有需要查询的用户ID和部门ID
            List<Long> userIds = records.stream()
                    .map(SrmProcurementPlanEntity::getApplicant)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            List<String> applyDeptIds = records.stream()
                    .map(SrmProcurementPlanEntity::getApplyDeptId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            List<String> procurementDeptIds = records.stream()
                    .map(SrmProcurementPlanEntity::getProcurementDeptId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            // 合并所有部门ID
            Set<String> allDeptIds = new HashSet<>();
            if (CollectionUtils.isNotEmpty(applyDeptIds)) {
                allDeptIds.addAll(applyDeptIds);
            }
            if (CollectionUtils.isNotEmpty(procurementDeptIds)) {
                allDeptIds.addAll(procurementDeptIds);
            }

            // 批量查询用户信息（不受数据权限控制）
            Map<Long, SysUser> userMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(userIds)) {
                List<SysUser> users = srmProcurementPlanQueryMapper.getUsersByIds(userIds, null);
                userMap = users.stream().collect(Collectors.toMap(SysUser::getUserId, user -> user));
            }

            // 批量查询部门信息（不受数据权限控制）
            Map<Long, SysDept> deptMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(allDeptIds)) {
                List<SysDept> depts = srmProcurementPlanQueryMapper.getDeptsByIds(new ArrayList<String>(allDeptIds), null);
                deptMap = depts.stream().collect(Collectors.toMap(SysDept::getDeptId, dept -> dept));
            }

            // 设置关联信息
            final Map<Long, SysUser> finalUserMap = userMap;
            final Map<Long, SysDept> finalDeptMap = deptMap;
            records.forEach(record -> {
                // 设置申请人姓名
                if (Objects.nonNull(record.getApplicant())) {
                    SysUser user = finalUserMap.get(record.getApplicant());
                    if (user != null) {
                        record.setApplicantName(user.getName());
                    }
                }

                // 设置申请部门名称
                if (record.getApplyDeptId() != null) {
                    SysDept dept = finalDeptMap.get(Long.parseLong(record.getApplyDeptId()));
                    if (dept != null) {
                        record.setApplyDeptName(dept.getName());
                    }
                }

                // 设置采购部门名称
                if (record.getProcurementDeptId() != null) {
                    SysDept dept = finalDeptMap.get(Long.parseLong(record.getProcurementDeptId()));
                    if (dept != null) {
                        record.setProcurementDeptName(dept.getName());
                    }
                }
            });
        }

        return resultPage;
    }

    @Override
    public boolean bizFlowUpdate(BizFlowUpdateVo bizFlowUpdateVo) {
        final SrmProcurementPlanEntity srmProcurementPlanEntity = getById(bizFlowUpdateVo.getId());
        BeanUtil.copyProperties(bizFlowUpdateVo, srmProcurementPlanEntity);

        Long bizId = srmProcurementPlanEntity.getId();
        srmProcessInstanceService.handlerInstanceStatus(bizId, SrmProcessConfigService.BizTypeEnum.SRM_PROCUREMENT_PLAN_CANCEL, bizFlowUpdateVo.getApproveStatus());
        return updateById(srmProcurementPlanEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSrmProcurementStatus(UpdateSrmProcurementPlanStatusVo updateSrmProcurementPlanStatusVo) {
        LambdaUpdateWrapper<SrmProcurementPlanEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(SrmProcurementPlanEntity::getId, updateSrmProcurementPlanStatusVo.getUpdateById())
                .set(SrmProcurementPlanEntity::getStatus, updateSrmProcurementPlanStatusVo.getStatus())
                .set(SrmProcurementPlanEntity::getUpdateBy, updateSrmProcurementPlanStatusVo.getUpdateBy())
                .set(SrmProcurementPlanEntity::getUpdateTime, updateSrmProcurementPlanStatusVo.getUpdateTime())
                .set(SrmProcurementPlanEntity::getUpdateByName, updateSrmProcurementPlanStatusVo.getUpdateByName());
        return update(lambdaUpdateWrapper);
    }

    @Override
    public SrmProcurementPlanEntity getByIdSrmProcurementPlan(Long id) {
        // TODO: 2025/6/6 这里可以只查询一次数据库
        List<SrmProcurementPlanEntity> srmProcurementPlanList = baseMapper.queryIgnorePermission(Wrappers.<SrmProcurementPlanEntity>lambdaQuery().eq(SrmProcurementPlanEntity::getId, id));
        ExceptionUtil.checkNotEmpty(srmProcurementPlanList, "采购计划不存在");
        return getSrmProcurementPlanEntity(srmProcurementPlanList.get(0));
    }

    @Override
    public SrmProcurementPlanEntity getByPlanCode(String planCode) {
        // TODO: 2025/6/6 这里可以只查询一次数据库
        List<SrmProcurementPlanEntity> srmProcurementPlanList = baseMapper.queryIgnorePermission(Wrappers.lambdaQuery(SrmProcurementPlanEntity.class)
                .eq(SrmProcurementPlanEntity::getPlanCode, planCode));
        ExceptionUtil.checkNotEmpty(srmProcurementPlanList, "采购计划不存在");
        return getSrmProcurementPlanEntity(srmProcurementPlanList.get(0));
    }

    @Nullable
    private SrmProcurementPlanEntity getSrmProcurementPlanEntity(SrmProcurementPlanEntity srmProcurementPlan) {
        if (Objects.nonNull(srmProcurementPlan)) {
            // 业务配置信息
            final BaseServiceTypeEntity baseServiceTypeEntity = applicationContext.getBean("baseServiceTypeServiceImpl", BaseServiceTypeServiceImpl.class)
                    .getByIdBaseServiceType(srmProcurementPlan.getServiceTypeId());
            srmProcurementPlan.setBaseServiceTypeEntity(baseServiceTypeEntity);
            // 前端回显
            final SysUser sysUser = sysUserService.getById(srmProcurementPlan.getApplicant());
            if (Objects.nonNull(sysUser)) {
                srmProcurementPlan.setApplicantName(sysUser.getName());
            }
            final SysDept procurementDept = sysDeptService.getById(srmProcurementPlan.getProcurementDeptId());
            final SysDept applyDept = sysDeptService.getById(srmProcurementPlan.getApplyDeptId());
            if (Objects.nonNull(procurementDept)) {
                srmProcurementPlan.setProcurementDeptName(procurementDept.getName());
            }
            if (Objects.nonNull(applyDept)) {
                srmProcurementPlan.setApplyDeptName(applyDept.getName());
            }
            final List<SrmProcurementPlanDetailEntity> planDetailEntityList = srmProcurementPlanDetailService
                    .list(Wrappers.<SrmProcurementPlanDetailEntity>lambdaQuery()
                            .eq(SrmProcurementPlanDetailEntity::getPlanId, srmProcurementPlan.getId()));
            // 动态字段
            List<Long> planDetailIds = planDetailEntityList.stream().map(SrmProcurementPlanDetailEntity::getId).toList();
            List<SrmProcurementPlanFieldValueEntity> fieldValueEntities = srmProcurementPlanFieldValueService.lambdaQuery().in(SrmProcurementPlanFieldValueEntity::getPlanDetailId, planDetailIds).list();
            Map<Long, List<SrmProcurementPlanFieldValueEntity>> planDetailIdGroup = fieldValueEntities.stream().collect(Collectors.groupingBy(SrmProcurementPlanFieldValueEntity::getPlanDetailId));

            // 牧场
            List<Long> locationIds = planDetailEntityList.stream().map(SrmProcurementPlanDetailEntity::getUsageLocationId).distinct().toList();
            Map<Long, BaseUsageLocationEntity> locationEntityMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(locationIds)) {
                List<BaseUsageLocationEntity> baseUsageLocationEntities = baseUsageLocationService.listByIds(locationIds);
                locationEntityMap = Optional.ofNullable(baseUsageLocationEntities).stream().flatMap(Collection::stream)
                        .collect(Collectors.toMap(BaseUsageLocationEntity::getId, Function.identity()));
            }

            // 质量指标
            List<Long> qualityIds = planDetailEntityList.stream().map(SrmProcurementPlanDetailEntity::getQualityIndicatorId).distinct().toList();
            Map<Long, String> qualityIndicatorEntityMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(qualityIds)) {
                List<BaseQualityIndicatorEntity> baseQualityIndicatorEntities = baseQualityIndicatorService.listByIds(qualityIds);
                qualityIndicatorEntityMap = Optional.ofNullable(baseQualityIndicatorEntities).stream().flatMap(Collection::stream)
                        .collect(Collectors.toMap(BaseQualityIndicatorEntity::getId, BaseQualityIndicatorEntity::getIndicatorName));
            }

            for (SrmProcurementPlanDetailEntity e : planDetailEntityList) {
                e.setSrmProcurementPlanFieldValueList(planDetailIdGroup.get(e.getId()));
                BaseUsageLocationEntity baseUsageLocationEntity = locationEntityMap.get(e.getUsageLocationId());
                if (Objects.nonNull(baseUsageLocationEntity)) {
                    e.setUsageLocationName(baseUsageLocationEntity.getLocationName());
                    e.setUsageLocationRegion(baseUsageLocationEntity.getProvince() + "/" + baseUsageLocationEntity.getCity() + "/" + baseUsageLocationEntity.getDistrict());
                    e.setUsageLocationAddress(baseUsageLocationEntity.getAddress());
                }
                e.setQualityIndicatorName(qualityIndicatorEntityMap.get(e.getQualityIndicatorId()));
            }

            srmProcurementPlan.setSrmProcurementPlanDetailList(planDetailEntityList);
        }
        return srmProcurementPlan;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIdsSrmProcurementPlan(List<Long> ids) {
        List<SrmProcurementPlanEntity> srmProcurementPlan = lambdaQuery().in(SrmProcurementPlanEntity::getId, ids).list();
        if (CollectionUtils.isNotEmpty(srmProcurementPlan)) {
            // 逻辑删除计划主表数据
            removeBatchByIds(srmProcurementPlan);
            // 逻辑删除计划明细及下面字段数据
            // 获取采购计划明细id
            final List<Long> planDetailIds = srmProcurementPlanDetailService.lambdaQuery().select(SrmProcurementPlanDetailEntity::getId).in(SrmProcurementPlanDetailEntity::getPlanId, ids).list().stream().map(SrmProcurementPlanDetailEntity::getId).collect(Collectors.toList());
            srmProcurementPlanDetailService.removeByIdsSrmProcurementPlanDetail(planDetailIds);
        }
        return true;
    }


    private String generatePlanCode(String procurementPlanPrefix) {
        final String date = DateUtil.format(new DateTime(), PURE_DATE_PATTERN);
        final String uniqueCode = defaultCodeGenerator.serialDaily(procurementPlanPrefix, 4);
        return procurementPlanPrefix + "-" + date + "-" + uniqueCode;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean entireSave(SrmProcurementPlanEntireVo srmProcurementPlanEntireVo) {
        // 兼容更新逻辑 - 通过实体中是否带入采购计划id来判定是新增还是修改
        if (Objects.nonNull(srmProcurementPlanEntireVo.getId())) {
            // 修改
            // 插入前先查询是否存在采购计划明细及字段可用的数据，如果有需要物理删除再插入
            final List<SrmProcurementPlanDetailEntity> planDetailEntityList = srmProcurementPlanDetailService.list(Wrappers.lambdaQuery(SrmProcurementPlanDetailEntity.class)

                    .eq(SrmProcurementPlanDetailEntity::getPlanId, srmProcurementPlanEntireVo.getId()));
            if (CollectionUtils.isNotEmpty(planDetailEntityList)) {
                final Long tenantId = TenantContextHolder.getTenantId();
                srmProcurementPlanDetailService.physicsRemoveByPlanId(srmProcurementPlanEntireVo.getId(), tenantId);
                final List<Long> planDetailIds = planDetailEntityList.stream().map(SrmProcurementPlanDetailEntity::getId).collect(Collectors.toList());
                srmProcurementPlanFieldValueService.physicsRemoveByPlanDetailIds(planDetailIds, tenantId);
            }
        }

        Long srmProcurementPlanId = null;
        String planCode = null;
        // 新增采购计划主表数据
        if (Objects.isNull(srmProcurementPlanEntireVo.getId())) {
            srmProcurementPlanEntireVo.setPlanCode(generatePlanCode(CodeGeneratorPrefixEnum.CGJH.name()));
            srmProcurementPlanEntireVo.setStatus(DocumentStatus.NEW);
            srmProcurementPlanEntireVo.setSource(SourceEnum.BUILD);

            SrmProcurementPlanEntity srmProcurementPlan = new SrmProcurementPlanEntity();
            BeanUtil.copyProperties(srmProcurementPlanEntireVo, srmProcurementPlan);
            save(srmProcurementPlan);
            srmProcurementPlanId = srmProcurementPlan.getId();
            planCode = srmProcurementPlan.getPlanCode();
        } else {
            // 修改
            final SrmProcurementPlanEntity old = getById(srmProcurementPlanEntireVo.getId());
            BeanUtil.copyProperties(srmProcurementPlanEntireVo, old);
            updateById(old);
            srmProcurementPlanId = srmProcurementPlanEntireVo.getId();
            planCode = srmProcurementPlanEntireVo.getPlanCode();
        }

        // 转换并保存采购计划明细列表及下面的动态字段列表
        final boolean res = convertAndSavePlanDetailEntityList(srmProcurementPlanEntireVo, srmProcurementPlanId);
        // 新增和修改后调用业务审批流发起采购计划生效审批
        if (res) {
            final ProcessInstanceStartReq processInstanceStartReq = new ProcessInstanceStartReq();
            processInstanceStartReq.setBizKey(planCode);
            processInstanceStartReq.setArgs(List.of(planCode, planCode));
            processInstanceStartReq.setBizType(SrmProcessConfigService.BizTypeEnum.SRM_PROCUREMENT_PLAN);
            processInstanceStartReq.setBizId(srmProcurementPlanId);
            srmProcessInstanceService.startProcessInstance(processInstanceStartReq);
        }
        return res;
    }

    public boolean convertAndSavePlanDetailEntityList(SrmProcurementPlanEntireVo srmProcurementPlanEntireVo, Long srmProcurementPlanId) {
        ExceptionUtil.check(CollectionUtils.isEmpty(srmProcurementPlanEntireVo.getConfig())
                        || CollectionUtils.isEmpty(srmProcurementPlanEntireVo.getSrmProcurementPlanFieldValueList()),
                "5002011",
                "动态字段配置集合或每条采购计划明细字段及值集合为空，无法新增");
        final Map<String, EnhanceBaseServiceTypeFieldVo> codeMap = srmProcurementPlanEntireVo.getConfig().stream()
                .collect(Collectors.toMap(EnhanceBaseServiceTypeFieldVo::getFieldCode, field -> field));
        srmProcurementPlanEntireVo.getSrmProcurementPlanFieldValueList().stream().forEach(e -> {
            // 组成采购计划明细及字段数据
            SrmProcurementPlanDetailEntity planDetailEntity = new SrmProcurementPlanDetailEntity();
            List<SrmProcurementPlanFieldValueEntity> fieldList = new ArrayList<>();

            final String requireNo = defaultCodeGenerator.generateWithPrefixCode(CodeGeneratorPrefixEnum.RN.name());
            planDetailEntity.setPlanId(srmProcurementPlanId);
            planDetailEntity.setBuyWay(srmProcurementPlanEntireVo.getBuyWay());
            planDetailEntity.setRequireNo(requireNo);
            planDetailEntity.setDeptId(srmProcurementPlanEntireVo.getDeptId());
            planDetailEntity.setTenantId(srmProcurementPlanEntireVo.getTenantId());
            planDetailEntity.setServiceTypeId(srmProcurementPlanEntireVo.getServiceTypeId());

            e.getData().forEach(ele -> {
                final EnhanceBaseServiceTypeFieldVo enhance = codeMap.get(ele.getCode());
                FixedFieldEnum fixedFieldEnum = FixedFieldEnum.getByCode(enhance.getFieldCode());
                if (fixedFieldEnum != null) {
                    if (FixedFieldEnum.isPlanRequireField(fixedFieldEnum) && Objects.equals(YesNoEnum.YES.getCode(), enhance.getPlanIsRequired())) {
                        ExceptionUtil.check(StringUtils.isBlank(String.valueOf(ele.getValue())), GlobalResultCode.INVALID_PARAMS, fixedFieldEnum.getDesc() + "不能为空");
                    }
                    if (StringUtils.isNotBlank(String.valueOf(ele.getValue()))) {
                        switch (fixedFieldEnum) {
                            case MATERIAL_CODE:
                                planDetailEntity.setMaterialCode(String.valueOf(ele.getValue()));
                                break;
                            case MATERIAL_NAME:
                                planDetailEntity.setMaterialName(String.valueOf(ele.getValue()));
                                break;
                            case SPEC_MODEL:
                                planDetailEntity.setSpecModel(String.valueOf(ele.getValue()));
                                break;
                            case UNIT:
                                planDetailEntity.setUnit(String.valueOf(ele.getValue()));
                                break;
                            case REQUIRED_QUANTITY:
                                planDetailEntity.setRequiredQuantity(new BigDecimal(String.valueOf(ele.getValue())));
                                break;
                            case USAGE_LOCATION_ID:
                                planDetailEntity.setUsageLocationId(Long.parseLong(String.valueOf(ele.getValue())));
                                break;
                            case QUALITY_INDICATOR_ID:
                                if (Objects.equals(YesNoEnum.YES.getCode(), enhance.getPlanIsRequired())) {
                                    planDetailEntity.setQualityIndicatorId(Long.parseLong(String.valueOf(ele.getValue())));
                                }
                                break;
                        }
                    }
                } else {
                    ExceptionUtil.check(Objects.equals(YesNoEnum.YES.getCode(), enhance.getPlanIsRequired())
                            && StringUtils.isBlank(String.valueOf(ele.getValue())), GlobalResultCode.INVALID_PARAMS,
                            enhance.getFieldName() + "不能为空");
                    // 动态字段
                    final SrmProcurementPlanFieldValueEntity fieldValueEntity = new SrmProcurementPlanFieldValueEntity();
                    fieldValueEntity.setServiceTypeId(srmProcurementPlanEntireVo.getServiceTypeId());
                    fieldValueEntity.setFieldName(enhance.getFieldName());
                    fieldValueEntity.setFieldCode(enhance.getFieldCode());
                    fieldValueEntity.setFieldType(enhance.getFieldType());
                    // 枚举类型需要存储原始的枚举配置
                    if (enhance.getFieldType() == BaseServiceTypeFieldService.FieldTypeEnum.LINK_FORM) {
                        fieldValueEntity.setEnumValues(null);
                    } else {
                        fieldValueEntity.setEnumValues(enhance.getEnumValues());
                    }
                    fieldValueEntity.setFieldValue(String.valueOf(ele.getValue()));
                    fieldValueEntity.setDeptId(srmProcurementPlanEntireVo.getDeptId());
                    fieldList.add(fieldValueEntity);
                }
            });

            // 保存采购明细得到明细ID
            srmProcurementPlanDetailService.save(planDetailEntity);
            // 保存明细字段数据
            fieldList.forEach(f -> f.setPlanDetailId(planDetailEntity.getId()));
            srmProcurementPlanFieldValueService.saveBatch(fieldList);
        });
        return true;
    }
}