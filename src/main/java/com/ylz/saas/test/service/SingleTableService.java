//package com.ylz.saas.test.service;
//
//import com.baomidou.mybatisplus.extension.service.IService;
//import com.ylz.saas.test.entity.SingleTableEntity;
//import com.ylz.saas.test.vo.SingleTableVo;
//
//import org.springframework.web.multipart.MultipartFile;
//
//import jakarta.servlet.http.HttpServletResponse;
//
//public interface SingleTableService extends IService<SingleTableEntity> {
//	/**
//     * 新增单表测试页面
//     * @param singleTableVo 单表测试页面
//     */
//    boolean saveSingleTable(SingleTableVo singleTableVo);
//
//	/**
//     * 修改单表测试页面
//     * @param singleTableVo 单表测试页面
//     */
//    boolean updateSingleTable(SingleTableVo singleTableVo);
//
//	/**
//     * 导入模板下载
//     * @param response
//     */
//    void template(HttpServletResponse response);
//
//    /**
//     * 数据导入
//     * @param file
//     * @param response
//     */
//    void uploadData(MultipartFile file, HttpServletResponse response);
//}