package com.ylz.saas.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/6/19 13:43
 * @Description
 */
@Getter
@AllArgsConstructor

public enum EnterpriseNatureEnum {


    LIMITED_LIABILITY_COMPANY("有限责任公司"),
    COMPANY_LIMITED("股份有限公司"),
    SOLE_PROPRIETORSHIP_COMPANY("个人独资"),
    LIMITED_PARTNERSHIP("有限合伙"),
    PARTNERSHIP("合伙企业"),
    ORDINARY_COOPERATION("普通合作"),
    STATE_OWNED("国有企业"),
    FOREIGN_INVESTED("外商投资"),
    HONGKONG_MACAO_TAIWAN("港澳台投资"),
    COLLECTIVE("集体所有制"),
    JOINT_VENTURE("联营"),
    PRIVATE("私营"),
    INDIVIDUAL("个体工商户");

    private final String value;

}
