package com.ylz.saas.controller;

import com.ylz.saas.common.core.util.R;
import com.ylz.saas.entity.SrmTenderAwardNotice;
import com.ylz.saas.req.AwardedItemsQueryReq;
import com.ylz.saas.req.AwardedNoticeDetailReq;
import com.ylz.saas.req.AwardedSuppliersQueryReq;
import com.ylz.saas.req.SrmTenderAwardNoticeReq;
import com.ylz.saas.req.SrmTenderBidBySectionReq;
import com.ylz.saas.req.SrmTenderBidPublicNoticeReq;
import com.ylz.saas.req.SrmTenderBidPublicityReq;
import com.ylz.saas.req.SrmTenderBidReq;
import com.ylz.saas.req.SrmTenderEditAwardNoticeReq;
import com.ylz.saas.req.SrmTenderEditBatchAwardNoticeReq;
import com.ylz.saas.resp.AwardedSupplierResp;
import com.ylz.saas.resp.BidSectionInfoResp;
import com.ylz.saas.resp.SrmTenderEvaluationResultDetailResp;
import com.ylz.saas.resp.TenderBidderItemsResp;
import com.ylz.saas.service.SrmTenderAwardNoticeService;
import com.ylz.saas.service.SrmTenderBidderQuoteItemService;
import com.ylz.saas.service.SrmTenderEvaluationResultService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 定标控制器
 */
@RestController
@RequestMapping("/tenderBid")
public class SrmTenderBidController {

    @Resource
    private SrmTenderEvaluationResultService evaluationResultService;

    @Resource
    private SrmTenderAwardNoticeService awardNoticeService;

    @Resource
    private SrmTenderBidderQuoteItemService srmTenderBidderQuoteItemService;




    /**
     * 定标提交审核
     *
     * @param req
     * @return
     */
    @PostMapping("/review")
    public R<Void> reviewBid(@RequestBody @Validated SrmTenderBidReq req) {
        evaluationResultService.reviewBid(req);
        return R.ok();
    }

    /**
     * 中标公示
     *
     * @param req
     * @return
     */
    @PostMapping("/bidPublicity")
    public R<Void> bidPublicity(@RequestBody @Validated SrmTenderBidPublicityReq req) {
        evaluationResultService.bidPublicity(req);
        return R.ok();
    }

    /**
     * 中标公告
     *
     * @param req
     * @return
     */
    @PostMapping("/bidPublicNotice")
    public R<Void> bidPublicNotice(@RequestBody @Validated SrmTenderBidPublicNoticeReq req) {
        evaluationResultService.bidPublicNotice(req);
        return R.ok();
    }


    /**
     * 中标通知书--一键发送
     *
     * @param req
     * @return
     */
    @PostMapping("/awardNoticeSent")
    public R<Void> awardNoticeSent(@RequestBody @Validated SrmTenderAwardNoticeReq req) {
        awardNoticeService.awardNoticeSent(req);
        return R.ok();
    }


    /**
     * 中标通知书--一键签章
     *
     * @param req
     * @return
     */
    @PostMapping("/awardNoticeSignature")
    public R<Void> awardNoticeSignature(@RequestBody @Validated SrmTenderAwardNoticeReq req) {
        awardNoticeService.awardNoticeSignature(req);
        return R.ok();
    }

    /**
     * 中标通知书--编辑中标通知书
     *
     * @param req
     * @return
     */
    @PostMapping("/editAwardNotice")
    public R<Void> editAwardNotice(@RequestBody @Validated SrmTenderEditAwardNoticeReq req) {
        awardNoticeService.editAwardNotice(req);
        return R.ok();
    }
    /**
     * 通过noticeId和projectId获取评标结果表明细（包含报价轮次）
     *
     * @param noticeId 招标公告ID
     * @param projectId 采购立项ID
     * @return 评标结果详情
     */
    @GetMapping("/getById/{noticeId}/{projectId}")
    public R<SrmTenderEvaluationResultDetailResp> getById(@PathVariable("noticeId") Long noticeId, @PathVariable("projectId") Long projectId) {
        SrmTenderEvaluationResultDetailResp result = evaluationResultService.getEvaluationResultDetail(noticeId, projectId);
        return R.ok(result);
    }

    /**
     * 查询中标明细列表
     * 此接口返回招标中可能中标的明细，筛选只显示中标的明细
     * 物料编码与物料名称为固定返回字段，其余还有动态字段
     * 支持按物料编码和物料名称进行查询筛选
     *
     * @param req 查询请求参数
     * @return 中标明细列表（包含动态字段）
     */
    @PostMapping("/awardedItems")
    public R<TenderBidderItemsResp> getAwardedItems(@RequestBody @Validated AwardedItemsQueryReq req) {
        TenderBidderItemsResp awardedItems = srmTenderBidderQuoteItemService.queryAwardedItems(req);
        return R.ok(awardedItems);
    }
    /**
     * 查询中标供应商列表
     * 根据noticeId和projectId查询中标供应商信息
     * 包含供应商基本信息、联系人信息和中标统计信息
     *
     * @param req 查询请求参数
     * @return 中标供应商列表
     */
    @PostMapping("/awardedSuppliers")
    public R<List<AwardedSupplierResp>> getAwardedSuppliers(@RequestBody @Validated AwardedSuppliersQueryReq req) {
        List<AwardedSupplierResp> awardedSuppliers = srmTenderBidderQuoteItemService.queryAwardedSuppliers(req);
        return R.ok(awardedSuppliers);
    }



    /**
     * 批量生成中标通知书
     *
     * @param req
     * @return
     */
    @PostMapping("/batchAwardNotice")
    public R<Void> batchAwardNotice(@RequestBody @Validated(SrmTenderEditBatchAwardNoticeReq.InterfaceFor.class) List<SrmTenderEditBatchAwardNoticeReq> req) {
        awardNoticeService.batchAwardNotice(req);
        return R.ok();
    }


    /**
     * 定标标段列表查询
     */
    @GetMapping("/list")
    public R<List<BidSectionInfoResp>> bidSectionList(@RequestParam("projectId") Long projectId, @RequestParam("noticeId") Long noticeId) {
        List<BidSectionInfoResp> result = evaluationResultService.bidSectionList(projectId, noticeId);
        return R.ok(result);
    }


    /**
     * 查询中标通知书
     *
     * @param req
     * @return
     */
    @PostMapping("/awardNotice/detail")
    public R<SrmTenderAwardNotice> awardNoticeDetail(@RequestBody @Validated AwardedNoticeDetailReq req) {
        SrmTenderAwardNotice awardNotice = awardNoticeService.awardNoticeDetail(req);
        return R.ok(awardNotice);
    }



}

