package com.ylz.saas.resp;

import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.enums.InviteMethodEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 中标公告信息
 */
@Data
public class SrmAllPublicNoticePageResp implements Serializable {

    /**
     * ID
     */
    private Long id;

    /**
     * 公开公告id
     */
    private Long noticeId;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 寻源方式
     */
    private BaseServiceTypeFieldService.BuyWayEnum sourcingMethod;

    /**
     * 公共标题
     */
    private String noticeTitle;
    
    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 公开类别
     */
    private InviteMethodEnum publicCategory;

    /**
     * 公告作者
     */
    private String noticeAuthor;

    /**
     * 发布人
     */
    private String publisher;

    /**
     * 发布时间
     */
    private String publishTime;

    /**
     * 附件信息
     */
    private List<SrmProjectAttachment> attachmentList;

}