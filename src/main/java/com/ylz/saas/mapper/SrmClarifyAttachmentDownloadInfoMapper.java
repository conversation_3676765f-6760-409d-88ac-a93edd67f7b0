package com.ylz.saas.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylz.saas.entity.SrmClarifyAttachmentDownloadInfo;
import com.ylz.saas.resp.ClarifyAttachmentDownloadInfoResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_clarify_attachment_download_info(澄清公告附件下载信息)】的数据库操作Mapper
* @createDate 2025-07-10 14:53:29
* @Entity com.ylz.saas.entity.SrmClarifyAttachmentDownloadInfo
*/
@Mapper
public interface SrmClarifyAttachmentDownloadInfoMapper extends BaseMapper<SrmClarifyAttachmentDownloadInfo> {

    /**
     * 查询供应商下载信息
     * @param clarifyNoticeId
     * @param supplierName
     * @return
     */
    List<ClarifyAttachmentDownloadInfoResp> querySupplierDownloadInfo(@Param("clarifyNoticeId")Long clarifyNoticeId,@Param("supplierName") String supplierName);

}




