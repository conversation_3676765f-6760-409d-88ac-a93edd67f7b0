package com.ylz.saas.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.entity.SrmTenderNotice;
import com.ylz.saas.req.SrmAllNoticePageReq;
import com.ylz.saas.req.SrmAppletNoticePageReq;
import com.ylz.saas.resp.SrmAllInvitePageResp;
import com.ylz.saas.resp.SrmAllNoticePageResp;
import com.ylz.saas.resp.SrmAllPublicNoticePageResp;
import com.ylz.saas.resp.SrmAllPublicityPageResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 统一公告查询Mapper
 * 专门用于处理统一公告页面的复杂查询逻辑
 */
@Mapper
public interface SrmAllNoticeTabMapper extends BaseMapper<SrmTenderNotice> {

    /**
     * 招标公告分页查询
     * @param page 分页参数
     * @param req 查询条件
     * @return 招标公告分页结果
     */
    Page<SrmAllNoticePageResp> selectTenderNoticePage(Page<SrmAllNoticePageResp> page, @Param("req") SrmAllNoticePageReq req);

    /**
     * 邀请函分页查询
     * @param page 分页参数
     * @param req 查询条件
     * @return 邀请函分页结果
     */
    Page<SrmAllInvitePageResp> selectInvitePage(Page<SrmAllInvitePageResp> page, @Param("req") SrmAllNoticePageReq req);


    /**
     * 邀请函和公告分页查询
     * @param page
     * @param req
     * @return
     */
    Page<SrmAllNoticePageResp> inviteAndNoticePage(Page<SrmAppletNoticePageReq> page, @Param("req") SrmAllNoticePageReq req);

    /**
     * 公示分页查询
     * @param page 分页参数
     * @param req 查询条件
     * @return 公示分页结果
     */
    Page<SrmAllPublicityPageResp> selectPublicityPage(Page<SrmAllPublicityPageResp> page, @Param("req") SrmAllNoticePageReq req);

    /**
     * 公告分页查询
     * @param page 分页参数
     * @param req 查询条件
     * @return 公告分页结果
     */
    Page<SrmAllPublicNoticePageResp> selectPublicNoticePage(Page<SrmAllPublicNoticePageResp> page, @Param("req") SrmAllNoticePageReq req);

    /**
     * 根据项目负责人姓名查询用户ID列表
     * @param projectLeaderName 项目负责人姓名
     * @return 用户ID列表
     */
    List<Long> getUserIdsByProjectLeaderName(@Param("projectLeaderName") String projectLeaderName);

    /**
     * 根据用户ID列表查询项目ID列表
     * @param userIds 用户ID列表
     * @return 项目ID列表
     */
    List<Long> getProjectIdsByUserIds(@Param("userIds") List<Long> userIds);
}
