package com.ylz.saas.codegen.srm_procurement_plan_detail.vo;

import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.codegen.srm_procurement_plan_field_value.entity.SrmProcurementPlanFieldValueEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购计划明细表
 *
 * <AUTHOR>
 * @date 2025-06-03 21:00:11
 */
@Data
public class SrmProcurementPlanDetailVo implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 组织ID
     */
    private Long deptId;

    /**
     * 采购计划ID
     */
    private Long planId;

    /**
     * 服务类型ID
     */
    private Long serviceTypeId;

    /**
     * 采招（寻源）方式
     */
    private BaseServiceTypeFieldService.BuyWayEnum buyWay;

    /**
     * 需求行号
     */
    private String requireNo;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 规格型号
     */
    private String specModel;

    /**
     * 单位
     */
    private String unit;

    /**
     * 需求数量
     */
    private BigDecimal requiredQuantity;

    /**
     * 计划单价
     */
    private BigDecimal unitPrice;

    /**
     * 计划总价
     */
    private BigDecimal totalPrice;

    /**
     * 上单价
     */
    private BigDecimal listPrice;

    /**
     * 预算价
     */
    private BigDecimal budgetPrice;

    /**
     * 质量指标ID
     */
    private Long qualityIndicatorId;

    /**
     * 支付方式ID
     */
    private Long paymentMethodId;

    /**
     * 备注
     */
    private String note;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 修改人名称
     */
    private String updateByName;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标识（0-正常、1-删除）
     */
    private int delFlag;

    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 子表集合:srm_procurement_plan_field_value
     */
    private List<SrmProcurementPlanFieldValueEntity> srmProcurementPlanFieldValueList;
}