package com.ylz.saas.service;

import com.ylz.saas.req.SrmNegotiationAddReq;

/**
* <AUTHOR>
* @description 针对表【srm_negotiation_bid_doc(竞谈文件)】的数据库操作Service
* @createDate 2025-07-10 14:31:42
*/
public interface SrmNegotiationBidDocService {

    /**
     * 添加竞谈文件
     *
     * @param req
     * @return
     */
    void addNegotiationDoc(SrmNegotiationAddReq req);

    /**
     * 竞谈文件核心处理
     *
     * @param req
     * @param isChange
     */
    void negotiationCoreHandler(SrmNegotiationAddReq req, boolean isChange);

    /**
     * 获取竞谈文件详情
     *
     * @param noticeId
     * @return
     */
    SrmNegotiationAddReq negotiationDocDetail(Long noticeId);

    /**
     * 获取竞谈文件详情for审批
     *
     * @param projectIdAndNoticeId
     * @return
     */
    SrmNegotiationAddReq negotiationDocDetailForApprove(String projectIdAndNoticeId);

    /**
     * 竞谈文件审批通过
     */
    void negotiationDocApprovePass(String projectIdAndNoticeId);

    /**
     * 竞谈文件审批拒绝
     */
    void negotiationDocApproveReject(String projectIdAndNoticeId);

    /**
     * 竞谈文件审批锁定修改为审批中
     *
     * @param id
     */
    void docApprovingHook(String id);
}
