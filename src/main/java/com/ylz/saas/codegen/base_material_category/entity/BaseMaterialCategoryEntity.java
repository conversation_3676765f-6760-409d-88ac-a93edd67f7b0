package com.ylz.saas.codegen.base_material_category.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ylz.saas.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


/**
 * 物料分类表
 *
 * <AUTHOR>
 * @date 2025-06-03 17:25:41
 */
@Data
@TableName("base_material_category")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "物料分类表")
@TenantTable
public class BaseMaterialCategoryEntity extends Model<BaseMaterialCategoryEntity> {


    /**
  	 * 创建人ID
   	 */
    @Schema(description="创建人ID")
    @TableField(fill = FieldFill.INSERT)
    private Long createById;

    /**
  	 * 主键ID
   	 */
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

    /**
  	 * 组织ID
   	 */
    @Schema(description="组织ID")
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
  	 * 更新人ID
   	 */
    @Schema(description="更新人ID")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateById;

    /**
  	 * 物料分类编码
   	 */
    @Schema(description="物料分类编码")
    private String categoryCode;

    /**
  	 * 物料分类名称
   	 */
    @Schema(description="物料分类名称")
    private String categoryName;

    /**
  	 * 上级物料分类编码
   	 */
    @Schema(description="上级物料分类编码")
    private String parentCategoryCode;

    /**
  	 * 上级物料分类名称
   	 */
    @Schema(description="上级物料分类名称")
    private String parentCategoryName;

    /**
  	 * 来源标识
   	 */
    @Schema(description="来源标识")
    private String dataSource;

    /**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
    @Schema(description="状态（DISABLED-禁用、ENABLED-启用）")
    private String status;

    /**
  	 * 创建人名称
   	 */
    @Schema(description="创建人名称")
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    @Schema(description="修改人名称")
    private String updateByName;

    /**
  	 * 创建人
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
  	 * 创建时间
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

    /**
  	 * 修改时间
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标识（0-正常、1-删除）")
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    @Schema(description="租户ID")
    private Long tenantId;
}