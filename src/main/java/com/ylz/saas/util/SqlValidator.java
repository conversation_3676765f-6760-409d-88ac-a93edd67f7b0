package com.ylz.saas.util;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlInjectionUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class SqlValidator {

    /**
     * sql注释的正则
     */
    private final static Pattern SQL_ANNOTATION = Pattern.compile("/\\*[\\s\\S]*\\*/");
    /**
     * 默认—sql注入关键词
     */
    private final static String SIMPLE_XSS_STR = "exec |peformance_schema|information_schema|extractvalue|updatexml|geohash|gtid_subset|gtid_subtract|insert |delete |update |drop |chr |mid |master |truncate |char |declare |;|--";
    // private final static String XSS_STR = "and |exec |peformance_schema|information_schema|extractvalue|updatexml|geohash|gtid_subset|gtid_subtract|insert |select |delete |update |drop |count |chr |mid |master |truncate |char |declare |;|or |+|--";
    /**
     * sql注入提示语
     */
    private final static String SQL_INJECTION_KEYWORD_TIP = "请注意，存在SQL注入关键词---> {}";
    private final static String SQL_INJECTION_TIP = "请注意，值可能存在SQL注入风险!--->";
    private final static String SQL_INJECTION_TIP_VARIABLE = "请注意，值可能存在SQL注入风险!---> {}";

    /**
     * 验证给定的 SQL 字符串是否仅包含 SELECT 查询语句
     * 1. 不是抛出异常
     *
     * @param sql 需要验证的 SQL 字符串
     * @return 如果是仅含 SELECT 的查询语句返回 true，否则返回 false
     */
    public static boolean isSelectOnly(String sql) {
        String trimSql = sql.trim();
        if (StrUtil.isBlank(trimSql)) {
            return false;
        }
        // 防止 SQL 注入
        checkSqlAnnotation(trimSql);
        checkTextSafety(trimSql);
        // 检查是否以 SELECT 开头（忽略大小写）
        if (!trimSql.toUpperCase().startsWith("SELECT")) {
            throw new IllegalArgumentException("不是 select 语句: " + trimSql);
        }
        return true;
    }

    /**
     * 移除 SQL 字符串中的注释
     */
    private static String removeComments(String sql) {
        // 移除多行注释 /* ... */
        sql = sql.replaceAll("/\\*.*?\\*/", "");

        // 移除单行注释 -- ...
        sql = sql.replaceAll("--.*?$", "");

        return sql;
    }

    /**
     * 校验是否有sql注释
     *
     * @return
     */
    public static void checkSqlAnnotation(String str) {
        Matcher matcher = SQL_ANNOTATION.matcher(str);
        if (matcher.find()) {
            String error = "请注意，值可能存在SQL注入风险(注释语句)---> " + str;
            log.error(error);
            throw new IllegalArgumentException(error);
        }
        if (str.contains("-- ")) {
            String error = "请注意，值可能存在SQL注入风险(单行注释语句)---> " + str;
            log.error(error);
            throw new IllegalArgumentException(error);
        }
    }

    /**
     * 检查是否有不安全的子串
     *
     * @param text
     */
    public static void checkTextSafety(String text) {

        // 二、SQL注入检测存在绕过风险 (普通文本校验)
        // https://gitee.com/jeecg/jeecg-boot/issues/I4NZGE
        List<String> xssArr = new ArrayList<>(Arrays.stream(SIMPLE_XSS_STR.split("\\|")).toList());
        xssArr.addAll(Arrays.stream(SIMPLE_XSS_STR.toUpperCase().split("\\|")).toList());
        for (int i = 0; i < xssArr.size(); i++) {

            if (text.contains(xssArr.get(i))) {
                log.error(SQL_INJECTION_TIP_VARIABLE, text);
                log.error(SQL_INJECTION_TIP_VARIABLE, xssArr.get(i));
                throw new IllegalArgumentException(SQL_INJECTION_TIP + text + "(关键词: %s)".formatted(xssArr.get(i)));
            }
        }
    }

    /**
     * 压缩 SQL 字符串中的空白字符
     */
    private static String compressWhitespace(String sql) {
        return sql.replaceAll("\\s+", " ").trim();
    }

    public static void main(String[] args) {
        // "SELECT * FROM orders; -- 这是一个注释",
        // "SELECT * FROM products /* 多行注释 */ WHERE price < 100",
        // "SELECT * FROM customers; INSERT INTO logs VALUES ('test')",
        // "UPDATE users SET status = 'active'",
        // "SELECT * FROM table1; SELECT * FROM table2",
        // "SELECT * FROM dual; DROP TABLE temp",
        String[] testCases = {
                "SELECT * FROM users",
                "SELECT name, age FROM people WHERE age > 25",
                // "SELECT * FROM orders; -- 这是一个注释",
                "select userId as \"key\", userName as \"value\" from user where userId = 1 and userName like '%白%'",


                "WITH temp AS (SELECT id FROM items) SELECT * FROM temp",

                "DELETE FROM expired_items"
        };

        for (String sql : testCases) {
            // boolean result = !SqlInjectionUtils.check(sql);
            // checkSqlAnnotation(sql);
            // checkTextSafety(sql);
            boolean result = isSelectOnly(sql);
            System.out.printf("%s %s %s%n",
                    result ? "✅" : "❌",
                    sql,
                    result ? "[仅SELECT]" : "[包含其他命令]"
            );
        }

        // test3();
    }

}
