package com.ylz.saas.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.entity.SrmTenantSupplierContact;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tenant_supplier_contact(租户供应商联系人表)】的数据库操作Mapper
* @createDate 2025-06-10 18:40:08
* @Entity com.ylz.saas.entity.SrmTenantSupplierContact
*/
@Mapper
public interface SrmTenantSupplierContactMapper extends SaasBaseMapper<SrmTenantSupplierContact> {

    List<SrmTenantSupplierContact> getContactsBySupplierId(@Param("supplierId") Long supplierId);

    /**
     * 分页查询供应商联系人列表（联查sys_user表获取密码）
     * @param supplierId 供应商ID
     * @param page 分页参数
     * @return 分页联系人列表
     */
    Page<SrmTenantSupplierContact> getContactsBySupplierIdPageWithUser(@Param("supplierId") Long supplierId, Page<SrmTenantSupplierContact> page);

    /**
     * 物理删除供应商联系人
     * @param id 联系人ID
     * @return 删除的记录数
     */
    int physicalDeleteById(@Param("id") Long id);

    /**
     * 根据用户id获取
     * @param account
     * @return
     */
    SrmTenantSupplierContact getByUserAccount(@Param("account") String account);
}




