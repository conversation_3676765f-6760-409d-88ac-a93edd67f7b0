package com.ylz.saas.mapper;


import com.github.houbb.heaven.annotation.reflect.Param;
import com.ylz.saas.entity.SrmTenderScoreTemplateItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylz.saas.resp.SrmTenderScoreTemplateDetailResp;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tender_score_template_item(评审/评分项表)】的数据库操作Mapper
* @createDate 2025-07-09 16:12:21
* @Entity com.ylz.saas.entity.SrmTenderScoreTemplateItem
*/
@Mapper
public interface SrmTenderScoreTemplateItemMapper extends BaseMapper<SrmTenderScoreTemplateItem> {
    void batchInsertTemplateItems(List<SrmTenderScoreTemplateItem> reviewEntities);
    /**
     * 查询评审项
     */
    List<SrmTenderScoreTemplateDetailResp.TemplateReviewItem> selectReviewItems(
            @Param("id") Long id,
            @Param("type") String type);

    /**
     * 查询评分项
     */
    List<SrmTenderScoreTemplateDetailResp.TemplateScoreItem> selectScoreItems(
            @Param("id") Long id,
            @Param("type") String type);

    int deleteItemsByTemplateIdAndType(@Param("templateId") Long templateId, @Param("type") String type);
}




