package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ylz.saas.common.core.util.TenantTable;
import com.ylz.saas.enums.BidsSegmentTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 招标要求表
 * @TableName srm_tender_requirement
 */
@TenantTable
@TableName(value ="srm_tender_requirement")
@Data
public class SrmTenderRequirement {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 招标公告ID
     */
    private Long noticeId;

    /**
     * 标段ID
     */
    private Long sectionId;

    /**
     * 要求类型(QUALIFICATION-资质要求、CONDITION-响应条件、FEE-费用)
     */
    private BidsSegmentTypeEnum requirementType;

    /**
     * 要求名称/资质证件ID
     */
    private String requirementName;

    /**
     * 要求内容/补充说明
     */
    private String requirementContent;

    /**
     * 是否需要响应(0-否、1-是)
     */
    private Integer needResponse;

    /**
     * 序号
     */
    private Integer sortNo;

    /**
     * 删除标识（0-正常、1-删除）
     */
    private Integer delFlag;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createById;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updateById;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

}