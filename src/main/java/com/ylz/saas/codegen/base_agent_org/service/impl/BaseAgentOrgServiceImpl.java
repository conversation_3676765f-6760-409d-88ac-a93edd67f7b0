package com.ylz.saas.codegen.base_agent_org.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.admin.api.dto.UserDTO;
import com.ylz.saas.admin.api.dto.UserIdentityRoleCodeBindDTO;
import com.ylz.saas.admin.api.entity.SysDept;
import com.ylz.saas.admin.api.entity.SysIdentity;
import com.ylz.saas.admin.api.entity.SysPost;
import com.ylz.saas.admin.api.entity.SysRole;
import com.ylz.saas.admin.api.entity.SysUser;
import com.ylz.saas.admin.api.entity.SysUserIdentityDataPermissionRelation;
import com.ylz.saas.admin.api.entity.SysUserIdentityRoleRelation;
import com.ylz.saas.admin.mapper.SysIdentityMapper;
import com.ylz.saas.admin.mapper.SysUserIdentityDataPermissionRelationMapper;
import com.ylz.saas.admin.mapper.SysUserIdentityRoleRelationMapper;
import com.ylz.saas.admin.mapper.SysUserMapper;
import com.ylz.saas.admin.service.SysUserService;
import com.ylz.saas.codegen.base_agent_org.entity.BaseAgentOrgEntity;
import com.ylz.saas.codegen.base_agent_org.mapper.BaseAgentOrgMapper;
import com.ylz.saas.codegen.base_agent_org.service.BaseAgentOrgService;
import com.ylz.saas.codegen.base_agent_org.vo.BaseAgentOrgVo;
import com.ylz.saas.codegen.base_agent_user.entity.BaseAgentUserEntity;
import com.ylz.saas.codegen.base_agent_user.service.BaseAgentUserService;

import com.ylz.saas.codegen.base_agent_org.vo.AgentUserInnerAccountReq;
import com.ylz.saas.common.enums.CodeGeneratorPrefixEnum;
import com.ylz.saas.common.enums.CommonSwitchEnum;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.enums.DefaultDeptExtEnum;
import com.ylz.saas.common.security.util.SecurityUtils;
import com.ylz.saas.common.sequence.generator.impl.DefaultCodeGenerator;
import com.ylz.saas.common.utils.DefaultDeptExtUtil;
import com.ylz.saas.enums.DefaultRoleEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ylz.saas.enums.DataPermissionTypeEnum.PROJECT;
import static com.ylz.saas.enums.DefaultRoleEnum.IdentifyCode.BID_AGENCY_IDENTITY;
import static com.ylz.saas.enums.DefaultRoleEnum.IdentifyCode.PURCHASER_IDENTITY;

/**
 * 代理机构表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:11
 */
@Service
@Slf4j
public class BaseAgentOrgServiceImpl extends ServiceImpl<BaseAgentOrgMapper, BaseAgentOrgEntity> implements BaseAgentOrgService {

    @Autowired
    private BaseAgentUserService baseAgentUserService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private DefaultDeptExtUtil defaultDeptExtUtil;

    @Autowired
    private DefaultCodeGenerator defaultCodeGenerator;

    @Resource
    private SysUserIdentityRoleRelationMapper userIdentityRoleRelationMapper;

    @Resource
    private SysIdentityMapper identityMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private SysUserIdentityDataPermissionRelationMapper dataPermissionRelationMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveBaseAgentOrg(BaseAgentOrgVo baseAgentOrgVo) {
        StringBuilder sb = new StringBuilder();
        sb.append("保存成功!");
        BaseAgentOrgEntity baseAgentOrg = new BaseAgentOrgEntity();
        BeanUtil.copyProperties(baseAgentOrgVo, baseAgentOrg);

        Long tenantId = SecurityUtils.getUser().getTenantId();
        if (StringUtils.isEmpty(baseAgentOrg.getStatus())) {
            baseAgentOrg.setStatus(CommonSwitchEnum.ENABLED.name());
        }
        baseAgentOrg.setAgentCode(defaultCodeGenerator.generate(CodeGeneratorPrefixEnum.JG.name(), CodeGeneratorPrefixEnum.JG.getLength()));

        // loginAccount唯一性校验
        if (CollectionUtils.isNotEmpty(baseAgentOrgVo.getBaseAgentUserList())) {
            for (BaseAgentUserEntity user : baseAgentOrgVo.getBaseAgentUserList()) {
                if (StrUtil.isNotBlank(user.getLoginAccount())) {
                    long count = sysUserService.lambdaQuery()
                            .eq(SysUser::getUsername, user.getLoginAccount())
                            .count();
                    ExceptionUtil.check(count > 0, "500", "登录账号已存在：" + user.getLoginAccount());
                }
            }
        }

        save(baseAgentOrg);
        // 处理机构关联用户
        if (CollectionUtils.isNotEmpty(baseAgentOrgVo.getBaseAgentUserList())) {
            baseAgentOrgVo.getBaseAgentUserList().forEach(user -> user.setDeptTypeId(baseAgentOrgVo.getDeptTypeId()));
            String userProcessResult = processAgentOrgUsers(baseAgentOrgVo.getBaseAgentUserList(),
                    baseAgentOrg.getId(), tenantId);
            sb.append(userProcessResult);
        }
        return sb.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBaseAgentOrg(BaseAgentOrgVo baseAgentOrgVo) {
        BaseAgentOrgEntity baseAgentOrg = getById(baseAgentOrgVo.getId());
        ExceptionUtil.check(baseAgentOrg == null, "500", "代理机构表不存在");

        // loginAccount唯一性校验（只校验新增用户）
        if (CollectionUtils.isNotEmpty(baseAgentOrgVo.getBaseAgentUserList())) {
            for (BaseAgentUserEntity user : baseAgentOrgVo.getBaseAgentUserList()) {
                if (user.getUserId() == null && StrUtil.isNotBlank(user.getLoginAccount())) {
                    long count = sysUserService.lambdaQuery()
                            .eq(SysUser::getUsername, user.getLoginAccount())
                            .count();
                    ExceptionUtil.check(count > 0, "500", "登录账号已存在：" + user.getLoginAccount());
                }
            }
        }

        // 获取当前机构的所有关联用户
        List<BaseAgentUserEntity> oldBaseAgentUserList = Optional.ofNullable(baseAgentUserService.list(
                        Wrappers.<BaseAgentUserEntity>lambdaQuery()
                                .eq(BaseAgentUserEntity::getAgentOrgId, baseAgentOrg.getId())
                ))
                .orElse(Collections.emptyList());

        // 获取新的用户列表
        List<BaseAgentUserEntity> newBaseAgentUserList = Optional.ofNullable(baseAgentOrgVo.getBaseAgentUserList())
                .orElse(Collections.emptyList());

        // 处理账号的增减逻辑
        handleAgentUserChanges(baseAgentOrg.getId(), oldBaseAgentUserList, newBaseAgentUserList);

        // 更新机构基本信息
        BeanUtil.copyProperties(baseAgentOrgVo, baseAgentOrg);
        return updateById(baseAgentOrg);
    }

    @Override
    public BaseAgentOrgEntity getByIdBaseAgentOrg(Long id) {
        BaseAgentOrgEntity baseAgentOrg = getById(id);
        if (baseAgentOrg != null) {
            baseAgentOrg.setBaseAgentUserList(baseAgentUserService.list(Wrappers.<BaseAgentUserEntity>lambdaQuery().eq(BaseAgentUserEntity::getAgentOrgId, baseAgentOrg.getId())));
        }
        return baseAgentOrg;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIdsBaseAgentOrg(List<Long> ids) {
        List<BaseAgentOrgEntity> baseAgentOrg = lambdaQuery()
                .in(BaseAgentOrgEntity::getId, ids)
                .list();
        if (CollectionUtils.isNotEmpty(baseAgentOrg)) {
            removeByIds(ids);
            List<Long> baseAgentUserBaseAgentOrgId = baseAgentOrg.stream().map(BaseAgentOrgEntity::getId).toList();
            baseAgentUserService.remove(Wrappers.<BaseAgentUserEntity>lambdaQuery().in(BaseAgentUserEntity::getAgentOrgId, baseAgentUserBaseAgentOrgId));
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean switchStatus(Long id) {
        BaseAgentOrgEntity baseMaterialCategory = getById(id);
        ExceptionUtil.check(baseMaterialCategory == null, "500", "代理机构不存在");
        // 用户 0未锁定，9已锁定
        String newStatus;
        if (CommonSwitchEnum.ENABLED.name().equals(baseMaterialCategory.getStatus())) {
            baseMaterialCategory.setStatus(CommonSwitchEnum.DISABLED.name());
            newStatus = "9";
        } else {
            baseMaterialCategory.setStatus(CommonSwitchEnum.ENABLED.name());
            newStatus = "0";
        }
        this.updateById(baseMaterialCategory);

        // 联动修改该机构下所有用户的状态
        List<BaseAgentUserEntity> userList = baseAgentUserService.list(Wrappers.<BaseAgentUserEntity>lambdaQuery().eq(BaseAgentUserEntity::getAgentOrgId, id));
        if (CollectionUtils.isNotEmpty(userList)) {

            // 批量更新sys_user表的status
            List<Long> userIds = userList.stream().map(BaseAgentUserEntity::getUserId).filter(Objects::nonNull).toList();
            if (CollectionUtils.isNotEmpty(userIds)) {
                sysUserService.lambdaUpdate()
                        .in(SysUser::getUserId, userIds)
                        .set(SysUser::getLockFlag, newStatus)
                        .update();
            }
        }
        return true;
    }

    @Override
    public List<SysUser> getInnerAccount(AgentUserInnerAccountReq req) {

        List<BaseAgentUserEntity> existRelation = baseAgentUserService
                .lambdaQuery()
                .list();

        List<Long> userIds = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(existRelation)) {
            userIds.addAll(existRelation.stream().map(BaseAgentUserEntity::getUserId).toList());
        }
        if (CollectionUtils.isNotEmpty(req.getNotInUserIds())) {
            userIds.addAll(req.getNotInUserIds());
        }
        List<SysUser> list = sysUserService
                .lambdaQuery()
                .notIn(CollectionUtils.isNotEmpty(userIds), SysUser::getUserId, userIds)
                .like(StringUtils.isNotEmpty(req.getUserName()), SysUser::getUsername, req.getUserName())
                .like(StringUtils.isNotEmpty(req.getLoginAccount()), SysUser::getUsername, req.getLoginAccount())
                .like(StringUtils.isNotEmpty(req.getContactPhone()), SysUser::getPhone, req.getContactPhone())
                .eq(SysUser::getLockFlag, "0")
                .list();

        return list;
    }

    /**
     * 处理代理机构用户的增减变化
     *
     * @param agentOrgId 机构ID
     * @param oldUserList 原有用户列表
     * @param newUserList 新用户列表
     */
    private void handleAgentUserChanges(Long agentOrgId, List<BaseAgentUserEntity> oldUserList, List<BaseAgentUserEntity> newUserList) {
        Long tenantId = SecurityUtils.getUser().getTenantId();

        // 获取默认部门、角色、岗位信息（用于创建新账号）
//        SysDept deptByName = defaultDeptExtUtil.getDeptByName(DefaultDeptExtEnum.ORG_DEPT.getValue());
//        SysRole roleByCode = defaultDeptExtUtil.getRoleByCode(DefaultDeptExtEnum.ORG_ROLE.name());
        SysPost postByCode = defaultDeptExtUtil.getPostByCode(DefaultDeptExtEnum.EXTERNAL_POST.name());

//        ExceptionUtil.checkNonNull(deptByName, "默认外部机构部门未创建");
//        ExceptionUtil.checkNonNull(roleByCode, "默认外部机构角色未创建");
        ExceptionUtil.checkNonNull(postByCode, "默认外部机构岗位未创建");

        // 创建映射：用于快速查找
        Map<Long, BaseAgentUserEntity> oldUserMap = oldUserList.stream()
                .filter(user -> user.getId() != null)
                .collect(Collectors.toMap(BaseAgentUserEntity::getId, Function.identity()));

        // 分类处理新用户列表
        List<BaseAgentUserEntity> usersToAdd = new ArrayList<>();      // 需要新增的用户
        List<BaseAgentUserEntity> usersToUpdate = new ArrayList<>();   // 需要更新的用户

        for (BaseAgentUserEntity newUser : newUserList) {
            newUser.setAgentOrgId(agentOrgId);

            if (newUser.getId() == null) {
                // 新增用户：需要创建账号和关联关系
                usersToAdd.add(newUser);
            } else if (oldUserMap.containsKey(newUser.getId())) {
                // 更新用户：只更新关联关系
                usersToUpdate.add(newUser);
                oldUserMap.remove(newUser.getId()); // 从旧用户映射中移除，剩下的就是要删除的
            } else {
                // 这种情况不应该出现，但为了安全起见，当作新增处理
                usersToAdd.add(newUser);
            }
        }

        // 处理需要删除的用户关联关系（oldUserMap中剩余的用户）
        if (!oldUserMap.isEmpty()) {
            List<Long> idsToDelete = new ArrayList<>(oldUserMap.keySet());
            baseAgentUserService.removeByIds(idsToDelete);
            // 处理用户移除代理机构后的与权限相关的一些逻辑
            handleAgentUserDataPermission(oldUserMap.values());
        }

        // 处理需要新增的用户（创建账号或关联已有账号）
        if (!usersToAdd.isEmpty()) {
            processNewAgentUsers(usersToAdd, agentOrgId, tenantId, null, null, postByCode);
        }

        // 处理需要更新的用户（更新关联关系和同步用户信息）
        if (!usersToUpdate.isEmpty()) {
            processUpdateAgentUsers(usersToUpdate);
        }
    }

    /**
     * 处理用户移除代理机构后的与权限相关一些逻辑
     *
     * @param baseAgentUserEntityCollection 用户列表
     */
    private void handleAgentUserDataPermission(Collection<BaseAgentUserEntity> baseAgentUserEntityCollection) {
        List<Long> userIdList = baseAgentUserEntityCollection.stream().map(BaseAgentUserEntity::getUserId).toList();
        List<SysIdentity> sysIdentityList = identityMapper.selectList(Wrappers.<SysIdentity>lambdaQuery()
                .select(SysIdentity::getId, SysIdentity::getIdentityCode)
                .in(SysIdentity::getIdentityCode, List.of(BID_AGENCY_IDENTITY, PURCHASER_IDENTITY))
                .eq(SysIdentity::getIsActive, Boolean.TRUE));
        if (CollectionUtils.isEmpty(sysIdentityList)) {
            return;
        }
        SysIdentity agentOrgIdentity = null;
        SysIdentity purchaserIdentity = null;
        for (SysIdentity sysIdentity : sysIdentityList) {
            if (BID_AGENCY_IDENTITY.name().equals(sysIdentity.getIdentityCode())) {
                agentOrgIdentity = sysIdentity;
            } else {
                purchaserIdentity = sysIdentity;
            }
        }
        // 删除用户关联的招标代理机构身份
        deleteUserAgentOrgIdentity(userIdList, agentOrgIdentity);
        // 删除用户身份的数据权限关联
        deleteUserIdentityDataPermission(userIdList, agentOrgIdentity, purchaserIdentity);
    }

    /**
     * 删除用户身份的数据权限关联
     * 1、删除所有用户的招标代理机构身份的所有类型的数据
     * 2、删除所有用户的采购方身份的 项目 类型数据
     *
     * @param userIdList 用户ID列表
     * @param agentOrgIdentity 招标代理机构身份
     * @param purchaserIdentity 采购方身份
     */
    private void deleteUserIdentityDataPermission(List<Long> userIdList, SysIdentity agentOrgIdentity, SysIdentity purchaserIdentity) {
        dataPermissionRelationMapper.delete(Wrappers.<SysUserIdentityDataPermissionRelation>lambdaQuery()
                .in(SysUserIdentityDataPermissionRelation::getUserId, userIdList)
                .and(wrapper -> wrapper
                        // 条件1：招标代理机构身份的所有数据
                        .eq(SysUserIdentityDataPermissionRelation::getIdentityId, agentOrgIdentity.getId())
                        // 条件2：采购方身份的项目类型数据
                        .or(subWrapper -> subWrapper
                                .eq(SysUserIdentityDataPermissionRelation::getIdentityId, purchaserIdentity.getId())
                                .eq(SysUserIdentityDataPermissionRelation::getTypeCode, PROJECT)
                        )
                ));
    }

    /**
     * 删除用户关联的招标代理机构身份
     *
     * @param userIdList 用户ID列表
     * @param agentOrgIdentity 招标代理机构身份
     */
    private void deleteUserAgentOrgIdentity(List<Long> userIdList, SysIdentity agentOrgIdentity) {
        if (agentOrgIdentity == null) {
            return;
        }
        sysUserMapper.update(Wrappers.<SysUser>lambdaUpdate()
                .set(SysUser::getLockFlag, "9")
                .in(SysUser::getUserId, userIdList));
        userIdentityRoleRelationMapper.delete(Wrappers.<SysUserIdentityRoleRelation>lambdaQuery()
                .eq(SysUserIdentityRoleRelation::getIdentityId, agentOrgIdentity.getId())
                .in(SysUserIdentityRoleRelation::getUserId, userIdList));
    }

    /**
     * 处理机构关联用户（新增机构时使用）
     *
     * @param userList 用户列表
     * @param agentOrgId 机构ID
     * @param tenantId 租户ID
     * @return 处理结果信息
     */
    private String processAgentOrgUsers(List<BaseAgentUserEntity> userList, Long agentOrgId, Long tenantId) {
        // 获取默认配置
        AgentOrgDefaultConfig config = getAgentOrgDefaultConfig();

        StringBuilder result = new StringBuilder();
        List<BaseAgentUserEntity> successUsers = new ArrayList<>();

        for (BaseAgentUserEntity user : userList) {
            user.setAgentOrgId(agentOrgId);

            // 根据登录账号查询是否已有此用户
            SysUser existingUser = sysUserService.lambdaQuery()
                    .eq(SysUser::getUsername, user.getLoginAccount())
                    .one();

            if (existingUser != null) {
                // 关联已有账号并更新用户信息
                user.setUserId(existingUser.getUserId());
                updateAgentUserInfo(existingUser, user);
                log.info("关联已有账号成功，用户名：{}", user.getLoginAccount());
            } else {
                // 创建新账号
                SysUser newUser = createAgentUser(user, tenantId, config);
                user.setUserId(newUser.getUserId());
                log.info("创建新账号成功，用户名：{}", user.getLoginAccount());
            }

            successUsers.add(user);
        }

        // 批量保存成功的用户
        if (!successUsers.isEmpty()) {
            baseAgentUserService.saveBatch(successUsers);
            result.append(String.format("成功处理%d个用户", successUsers.size()));
        }

        return result.toString();
    }

    /**
     * 代理机构默认配置
     */
    private static class AgentOrgDefaultConfig {
        private final SysDept dept;
        private final SysRole role;
        private final SysPost post;

        public AgentOrgDefaultConfig(SysDept dept, SysRole role, SysPost post) {
            this.dept = dept;
            this.role = role;
            this.post = post;
        }

        public SysDept getDept() {
            return dept;
        }

        public SysRole getRole() {
            return role;
        }

        public SysPost getPost() {
            return post;
        }
    }

    /**
     * 获取代理机构默认配置
     *
     * @return 默认配置
     */
    private AgentOrgDefaultConfig getAgentOrgDefaultConfig() {
        SysDept deptByName = defaultDeptExtUtil.getDeptByName(DefaultDeptExtEnum.ORG_DEPT.getValue());
        SysRole roleByCode = defaultDeptExtUtil.getRoleByCode(DefaultDeptExtEnum.ORG_ROLE.name());
        SysPost postByCode = defaultDeptExtUtil.getPostByCode(DefaultDeptExtEnum.EXTERNAL_POST.name());

        ExceptionUtil.checkNonNull(deptByName, "默认外部机构部门未创建");
        ExceptionUtil.checkNonNull(roleByCode, "默认外部机构角色未创建");
        ExceptionUtil.checkNonNull(postByCode, "默认外部机构岗位未创建");

        return new AgentOrgDefaultConfig(deptByName, roleByCode, postByCode);
    }

    /**
     * 处理新增的代理机构用户
     *
     * @param usersToAdd 需要新增的用户列表
     * @param agentOrgId 机构ID
     * @param tenantId 租户ID
     * @param deptByName 默认部门
     * @param roleByCode 默认角色
     * @param postByCode 默认岗位
     */
    private void processNewAgentUsers(List<BaseAgentUserEntity> usersToAdd, Long agentOrgId, Long tenantId,
                                      SysDept deptByName, SysRole roleByCode, SysPost postByCode) {
        AgentOrgDefaultConfig config = new AgentOrgDefaultConfig(deptByName, roleByCode, postByCode);
        List<BaseAgentUserEntity> successUsers = new ArrayList<>();

        for (BaseAgentUserEntity user : usersToAdd) {
            user.setAgentOrgId(agentOrgId);

            // 根据登录账号查询是否已有此用户
            SysUser existingUser = sysUserService.lambdaQuery()
                    .eq(SysUser::getUsername, user.getLoginAccount())
                    .one();

            if (existingUser != null) {
                // 关联已有账号
                user.setUserId(existingUser.getUserId());
                updateAgentUserInfo(existingUser, user);
            } else {
                // 创建新账号
                SysUser newUser = createAgentUser(user, tenantId, config);
                user.setUserId(newUser.getUserId());
            }

            successUsers.add(user);
        }

        if (!successUsers.isEmpty()) {
            baseAgentUserService.saveBatch(successUsers);
        }
    }

    /**
     * 处理需要更新的代理机构用户
     *
     * @param usersToUpdate 需要更新的用户列表
     */
    private void processUpdateAgentUsers(List<BaseAgentUserEntity> usersToUpdate) {
        for (BaseAgentUserEntity user : usersToUpdate) {
            // 同步更新SysUser信息
            if (user.getUserId() != null) {
                SysUser existingUser = sysUserService.getById(user.getUserId());
                if (existingUser != null) {
                    updateAgentUserInfo(existingUser, user);
                }
            }
        }

        baseAgentUserService.saveOrUpdateBatch(usersToUpdate);
    }

    /**
     * 创建代理机构用户账号
     *
     * @param baseAgentUser 代理机构用户信息
     * @param tenantId 租户ID
     * @param config 默认配置
     * @return 创建的用户
     */
    private SysUser createAgentUser(BaseAgentUserEntity baseAgentUser, Long tenantId, AgentOrgDefaultConfig config) {
        // 校验手机号和账号唯一性
        validatePhoneUniqueness(baseAgentUser);

        // 构建用户创建信息
        UserDTO userDTO = buildUserCreateDTO(baseAgentUser, tenantId, config);

        // 保存用户
        boolean saved = sysUserService.saveUserByCode(userDTO);
        if (!saved) {
            throw new RuntimeException("创建用户失败");
        }

        // 查询并返回创建的用户
        return sysUserService.lambdaQuery()
                .eq(SysUser::getUsername, baseAgentUser.getLoginAccount())
                .one();
    }

    /**
     * 更新代理机构用户信息（同步到SysUser）
     *
     * @param existingUser 已有用户
     * @param baseAgentUser 代理机构用户信息
     */
    private void updateAgentUserInfo(SysUser existingUser, BaseAgentUserEntity baseAgentUser) {
        // 检查是否需要更新
        if (!needUpdateUserInfo(existingUser, baseAgentUser)) {
            return;
        }

        // 校验手机号唯一性（排除当前用户）
        validatePhoneUniquenessExcludeUser(baseAgentUser.getContactPhone(), existingUser.getUserId());

        // 构建用户更新信息
        UserDTO userDTO = buildUserUpdateDTO(existingUser, baseAgentUser);

        // 执行更新
        boolean updated = sysUserService.updateUser(userDTO);

        if (!updated) {
            throw new RuntimeException("更新用户信息失败");
        }

        log.info("成功更新代理机构用户信息，用户ID：{}，姓名：{}", existingUser.getUserId(), baseAgentUser.getUserName());
    }

    /**
     * 校验手机号唯一性
     *
     * @param user 用户信息
     */
    private void validatePhoneUniqueness(BaseAgentUserEntity user) {

        Long phoneCount = sysUserService.lambdaQuery()
                .eq(StringUtils.isNotEmpty(user.getContactPhone()), SysUser::getPhone, user.getContactPhone())
                .or()
                .eq(StringUtils.isNotEmpty(user.getLoginAccount()), SysUser::getUsername, user.getLoginAccount())
                .count();
        if (phoneCount > 0) {
            throw new RuntimeException("手机号或账号已被占用");
        }

    }

    /**
     * 校验手机号唯一性（排除指定用户）
     *
     * @param phone 手机号
     * @param excludeUserId 排除的用户ID
     */
    private void validatePhoneUniquenessExcludeUser(String phone, Long excludeUserId) {
        if (StrUtil.isNotBlank(phone)) {
            Long phoneCount = sysUserService.lambdaQuery()
                    .eq(SysUser::getPhone, phone)
                    .ne(SysUser::getUserId, excludeUserId)
                    .count();
            if (phoneCount > 0) {
                throw new RuntimeException("手机号已被其他用户使用：" + phone);
            }
        }
    }

    /**
     * 检查是否需要更新用户信息
     *
     * @param existingUser 已有用户
     * @param baseAgentUser 代理机构用户信息
     * @return 是否需要更新
     */
    private boolean needUpdateUserInfo(SysUser existingUser, BaseAgentUserEntity baseAgentUser) {
        return (StrUtil.isNotBlank(baseAgentUser.getContactPhone()) &&
                !Objects.equals(existingUser.getPhone(), baseAgentUser.getContactPhone())) ||
                (StrUtil.isNotBlank(baseAgentUser.getUserName()) &&
                        !Objects.equals(existingUser.getName(), baseAgentUser.getUserName()));
    }

    /**
     * 构建用户创建DTO
     *
     * @param baseAgentUser 代理机构用户信息
     * @param tenantId 租户ID
     * @param config 默认配置
     * @return UserDTO
     */
    private UserDTO buildUserCreateDTO(BaseAgentUserEntity baseAgentUser, Long tenantId, AgentOrgDefaultConfig config) {
        UserDTO userDTO = new UserDTO();
        userDTO.setUsername(baseAgentUser.getLoginAccount());
        userDTO.setPassword(StrUtil.isNotBlank(baseAgentUser.getPassword()) ? baseAgentUser.getPassword() : "123456");
        userDTO.setNickname(baseAgentUser.getUserName());
        userDTO.setName(baseAgentUser.getUserName());
        userDTO.setPhone(baseAgentUser.getContactPhone());
        userDTO.setLockFlag("0");
        userDTO.setDeptId(baseAgentUser.getDeptTypeId());
//        userDTO.setRole(List.of(config.getRole().getRoleId()));
        userDTO.setPost(List.of(config.getPost().getPostId()));
        userDTO.setTenantId(tenantId);
        userDTO.setTenantIds(List.of(tenantId));

        UserIdentityRoleCodeBindDTO identityRoleCodeBindDTO = new UserIdentityRoleCodeBindDTO();
        identityRoleCodeBindDTO.setIdentityCode(BID_AGENCY_IDENTITY.name());
        identityRoleCodeBindDTO.setRoleCodeList(List.of(DefaultRoleEnum.Role.BID_AGENCY_ROLE.name()));
        List<UserIdentityRoleCodeBindDTO> bindRoles = List.of(identityRoleCodeBindDTO);

        userDTO.setIdentityRoleCodeBindList(bindRoles);
        return userDTO;
    }

    /**
     * 构建用户更新DTO
     *
     * @param existingUser 已有用户
     * @param baseAgentUser 代理机构用户信息
     * @return UserDTO
     */
    private UserDTO buildUserUpdateDTO(SysUser existingUser, BaseAgentUserEntity baseAgentUser) {
        UserDTO userDTO = new UserDTO();
        userDTO.setUserId(existingUser.getUserId());
        userDTO.setUsername(existingUser.getUsername()); // 登录账号不允许修改
        userDTO.setName(baseAgentUser.getUserName());
        userDTO.setPhone(baseAgentUser.getContactPhone());
        userDTO.setNickname(baseAgentUser.getUserName());
        userDTO.setEmail(existingUser.getEmail());
        userDTO.setLockFlag(existingUser.getLockFlag());
        userDTO.setDeptId(existingUser.getDeptId());
        userDTO.setTenantId(existingUser.getTenantId());
        userDTO.setTenantIds(List.of(existingUser.getTenantId()));

//        userDTO.setRole(List.of()); // 保持原有角色
//        userDTO.setPost(List.of()); // 保持原有岗位
        return userDTO;
    }
}