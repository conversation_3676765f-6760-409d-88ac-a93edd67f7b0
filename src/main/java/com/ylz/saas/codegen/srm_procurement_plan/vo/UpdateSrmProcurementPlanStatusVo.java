package com.ylz.saas.codegen.srm_procurement_plan.vo;

import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.codegen.srm_procurement_plan.service.SrmProcurementPlanService;
import com.ylz.saas.codegen.srm_procurement_plan_detail.entity.SrmProcurementPlanDetailEntity;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 采购计划表
 *
 * <AUTHOR>
 * @date 2025-06-03 20:39:40
 */
@Data
public class UpdateSrmProcurementPlanStatusVo implements Serializable {

    /**
  	 * 主键ID
   	 */
    @NotBlank(message = "ID不能为空")
    private Long id;

    /**
  	 * 单据状态
   	 */
    @NotBlank(message = "单据状态不能为空")
    private SrmProcurementPlanService.DocumentStatus status;


    /**
  	 * 修改人名称
   	 */
    private String updateByName;

    /**
  	 * 修改人
   	 */
    private String updateBy;

    /**
  	 * 修改时间
   	 */
    private LocalDateTime updateTime;

    /**
  	 * 更新人ID
   	 */
    private Long updateById;
}