package com.ylz.saas.codegen.srm_procurement_decision.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 决策（申请）类型
 *
 * <AUTHOR>
 * @date 2025/8/5 16:05
 * @since v0.0.1
 */
@Getter
@AllArgsConstructor
public enum DecisionType {
    INSUFFICIENT_COMPETITORS("不足3家竞争条件开标"),
    DIRECT_ASSIGNMENT("直接委托"),
    UNMET_BIDDING_RATIO("定标数量与竞争数量不满足2:1"),
    WINNER_ADJUSTMENT("定标调整成交人数量"),
    LOT_DIVISION("标段标包划分"),
    NON_SEQUENTIAL_WINNER("非顺序中标人"),
    ANNUAL_PROCUREMENT_PLAN("年度采购招标规划与策略"),
    OTHER_CASE("其他特殊事项");

    private final String desc;

    private static final Map<String, DecisionType> CACHE_MAP = EnumSet.allOf(DecisionType.class)
            .stream()
            .collect(Collectors.toMap(DecisionType::name, Function.identity()));

    public static String getDecisionTypeByCode(String value) {
        return CACHE_MAP.getOrDefault(value.toUpperCase(), DecisionType.OTHER_CASE).getDesc();
    }
}