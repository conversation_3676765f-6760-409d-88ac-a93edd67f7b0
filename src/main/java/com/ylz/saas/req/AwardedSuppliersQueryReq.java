package com.ylz.saas.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 中标供应商查询请求参数
 */
@Data
public class AwardedSuppliersQueryReq {
    
    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;
    
    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;
    
    /**
     * 供应商名称（查询条件）
     */
    private String supplierName;
}
