package com.ylz.saas.codegen.srm_procurement_plan_detail.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.codegen.srm_procurement_plan_detail.entity.SrmProcurementPlanDetailEntity;
import com.ylz.saas.codegen.srm_procurement_plan_detail.param.SrmProcurementPlanDetailParam;
import com.ylz.saas.codegen.srm_procurement_plan_detail.param.SrmProcurementPlanDetailQueryParam;
import com.ylz.saas.codegen.srm_procurement_plan_detail.service.SrmProcurementPlanDetailService;
import com.ylz.saas.codegen.srm_procurement_plan_detail.vo.SrmProcurementPlanDetailVo;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 采购计划明细表
 *
 * <AUTHOR>
 * @date 2025-06-03 21:00:11
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/srmProcurementPlanDetail" )
@Tag(description = "srmProcurementPlanDetail" , name = "采购计划明细表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class SrmProcurementPlanDetailController {

    private final  SrmProcurementPlanDetailService srmProcurementPlanDetailService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param param 采购计划明细表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @PostMapping("/page" )
    public R getSrmProcurementPlanDetailPage(@RequestBody SrmProcurementPlanDetailParam param, Page page) {
        QueryWrapper<SrmProcurementPlanDetailEntity> queryWrapper = param.toWrapper(); 
        return R.ok(srmProcurementPlanDetailService.page(page, queryWrapper));
    }

    /**
     * 通过id查询采购计划明细表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(srmProcurementPlanDetailService.getByIdSrmProcurementPlanDetail(id));
    }

    /**
     * 新增采购计划明细表
     * @param srmProcurementPlanDetailVo 采购计划明细表
     * @return R
     */
    @Operation(summary = "新增采购计划明细表" , description = "新增采购计划明细表" )
    @SysLog("新增采购计划明细表" )
    @PostMapping("/save")
    public R save(@Valid @RequestBody SrmProcurementPlanDetailVo srmProcurementPlanDetailVo) {
        return R.ok(srmProcurementPlanDetailService.saveSrmProcurementPlanDetail(srmProcurementPlanDetailVo));
    }

    /**
     * 修改采购计划明细表
     * @param srmProcurementPlanDetailVo 采购计划明细表
     * @return R
     */
    @Operation(summary = "修改采购计划明细表" , description = "修改采购计划明细表" )
    @SysLog("修改采购计划明细表" )
    @PostMapping("/update")
    public R updateById(@Valid @RequestBody SrmProcurementPlanDetailVo srmProcurementPlanDetailVo) {
        return R.ok(srmProcurementPlanDetailService.updateSrmProcurementPlanDetail(srmProcurementPlanDetailVo));
    }

    /**
     * 通过id删除采购计划明细表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除采购计划明细表" , description = "通过id删除采购计划明细表" )
    @SysLog("通过id删除采购计划明细表" )
    @GetMapping("remove")
    public R removeById(@RequestParam List<Long> ids) {
		boolean remove = srmProcurementPlanDetailService.removeByIdsSrmProcurementPlanDetail(ids);
		return R.ok(remove);
    }


    /**
     * 通过planIds批量获取采购计划明细表
     */
    @PostMapping("/getBatchByPlanIds")
    public R<List<SrmProcurementPlanDetailEntity>> getBatchByPlanIds(@RequestBody @Validated SrmProcurementPlanDetailQueryParam param) {
    	List<SrmProcurementPlanDetailEntity> list = srmProcurementPlanDetailService.getBatchByPlanIds(param);
    	return R.ok(list);
    }

}