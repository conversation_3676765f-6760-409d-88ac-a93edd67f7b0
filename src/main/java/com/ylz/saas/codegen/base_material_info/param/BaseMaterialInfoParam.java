package com.ylz.saas.codegen.base_material_info.param;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ylz.saas.codegen.base_material_info.entity.BaseMaterialInfoEntity;
import com.ylz.saas.common.data.mybatis.helper.Operator;
import com.ylz.saas.common.data.mybatis.helper.QueryWrapperHelper;
import com.ylz.saas.common.data.mybatis.wrapper.CriteriaField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * mybatis-plus的查询帮助类
 * 
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseMaterialInfoParam {

	/**
  	 * 主键ID
   	 */
 	@CriteriaField(field = "id", operator = Operator.LIKE)
    private Long id;
	/**
  	 * 创建人ID
   	 */
 	@CriteriaField(field = "createById", operator = Operator.EQ)
    private Long createById;
	/**
  	 * 组织ID
   	 */
 	@CriteriaField(field = "deptId", operator = Operator.IN)
    private List<String> deptId;
	/**
  	 * 更新人ID
   	 */
 	@CriteriaField(field = "updateById", operator = Operator.LIKE)
    private Long updateById;
	/**
  	 * 物料编码
   	 */
 	@CriteriaField(field = "materialCode", operator = Operator.LIKE)
    private String materialCode;
	/**
  	 * 物料名称（支持拼音搜索）
   	 */
    private String materialName;
	/**
  	 * 物料简称
   	 */
 	@CriteriaField(field = "materialSimpleName", operator = Operator.LIKE)
    private String materialSimpleName;
	/**
  	 * 规格/型号
   	 */
 	@CriteriaField(field = "spec", operator = Operator.LIKE)
    private String spec;
	/**
  	 * 物料所属分类ID（支持层级查询）
   	 */
    private Long materialCategoryId;

    /**
     * 物料所属分类ID列表（内部使用，用于层级查询）
     */
    @CriteriaField(field = "materialCategoryId", operator = Operator.IN)
    private List<Long> materialCategoryIds;

    /**
     * 物料分类名称（用于按分类名称查询）
     */
    private String materialCategoryName;
	/**
  	 * 计量单位
   	 */
 	@CriteriaField(field = "unit", operator = Operator.LIKE)
    private String unit;
	/**
  	 * 来源标识
   	 */
 	@CriteriaField(field = "dataSource", operator = Operator.LIKE)
    private String dataSource;
	/**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
 	@CriteriaField(field = "status", operator = Operator.IN)
    private List<String> statusList;
	/**
  	 * 备注
   	 */
 	@CriteriaField(field = "remark", operator = Operator.LIKE)
    private String remark;
	/**
  	 * 创建人名称
   	 */
 	@CriteriaField(field = "createByName", operator = Operator.LIKE)
    private String createByName;
	/**
  	 * 修改人名称
   	 */
 	@CriteriaField(field = "updateByName", operator = Operator.LIKE)
    private String updateByName;
	/**
  	 * 创建人
   	 */
 	@CriteriaField(field = "createBy", operator = Operator.LIKE)
    private String createBy;
	/**
  	 * 创建时间_开始
   	 */
	@CriteriaField(field = "createTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime createTimeStart;
	/**
  	 * 创建时间_结束
   	 */
    @CriteriaField(field = "createTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime createTimeEnd;
	/**
  	 * 修改人
   	 */
 	@CriteriaField(field = "updateBy", operator = Operator.LIKE)
    private String updateBy;
	/**
  	 * 修改时间_开始
   	 */
	@CriteriaField(field = "updateTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime updateTimeStart;
	/**
  	 * 修改时间_结束
   	 */
    @CriteriaField(field = "updateTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime updateTimeEnd;
	/**
  	 * 删除标识（0-正常、1-删除）
   	 */
	@CriteriaField(field = "delFlag", operator = Operator.EQ)
	private int delFlag;
	/**
  	 * 租户ID
   	 */
 	@CriteriaField(field = "tenantId", operator = Operator.LIKE)
    private Long tenantId;

    /**
    * 构建查询
    */
    public QueryWrapper<BaseMaterialInfoEntity> toWrapper() {
        QueryWrapper<BaseMaterialInfoEntity> queryWrapper = QueryWrapperHelper.fromBean(this, BaseMaterialInfoEntity.class)
            .orderByDesc("id");
       return queryWrapper;
    }
}