package com.ylz.saas.codegen.base_expert_education.param;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ylz.saas.codegen.base_expert_education.entity.BaseExpertEducationEntity;
import com.ylz.saas.common.data.mybatis.helper.Operator;
import com.ylz.saas.common.data.mybatis.helper.QueryWrapperHelper;
import com.ylz.saas.common.data.mybatis.wrapper.CriteriaField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * mybatis-plus的查询帮助类
 * 
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseExpertEducationParam {

	/**
  	 * 主键ID
   	 */
 	@CriteriaField(field = "id", operator = Operator.LIKE)
    private Long id;
	/**
  	 * 组织ID
   	 */
 	@CriteriaField(field = "deptId", operator = Operator.LIKE)
    private Long deptId;
	/**
  	 * 专家ID
   	 */
 	@CriteriaField(field = "expertId", operator = Operator.LIKE)
    private Long expertId;
	/**
  	 * 开始时间_开始
   	 */
	@CriteriaField(field = "startDate", operator = Operator.GE, filterBlank = true)
	private LocalDate startDateStart;
	/**
  	 * 开始时间_结束
   	 */
    @CriteriaField(field = "startDate", operator = Operator.LE, filterBlank = true)
	private LocalDate startDateEnd;
	/**
  	 * 截止时间_开始
   	 */
	@CriteriaField(field = "endDate", operator = Operator.GE, filterBlank = true)
	private LocalDate endDateStart;
	/**
  	 * 截止时间_结束
   	 */
    @CriteriaField(field = "endDate", operator = Operator.LE, filterBlank = true)
	private LocalDate endDateEnd;
	/**
  	 * 毕业院校
   	 */
 	@CriteriaField(field = "school", operator = Operator.LIKE)
    private String school;
	/**
  	 * 专业
   	 */
 	@CriteriaField(field = "major", operator = Operator.LIKE)
    private String major;
	/**
  	 * 学位
   	 */
 	@CriteriaField(field = "degree", operator = Operator.LIKE)
    private String degree;
	/**
  	 * 创建人名称
   	 */
 	@CriteriaField(field = "createByName", operator = Operator.LIKE)
    private String createByName;
	/**
  	 * 修改人名称
   	 */
 	@CriteriaField(field = "updateByName", operator = Operator.LIKE)
    private String updateByName;
	/**
  	 * 创建人
   	 */
 	@CriteriaField(field = "createBy", operator = Operator.LIKE)
    private String createBy;
	/**
  	 * 创建时间_开始
   	 */
	@CriteriaField(field = "createTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime createTimeStart;
	/**
  	 * 创建时间_结束
   	 */
    @CriteriaField(field = "createTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime createTimeEnd;
	/**
  	 * 修改人
   	 */
 	@CriteriaField(field = "updateBy", operator = Operator.LIKE)
    private String updateBy;
	/**
  	 * 修改时间_开始
   	 */
	@CriteriaField(field = "updateTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime updateTimeStart;
	/**
  	 * 修改时间_结束
   	 */
    @CriteriaField(field = "updateTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime updateTimeEnd;
	/**
  	 * 租户ID
   	 */
 	@CriteriaField(field = "tenantId", operator = Operator.LIKE)
    private Long tenantId;
	/**
  	 * 创建人ID
   	 */
 	@CriteriaField(field = "createById", operator = Operator.LIKE)
    private Long createById;
	/**
  	 * 更新人ID
   	 */
 	@CriteriaField(field = "updateById", operator = Operator.LIKE)
    private Long updateById;

    /**
    * 构建查询
    */
    public QueryWrapper<BaseExpertEducationEntity> toWrapper() {
        QueryWrapper<BaseExpertEducationEntity> queryWrapper = QueryWrapperHelper.fromBean(this, BaseExpertEducationEntity.class)
            .orderByDesc("id");
       return queryWrapper;
    }
}