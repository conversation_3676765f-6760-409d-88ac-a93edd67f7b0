package com.ylz.saas.codegen.base_expert.param;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ylz.saas.codegen.base_expert.entity.BaseExpertEntity;
import com.ylz.saas.common.data.mybatis.helper.Operator;
import com.ylz.saas.common.data.mybatis.helper.QueryWrapperHelper;
import com.ylz.saas.common.data.mybatis.wrapper.CriteriaField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * mybatis-plus的查询帮助类
 * 
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseExpertParam {

	/**
  	 * 主键ID
   	 */
 	@CriteriaField(field = "id", operator = Operator.LIKE)
    private Long id;
	/**
  	 * 组织ID
   	 */
 	@CriteriaField(field = "deptId", operator = Operator.LIKE)
    private Long deptId;
	/**
  	 * 专家编码
   	 */
 	@CriteriaField(field = "expertCode", operator = Operator.LIKE)
    private String expertCode;
	/**
  	 * 姓名
   	 */
 	@CriteriaField(field = "name", operator = Operator.LIKE)
    private String name;
	/**
  	 * 手机号
   	 */
 	@CriteriaField(field = "phone", operator = Operator.LIKE)
    private String phone;
	/**
  	 * 专家分类
   	 */
 	@CriteriaField(field = "expertCategory", operator = Operator.IN)
    private List<String> expertCategoryList;
	/**
  	 * 登录账号
   	 */
 	@CriteriaField(field = "loginAccount", operator = Operator.LIKE)
    private String loginAccount;
	/**
  	 * 所在地
   	 */
 	@CriteriaField(field = "address", operator = Operator.LIKE)
    private String address;
	/**
  	 * 身份证号码
   	 */
 	@CriteriaField(field = "idNumber", operator = Operator.LIKE)
    private String idNumber;
	/**
  	 * 专家类型
   	 */
 	@CriteriaField(field = "expertType", operator = Operator.LIKE)
    private String expertType;
	/**
  	 * 关联用户ID
   	 */
 	@CriteriaField(field = "userId", operator = Operator.LIKE)
    private Long userId;
	/**
  	 * 邮箱
   	 */
 	@CriteriaField(field = "email", operator = Operator.LIKE)
    private String email;
	/**
  	 * 状态
   	 */
 	@CriteriaField(field = "status", operator = Operator.IN)
    private List<String> statusList;
	/**
  	 * 创建人名称
   	 */
 	@CriteriaField(field = "createByName", operator = Operator.LIKE)
    private String createByName;
	/**
  	 * 修改人名称
   	 */
 	@CriteriaField(field = "updateByName", operator = Operator.LIKE)
    private String updateByName;
	/**
  	 * 创建人
   	 */
 	@CriteriaField(field = "createBy", operator = Operator.LIKE)
    private String createBy;
	/**
  	 * 创建时间_开始
   	 */
	@CriteriaField(field = "createTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime createTimeStart;
	/**
  	 * 创建时间_结束
   	 */
    @CriteriaField(field = "createTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime createTimeEnd;
	/**
  	 * 修改人
   	 */
 	@CriteriaField(field = "updateBy", operator = Operator.LIKE)
    private String updateBy;
	/**
  	 * 修改时间_开始
   	 */
	@CriteriaField(field = "updateTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime updateTimeStart;
	/**
  	 * 修改时间_结束
   	 */
    @CriteriaField(field = "updateTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime updateTimeEnd;
	/**
  	 * 删除标识（0-正常、1-删除）
   	 */
	@CriteriaField(field = "delFlag", operator = Operator.EQ)
	private int delFlag;
	/**
  	 * 租户ID
   	 */
 	@CriteriaField(field = "tenantId", operator = Operator.LIKE)
    private Long tenantId;
	/**
  	 * 创建人ID
   	 */
 	@CriteriaField(field = "createById", operator = Operator.LIKE)
    private Long createById;
	/**
  	 * 更新人ID
   	 */
 	@CriteriaField(field = "updateById", operator = Operator.LIKE)
    private Long updateById;

    /**
    * 构建查询
    */
    public QueryWrapper<BaseExpertEntity> toWrapper() {
        QueryWrapper<BaseExpertEntity> queryWrapper = QueryWrapperHelper.fromBean(this, BaseExpertEntity.class)
            .orderByDesc("id");
       return queryWrapper;
    }
}