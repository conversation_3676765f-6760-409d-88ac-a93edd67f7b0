<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmProcurementProjectMapper">



    <!-- 供应商分页查询（禁用数据权限控制） -->
    <select id="selectPageForSupplier" resultType="com.ylz.saas.entity.SrmProcurementProject">
        SELECT
           *
        FROM srm_procurement_project
        ${ew.customSqlSegment}
    </select>

    <!-- 小程序分页查询（关联多表查询） -->
    <select id="pageForApplet" resultType="com.ylz.saas.entity.SrmProcurementProject">
        SELECT DISTINCT
            pp.id,
            pp.tenant_id,
            pp.dept_id,
            pp.purchase_dept_id,
            pp.buyer_dept_id,
            pp.project_code,
            pp.project_name,
            pp.service_type_id,
            pp.sourcing_type,
            pp.sourcing_method,
            pp.invite_method,
            pp.invite_receipt,
            pp.province,
            pp.city,
            pp.district,
            pp.address,
            pp.budget_amount,
            pp.purchase_reason,
            pp.supplier_requirement,
            pp.project_desc,
            pp.agency_org_id,
            pp.agency_org_name,
            pp.multi_section,
            pp.pre_qualification,
            pp.register_start_time,
            pp.register_end_time,
            pp.bid_open_time,
            pp.bid_end_time,
            pp.progress_status,
            pp.status,
            pp.del_flag,
            pp.create_by_id,
            pp.create_by,
            pp.create_by_name,
            pp.create_time,
            pp.update_by_id,
            pp.update_by,
            pp.update_by_name,
            pp.update_time,
            -- 从物料明细表获取的字段
            ppi.material_code,
            ppi.material_name,
            ppi.unit,
            ppi.required_quantity,
            ppi.usage_location_id,
            -- 从使用地点表获取的字段
            bul.location_name AS usageLocationName,
            CONCAT(bul.province, '/', bul.city, '/', bul.district) AS usageLocationRegion,
            bul.address AS usageLocationAddress,
            -- 从招标公告表获取的字段
            tn.notice_title AS demandName
        FROM srm_procurement_project pp
        LEFT JOIN srm_procurement_project_item ppi ON pp.id = ppi.project_id AND ppi.del_flag = 0
        LEFT JOIN base_usage_location bul ON ppi.usage_location_id = bul.id AND bul.del_flag = 0
        LEFT JOIN srm_tender_notice tn ON pp.id = tn.project_id AND tn.del_flag = 0
        WHERE pp.del_flag = 0
            <if test="req.searchContent != null and req.searchContent != ''">
                AND (
                    pp.project_code LIKE CONCAT('%', #{req.searchContent}, '%')
                    OR ppi.material_name LIKE CONCAT('%', #{req.searchContent}, '%')
                    OR tn.notice_title LIKE CONCAT('%', #{req.searchContent}, '%')
                    OR bul.location_name LIKE CONCAT('%', #{req.searchContent}, '%')
                )
            </if>
            <if test="req.progressStatus != null and req.progressStatus.size() > 0">
                AND pp.progress_status IN
                <foreach collection="req.progressStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="req.approveStatus != null and req.approveStatus.size() > 0">
                AND pp.status IN
                <foreach collection="req.approveStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
        ORDER BY pp.create_time DESC
    </select>
</mapper>
