package com.ylz.saas.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 中标通知查询入参
 */
@Data
public class AwardedNoticeDetailReq {
    
    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 标段id
     */
    @NotNull(message = "标段ID不能为空")
    private Long sectionId;

    /**
     * 租户供应商ID
     */
    @NotNull(message = "租户供应商id不能为空")
    private Long tenantSupplierId;
}
