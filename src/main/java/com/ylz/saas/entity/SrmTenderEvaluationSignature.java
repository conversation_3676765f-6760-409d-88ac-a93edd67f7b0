package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.ylz.saas.common.core.util.TenantTable;
import com.ylz.saas.enums.SignatureStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 评标汇总签名进度表
 * @TableName srm_tender_evaluation_signature
 */
@TableName(value = "srm_tender_evaluation_signature")
@Data
@TenantTable
@Schema(description = "评标汇总签名进度表")
public class SrmTenderEvaluationSignature implements Serializable {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "部门ID")
    private Long deptId;

    /**
     * 采购立项ID
     */
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 标段ID
     */
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 评标委员会ID
     */
    @Schema(description = "评标委员会ID")
    private Long evaluationId;

    /**
     * 评标人员用户ID
     */
    @Schema(description = "评标人员用户ID")
    private Long userId;

    /**
     * 评标人员姓名
     */
    @Schema(description = "评标人员姓名")
    private String userName;

    /**
     * 签名状态
     */
    @Schema(description = "签名状态")
    private SignatureStatusEnum signatureStatus;

    /**
     * 签名时间
     */
    @Schema(description = "签名时间")
    private LocalDateTime signatureTime;

    /**
     * 签名意见
     */
    @Schema(description = "签名意见")
    private String signatureComment;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "删除标识（0-正常、1-删除）")
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人名称")
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人")
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改人名称")
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}
