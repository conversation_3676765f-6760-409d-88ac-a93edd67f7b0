<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmRecruitSupplierChangeMapper">

    <!-- 分页查询变更记录 -->
    <select id="selectChangePage" resultType="com.ylz.saas.resp.SrmRecruitSupplierChangePageResp">
        SELECT
            sc.id,
            sc.recruit_id,
            sc.recruit_code,
            srs.recruit_title,
            sc.change_by_id,
            sc.change_by_name,
            sc.change_time,
            sc.approval_status,

            sc.change_remark
        FROM srm_recruit_supplier_change sc
        LEFT JOIN srm_recruit_supplier srs ON sc.recruit_id = srs.id
        <where>
            sc.del_flag = 0
            <if test="req.recruitId != null">
                AND sc.recruit_id = #{req.recruitId}
            </if>
            <if test="req.recruitCode != null and req.recruitCode != ''">
                AND sc.recruit_code LIKE CONCAT('%', #{req.recruitCode}, '%')
            </if>
            <if test="req.recruitTitle != null and req.recruitTitle != ''">
                AND srs.recruit_title LIKE CONCAT('%', #{req.recruitTitle}, '%')
            </if>
            <if test="req.approvalStatus != null">
                AND sc.approval_status = #{req.approvalStatus}
            </if>
            <if test="req.changeByName != null and req.changeByName != ''">
                AND sc.change_by_name LIKE CONCAT('%', #{req.changeByName}, '%')
            </if>
            <if test="req.changeTimeStart != null">
                AND sc.change_time >= #{req.changeTimeStart}
            </if>
            <if test="req.changeTimeEnd != null">
                AND sc.change_time &lt;= #{req.changeTimeEnd}
            </if>
        </where>
        ORDER BY sc.change_time DESC
    </select>

</mapper>
