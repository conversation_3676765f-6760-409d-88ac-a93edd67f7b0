package com.ylz.saas.codegen.base_material_category.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 物料分类表
 *
 * <AUTHOR>
 * @date 2025-06-03 17:25:41
 */
@Data
public class BaseMaterialCategoryReq implements Serializable {



//    /**
//  	 * 组织ID
//   	 */
//	@ExcelProperty(value = "组织ID")
//    private Long deptId;



    /**
  	 * 物料分类编码
   	 */
	@ExcelProperty(value = "物料分类编码")
    private String categoryCode;


    /**
  	 * 物料分类名称
   	 */
	@ExcelProperty(value = "物料分类名称")
    private String categoryName;


    /**
  	 * 上级物料分类编码
   	 */
	@ExcelProperty(value = "上级物料分类编码")
    private String parentCategoryCode;


    /**
  	 * 上级物料分类名称
   	 */
	@ExcelProperty(value = "上级物料分类名称")
    private String parentCategoryName;


//    /**
//  	 * 来源标识
//   	 */
//	@ExcelProperty(value = "来源标识")
//    private String dataSource;


//    /**
//  	 * 状态（DISABLED-禁用、ENABLED-启用）
//   	 */
//	@ExcelProperty(value = "状态（DISABLED-禁用、ENABLED-启用）")
//    private String status;









}