package com.ylz.saas.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.entity.SrmTenderFailedLog;
import com.ylz.saas.entity.SrmTenderOpen;
import com.ylz.saas.req.*;
import com.ylz.saas.resp.BidOpenConfigResp;
import com.ylz.saas.resp.SrmOpenTenderMaterialResp;
import com.ylz.saas.resp.SrmOpenTenderSupplierResp;
import com.ylz.saas.vo.SectionQuoteRoundVo;

import java.util.HashSet;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tender_open(开标表)】的数据库操作Service
* @createDate 2025-06-18 11:29:38
*/
public interface SrmTenderOpenService extends IService<SrmTenderOpen> {

    /**
     * 结束开标
     * @param req
     */
    void endOpenBid(SrmTenderOpenReq req);

    /**
     * 开标
     * @param req
     */
    void openBid(SrmTenderOpenReq req);

    /**
     * 新增开标信息
     *
     * @param req
     */
    void addTenderOpen(SrmTenderOpenAddReq req);

    /**
     * 添加开标异常处理信息
     *
     * @param req
     */
    void failedTender(SrmTenderFailedLogReq req);


    List<SectionQuoteRoundVo> getSectionQuoteRound(Long noticeId);

    /**
     * 获取开标记录信息
     * @param req
     * @return
     */
    List<SrmOpenTenderMaterialResp> openTenderInfoMaterial(SrmTenderOpenInfoReq req);

    /**
     *  获取开标供应商信息
     * @param req
     */
    List<SrmOpenTenderSupplierResp> openTenderInfoSupplier(SrmTenderOpenInfoReq req);

    SrmTenderFailedLog failedTenderDetail(Long id);

    SrmTenderOpen getQuoteAgainBidOpenInfo(QuoteAgainBidOpenInfoQueryReq req);

    void bidOpenConfig(BidOpenConfigReq req);

    void handlerProjectUserIdentityDataPermission(Long noticeId, Long bidOpenUserId, HashSet<Long> newUserIds, SrmProcurementProject project);

    BidOpenConfigResp getBidOpenConfig(Long noticeId);

}
