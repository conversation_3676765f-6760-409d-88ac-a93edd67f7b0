package com.ylz.saas.codegen.base_expert_work_experience.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 专家职业履历表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:42:08
 */
@Data
public class BaseExpertWorkExperienceResp implements Serializable {

    /**
  	 * 主键ID
   	 */
	@ExcelProperty(value = "主键ID")
    private Long id;


    /**
  	 * 附件
   	 */
	@ExcelProperty(value = "附件")
    private String attachment;


    /**
  	 * 开始时间
   	 */
	@ExcelProperty(value = "开始时间")
    private LocalDate startDate;

    /**
  	 * 截止时间
   	 */
	@ExcelProperty(value = "截止时间")
    private LocalDate endDate;

    /**
  	 * 工作单位
   	 */
	@ExcelProperty(value = "工作单位")
    private String company;

    /**
  	 * 职位
   	 */
	@ExcelProperty(value = "职位")
    private String position;

    /**
  	 * 备注
   	 */
	@ExcelProperty(value = "备注")
    private String remarks;









}