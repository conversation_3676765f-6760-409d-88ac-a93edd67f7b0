package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ylz.saas.common.core.util.TenantTable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 租户供应商部门关联表
 * @TableName srm_tenant_supplier_dept
 */
@TableName(value = "srm_tenant_supplier_dept")
@Data
@EqualsAndHashCode(callSuper = true)
@TenantTable
public class SrmTenantSupplierDept extends Model<SrmTenantSupplierDept> {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 租户供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 部门名称（冗余字段，便于查询）
     */
    @TableField(exist = false)
    private String deptName;

    /**
     * 关联类型(CREATE-创建关联、MANAGE-管理关联、INVITE-邀请关联)
     */
    private String relationType;

    /**
     * 审核状态
     */
    private String approvalStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createById;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateById;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新人名称
     */
    private String updateByName;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
