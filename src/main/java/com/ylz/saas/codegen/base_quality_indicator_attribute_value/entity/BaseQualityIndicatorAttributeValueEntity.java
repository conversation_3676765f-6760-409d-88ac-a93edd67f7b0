package com.ylz.saas.codegen.base_quality_indicator_attribute_value.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ylz.saas.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;


/**
 * 质量指标属性值表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:00
 */
@Data
@TableName("base_quality_indicator_attribute_value")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "质量指标属性值表")
@TenantTable
public class BaseQualityIndicatorAttributeValueEntity extends Model<BaseQualityIndicatorAttributeValueEntity> {


    /**
  	 * 主键ID
   	 */
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

    /**
  	 * 组织ID
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="组织ID")
    private Long deptId;

    /**
  	 * 质量指标ID
   	 */
    @Schema(description="质量指标ID")
    private Long qualityIndicatorId;

    /**
  	 * 质量属性ID
   	 */
    @Schema(description="质量属性ID")
    private String attributeId;

    /**
  	 * 质量属性值ID
   	 */
    @Schema(description="质量属性值ID")
    private String attributeValueId;

    /**
  	 * 属性值内容
   	 */
    @Size(max = 200, message = "属性值内容不能超过200字符")
    @Schema(description="属性值内容")
    private String valueContent;

    /**
  	 * 拒收标准
   	 */
    @Size(max = 200, message = "拒收标准不能超过200字符")
    @Schema(description="拒收标准")
    private String rejectionStandard;

    /**
  	 * 扣款标准
   	 */
    @Size(max = 200, message = "扣款标准不能超过200字符")
    @Schema(description="扣款标准")
    private String penaltyStandard;

    /**
  	 * 排序
   	 */
    @Schema(description="排序")
    private Integer sortOrder;

    /**
  	 * 创建人名称
   	 */
    @Schema(description="创建人名称")
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    @Schema(description="修改人名称")
    private String updateByName;

    /**
  	 * 创建人
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
  	 * 创建时间
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

    /**
  	 * 修改时间
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

    /**
  	 * 租户ID
   	 */
    @Schema(description="租户ID")
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人ID")
    private Long createById;

    /**
  	 * 更新人ID
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人ID")
    private Long updateById;
}