package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ylz.saas.common.core.util.TenantTable;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.ChangeTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 招标公告变更记录表
 * @TableName srm_tender_notice_change
 */
@TenantTable
@TableName(value ="srm_tender_notice_change")
@Data
public class SrmTenderNoticeChange {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 业务id
     */
    private Long bizId;

    /**
     * 招标公告ID
     */
    private Long noticeId;

    /**
     * 采购立项ID
     */
    private Long projectId;

    /**
     * 变更类型
     */
    private ChangeTypeEnum changeType;

    /**
     * 变更摘要
     */
    private String changeDigest;

    /**
     * 变更内容
     */
    @Deprecated
    private String changeContent;

    /**
     * web端入参信息
     */
    private String webReq;

    /**
     * 变更前参数JSON
     */
    private String oldParams;

    /**
     * 变更后参数JSON
     */
    private String newParams;

    /**
     * 处理时间
     */
    private LocalDateTime changeTime;

    /**
     * 处理人
     */
    private String changeBy;

    /**
     * 处理人名称
     */
    private String changeByName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 变更审核状态
     * TO_APPROVE("待审批"),
     *     APPROVING("审批中"),
     *     APPROVE("审批通过"),
     *     APPROVE_REJECT("审批驳回"),
     *     APPROVE_REVOKE("审批撤销"),
     */
    private ApproveStatusEnum approvedStatus;

    /**
     * 审核备注
     */
    private String approvedRemark;

    /**
     * 删除标识（0-正常、1-删除）
     */
    private Integer delFlag;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createById;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    @TableField(fill = FieldFill.UPDATE)
    private Long updateById;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}