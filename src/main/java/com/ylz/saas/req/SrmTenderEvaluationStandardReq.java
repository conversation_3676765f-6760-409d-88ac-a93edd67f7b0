package com.ylz.saas.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 评标标准请求类
 */
@Data
@Schema(description = "评标标准请求")
public class SrmTenderEvaluationStandardReq implements Serializable {

    /**
     * 主键ID（更新时需要）
     */
    @Schema(description = "主键ID（更新时需要）")
    private Long id;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 竞谈文件id
     */
    @Schema(description = "竞谈文件id")
    private Long negotiationId;

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 评审类型（评审项、评分项）
     */
    @NotBlank(message = "评审类型不能为空")
    @Schema(description = "评审类型（评审项、评分项）")
    private String type;

    /**
     * 节点名称（评审类型下的子分类）
     */
    @NotBlank(message = "节点名称不能为空")
    @Schema(description = "节点名称（评审类型下的子分类）")
    private String nodeName;

    /**
     * 评审项名称
     */
    @NotBlank(message = "评审项名称不能为空")
    @Schema(description = "评审项名称")
    private String itemName;

    /**
     * 评审项描述
     */
    @Schema(description = "评审项描述")
    private String itemDescription;

    /**
     * 满分分值（仅评分项有效）
     */
    @Schema(description = "满分分值（仅评分项有效）")
    private Integer maxScore;

    /**
     * 权重（仅评分项有效）
     */
    @Schema(description = "权重（仅评分项有效）")
    private BigDecimal weight;

    /**
     * 分值最小值
     */
    @Schema(description = "分值最小值")
    private Integer minScore;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortOrder;

    /**
     * 是否必填
     */
    @Schema(description = "是否必填")
    private Integer isRequired;

    private static final long serialVersionUID = 1L;
}
