package com.ylz.saas.mapper;

import com.ylz.saas.common.data.datascope.annotation.DataPermission;
import com.ylz.saas.entity.SrmTenderAwardNotice;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tender_award_notice(中标通知书表)】的数据库操作Mapper
* @createDate 2025-06-17 19:17:17
* @Entity com.ylz.saas.entity.SrmTenderAwardNotice
*/
@Mapper
//@DataPermission(userEnabled = true, dataPermissionRuleList = "[{\"dataTypeCode\":\"PROJECT\",\"targetField\":\"project_id\",\"dataSourceField\":\"ID\"}]")
public interface SrmTenderAwardNoticeMapper extends BaseMapper<SrmTenderAwardNotice> {

    List<SrmTenderAwardNotice> getAwardNoticeList(@Param("noticeId")Long noticeId,@Param("projectId") Long projectId,@Param("supplierIdList") List<Long> supplierIdList, @Param("sectionIdList")List<Long> sectionIdList);
}




