package com.ylz.saas.codegen.srm_procurement_plan.vo;

import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.codegen.srm_procurement_plan.entity.SrmProcurementPlanEntity;
import com.ylz.saas.codegen.srm_procurement_plan.service.SrmProcurementPlanService;
import com.ylz.saas.codegen.srm_procurement_plan_detail.entity.SrmProcurementPlanDetailEntity;
import com.ylz.saas.codegen.srm_procurement_plan_field_value.entity.SrmProcurementPlanFieldValueEntity;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.sequence.generator.impl.DefaultCodeGenerator;
import com.ylz.saas.enums.ApproveStatusEnum;
import groovy.lang.Tuple2;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.ylz.saas.codegen.srm_procurement_plan.service.impl.SrmProcurementPlanServiceImpl.*;

/**
 * 采购计划表
 *
 * <AUTHOR>
 * @date 2025-06-03 20:39:40
 */
@Data
public class SrmProcurementPlanEntireVo implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 组织ID
     */
    @NotNull(message = "组织ID不能为空")
    private Long deptId;

    /**
     * 采购计划编号
     */
    private String planCode;

    /**
     * 采购计划名称
     */
    @NotBlank(message = "采购计划名称不能为空")
    private String planName;

    /**
     * 服务类型ID
     */
    @NotNull(message = "服务类型ID不能为空")
    private Long serviceTypeId;

    /**
     * 服务类型名
     */
    @NotBlank(message = "服务类型名不能为空")
    private String serviceTypeName;

    /**
     * 申请日期
     */
    @NotNull(message = "申请日期不能为空")
    private LocalDate applyDate;

    /**
     * 申请部门
     */
    @NotBlank(message = "申请部门不能为空")
    private String applyDeptId;

    /**
     * 申请人
     */
    @NotBlank(message = "申请人不能为空")
    private String applicant;

    /**
     * 申请人电话
     */
    @Pattern(regexp = "^1(3|4|5|7|8|9)\\d{9}$", message = "手机号码格式错误")
    @NotBlank(message = "申请人电话不能为空")
    private String applicantPhone;

    /**
     * 采购部门
     */
    @NotBlank(message = "采购部门不能为空")
    private String procurementDeptId;

    /**
     * 计划招标方式
     */
    private String planRecruitMethod;

    /**
     * 期限到货日期
     */
    private LocalDate periodStartDate;

    /**
     * 采购方式
     */
    private String procurementMethod;

    /**
     * 资金来源
     */
    private String fundSource;

    /**
     * 预算金额
     */
    private BigDecimal budgetAmount;

    /**
     * 寻源方式
     */
    private BaseServiceTypeFieldService.BuyWayEnum planSource;

    /**
     * 计划来源方式
     */
    private SrmProcurementPlanService.SourceEnum source;

    /**
     * 单据状态
     */
    private SrmProcurementPlanService.DocumentStatus status;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 修改人名称
     */
    private String updateByName;

    /**
     * 当前审批人
     */
    private String currentApprover;

    /**
     * 审批状态
     */
    private ApproveStatusEnum approveStatus;

    /**
     * 代理机构表ID
     */
    private Long baseAgentDeptId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标识（0-正常、1-删除）
     */
    private int delFlag;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 创建人ID
     */
    private Long createById;

    /**
     * 更新人ID
     */
    private Long updateById;

    /**
     * 附件
     */
    private String attachment;

    /**
     * 备注
     */
    private String remarks;


    /**
     * 寻源方式
     */
    private BaseServiceTypeFieldService.BuyWayEnum buyWay;

    /**
     * 动态字段配置集合
     */
    private List<EnhanceBaseServiceTypeFieldVo> config;

    /**
     * 每条采购计划明细字段及值集合
     */
    private List<PlanFieldListVo> srmProcurementPlanFieldValueList;

    /**
     * 申请理由
     */
    private String applyReason;

    /**
     * 收货地址
     */
    private String shippingAddress;

    /**
     * 供应商要求
     */
    private String supplierRequirements;


}