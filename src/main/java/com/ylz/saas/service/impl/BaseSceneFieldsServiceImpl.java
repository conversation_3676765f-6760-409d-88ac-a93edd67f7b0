package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.BaseSceneFields;
import com.ylz.saas.service.BaseSceneFieldsService;
import com.ylz.saas.mapper.BaseSceneFieldsMapper;
import org.springframework.stereotype.Service;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【base_scene_fields(场景字段表)】的数据库操作Service实现
* @createDate 2025-06-11 13:07:56
*/
@Service
public class BaseSceneFieldsServiceImpl extends ServiceImpl<BaseSceneFieldsMapper, BaseSceneFields> implements BaseSceneFieldsService{

    @Override
    public List<BaseSceneFields> getSceneFieldsBySceneId(Long sceneId) {
        return query().eq("template_scene_id", sceneId).list();
    }
    @Override
    public boolean saveField(BaseSceneFields field) {
        // 校验该场景下是否已有相同名称的字段
        boolean exists = query().eq("template_scene_id", field.getTemplateSceneId())
                .eq("field_name", field.getFieldName())
                .exists();

        if (exists) {
            throw new IllegalArgumentException("字段名称已存在");
        }

        return super.save(field);
    }


}




