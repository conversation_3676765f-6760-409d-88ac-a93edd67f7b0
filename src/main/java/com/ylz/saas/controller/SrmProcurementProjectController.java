package com.ylz.saas.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.common.data.datascope.annotation.DataPermission;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.enums.InviteMethodEnum;
import com.ylz.saas.enums.ProjectProgressStatusEnum;
import com.ylz.saas.req.SrmProcurementProjectPageQueryReq;
import com.ylz.saas.resp.ProcessInstanceStartResp;
import com.ylz.saas.resp.ProjectArchiveResp;
import com.ylz.saas.service.SrmProcurementProjectService;
import com.ylz.saas.req.SrmProcurementProjectCreateReq;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 采购项目
 * <AUTHOR>
 * @Date 2025/6/18 15:55
 * @Description
 */
@RestController
@RequestMapping("/srmProcurementProject")
@RequiredArgsConstructor
public class SrmProcurementProjectController {

    private final SrmProcurementProjectService srmProcurementProjectService;

    /**
     * 新建/编辑采购项目
     */
    @PostMapping("/upsert")
    public R<ProcessInstanceStartResp> upsert(@RequestBody @Validated SrmProcurementProjectCreateReq req) {
        return R.ok(srmProcurementProjectService.upsertAndStartFlow(req));
    }


    /**
     * 分页查询采购立项
     */
    @PostMapping("/pageQuery")
    public R<Page<SrmProcurementProject>> pageQuery(@RequestBody SrmProcurementProjectPageQueryReq req, Page<SrmProcurementProject> page) {
        Page<SrmProcurementProject> srmProcurementProjectPage = srmProcurementProjectService.pageQuery(req, page);

        if (CollectionUtils.isNotEmpty(srmProcurementProjectPage.getRecords())) {
            for (SrmProcurementProject record : srmProcurementProjectPage.getRecords()) {
                this.changeStatusAndProcess(record, false);
            }
        }
        return R.ok(srmProcurementProjectPage);
    }

    /**
     * 分页查询采购方询价单
     */
    @PostMapping("/pageQueryWithNodeStatusPurchase")
    public R<Page<SrmProcurementProject>> pageQueryWithNodeStatusPurchase(@RequestBody SrmProcurementProjectPageQueryReq req, Page<SrmProcurementProject> page) {
        Page<SrmProcurementProject> srmProcurementProjectPage = srmProcurementProjectService.pageQueryWithProcessInstance(req, page);

        if (CollectionUtils.isNotEmpty(srmProcurementProjectPage.getRecords())) {
            for (SrmProcurementProject record : srmProcurementProjectPage.getRecords()) {
                this.changeStatusAndProcess(record, true);
            }
        }
        return R.ok(srmProcurementProjectPage);
    }
    /**
     * 供方采购询价单分页
     */
    @PostMapping("/pageQueryWithNodeStatusSupplier")
    public R<Page<SrmProcurementProject>> pageQueryWithNodeStatusSupplier(@RequestBody SrmProcurementProjectPageQueryReq req, Page<SrmProcurementProject> page) {
        Page<SrmProcurementProject> srmProcurementProjectPage = srmProcurementProjectService.pageQueryWithProcessInstanceForSupplier(req, page);

        if (CollectionUtils.isNotEmpty(srmProcurementProjectPage.getRecords())) {
            for (SrmProcurementProject record : srmProcurementProjectPage.getRecords()) {
                this.changeStatusAndProcess(record, true);
            }
        }
        return R.ok(srmProcurementProjectPage);
    }
    private void changeStatusAndProcess(SrmProcurementProject record,Boolean flag) {
        InviteMethodEnum inviteMethod = record.getInviteMethod();
        if (InviteMethodEnum.INVITE == inviteMethod) {
//    如果是邀请类别
            ProjectProgressStatusEnum progressStatus = record.getProgressStatus();
            ProjectProgressStatusEnum projectProgressStatusEnum = ProjectProgressStatusEnum.setInviteStatus(progressStatus);
            record.setProgressStatus(projectProgressStatusEnum);
            if(flag){
                SrmProcurementProject.NodeStatus nodeStatus = record.getNodeStatus();
                SrmProcurementProject.NodeStatus inviteNodeStatus = SrmProcurementProject.NodeStatus.setInviteStatus(nodeStatus);
                record.setNodeStatus(inviteNodeStatus);
            }

        }


    }

    /**
     * 详情
     */
    @GetMapping("/detail")
    public R<SrmProcurementProject> detail(@RequestParam(value = "projectCode") String projectCode) {
        return R.ok(srmProcurementProjectService.detail(projectCode, false));
    }

    /**
     * 删除
     */
    @PostMapping("/delete/{id}")
    public R<Void> delete(@PathVariable Long id) {
        srmProcurementProjectService.delete(id);
        return R.ok();
    }


    /**
     * 完成项目（归档）
     */
    @PostMapping("/completeProject/{projectCode}")
    public R<Void> completeProject(@PathVariable String projectCode) {
        srmProcurementProjectService.updateProgressStatus(projectCode, ProjectProgressStatusEnum.COMPLETED, true);
        return R.ok();
    }


    /**
     * 获取项目归档信息
     */
    @GetMapping("/getProjectArchive/{projectCode}")
    public R<ProjectArchiveResp> getProjectArchive(@PathVariable String projectCode) {
        return R.ok(srmProcurementProjectService.getProjectArchive(projectCode));
    }

}
