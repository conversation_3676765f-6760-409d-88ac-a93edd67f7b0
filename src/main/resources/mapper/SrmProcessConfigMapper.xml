<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmProcessConfigMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmProcessConfig">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="bizType" column="biz_type" jdbcType="VARCHAR"/>
            <result property="flowKey" column="flow_key" jdbcType="VARCHAR"/>
            <result property="variables" column="variables" jdbcType="OTHER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,biz_type,
        flow_key,variables
    </sql>
</mapper>
