package com.ylz.saas.req;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.entity.SrmTenderNoticeChange;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.ChangeTypeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 变更查询信息
 */
@Data
public class SrmTenderNoticeChangePageReq extends Page<SrmTenderNoticeChange> {
    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 变更类型
     */
    private List<ChangeTypeEnum> changeTypeEnums;

    /**
     * 变更人
     */
    private String changeBy;

    /**
     * 变更审核状态
     * TO_APPROVE("待审批"),
     *     APPROVING("审批中"),
     *     APPROVE("审批通过"),
     *     APPROVE_REJECT("审批驳回"),
     *     APPROVE_REVOKE("审批撤销"),
     */
    private List<ApproveStatusEnum> approvedStatusList;

}