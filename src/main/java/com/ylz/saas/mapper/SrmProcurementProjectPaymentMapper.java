package com.ylz.saas.mapper;

import com.ylz.saas.common.data.datascope.annotation.DataPermission;
import com.ylz.saas.entity.SrmProcurementProjectPayment;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【srm_procurement_project_payment(采购立项支付方式表)】的数据库操作Mapper
* @createDate 2025-06-11 11:21:09
* @Entity com.ylz.saas.entity.SrmProcurementProjectPayment
*/
@Mapper
//@DataPermission(userEnabled = true, dataPermissionRuleList = "[{\"dataTypeCode\":\"PROJECT\",\"targetField\":\"project_id\",\"dataSourceField\":\"ID\"}]")
public interface SrmProcurementProjectPaymentMapper extends SaasBaseMapper<SrmProcurementProjectPayment> {

}




