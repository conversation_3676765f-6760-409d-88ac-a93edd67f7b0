package com.ylz.saas.req;

import com.ylz.saas.enums.ExceptionHandleTypeEnum;
import com.ylz.saas.enums.ExceptionTypeEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 流标参数
 */
@Data
public class SrmTenderFailedLogReq {
    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 标段ID
     */
    private List<Long> sectionIdList;

    /**
     * 异常处理方式：重新采购，项目终止
     */
    @NotNull(message = "异常处理方式不能为空")
    private ExceptionHandleTypeEnum exceptionHandleType;

    /**
     * 异常分类: 临时不采购，供应商报价不恰当，其他
     */
    @NotNull(message = "异常分类不能为空")
    private ExceptionTypeEnum exceptionType;

    /**
     * 是否发布流标公示
     */
    @NotNull(message = "是否发布流标公示不能为空")
    private Boolean isPublicity;

    /**
     * 流标公示标题
     */
    private String tenderFailedTitle;

    /**
     * 流标公示内容
     */
    private String tenderFailedContent;

    /**
     * 异常情况描述
     */
    @NotBlank(message = "异常情况描述不能为空")
    private String remark;

    /**
     * 附件信息
     */
    private List<AttachmentInfoReq> attachmentInfos;
}