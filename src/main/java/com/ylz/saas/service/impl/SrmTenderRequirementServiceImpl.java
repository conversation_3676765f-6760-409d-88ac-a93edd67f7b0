package com.ylz.saas.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmTenderRequirement;
import com.ylz.saas.enums.BidsSegmentTypeEnum;
import com.ylz.saas.mapper.SrmTenderRequirementMapper;
import com.ylz.saas.req.BidFeeInfoReq;
import com.ylz.saas.req.SrmTenderNoticeAddReq;
import com.ylz.saas.service.SrmTenderRequirementService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【srm_tender_requirement(招标要求表)】的数据库操作Service实现
* @createDate 2025-06-10 14:39:25
*/
@Service
public class SrmTenderRequirementServiceImpl extends ServiceImpl<SrmTenderRequirementMapper, SrmTenderRequirement>
    implements SrmTenderRequirementService{

    @Override
    public boolean isNeedEffInfo(Long noticeId, Long sectionId) {
        List<SrmTenderRequirement> requirementList = lambdaQuery()
                .eq(SrmTenderRequirement::getNoticeId, noticeId)
                .eq(SrmTenderRequirement::getSectionId, sectionId)
                .eq(SrmTenderRequirement::getRequirementType, BidsSegmentTypeEnum.FEE)
                .list();
        if(CollectionUtils.isEmpty(requirementList)){
            return false;
        }
        SrmTenderRequirement srmTenderRequirement = requirementList.get(0);
        SrmTenderNoticeAddReq.FeeInfo feeInfo = JSONObject.parseObject(srmTenderRequirement.getRequirementContent(), SrmTenderNoticeAddReq.FeeInfo.class);
        return feeInfo.getGuaranteeAmount() != null && feeInfo.getGuaranteeAmount().compareTo(BigDecimal.ZERO) > 0;

    }

    @Override
    public Map<Long,Boolean> isNeedEffInfoBySectionIds(Long noticeId, List<Long> sectionIds, BidsSegmentTypeEnum requirementType) {
        List<SrmTenderRequirement> requirementList = lambdaQuery()
                .eq(SrmTenderRequirement::getNoticeId, noticeId)
                .in(SrmTenderRequirement::getSectionId, sectionIds)
                .eq(SrmTenderRequirement::getRequirementType, requirementType)
                .list();
        Map<Long,Boolean> result = new HashMap<>();
        if(CollectionUtils.isEmpty(requirementList)){
            sectionIds.forEach(sectionId -> result.put(sectionId, false));
        } else {
            for (SrmTenderRequirement srmTenderRequirement : requirementList) {
                if (requirementType == BidsSegmentTypeEnum.FEE) {
                    SrmTenderNoticeAddReq.FeeInfo feeInfo = JSONObject.parseObject(srmTenderRequirement.getRequirementContent(), SrmTenderNoticeAddReq.FeeInfo.class);
                    result.put(srmTenderRequirement.getSectionId(), feeInfo.getGuaranteeAmount() != null && feeInfo.getGuaranteeAmount().compareTo(BigDecimal.ZERO) > 0);
                } else if (requirementType == BidsSegmentTypeEnum.BID_DOCUMENT) {
                    BidFeeInfoReq bidFeeInfo = JSONObject.parseObject(srmTenderRequirement.getRequirementContent(), BidFeeInfoReq.class);
                    if(bidFeeInfo != null){
                        result.put(srmTenderRequirement.getSectionId(), bidFeeInfo.getBidFee() != null && bidFeeInfo.getBidFee().compareTo(BigDecimal.ZERO) > 0);
                    } else {
                        result.put(srmTenderRequirement.getSectionId(), false);
                    }
                } else {
                    result.put(srmTenderRequirement.getSectionId(), false);
                }
            }
        }
        return result;
    }


    @Override
    public Map<Long, Boolean> isNeedRequirement(List<Long> noticeIds, BidsSegmentTypeEnum requirementType) {
        List<SrmTenderRequirement> requirementList = lambdaQuery()
                .in(SrmTenderRequirement::getNoticeId, noticeIds)
                .eq(SrmTenderRequirement::getRequirementType, requirementType)
                .list();
        Map<Long, Boolean> result = new HashMap<>();
        if (CollectionUtils.isEmpty(requirementList)) {
            noticeIds.forEach(noticeId -> result.put(noticeId, false));
        } else {
            Map<Long, List<SrmTenderRequirement>> noticeGroup = requirementList.stream().collect(Collectors.groupingBy(SrmTenderRequirement::getNoticeId));
            noticeGroup.forEach((noticeId, list) -> {
                if (requirementType == BidsSegmentTypeEnum.FEE) {
                    Optional<SrmTenderRequirement> depositOpt = list.stream()
                            .filter(item -> Objects.equals(BidsSegmentTypeEnum.FEE, item.getRequirementType()))
                            .filter(item -> {
                                SrmTenderNoticeAddReq.FeeInfo feeInfo = JSONObject.parseObject(item.getRequirementContent(), SrmTenderNoticeAddReq.FeeInfo.class);
                                return feeInfo.getGuaranteeAmount() != null && feeInfo.getGuaranteeAmount().compareTo(BigDecimal.ZERO) > 0;
                            }).findAny();
                    result.put(noticeId, depositOpt.isPresent());
                } else if (requirementType == BidsSegmentTypeEnum.BID_DOCUMENT) {
                    Optional<SrmTenderRequirement> depositOpt = list.stream()
                            .filter(item -> Objects.equals(BidsSegmentTypeEnum.BID_DOCUMENT, item.getRequirementType()))
                            .filter(item -> {
                                BidFeeInfoReq tenderFeeInfo = JSONObject.parseObject(item.getRequirementContent(), BidFeeInfoReq.class);
                                if(tenderFeeInfo != null){
                                    return tenderFeeInfo.getBidFee() != null && tenderFeeInfo.getBidFee().compareTo(BigDecimal.ZERO) > 0;
                                } else {
                                    return false;
                                }
                            }).findAny();
                    result.put(noticeId, depositOpt.isPresent());
                } else {
                    result.put(noticeId, false);
                }
            });
        }
        return result;
    }


}




