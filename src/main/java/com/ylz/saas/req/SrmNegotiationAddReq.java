package com.ylz.saas.req;

import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.BidsSegmentTypeEnum;
import com.ylz.saas.enums.ReviewEnum;
import com.ylz.saas.enums.TenderWayEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 招标公告表
 * @TableName srm_tender_notice
 */
@Data
public class SrmNegotiationAddReq implements Serializable {
    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 文件递交地址
     */
    @NotBlank(message = "文件递交地址不能为空")
    private String fileSubmissionAddress;

    /**
     * 标书费是否收取
     */
    @NotNull(message = "标书费是否收取不能为空")
    private Boolean bidFeeCollection;

    /**
     * 标书费&标书文件设置
     */
    private List<BidsSegment> bidsSegments;

    /**
     * 投标须知
     */
    private String biddingNotice;

    /**
     * 评审规则
     */
    @NotBlank(message = "评审规则不能为空")
    private String evaluationMethod;

    /**
     * 文件获取开始时间
     */
    @NotNull(message = "文件获取开始时间不能为空")
    private LocalDateTime fileObtainStartTime;

    /**
     * 文件获取截止时间
     */
    @NotNull(message = "文件获取截止时间不能为空")
    private LocalDateTime fileObtainEndTime;

    /**
     * 标书费缴纳开始时间
     */
    @NotNull(message = "标书费缴纳开始时间不能为空")
    private LocalDateTime bidDocPayStartTime;

    /**
     * 标书费缴纳截止时间
     */
    @NotNull(message = "标书费缴纳截止时间不能为空")
    private LocalDateTime bidDocPayEndTime;

    /**
     * 报价开始时间
     */
    @NotNull(message = "报价开始时间不能为空")
    private LocalDateTime quoteStartTime;

    /**
     * 报价截止时间
     */
    @NotNull(message = "报价截止时间不能为空")
    private LocalDateTime quoteEndTime;

    /**
     * 竞谈文件的审核状态
     * TO_APPROVE("待审批"),
     *     APPROVING("审批中"),
     *     APPROVE("审批通过"),
     *     APPROVE_REJECT("审批驳回"),
     *     APPROVE_REVOKE("审批撤销"),
     */
    private ApproveStatusEnum docStatus;

    /**
     * 开标方式
     */
    private TenderWayEnum tenderWay;

    /**
     * 评标标准
     */
    @Valid
    @Size(min = 1, message = "评标标准不能为空")
    private List<EvaluationStandardInfo> evaluationStandardInfos;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 评标标准
     */
    @Data
    public static class EvaluationStandardInfo implements Serializable{
        /**
         * 标段ID
         */
        private Long sectionId;

        /**
         * 评审类型（REVIEW_ITEM-评审项, SCORE_ITEM-评分项）
         */
        private ReviewEnum type;

        /**
         * 节点名称（评审类型下的子分类）
         */
        private String nodeName;

        /**
         * 评审项名称
         */
        private String itemName;

        /**
         * 评审项描述
         */
        private String itemDescription;

        /**
         * 总分(评分)
         */
        private BigDecimal totalScore;

        /**
         * 满分分值（仅评分项有效）
         */
        private Integer maxScore;

        /**
         * 分值最小值
         */
        private Integer minScore;

        /**
         * 权重（仅评分项有效）
         */
        private BigDecimal weight;

        /**
         * 否决投标数量阈值
         */
        private Integer vetoThreshold;

        /**
         * 排序
         */
        private Integer sortOrder;

        /**
         * 是否必填
         */
        private Integer isRequired;
    }

    /**
     * 标段：资质要求、报名响应条件、保证金设置
     */
    @Data
    public static class BidsSegment implements Serializable {

        /**
         * 要求类型
         *     BID_DOCUMENT, // 标书文件
         */
        private BidsSegmentTypeEnum requirementType;

        /**
         * 标段ID
         */
        private Long sectionId;

        /**
         * 要求名称/资质证件ID
         */
        private String requirementName;

        /**
         * 要求内容/补充说明/金额银行信息（json）
         */
        private String requirementContent;

        /**
         * 标书费相关信息
         */
        private BidFeeInfoReq bidFeeInfo;

        /**
         * 附件信息
         */
        private List<AttachmentInfoReq> attachmentInfos;

    }



}