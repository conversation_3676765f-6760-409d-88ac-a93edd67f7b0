package com.ylz.saas.codegen.base_agent_org.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.admin.api.entity.SysUser;
import com.ylz.saas.codegen.base_agent_org.entity.BaseAgentOrgEntity;
import com.ylz.saas.codegen.base_agent_org.vo.BaseAgentOrgVo;
import com.ylz.saas.codegen.base_agent_org.vo.AgentUserInnerAccountReq;

import java.util.List;

public interface BaseAgentOrgService extends IService<BaseAgentOrgEntity> {
	/**
     * 新增代理机构表
     * @param baseAgentOrgVo 代理机构表
     */
    String saveBaseAgentOrg(BaseAgentOrgVo baseAgentOrgVo);
	
	/**
     * 修改代理机构表
     * @param baseAgentOrgVo 代理机构表
     */
    boolean updateBaseAgentOrg(BaseAgentOrgVo baseAgentOrgVo);
	
	/**
     * 查询代理机构表详情
     * @param id
     */
    BaseAgentOrgEntity getByIdBaseAgentOrg(Long id);
	
	/**
	* 通过id删除代理机构表
	*/
	boolean removeByIdsBaseAgentOrg(List<Long> ids);


	/**
	 * 新增代理机构  查询内部账号（已绑定不显示）
	 * @param req
	 * @return
	 */
	List<SysUser> getInnerAccount(AgentUserInnerAccountReq req);

	/**
	 * 开关
	 * @param id
	 * @return
	 */
	Object switchStatus(Long id);
}