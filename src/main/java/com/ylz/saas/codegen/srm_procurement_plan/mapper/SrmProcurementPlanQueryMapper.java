package com.ylz.saas.codegen.srm_procurement_plan.mapper;

import com.ylz.saas.admin.api.entity.SysDept;
import com.ylz.saas.admin.api.entity.SysUser;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import com.ylz.saas.codegen.srm_procurement_plan.entity.SrmProcurementPlanEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采购计划查询Mapper（不受数据权限控制）
 * 专门用于查询用户和部门等辅助信息
 */
@Mapper
public interface SrmProcurementPlanQueryMapper extends SaasBaseMapper<SrmProcurementPlanEntity> {

    /**
     * 根据用户ID列表查询用户信息
     */
    List<SysUser> getUsersByIds(@Param("userIds") List userIds,
                                @Param("applicantName") String applicantName);

    /**
     * 根据部门ID列表查询部门信息
     */
    List<SysDept> getDeptsByIds(@Param("deptIds") List deptIds,
                                @Param("applyDeptName") String applyDeptName);

    /**
     * 根据申请人姓名查询用户ID列表（用于过滤）
     */
    List<String> getUserIdsByName(@Param("applicantName") String applicantName);

    /**
     * 根据申请部门名称查询部门ID列表（用于过滤）
     */
    List<Long> getDeptIdsByName(@Param("applyDeptName") String applyDeptName);
}
