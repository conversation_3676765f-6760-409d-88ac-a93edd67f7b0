package com.ylz.saas.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.entity.BaseTemplateScenes;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.req.BaseAnnouncementSceneAddReq;
import com.ylz.saas.req.BaseAnnouncementSceneUpdateReq;
import com.ylz.saas.req.SceneWithFieldsReq;
import com.ylz.saas.resp.BaseAnnouncementTemplateSceneQueryResp;
import com.ylz.saas.resp.SceneWithFieldsResp;

import java.lang.reflect.InvocationTargetException;

/**
* <AUTHOR>
* @description 针对表【base_template_scenes(模板场景表)】的数据库操作Service
* @createDate 2025-06-11 13:07:56
*/
public interface BaseTemplateScenesService extends IService<BaseTemplateScenes> {
    boolean saveScene(BaseAnnouncementSceneAddReq req);

    boolean updateScene(BaseAnnouncementSceneUpdateReq req);

    boolean deleteSceneById(Long id);

    SceneWithFieldsResp getSceneWithFields(Long sceneId) throws InvocationTargetException, IllegalAccessException;
    boolean saveSceneWithFields(SceneWithFieldsReq req);
    boolean updateSceneWithFields(SceneWithFieldsReq req);

    Page<BaseAnnouncementTemplateSceneQueryResp> query(Page<BaseAnnouncementTemplateSceneQueryResp> page, String sceneName);
}