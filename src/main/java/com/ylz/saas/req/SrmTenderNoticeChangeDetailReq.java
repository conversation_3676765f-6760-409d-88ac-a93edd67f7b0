package com.ylz.saas.req;

import com.ylz.saas.enums.ChangeTypeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 招标公告表
 * @TableName srm_tender_notice
 */
@Data
public class SrmTenderNoticeChangeDetailReq {

    /**
     * 招标公告ID
     */
    private Long noticeId;

    /**
     * 采购立项ID
     */
    private Long projectId;

    /**
     * 业务ID
     */
    private Long bizId;

    /**
     * 变更类型
     */
    @NotNull(message = "变更类型不能为空")
    private ChangeTypeEnum changeTypeEnum;

}