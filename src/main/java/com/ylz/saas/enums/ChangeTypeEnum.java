package com.ylz.saas.enums;

import lombok.Getter;

/**
 * 变更状态枚举
 */
@Getter
public enum ChangeTypeEnum {
    CHANGE_NOTICE, // 变更公告
    CHANGE_INVITE, // 变更邀请函
    CHANGE_PROJECT_TIME, // 变更项目时间
    CHANGE_OPENER, // 变更开标人
    CHANGE_BID, // 变更定标
    CHANGE_BID_PUBLICITY, // 变更中标公示
    CHANGE_BID_NOTICE, // 变更中标公告
    CHANGE_BID_DOC, // 变更竞谈文件
    CHANGE_ENSILAGE, // 变更青贮信息
    CHANGE_CONTRACT, // 变更合同信息
    ;
}
