package com.ylz.saas.controller;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.entity.SrmProcurementProjectItem;
import com.ylz.saas.entity.SrmProcurementProjectSection;
import com.ylz.saas.req.*;
import com.ylz.saas.resp.QuoteDetailQueryResp;
import com.ylz.saas.resp.QuoteQueryByMaterialResp;
import com.ylz.saas.resp.QuoteQueryBySupplierResp;
import com.ylz.saas.resp.SrmTenderQuoteDetailBySupplierResp;
import com.ylz.saas.service.SrmTenderBidderQuoteItemService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ylz.saas.common.core.util.R;

import java.util.List;

/**
 * 报价接口
 * <AUTHOR>
 * @Date 2025/6/13 15:01
 * @Description
 */
@RestController
@RequestMapping("/srmTenderBidderQuote")
@RequiredArgsConstructor
public class SrmTenderBidderQuoteController {

    private final SrmTenderBidderQuoteItemService srmTenderBidderQuoteItemService;

    /**
     * 报价提交接口
     */
    @PostMapping("/submit")
    public R<Void> submitQuote(@RequestBody QuoteSubmitReq req) {
        srmTenderBidderQuoteItemService.submitQuote(req);
        return R.ok();
    }


    /**
     * 获取再次报价的供应商列表
     */
    @PostMapping("/getQuoteAgainSupplierList")
    public R<List<Pair<String, String>>> getQuoteAgainSupplierList(@RequestBody @Validated QuoteAgainSupplierQueryReq req) {
        return R.ok(srmTenderBidderQuoteItemService.getQuoteAgainSupplierList(req));
    }


    /**
     * 再次发起报价
     */
    @PostMapping("/quoteAgain")
    public R<Void> quoteAgain(@RequestBody @Validated QuoteAgainReq req){
        srmTenderBidderQuoteItemService.quoteAgain(req);
        return R.ok();
    }


    /**
     * 获取再次报价的标段
     */
    @PostMapping("/getQuoteAgainSection")
    public R<List<SrmProcurementProjectSection>> getQuoteAgainSection(@RequestBody @Validated QuoteAgainSectionQueryReq req) {
        return R.ok(srmTenderBidderQuoteItemService.getQuoteAgainSection(req));
    }


    /**
     * 按物料查询报价列表（物料分页，内部按供应商维度组织）
     */
    @PostMapping("/queryQuoteListByMaterial")
    public R<Page<QuoteQueryByMaterialResp>> queryQuoteListByMaterial(@RequestBody @Validated BidderQuoteQueryReq req, Page<QuoteQueryByMaterialResp> page) {
        if (req.getAwarded() == null || !req.getAwarded()) {
            ExceptionUtil.checkNonNull(req.getRoundNo(), "请选择报价轮次！");
        }
        return R.ok(srmTenderBidderQuoteItemService.queryQuoteListByMaterial(req, page));
    }


    /**
     * 按供应商查询报价列表
     */
    @PostMapping("/queryQuoteListBySupplier")
    public R<List<QuoteQueryBySupplierResp>> queryQuoteListBySupplier(@RequestBody @Validated BidderQuoteQueryReq req) {
        return R.ok(srmTenderBidderQuoteItemService.queryQuoteListBySupplier(req));
    }


    /**
     * 查询报价的物料列表
     */
    @PostMapping("/queryMaterialList")
    public R<List<SrmProcurementProjectItem>> queryMaterialList(@RequestBody @Validated QuoteMaterialQueryReq req) {
        return R.ok(srmTenderBidderQuoteItemService.queryMaterialList(req));
    }


    /**
     * 查询报价次数
     */
    @PostMapping("/quoteCount")
    public R<Integer> quoteCount(@RequestBody @Validated QuoteCountReq req) {
        return R.ok(srmTenderBidderQuoteItemService.quoteCount(req));
    }


    /**
     * 查询报价少于3家的物料code列表
     */
    @PostMapping("/getQuoteLessThanThreeMaterialCodeList")
    public R<List<String>> getQuoteLessThanThreeMaterialCodeList(@RequestBody @Validated QuoteLessThanThresholdSupplierQueryReq req) {
        return R.ok(srmTenderBidderQuoteItemService.getQuoteLessThanThreeMaterialCodeList(req));
    }


    /**
     * 报价明细
     */
    @PostMapping("/queryQuoteDetail")
    public R<List<QuoteDetailQueryResp>> queryQuoteDetail(@RequestBody @Validated QuoteDetailQueryReq req, Page page) {
        return R.ok(srmTenderBidderQuoteItemService.queryQuoteDetail(req, page));
    }
    /**
     * 报价明细（供应商）- 按轮次分组查询
     */
    @PostMapping("/queryQuoteDetailBySupplier")
    @Operation(summary = "报价明细（供应商）", description = "查询根据报价轮次分别返回列表，列表属性包括：供应商名称，联系人，联系电话，报价状态，报价ip，报价时间，报价排名")
    public R<SrmTenderQuoteDetailBySupplierResp> queryQuoteDetailBySupplier(@RequestBody @Validated com.ylz.saas.req.SrmTenderQuoteDetailBySupplierQueryReq req) {
        return R.ok(srmTenderBidderQuoteItemService.queryQuoteDetailBySupplier(req));
    }
}
