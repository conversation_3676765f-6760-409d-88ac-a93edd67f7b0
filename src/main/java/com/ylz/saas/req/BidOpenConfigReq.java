package com.ylz.saas.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/14 16:56
 * @Description
 */
@Data
public class BidOpenConfigReq {

    /**
     * 公告id
     */
    @NotNull(message = "公告id不能为空")
    private Long noticeId;

    /**
     * 是否公开报价
     */
    @NotNull(message = "是否公开报价不能为空")
    @Range(min = 0, max = 1, message = "是否公开报价只能是0或1")
    private Integer publicQuote;

    /**
     * 开标人员id
     */
    private Long bidOpenUserId;

    /**
     * 监督人员id列表
     */
    @NotNull(message = "监督人员id列表不能为空")
    private List<Long> supervisionUserIdList;

}
