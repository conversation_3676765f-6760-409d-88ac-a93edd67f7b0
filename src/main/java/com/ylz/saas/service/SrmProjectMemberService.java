package com.ylz.saas.service;

import com.ylz.saas.entity.SrmProjectMember;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.enums.ProjectMemberTypeEnum;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_project_member(成员表)】的数据库操作Service
* @createDate 2025-06-11 11:21:09
*/
public interface SrmProjectMemberService extends IService<SrmProjectMember> {

    List<SrmProjectMember> getMyMemberList(long projectId, Long userId, String role);


}
