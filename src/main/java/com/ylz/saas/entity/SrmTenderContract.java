package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.ylz.saas.common.core.util.TenantTable;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.ContractStatusEnum;
import com.ylz.saas.enums.ContractTypeEnum;
import lombok.Data;

/**
 * 合同表
 * @TableName srm_tender_contract
 */
@TableName(value ="srm_tender_contract")
@Data
@TenantTable
public class SrmTenderContract {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 采购立项ID
     */
    private Long projectId;

    /**
     * 公告ID
     */
    private Long noticeId;

    /**
     * 标段ID
     */
    private Long sectionId;

    /**
     * 评标结果ID
     */
    private Long evaluationResultId;

    /**
     * 合同编号
     */
    private String contractCode;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 采购方ID
     */
    private String purchaserDeptId;

    /**
     * 采购方名称
     */
    private String purchaserDeptName;

    /**
     * 采购联系人ID
     */
    private Long purchaseContactId;

    /**
     * 采购联系电话
     */
    private String purchaseContactPhone;

    /**
     * 合同有效期起始时间
     */
    private LocalDateTime effectStartDate;

    /**
     * 合同有效期终止时间
     */
    private LocalDateTime effectEndDate;

    /**
     * 签约时间
     */
    private LocalDateTime signDate;

    /**
     * 签约地点
     */
    private String signLocation;

    /**
     * 支付方式ID
     */
//    private Long paymentMethodId;

    /**
     * 账期ID
     */
//    private Long paymentPeriodId;

    /**
     * 履约保证金
     */
    private BigDecimal performanceBond;

    /**
     * 合同含税总金额
     */
    private BigDecimal totalAmount;

    /**
     * 是否为电子章(0-否、1-是)
     */
    private Integer electronicSeal;

    /**
     * 供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 销售负责人
     */
    private String supplierSalesPrincipal;

    /**
     * 销售联系电话
     */
    private String supplierSalesPrincipalPhone;

    /**
     * 收款银行卡ID
     */
    private Long supplierBankCardId;


    /**
     * 收款银行
     */
    private String supplierBankName;

    /**
     * 收款银行账号
     */
    private String supplierBankAccount;

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 合同正文
     */
    private String contractBody;

    /**
     * 合同状态（NEW-新建、PENDING_SUPPLIER-待供应商确认、EFFECTIVE-合同生效、INVALID-已失效、REVOKED-已作废、SUPPLIER_REJECTED-供应商退回）
     */
    private ContractStatusEnum contractStatus;

    /**
     * 审批状态（APPROVING-审批中、APPROVE_REJECT-审批驳回、APPROVE-审批通过）
     */
    private ApproveStatusEnum approvalStatus;

    /**
     * 供方退回且采方修改后保存待确认时合同信息
     */
    private String supplierRejectedContractInfo;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createById;

    /**
     * 更新人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateById;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}