package com.ylz.saas.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


import java.util.List;

/**
 * 邀请供应商请求VO
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@Data
@Schema(description = "邀请供应商请求")
public class SupplierInviteRequestVo {

    /**
     * 租户供应商ID
     */

    @Schema(description = "租户供应商ID", required = true)
    private Long tenantSupplierId;

    /**
     * 部门关联信息列表id
     */
    @Schema(description = "部门关联信息列表", required = true)
    private List<Long> deptIds;


}
