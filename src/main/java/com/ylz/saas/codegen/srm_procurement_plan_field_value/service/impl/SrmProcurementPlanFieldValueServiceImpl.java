package com.ylz.saas.codegen.srm_procurement_plan_field_value.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.codegen.srm_procurement_plan_field_value.entity.SrmProcurementPlanFieldValueEntity;
import com.ylz.saas.codegen.srm_procurement_plan_field_value.excel.SrmProcurementPlanFieldValueReq;
import com.ylz.saas.codegen.srm_procurement_plan_field_value.mapper.SrmProcurementPlanFieldValueMapper;
import com.ylz.saas.codegen.srm_procurement_plan_field_value.service.SrmProcurementPlanFieldValueService;
import com.ylz.saas.codegen.srm_procurement_plan_field_value.vo.SrmProcurementPlanFieldValueVo;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.data.excel.ExcelAnalysisUtil;
import com.ylz.saas.common.data.excel.ExcelTemplateGenerator;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Path;
import java.util.List;
/**
 * 采购计划字段值表
 *
 * <AUTHOR>
 * @date 2025-06-03 21:00:11
 */
@Service
@Slf4j
public class SrmProcurementPlanFieldValueServiceImpl extends ServiceImpl<SrmProcurementPlanFieldValueMapper, SrmProcurementPlanFieldValueEntity> implements SrmProcurementPlanFieldValueService {

	@Autowired
	private SrmProcurementPlanFieldValueMapper srmProcurementPlanFieldValueMapper;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveSrmProcurementPlanFieldValue(SrmProcurementPlanFieldValueVo srmProcurementPlanFieldValueVo) {
		SrmProcurementPlanFieldValueEntity srmProcurementPlanFieldValue = new SrmProcurementPlanFieldValueEntity();
        BeanUtil.copyProperties(srmProcurementPlanFieldValueVo, srmProcurementPlanFieldValue);
        return save(srmProcurementPlanFieldValue);
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateSrmProcurementPlanFieldValue(SrmProcurementPlanFieldValueVo srmProcurementPlanFieldValueVo) {
		SrmProcurementPlanFieldValueEntity srmProcurementPlanFieldValue = getById(srmProcurementPlanFieldValueVo.getId());
		ExceptionUtil.check(srmProcurementPlanFieldValue == null, "500", "采购计划字段值表不存在");
		BeanUtil.copyProperties(srmProcurementPlanFieldValueVo, srmProcurementPlanFieldValue);
        return updateById(srmProcurementPlanFieldValue);
	}
	
	@Override
    public void template(HttpServletResponse response) {
		// 创建目录及导入模板文件
		isExcelExist();
		// 读取导入模板
		FileInputStream inputStream = null;
		ServletOutputStream outputStream = null;
		try {
			String oriFileName = "采购计划字段值表导入模板";
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding("utf-8");
			String fileName = URLEncoder.encode(oriFileName, "UTF-8").replaceAll("\\+", "%20");
			response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
			Path result = ExcelTemplateGenerator.getResult();
			Path filePath = result.resolve("采购计划字段值表srmProcurementPlanFieldValue.xlsx");
			// 绝对路径
			File file = new File(filePath.toString());
			inputStream = new FileInputStream(file);

			// 获取输出流
			outputStream = response.getOutputStream();

			// 将输入流的内容写入到输出流中
			byte[] buffer = new byte[1024];
			int bytesRead;
			while ((bytesRead = inputStream.read(buffer)) != -1) {
				outputStream.write(buffer, 0, bytesRead);
			}

			// 确保输出流的数据完全写入
			outputStream.flush();
			log.info("模板下载成功");
		} catch (IOException e) {
			System.out.println("e = " + e.getMessage());
			log.info("模板下载失败");
		} finally {
			// 关闭流
			try {
				if (inputStream != null) {
					inputStream.close();
				}
				if (outputStream != null) {
					outputStream.close();
				}
			} catch (IOException e) {
				log.error("关闭流时发生错误: {}", e.getMessage());
			}
		}
	}

	@Override
	@Transactional
	public void uploadData(MultipartFile file, HttpServletResponse response) {
		try {
			List<SrmProcurementPlanFieldValueReq> importGoodsList = new ExcelAnalysisUtil<SrmProcurementPlanFieldValueReq>()
					.readFile(file, SrmProcurementPlanFieldValueReq.class, 1);
			if (CollectionUtils.isEmpty(importGoodsList)) {
				writeImportResponse(response, 1, "导入数据为空");
				return;
			}
			List<SrmProcurementPlanFieldValueEntity> srmProcurementPlanFieldValueEntities = BeanUtil.copyToList(importGoodsList, SrmProcurementPlanFieldValueEntity.class);
			// 做字段的必传校验
			for (SrmProcurementPlanFieldValueEntity srmProcurementPlanFieldValueEntity : srmProcurementPlanFieldValueEntities) {
				// 默认值，可修改
				if (srmProcurementPlanFieldValueEntity.getCreateById() == null){
					srmProcurementPlanFieldValueEntity.setCreateById(1792846774462181377L);
				}
				if (srmProcurementPlanFieldValueEntity.getUpdateById() == null){
					srmProcurementPlanFieldValueEntity.setUpdateById(1792846774462181377L);
				}
			}
			// 保存数据
			this.saveOrUpdateBatch(srmProcurementPlanFieldValueEntities);
		} catch (IOException e) {
			log.info("文件解析失败");
			throw new RuntimeException(e);
		}
	}

	@Override
	public void physicsRemoveByPlanDetailIds(List<Long> planDetailIds, Long tenantId) {
		srmProcurementPlanFieldValueMapper.physicsRemoveByPlanDetailIds(planDetailIds, tenantId);
	}

	private void writeImportResponse(HttpServletResponse response, int code, String errorMsg) throws IOException {
		JSONObject obj = new JSONObject();
		response.setContentType("text/html;charset=utf-8");
		obj.put("code", code);
		obj.put("msg", errorMsg);
		PrintWriter writer = response.getWriter();
		writer.write(obj.toJSONString());
		writer.close();
	}

	private static void isExcelExist() {
		try {
			Path result = ExcelTemplateGenerator.getResult();

			File file = new File(result.toString());
			if (file.exists()) {
				log.info("excel文件夹路径存在");
			} else {
				// 创建文件夹
				if (file.mkdirs()) {
					log.info("目录已创建: {}" , file);
				} else {
					log.info("创建目录失败: {}", file);
					throw new RuntimeException("创建目录失败: " + file);
				}
			}
			// 设置 Excel 文件的完整路径
			Path filePath = result.resolve("采购计划字段值表srmProcurementPlanFieldValue.xlsx");
			// 生成模板文件
			ExcelTemplateGenerator.createExcelTemplate(filePath.toString(),SrmProcurementPlanFieldValueReq.class);
		} catch (UnsupportedEncodingException e) {
			log.info("编码失败");
		}
	}
}