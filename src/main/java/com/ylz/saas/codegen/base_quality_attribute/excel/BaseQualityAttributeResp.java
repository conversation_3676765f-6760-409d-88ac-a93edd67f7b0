package com.ylz.saas.codegen.base_quality_attribute.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 质量属性表
 *
 * <AUTHOR>
 */
@Data
public class BaseQualityAttributeResp extends Model<BaseQualityAttributeResp> {

    /**
     * 属性编码
     */
    @ExcelProperty(value = "属性编码")
    private String attributeCode;

    /**
     * 属性名称
     */
    @ExcelProperty(value = "属性名称")
    private String attributeName;

    /**
     * 属性描述
     */
    @ExcelProperty(value = "属性描述")
    private String description;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;


    /**
     * 状态（DISABLED-禁用、ENABLED-启用）
     */
    @ExcelProperty(value = "状态（DISABLED-禁用、ENABLED-启用）")
    private String status;
}