package com.ylz.saas.codegen.base_usage_location.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 使用地点信息表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:41:24
 */
@Data
public class BaseUsageLocationResp implements Serializable {

    /**
  	 * 主键ID
   	 */
	@ExcelProperty(value = "主键ID")
    private Long id;

    /**
  	 * 组织名称
   	 */
	@ExcelProperty(value = "组织名称")
    private String deptName;

    /**
  	 * 牧场名称（使用地点名称）
   	 */
	@ExcelProperty(value = "牧场名称")
    private String locationName;

    /**
  	 * 省
   	 */
	@ExcelProperty(value = "省")
    private String province;

    /**
  	 * 市
   	 */
	@ExcelProperty(value = "市")
    private String city;

    /**
  	 * 区
   	 */
	@ExcelProperty(value = "区")
    private String district;

    /**
  	 * 详细地址
   	 */
	@ExcelProperty(value = "详细地址")
    private String address;



    /**
  	 * 状态
   	 */
	@ExcelProperty(value = "状态")
    private String status;



    /**
  	 * 创建人
   	 */
	@ExcelProperty(value = "创建人")
    private String createBy;

    /**
  	 * 创建时间
   	 */
	@ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
	@ExcelProperty(value = "修改人")
    private String updateBy;

    /**
  	 * 修改时间
   	 */
	@ExcelProperty(value = "修改时间")
    private LocalDateTime updateTime;




}