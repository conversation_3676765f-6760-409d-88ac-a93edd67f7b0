<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenderScoreTemplateMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenderScoreTemplate">
            <id property="id" column="id" />
            <result property="tenantId" column="tenant_id" />
            <result property="deptId" column="dept_id" />
            <result property="templateName" column="template_name" />
            <result property="templateCode" column="template_code" />
            <result property="templateDesc" column="template_desc" />
            <result property="delFlag" column="del_flag" />
            <result property="createBy" column="create_by" />
            <result property="createByName" column="create_by_name" />
            <result property="createTime" column="create_time" />
            <result property="createById" column="create_by_id" />
            <result property="updateById" column="update_by_id" />
            <result property="updateBy" column="update_by" />
            <result property="updateByName" column="update_by_name" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,template_name,template_code,template_desc,
        del_flag,create_by,create_by_name,create_time,create_by_id,
        update_by_id,update_by,update_by_name,update_time
    </sql>



    <select id="selectPage" resultType="com.ylz.saas.entity.SrmTenderScoreTemplate">
        SELECT *
        FROM srm_tender_score_template
        <where>
            del_flag = 0
            <if test="templateName != null and templateName != ''">
                AND template_name LIKE CONCAT('%', #{templateName}, '%')
            </if>
            <if test="templateCode != null and templateCode != ''">
                AND template_code LIKE CONCAT('%', #{templateCode}, '%')
            </if>
        </where>
    </select>


    <select id="getMaxTemplateCodeForUpdate" resultType="string">
        SELECT MAX(template_code) FROM srm_tender_score_template FOR UPDATE
    </select>





</mapper>
