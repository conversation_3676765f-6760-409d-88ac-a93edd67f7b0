package com.ylz.saas.codegen.srm_procurement_plan_field_value.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 采购计划字段值表
 *
 * <AUTHOR>
 * @date 2025-06-03 21:00:11
 */
@Data
public class SrmProcurementPlanFieldValueResp implements Serializable {

    /**
  	 * 主键ID
   	 */
	@ExcelProperty(value = "主键ID")
    private Long id;

    /**
  	 * 组织ID
   	 */
	@ExcelProperty(value = "组织ID")
    private Long deptId;

    /**
  	 * 采购计划明细ID
   	 */
	@ExcelProperty(value = "采购计划明细ID")
    private Long planDetailId;

    /**
  	 * 服务类型ID
   	 */
	@ExcelProperty(value = "服务类型ID")
    private Long serviceTypeId;

    /**
  	 * 字段ID
   	 */
	@ExcelProperty(value = "字段ID")
    private Long fieldId;

    /**
  	 * 字段编码
   	 */
	@ExcelProperty(value = "字段编码")
    private String fieldCode;

    /**
  	 * 字段类型（string-字符串、int-整数、decimal-小数、date-日期、datetime-日期时间、enum-枚举）
   	 */
	@ExcelProperty(value = "字段类型（string-字符串、int-整数、decimal-小数、date-日期、datetime-日期时间、enum-枚举）")
    private String fieldType;

    /**
  	 * 字段值
   	 */
	@ExcelProperty(value = "字段值")
    private String fieldValue;

    /**
  	 * 创建人名称
   	 */
	@ExcelProperty(value = "创建人名称")
    private String createByName;

    /**
  	 * 修改人名称
   	 */
	@ExcelProperty(value = "修改人名称")
    private String updateByName;







}