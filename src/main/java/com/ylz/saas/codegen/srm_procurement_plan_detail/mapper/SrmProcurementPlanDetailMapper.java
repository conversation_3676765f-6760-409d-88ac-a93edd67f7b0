package com.ylz.saas.codegen.srm_procurement_plan_detail.mapper;

import com.ylz.saas.codegen.srm_procurement_plan_detail.entity.SrmProcurementPlanDetailEntity;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import com.ylz.saas.common.data.datascope.annotation.DataPermission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
//@DataPermission(userEnabled = true, dataPermissionRuleList = "[{\"dataTypeCode\":\"PLAN\",\"targetField\":\"plan_id\",\"dataSourceField\":\"ID\"}]")
public interface SrmProcurementPlanDetailMapper extends SaasBaseMapper<SrmProcurementPlanDetailEntity> {


    boolean physicsRemoveByPlanId(@Param("pid")Long planId, @Param("tid")Long tenantId);
}