package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import com.ylz.saas.common.core.util.TenantTable;
import lombok.Data;

/**
 * 评审/评分项表
 * @TableName srm_tender_score_template_item
 */
@TableName(value ="srm_tender_score_template_item")
@TenantTable
@Data
public class SrmTenderScoreTemplateItem {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 组织ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 名称
     */
    private String name;

    /**
     * 详情
     */
    private String detail;

    /**
     * 节点名称
     */
    private String node;

    /**
     * 类型(REVIEW-评审项、SCORE-评分项)
     */
    private String type;

    /**
     * 不符合否决投标数量(评审)
     */
    private Integer rejectCount;

    /**
     * 总分(评分)
     */
    private BigDecimal totalScore;

    /**
     * 权重(评分)
     */
    private BigDecimal weight;

    /**
     * 分值下限(评分)
     */
    private BigDecimal minScore;

    /**
     * 分值上限(评分)
     */
    private BigDecimal maxScore;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createById;

    /**
     * 更新人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateById;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}