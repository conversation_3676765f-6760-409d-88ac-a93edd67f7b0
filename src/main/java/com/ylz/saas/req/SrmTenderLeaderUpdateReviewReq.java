package com.ylz.saas.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 组长修改评审结果请求类
 * <AUTHOR>
 * @createDate 2025-07-14
 */
@Data
@Schema(description = "组长修改评审结果请求类")
public class SrmTenderLeaderUpdateReviewReq implements Serializable {

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 租户供应商ID
     */
    @NotNull(message = "租户供应商ID不能为空")
    @Schema(description = "租户供应商ID")
    private Long tenantSupplierId;

    /**
     * 修改的评审结果列表
     */
    @Schema(description = "修改的评审结果列表")
    private List<ReviewUpdateInfo> reviewUpdateList;

    /**
     * 评审结果修改信息
     */
    @Data
    @Schema(description = "评审结果修改信息")
    public static class ReviewUpdateInfo implements Serializable {

        /**
         * 评分记录ID
         */
        @NotNull(message = "评分记录ID不能为空")
        @Schema(description = "评分记录ID")
        private Long scoringId;

        /**
         * 是否符合（评审项）1-符合，0-不符合
         */
        @Schema(description = "是否符合（评审项）1-符合，0-不符合")
        private Integer isConform;

        /**
         * 评审结论
         */
        @Schema(description = "评审结论")
        private String conclusion;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
