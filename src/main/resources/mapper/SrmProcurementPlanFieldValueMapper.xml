<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ylz.saas.codegen.srm_procurement_plan_field_value.mapper.SrmProcurementPlanFieldValueMapper">
    <delete id="physicsRemoveByPlanDetailIds">
        DELETE FROM srm_procurement_plan_field_value
        WHERE del_flag = 0
          AND plan_detail_id IN
        <foreach collection="pdids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
