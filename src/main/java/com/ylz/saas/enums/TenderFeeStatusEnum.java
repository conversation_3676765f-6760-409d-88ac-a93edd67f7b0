package com.ylz.saas.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @<PERSON> huanghong
 * @Date 2025/7/14 10:54
 * @Description
 */
@Getter
@AllArgsConstructor
public enum TenderFeeStatusEnum {

    WITHDRAWN("WITHDRAWN", "已撤回"),
    UNPAID("UNPAID", "未缴纳"),
    PAID("PAID", "已缴纳"),
    VERIFIED("VERIFIED", "已核实"),
    REJECTED("REJECTED", "被拒绝"),
    ;

    @EnumValue
    @JsonValue
    private final String code;
    private final String desc;


}
