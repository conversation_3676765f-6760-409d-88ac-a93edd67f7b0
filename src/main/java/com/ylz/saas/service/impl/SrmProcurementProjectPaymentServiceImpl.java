package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmProcurementProjectPayment;
import com.ylz.saas.service.SrmProcurementProjectPaymentService;
import com.ylz.saas.mapper.SrmProcurementProjectPaymentMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【srm_procurement_project_payment(采购立项支付方式表)】的数据库操作Service实现
* @createDate 2025-06-11 11:21:09
*/
@Service
public class SrmProcurementProjectPaymentServiceImpl extends ServiceImpl<SrmProcurementProjectPaymentMapper, SrmProcurementProjectPayment>
    implements SrmProcurementProjectPaymentService{

}




