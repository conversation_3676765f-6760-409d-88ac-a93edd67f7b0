package com.ylz.saas.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 评标进度查询请求类
 */
@Data
@Schema(description = "评标进度查询请求")
public class SrmTenderEvaluationProgressReq implements Serializable {

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 评标委员会ID
     */
    @Schema(description = "评标委员会ID")
    private Long evaluationId;

    /**
     * 当前报价轮次（可选，如果提供evaluationId则不需要）
     */
    @Schema(description = "当前报价轮次（可选，如果提供evaluationId则不需要）")
    @NotNull(message = "当前报价轮次不能为空")
    private Integer currentRound;

    private static final long serialVersionUID = 1L;
}
