package com.ylz.saas.req;

import com.ylz.saas.vo.BidderResponseVo;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/10 16:02
 * @Description
 */
@Data
public class SrmTenderSupplierResponseApplyReq {

    /**
     * 公告id
     */
    @NotNull(message = "公告id不能为空")
    private Long noticeId;

    /**
     * 标段id
     */
    @NotNull(message = "标段id不能为空")
    private Long sectionId;

    /**
     * 联系人
     */
    @NotBlank(message = "联系人不能为空")
    private String contactPerson;

    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空")
    private String contactPhone;

    /**
     * 联系邮箱
     */
    private String contactEmail;

    /**
     * 邀请回执
     */
    private String inviteReceipt;

    /**
     * 附件名称
     */
    private String attachmentName;

    /**
     * 附件
     */
    private String attachmentPath;

    /**
     * 投标响应列表
     */
    @Valid
    private List<BidderResponseVo> bidderResponseList;

}
