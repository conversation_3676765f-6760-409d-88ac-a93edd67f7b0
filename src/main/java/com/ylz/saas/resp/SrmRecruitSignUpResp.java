package com.ylz.saas.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.lark.oapi.service.attendance.v1.enums.ApprovalStatusEnum;
import com.ylz.saas.entity.SrmRecruitSupplierSignUp;
import com.ylz.saas.req.SrmRecruitSignUpReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 招募申请详情返回类
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@Schema(description = "招募申请详情返回类")
public class SrmRecruitSignUpResp extends SrmRecruitSupplierSignUp implements Serializable {

    /**
     * 资质材料
     */
    @Schema(description = "资质材料")
    private List<SrmRecruitSignUpResp.SignUpCertificate> signUpCertificates;

    @Data
    public static class SignUpCertificate {

        /**
         * 文件地址
         */
        @Schema(description = "文件地址")
        private String documentUrl;

        /**
         * 文件名称
         */
        @Schema(description = "文件地址")
        private String documentName;



    }


    @Serial
    private static final long serialVersionUID = 1L;
}
