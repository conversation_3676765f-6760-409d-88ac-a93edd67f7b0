package com.ylz.saas.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/7/9 15:12
 * @Description 创建用户时默认的身份与角色
 */

public class DefaultRoleEnum {


    /**
     * 角色
     */
    @Getter
    @AllArgsConstructor
    public enum Role {

        PURCHASER_ROLE("采购方"),
        SUPPLIER_ROLE("供应方"),
        BID_EXPERT_ROLE("评标专家"),
        BID_AGENCY_ROLE("招标代理机构");

        private final String desc;

    }

    /**
     * 身份code
     */
    @Getter
    @AllArgsConstructor
    public enum IdentifyCode {

        PURCHASER_IDENTITY("采购方"),
        SUPPLIER_IDENTITY("供应方"),
        BID_EXPERT_IDENTITY("评标专家"),
        BID_AGENCY_IDENTITY("招标代理机构");
        public static IdentifyCode getIdentifyCode(String desc) {
            for (IdentifyCode value : values()) {
                if (value.name().equals(desc)) {
                    return value;
                }
            }
            return null;
        }

        private final String desc;

    }


}
