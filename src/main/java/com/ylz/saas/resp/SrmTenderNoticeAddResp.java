package com.ylz.saas.resp;

import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.PublicNoticeStatusEnum;
import com.ylz.saas.enums.TenderWayEnum;
import com.ylz.saas.req.AttachmentInfoReq;
import com.ylz.saas.req.SrmTenderNoticeAddReq;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 招标公告信息
 */
@Data
public class SrmTenderNoticeAddResp implements Serializable {

    /**
     * 公告ID
     */
    private Long id;

    /**
     * 公告标题
     */
    private String noticeTitle;

    /**
     * 公告编号
     */
    private String tenderNoticeCode;

    /**
     * 引用模版ID
     */
    private Long noticeTemplateId;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 采购立项ID
     */
    private Long projectId;

    /**
     * 公告内容（富文本）
     */
    private String noticeContent;

    /**
     * 采购时间要求
     */
    private SrmTenderNoticeAddReq.PurchaseDateDemand purchaseDateDemand;

    /**
     * 报价要求
     */
    private SrmTenderNoticeAddReq.QuotationDemand quotationDemand;

    /**
     * 标书费是否收取
     */
    private Boolean bidFeeCollection;

    /**
     * 资质要求、报名响应条件、保证金设置
     */
    private List<SrmTenderNoticeAddReq.BidsSegment> bidsSegments;

    /**
     * 报价须知
     */
    private String quotationNotice;

    /**
     * 评审规则(COMPREHENSIVE-综合评标低价法、LOWEST_PRICE-最低价法、COMPREHENSIVE_SCORE-综合评分法、HIGHEST_PRICE-最高价法)
     */
    private String evaluationMethod;

    /**
     * 联系方式
     */
    private SrmTenderNoticeAddReq.ContactInfo contactInfo;

    /**
     * 附件信息
     */
    private List<AttachmentInfoReq> attachmentInfos;

    /**
     * 浏览次数
     */
    private Integer viewCount;


    /**
     * 招标审核状态
     * TO_APPROVE("待审批"),
     *     APPROVING("审批中"),
     *     APPROVE("审批通过"),
     *     APPROVE_REJECT("审批驳回"),
     *     APPROVE_REVOKE("审批撤销"),
     */
    private ApproveStatusEnum noticeStatus;

    /**
     * 公告的公告状态
     */
    private PublicNoticeStatusEnum status;

    /**
     * 开标方式
     */
    private TenderWayEnum tenderWay;

    /**
     * 开标地点
     */
    private String bidOpeningAddress;

}