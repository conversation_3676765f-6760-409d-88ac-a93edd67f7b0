package com.ylz.saas.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 报价明细（供应商）查询请求参数
 * <AUTHOR>
 * @createDate 2025-07-17
 */
@Data
@Schema(description = "报价明细（供应商）查询请求参数")
public class SrmTenderQuoteDetailBySupplierQueryReq {

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    @Schema(description = "采购立项ID", required = true)
    private Long projectId;

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    @Schema(description = "标段ID", required = true)
    private Long sectionId;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    @Schema(description = "招标公告ID", required = true)
    private Long noticeId;

    /**
     * 供应商名称（可选，用于筛选）
     */
    @Schema(description = "供应商名称（可选，用于筛选）")
    private String supplierName;
}
