package com.ylz.saas.codegen.srm_procurement_plan_detail.param;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/23 09:49
 * @Description
 */
@Data
public class SrmProcurementPlanDetailQueryParam {

    /**
     * 计划id
     */
    @NotNull(message = "计划id不能为空")
    private List<Long> ids;

    /**
     * 计划编号/名称
     */
    private String searchContent;

    /**
     * 需求行号
     */
    private String requireNo;

    /**
     * 物料编码/名称
     */
    private String materialSearchContent;


}
