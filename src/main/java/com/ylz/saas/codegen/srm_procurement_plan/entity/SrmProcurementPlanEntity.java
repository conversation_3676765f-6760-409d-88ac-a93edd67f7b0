package com.ylz.saas.codegen.srm_procurement_plan.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ylz.saas.codegen.base_service_type.entity.BaseServiceTypeEntity;
import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.codegen.srm_procurement_plan.service.SrmProcurementPlanService;
import com.ylz.saas.codegen.srm_procurement_plan_detail.entity.SrmProcurementPlanDetailEntity;
import com.ylz.saas.common.core.util.TenantTable;
import com.ylz.saas.enums.ApproveStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 采购计划表
 *
 * <AUTHOR>
 * @date 2025-06-03 20:39:40
 */
@Data
@TableName("srm_procurement_plan")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "采购计划表")
@TenantTable
public class SrmProcurementPlanEntity extends Model<SrmProcurementPlanEntity> {


    /**
  	 * 主键ID
   	 */
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

    /**
  	 * 组织ID
   	 */
    @Schema(description="组织ID")
    private String deptId;

    /**
  	 * 采购计划编号
   	 */
    @Schema(description="采购计划编号")
    private String planCode;

    /**
  	 * 采购计划名称
   	 */
    @Schema(description="采购计划名称")
    private String planName;

    /**
  	 * 服务类型ID
   	 */
    @Schema(description="服务类型ID")
    private Long serviceTypeId;

    /**
     * 服务类型名
     */
    @Schema(description="服务类型名")
    private String serviceTypeName;

    /**
  	 * 申请日期
   	 */
    @Schema(description="申请日期")
    private LocalDate applyDate;

    /**
  	 * 申请部门
   	 */
    @Schema(description="申请部门")
    private String applyDeptId;

    /**
  	 * 申请人
   	 */
    @Schema(description="申请人")
    private Long applicant;

    /**
  	 * 申请人电话
   	 */
    @Schema(description="申请人电话")
    private String applicantPhone;

    /**
  	 * 采购部门
   	 */
    @Schema(description="采购部门")
    private String procurementDeptId;

    /**
  	 * 计划招标方式
   	 */
    @Schema(description="计划招标方式")
    private BaseServiceTypeFieldService.BuyWayEnum planRecruitMethod;

    /**
  	 * 期限到货日期
   	 */
    @Schema(description="期限到货日期")
    private LocalDate periodStartDate;

    /**
  	 * 采购方式
   	 */
    @Schema(description="采购方式")
    private String procurementMethod;

    /**
  	 * 资金来源
   	 */
    @Schema(description="资金来源")
    private String fundSource;

    /**
  	 * 预算金额
   	 */
    @Schema(description="预算金额")
    private BigDecimal budgetAmount;

    /**
     * 计划来源方式
     */
    @Schema(description="计划来源方式")
    private SrmProcurementPlanService.SourceEnum source;

    /**
  	 * 单据状态
   	 */
    @Schema(description="单据状态")
    private SrmProcurementPlanService.DocumentStatus status;

    /**
  	 * 创建人名称
   	 */
    @Schema(description="创建人名称")
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    @Schema(description="修改人名称")
    private String updateByName;

    /**
  	 * 当前审批人
   	 */
    @Schema(description="当前审批人")
    private String currentApprover;

    /**
     * 审批状态
     */
    @Schema(description="审批状态")
    private ApproveStatusEnum approveStatus;

    /**
     * 附件
     */
    @Schema(description="附件")
    private String attachment;

    /**
     * 备注
     */
    @Schema(description="备注")
    private String remarks;

    /**
  	 * 代理机构表ID
   	 */
    @Schema(description="代理机构表ID")
    private Long baseAgentDeptId;

    /**
  	 * 创建人
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
  	 * 创建时间
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

    /**
  	 * 修改时间
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标识（0-正常、1-删除）")
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    @Schema(description="租户ID")
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人ID")
    private Long createById;

    /**
  	 * 更新人ID
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人ID")
    private Long updateById;

    /**
     * 申请理由
     */
    @Schema(description="申请理由")
    private String applyReason;

    /**
     * 收货地址
     */
    @Schema(description="收货地址")
    private String shippingAddress;

    /**
     * 供应商要求
     */
    @Schema(description="供应商要求")
    private String supplierRequirements;

    /**
     * 申请人名
     */
    @TableField(exist = false)
    private String applicantName;

    /**
     * 申请部门明
     */
    @TableField(exist = false)
    private String applyDeptName;

    /**
     * 采购部门名
     */
    @TableField(exist = false)
    private String procurementDeptName;

    /**
     * 业务配置信息
     */
    @TableField(exist = false)
    private BaseServiceTypeEntity baseServiceTypeEntity;

    /**
     * 子表集合:srm_procurement_plan_detail
     */
    @ExcelIgnore
    @TableField(exist = false)
    private List<SrmProcurementPlanDetailEntity> srmProcurementPlanDetailList;

}