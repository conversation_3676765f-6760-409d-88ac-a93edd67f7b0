package com.ylz.saas.codegen.base_material_category.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.codegen.base_material_category.entity.BaseMaterialCategoryEntity;
import com.ylz.saas.codegen.base_material_category.excel.BaseMaterialCategoryResp;
import com.ylz.saas.codegen.base_material_category.param.BaseMaterialCategoryParam;
import com.ylz.saas.codegen.base_material_category.service.BaseMaterialCategoryService;
import com.ylz.saas.codegen.base_material_category.vo.BaseMaterialCategoryTreeVo;
import com.ylz.saas.codegen.base_material_category.vo.BaseMaterialCategoryVo;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.common.excel.annotation.ResponseExcel;
import com.ylz.saas.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * 物料分类表
 *
 * <AUTHOR>
 * @date 2025-06-03 17:25:41
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/baseMaterialCategory" )
@Tag(description = "baseMaterialCategory" , name = "物料分类表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class BaseMaterialCategoryController {

    private final BaseMaterialCategoryService baseMaterialCategoryService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param param 物料分类表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @PostMapping("/page" )
    public R getBaseMaterialCategoryPage(@RequestBody BaseMaterialCategoryParam param, Page page) {
        QueryWrapper<BaseMaterialCategoryEntity> queryWrapper = param.toWrapper(); 
        return R.ok(baseMaterialCategoryService.page(page, queryWrapper));
    }


    /**
     * 通过id查询物料分类表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(baseMaterialCategoryService.getById(id));
    }

    /**
     * 新增物料分类表
     * @param baseMaterialCategoryVo 物料分类表
     * @return R
     */
    @Operation(summary = "新增物料分类表" , description = "新增物料分类表" )
    @SysLog("新增物料分类表" )
    @PostMapping("/save")
    public R save(@Valid @RequestBody BaseMaterialCategoryVo baseMaterialCategoryVo) {
        return R.ok(baseMaterialCategoryService.saveBaseMaterialCategory(baseMaterialCategoryVo));
    }

    /**
     * 修改物料分类表
     * @param baseMaterialCategoryVo 物料分类表
     * @return R
     */
    @Operation(summary = "修改物料分类表" , description = "修改物料分类表" )
    @SysLog("修改物料分类表" )
    @PostMapping("/update")
    public R updateById(@Valid @RequestBody BaseMaterialCategoryVo baseMaterialCategoryVo) {
        return R.ok(baseMaterialCategoryService.updateBaseMaterialCategory(baseMaterialCategoryVo));
    }

    /**
     * 通过id删除物料分类表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除物料分类表" , description = "通过id删除物料分类表" )
    @SysLog("通过id删除物料分类表" )
    @GetMapping("remove")
    public R removeById(@RequestParam List<Long> ids) {
		boolean remove = baseMaterialCategoryService.removeByIds(ids);
		return R.ok(remove);
    }


    /**
     * 导出excel 表格
     * @param param 查询条件
     * @return excel 文件流
     */
    @ResponseExcel
    @PostMapping("/export")
	@Operation(summary = "导出excel" , description = "导出excel" )
    public List<BaseMaterialCategoryResp> export(@RequestBody BaseMaterialCategoryParam param) {
		List<BaseMaterialCategoryEntity> entityList = baseMaterialCategoryService.listInfo(param);
        return BeanUtil.copyToList(entityList, BaseMaterialCategoryResp.class);
    }
	
	/**
     * 导入excel 表格
     * @param file
     * @param response
     * @return
     */
    @PostMapping("/import")
    @Operation(summary = "导入excel" , description = "导入excel" )
    public R uploadData(@RequestParam("file") MultipartFile file,HttpServletResponse response) {
        baseMaterialCategoryService.uploadData(file,response);
        return R.ok();
    }

    /**
     * excel模板下载
     * @param response
     */
    @GetMapping("/template")
    @Operation(summary = "excel模板下载" , description = "excel模板下载" )
    public void template(HttpServletResponse response) {
        baseMaterialCategoryService.template(response);
    }

    /**
     * 启用/禁用
     * @return R
     */
    @Operation(summary = "启用/禁用" , description = "启用/禁用" )
    @PostMapping("/switch/{id}")
    public R switchStatus(@PathVariable("id") Long id) {
        return R.ok(baseMaterialCategoryService.switchStatus(id));
    }

    /**
     * 获取物料分类树形结构
     * @return R
     */
    @Operation(summary = "获取物料分类树形结构" , description = "获取物料分类树形结构" )
    @GetMapping("/tree")
    public R<List<BaseMaterialCategoryTreeVo>> getTreeList() {
        return R.ok(baseMaterialCategoryService.getTreeList());
    }

    /**
     * 根据状态获取物料分类树形结构
     * @param status 状态（DISABLED-禁用、ENABLED-启用）
     * @return R
     */
    @Operation(summary = "根据状态获取物料分类树形结构" , description = "根据状态获取物料分类树形结构" )
    @GetMapping("/tree/status/{status}")
    public R<List<BaseMaterialCategoryTreeVo>> getTreeListByStatus(@PathVariable("status") String status) {
        return R.ok(baseMaterialCategoryService.getTreeListByStatus(status));
    }

    /**
     * 根据父级编码获取子节点树形结构
     * @param parentCategoryCode 父级分类编码
     * @return R
     */
    @Operation(summary = "根据父级编码获取子节点树形结构" , description = "根据父级编码获取子节点树形结构" )
    @GetMapping("/tree/parent/{parentCategoryCode}")
    public R<List<BaseMaterialCategoryTreeVo>> getTreeListByParent(@PathVariable("parentCategoryCode") String parentCategoryCode) {
        return R.ok(baseMaterialCategoryService.getTreeListByParent(parentCategoryCode));
    }

    /**
     * 获取所有子节点编码
     * @param parentCategoryCode 父级分类编码
     * @return R
     */
    @Operation(summary = "获取所有子节点编码" , description = "获取所有子节点编码（包括子节点的子节点）" )
    @GetMapping("/children/{parentCategoryCode}")
    public R<List<String>> getAllChildrenCodes(@PathVariable("parentCategoryCode") String parentCategoryCode) {
        return R.ok(baseMaterialCategoryService.getAllChildrenCodes(parentCategoryCode));
    }

    /**
     * 获取所有父节点编码
     * @param categoryCode 分类编码
     * @return R
     */
    @Operation(summary = "获取所有父节点编码" , description = "获取所有父节点编码（包括父节点的父节点）" )
    @GetMapping("/parents/{categoryCode}")
    public R<List<String>> getAllParentCodes(@PathVariable("categoryCode") String categoryCode) {
        return R.ok(baseMaterialCategoryService.getAllParentCodes(categoryCode));
    }
}