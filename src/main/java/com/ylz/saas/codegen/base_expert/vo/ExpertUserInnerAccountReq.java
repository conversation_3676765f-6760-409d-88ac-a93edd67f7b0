package com.ylz.saas.codegen.base_expert.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/10 16:05
 * @Description
 */
@Data
public class ExpertUserInnerAccountReq {
    /**
     * 登录账号
     */
    @Schema(description="登录账号")
    private String loginAccount;

    /**
     * 手机号
     */
    @Schema(description="手机号")
    private String phone;
    /**
     * 不包括的用户id
     */
    @Schema(description="不包括的用户id")
    private List<Long> notInUserIds;
}
