package com.ylz.saas.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 供应商评审项详情查询响应类
 * <AUTHOR>
 * @createDate 2025-07-14
 */
@Data
@Schema(description = "供应商评审项详情查询响应类")
public class SrmTenderSupplierReviewDetailQueryResp implements Serializable {

    /**
     * 供应商ID
     */
    @Schema(description = "供应商ID")
    private Long tenantSupplierId;

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    private String supplierName;

    /**
     * 评审项节点列表
     */
    @Schema(description = "评审项节点列表")
    private List<String> reviewNodes;

    /**
     * 专家评审详情列表
     */
    @Schema(description = "专家评审详情列表")
    private List<ExpertReviewDetail> expertReviewList;

    /**
     * 专家评审详情
     */
    @Data
    @Schema(description = "专家评审详情")
    public static class ExpertReviewDetail implements Serializable {

        /**
         * 专家用户ID
         */
        @Schema(description = "专家用户ID")
        private Long userId;

        /**
         * 专家名称
         */
        @Schema(description = "专家名称")
        private String expertName;

        /**
         * 专家编码
         */
        @Schema(description = "专家编码")
        private String expertCode;

        /**
         * 专家分类
         */
        @Schema(description = "专家分类")
        private String expertCategory;

        /**
         * 角色（评标组长/评标成员）
         */
        @Schema(description = "角色（评标组长/评标成员）")
        private String role;

        /**
         * 各节点评审结果
         */
        @Schema(description = "各节点评审结果")
        private List<NodeReviewResult> nodeReviewResults;

        private static final long serialVersionUID = 1L;
    }

    /**
     * 节点评审结果
     */
    @Data
    @Schema(description = "节点评审结果")
    public static class NodeReviewResult implements Serializable {

        /**
         * 评分记录ID
         */
        @Schema(description = "评分记录ID")
        private Long scoringId;

        /**
         * 评标标准ID
         */
        @Schema(description = "评标标准ID")
        private Long standardId;

        /**
         * 节点名称
         */
        @Schema(description = "节点名称")
        private String nodeName;

        /**
         * 评审项名称
         */
        @Schema(description = "评审项名称")
        private String itemName;

        /**
         * 评审项描述
         */
        @Schema(description = "评审项描述")
        private String itemDescription;

        /**
         * 是否符合（1-符合，0-不符合）
         */
        @Schema(description = "是否符合（1-符合，0-不符合）")
        private Integer isConform;

        /**
         * 是否符合描述
         */
        @Schema(description = "是否符合描述")
        private String isConformDesc;

        /**
         * 评审结论
         */
        @Schema(description = "评审结论")
        private String conclusion;

        /**
         * 评审时间
         */
        @Schema(description = "评审时间")
        private LocalDateTime reviewTime;

        /**
         * 报价轮次
         */
        @Schema(description = "报价轮次")
        private Integer currentRound;

        private static final long serialVersionUID = 1L;
    }

    private static final long serialVersionUID = 1L;
}
