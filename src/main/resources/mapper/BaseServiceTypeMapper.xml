<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.ylz.saas.codegen.base_service_type.mapper.BaseServiceTypeMapper">

  <resultMap id="baseServiceTypeMap" type="com.ylz.saas.codegen.base_service_type.entity.BaseServiceTypeEntity">
        <id property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="typeCode" column="type_code"/>
        <result property="typeName" column="type_name"/>
        <result property="description" column="description"/>
        <result property="status" column="status"/>
        <result property="sort" column="sort"/>
        <result property="createByName" column="create_by_name"/>
        <result property="updateByName" column="update_by_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createById" column="create_by_id"/>
        <result property="updateById" column="update_by_id"/>
  </resultMap>
</mapper>