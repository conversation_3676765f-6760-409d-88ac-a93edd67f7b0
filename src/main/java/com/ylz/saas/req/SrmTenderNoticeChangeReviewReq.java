package com.ylz.saas.req;

import com.ylz.saas.enums.ApproveStatusEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 变更信息审批
 */
@Data
public class SrmTenderNoticeChangeReviewReq {

    /**
     * 招标变更ID
     */
    @NotNull(message = "招标变更ID不能为空")
    private Long changeId;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 变更审核状态
     * TO_APPROVE("待审批"),
     *     APPROVING("审批中"),
     *     APPROVE("审批通过"),
     *     APPROVE_REJECT("审批驳回"),
     *     APPROVE_REVOKE("审批撤销"),
     */
    @NotNull(message = "招标变更审核状态不能为空")
    private ApproveStatusEnum approvedStatus;

    /**
     * 审核备注
     */
    private String approvedRemark;


}