package com.ylz.saas.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 编辑中标通知书
 */
@Data
public class SrmTenderEditAwardNoticeReq {
    /**
     * 公告id
     */
    @NotNull(message = "公告id不能为空")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    private Long sectionId;

    /**
     * 租户供应商ID
     */
    @NotNull(message = "租户供应商ID不能为空")
    private Long tenantSupplierId;

    /**
     * 通知书内容
     */
    @NotBlank(message = "通知书内容不能为空")
    private String noticeContent;

}