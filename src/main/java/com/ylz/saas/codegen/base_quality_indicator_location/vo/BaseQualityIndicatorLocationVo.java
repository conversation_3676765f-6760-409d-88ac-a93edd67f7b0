package com.ylz.saas.codegen.base_quality_indicator_location.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 质量指标与场地关联表
 *
 * <AUTHOR>
 * @date 2025-06-05 19:18:27
 */
@Data
public class BaseQualityIndicatorLocationVo implements Serializable {

    /**
  	 * 质量指标ID
   	 */
    private Long indicatorId;

    /**
  	 * 使用部门ID
   	 */
    private Long locationId;

    /**
  	 * 创建人名称
   	 */
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    private String updateByName;

    /**
  	 * 创建人
   	 */
    private String createBy;

    /**
  	 * 创建时间
   	 */
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
    private String updateBy;

    /**
  	 * 修改时间
   	 */
    private LocalDateTime updateTime;

    /**
  	 * 租户ID
   	 */
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
    private Long createById;

    /**
  	 * 更新人ID
   	 */
    private Long updateById;

    /**
  	 * 组织ID
   	 */
    private Long deptId;

    /**
  	 * 主键ID
   	 */
    private Long id;
}