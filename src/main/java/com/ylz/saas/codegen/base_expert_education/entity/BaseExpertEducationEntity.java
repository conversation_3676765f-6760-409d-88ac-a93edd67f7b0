package com.ylz.saas.codegen.base_expert_education.entity;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ylz.saas.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;


/**
 * 专家教育经历表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:42:08
 */
@Data
@TableName("base_expert_education")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "专家教育经历表")
@TenantTable
public class BaseExpertEducationEntity extends Model<BaseExpertEducationEntity> {


    /**
  	 * 主键ID
   	 */
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

    /**
  	 * 组织ID
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="组织ID")
    private Long deptId;

    /**
  	 * 证书附件
   	 */
    @Schema(description="证书附件")
    private JSONArray certificate;

    /**
  	 * 专家ID
   	 */
    @Schema(description="专家ID")
    private Long expertId;

    /**
  	 * 开始时间
   	 */
    @Schema(description="开始时间")
    private LocalDate startDate;

    /**
  	 * 截止时间
   	 */
    @Schema(description="截止时间")
    private LocalDate endDate;

    /**
  	 * 毕业院校
   	 */
    @Schema(description="毕业院校")
    private String school;

    /**
  	 * 专业
   	 */
    @Schema(description="专业")
    private String major;

    /**
  	 * 学位
   	 */
    @Schema(description="学位")
    private String degree;

    /**
  	 * 创建人名称
   	 */
    @Schema(description="创建人名称")
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    @Schema(description="修改人名称")
    private String updateByName;

    /**
  	 * 创建人
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
  	 * 创建时间
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

    /**
  	 * 修改时间
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

    /**
  	 * 租户ID
   	 */
    @Schema(description="租户ID")
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人ID")
    private Long createById;

    /**
  	 * 更新人ID
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人ID")
    private Long updateById;
}