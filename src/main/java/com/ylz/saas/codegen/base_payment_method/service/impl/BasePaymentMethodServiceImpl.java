package com.ylz.saas.codegen.base_payment_method.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.codegen.base_payment_method.entity.BasePaymentMethodEntity;
import com.ylz.saas.codegen.base_payment_method.mapper.BasePaymentMethodMapper;
import com.ylz.saas.codegen.base_payment_method.service.BasePaymentMethodService;
import com.ylz.saas.codegen.base_payment_method.vo.BasePaymentMethodVo;
import com.ylz.saas.codegen.base_payment_method_period.entity.BasePaymentMethodPeriodEntity;
import com.ylz.saas.codegen.base_payment_method_period.service.BasePaymentMethodPeriodService;
import com.ylz.saas.common.enums.CodeGeneratorPrefixEnum;
import com.ylz.saas.common.enums.CommonSwitchEnum;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.sequence.generator.impl.DefaultCodeGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
/**
 * 支付方式表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:34
 */
@Service
@Slf4j
public class BasePaymentMethodServiceImpl extends ServiceImpl<BasePaymentMethodMapper, BasePaymentMethodEntity> implements BasePaymentMethodService {

	@Autowired
	private BasePaymentMethodPeriodService basePaymentMethodPeriodService;

	@Autowired
	private DefaultCodeGenerator defaultCodeGenerator;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveBasePaymentMethod(BasePaymentMethodVo basePaymentMethodVo) {
		// 1. 支付方式名称必填校验
		ExceptionUtil.check(StrUtil.isBlank(basePaymentMethodVo.getMethodName()), "500", "支付方式名称不能为空");
		
		// 2. 支付方式名称长度校验
		if (basePaymentMethodVo.getMethodName().length() > 30) {
			ExceptionUtil.check(true, "500", "支付方式名称长度不能超过30个字符");
		}
		
		// 3. 支付方式名称唯一性校验
		Long methodNameCount = this.lambdaQuery()
				.eq(BasePaymentMethodEntity::getMethodName, basePaymentMethodVo.getMethodName())
				.count();
		ExceptionUtil.check(methodNameCount > 0, "500", "支付方式名称已存在，不可重复提交");
		
		// 4. 账期名称唯一性校验（如果有账期数据）
		if (CollectionUtils.isNotEmpty(basePaymentMethodVo.getBasePaymentMethodPeriodList())) {
			validatePeriodNames(basePaymentMethodVo.getBasePaymentMethodPeriodList(), null);
		}
		
		BasePaymentMethodEntity basePaymentMethod = new BasePaymentMethodEntity();
        BeanUtil.copyProperties(basePaymentMethodVo, basePaymentMethod);
		basePaymentMethod.setMethodCode(defaultCodeGenerator.generate(CodeGeneratorPrefixEnum.ZFFS.name(),CodeGeneratorPrefixEnum.ZFFS.getLength()));
		save(basePaymentMethod);
		if (CollectionUtils.isNotEmpty(basePaymentMethodVo.getBasePaymentMethodPeriodList())) {
			basePaymentMethodVo.getBasePaymentMethodPeriodList().forEach(basePaymentMethodPeriod -> {
				basePaymentMethodPeriod.setPaymentMethodId(basePaymentMethod.getId());
			});
			basePaymentMethodPeriodService.saveBatch(basePaymentMethodVo.getBasePaymentMethodPeriodList());
		}
        return Boolean.TRUE;
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateBasePaymentMethod(BasePaymentMethodVo basePaymentMethodVo) {
		BasePaymentMethodEntity basePaymentMethod = getById(basePaymentMethodVo.getId());
		ExceptionUtil.check(basePaymentMethod == null, "500", "支付方式表不存在");
		
		// 1. 支付方式名称必填校验
		ExceptionUtil.check(StrUtil.isBlank(basePaymentMethodVo.getMethodName()), "500", "支付方式名称不能为空");
		
		// 2. 支付方式名称长度校验
		if (basePaymentMethodVo.getMethodName().length() > 30) {
			ExceptionUtil.check(true, "500", "支付方式名称长度不能超过30个字符");
		}
		
		// 3. 支付方式名称唯一性校验（排除当前记录）
		Long methodNameCount = this.lambdaQuery()
				.eq(BasePaymentMethodEntity::getMethodName, basePaymentMethodVo.getMethodName())
				.ne(BasePaymentMethodEntity::getId, basePaymentMethodVo.getId())
				.count();
		ExceptionUtil.check(methodNameCount > 0, "500", "支付方式名称已存在，不可重复提交");
		
		// 4. 账期名称唯一性校验（如果有账期数据）
		if (CollectionUtils.isNotEmpty(basePaymentMethodVo.getBasePaymentMethodPeriodList())) {
			validatePeriodNames(basePaymentMethodVo.getBasePaymentMethodPeriodList(), basePaymentMethod.getId());
		}
		
		List<BasePaymentMethodPeriodEntity> oldBasePaymentMethodPeriodList = Optional.ofNullable(basePaymentMethodPeriodService.list(
						Wrappers.<BasePaymentMethodPeriodEntity>lambdaQuery()
								.eq(BasePaymentMethodPeriodEntity::getPaymentMethodId, basePaymentMethod.getId())
				))
				.orElse(Collections.emptyList());

		List<BasePaymentMethodPeriodEntity> newBasePaymentMethodPeriodList = Optional.ofNullable(basePaymentMethodVo.getBasePaymentMethodPeriodList())
				.orElse(Collections.emptyList());

		Set<Long> newBasePaymentMethodPeriodIds = newBasePaymentMethodPeriodList.stream()
				.map(BasePaymentMethodPeriodEntity::getId)
				.filter(Objects::nonNull)
				.collect(Collectors.toSet());

		List<Long> idsBasePaymentMethodPeriodToDelete = oldBasePaymentMethodPeriodList.stream()
				.filter(old -> !newBasePaymentMethodPeriodIds.contains(old.getId()))
				.map(BasePaymentMethodPeriodEntity::getId)
				.collect(Collectors.toList());

		if (!idsBasePaymentMethodPeriodToDelete.isEmpty()) {
			basePaymentMethodPeriodService.removeByIds(idsBasePaymentMethodPeriodToDelete);
		}
		for (BasePaymentMethodPeriodEntity basePaymentMethodPeriod : newBasePaymentMethodPeriodList) {
			basePaymentMethodPeriod.setPaymentMethodId(basePaymentMethod.getId());
		}
		basePaymentMethodPeriodService.saveOrUpdateBatch(basePaymentMethodVo.getBasePaymentMethodPeriodList());
		BeanUtil.copyProperties(basePaymentMethodVo, basePaymentMethod);
        return updateById(basePaymentMethod);
	}
	
	@Override
	public BasePaymentMethodEntity getByIdBasePaymentMethod(Long id) {
		BasePaymentMethodEntity basePaymentMethod = getById(id);
		if (basePaymentMethod != null) {
			basePaymentMethod.setBasePaymentMethodPeriodList(basePaymentMethodPeriodService.list(Wrappers.<BasePaymentMethodPeriodEntity>lambdaQuery().eq(BasePaymentMethodPeriodEntity::getPaymentMethodId, basePaymentMethod.getId())));
		}
		return basePaymentMethod;
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean removeByIdsBasePaymentMethod(List<Long> ids) {
		List<BasePaymentMethodEntity> basePaymentMethod = lambdaQuery()
				.in(BasePaymentMethodEntity::getId, ids)
				.list();
		if (CollectionUtils.isNotEmpty(basePaymentMethod)) {
			removeByIds(ids);
			List<Long> basePaymentMethodPeriodBasePaymentMethodId = basePaymentMethod.stream().map(BasePaymentMethodEntity::getId).toList();
			basePaymentMethodPeriodService.remove(Wrappers.<BasePaymentMethodPeriodEntity>lambdaQuery().in(BasePaymentMethodPeriodEntity::getPaymentMethodId, basePaymentMethodPeriodBasePaymentMethodId));
		}
		return true;
	}
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean switchStatus(Long id) {
		BasePaymentMethodEntity baseExpert = getById(id);
		ExceptionUtil.check(baseExpert == null, "500", "支付方式不存在");
		if (CommonSwitchEnum.ENABLED.name().equals(baseExpert.getStatus())) {
			baseExpert.setStatus(CommonSwitchEnum.DISABLED.name());
		} else {
			baseExpert.setStatus(CommonSwitchEnum.ENABLED.name());
		}
		this.updateById(baseExpert);
		return true;
	}
	
	/**
	 * 校验账期名称的唯一性
	 * @param periodList 账期列表
	 * @param paymentMethodId 支付方式ID（更新时传入，新增时为null）
	 */
	private void validatePeriodNames(List<BasePaymentMethodPeriodEntity> periodList, Long paymentMethodId) {
		if (CollectionUtils.isEmpty(periodList)) {
			return;
		}
		// 1. 检查账期列表内部是否有重复名称
		List<String> periodNames = periodList.stream()
				.map(BasePaymentMethodPeriodEntity::getPeriodName)
				.filter(StrUtil::isNotBlank)
				.toList();
		if (periodNames.size() != periodNames.stream().distinct().count()) {
			ExceptionUtil.check(true, "500", "账期名称不能重复");
		}
		// 2. 检查账期名称长度
		for (BasePaymentMethodPeriodEntity period : periodList) {
			if (StrUtil.isNotBlank(period.getPeriodName()) && period.getPeriodName().length() > 30) {
				ExceptionUtil.check(true, "500", "账期名称长度不能超过30个字符");
			}
		}
		// 3. 检查同一支付方式下账期名称唯一性
		for (BasePaymentMethodPeriodEntity period : periodList) {
			if (StrUtil.isNotBlank(period.getPeriodName()) && paymentMethodId != null) {
				Long periodNameCount = basePaymentMethodPeriodService.lambdaQuery()
						.eq(BasePaymentMethodPeriodEntity::getPeriodName, period.getPeriodName())
						.eq(BasePaymentMethodPeriodEntity::getPaymentMethodId, paymentMethodId)
						.ne(period.getId() != null, BasePaymentMethodPeriodEntity::getId, period.getId())
						.count();
				ExceptionUtil.check(periodNameCount > 0, "500", "该支付方式下账期名称已存在：" + period.getPeriodName());
			}
		}
	}
}