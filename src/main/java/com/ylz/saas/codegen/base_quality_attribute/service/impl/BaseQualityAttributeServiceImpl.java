package com.ylz.saas.codegen.base_quality_attribute.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.codegen.base_material_category.entity.BaseMaterialCategoryEntity;
import com.ylz.saas.codegen.base_material_info.entity.BaseMaterialInfoEntity;
import com.ylz.saas.codegen.base_material_info.enums.MaterialDateSourceEnum;
import com.ylz.saas.codegen.base_material_info.excel.BaseMaterialInfoReq;
import com.ylz.saas.codegen.base_quality_attribute.entity.BaseQualityAttributeEntity;
import com.ylz.saas.codegen.base_quality_attribute.excel.BaseQualityAttributeReq;
import com.ylz.saas.codegen.base_quality_attribute.excel.BaseQualityAttributeResp;
import com.ylz.saas.codegen.base_quality_attribute.mapper.BaseQualityAttributeMapper;
import com.ylz.saas.codegen.base_quality_attribute.service.BaseQualityAttributeService;
import com.ylz.saas.codegen.base_quality_attribute.vo.BaseQualityAttributeVo;
import com.ylz.saas.codegen.base_quality_attribute_value.entity.BaseQualityAttributeValueEntity;
import com.ylz.saas.codegen.base_quality_attribute_value.service.BaseQualityAttributeValueService;
import com.ylz.saas.common.data.excel.ExcelAnalysisUtil;
import com.ylz.saas.common.data.excel.ExcelTemplateGenerator;
import com.ylz.saas.common.enums.CommonSwitchEnum;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;
/**
 * 质量属性表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:42:43
 */
@Service
@Slf4j
public class BaseQualityAttributeServiceImpl extends ServiceImpl<BaseQualityAttributeMapper, BaseQualityAttributeEntity> implements BaseQualityAttributeService {

	@Autowired
	private BaseQualityAttributeValueService baseQualityAttributeValueService;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean saveBaseQualityAttribute(BaseQualityAttributeVo baseQualityAttributeVo) {
		// 1. 必填校验
		ExceptionUtil.check(StrUtil.isBlank(baseQualityAttributeVo.getAttributeName()), "500", "属性名称不能为空");
		
		// 2. 属性名称长度校验
		if (baseQualityAttributeVo.getAttributeName().length() > 30) {
			ExceptionUtil.check(true, "500", "属性名称长度不能超过30个字符");
		}
		
		// 3. 属性名称唯一性校验
		Long nameCount = this.lambdaQuery()
				.eq(BaseQualityAttributeEntity::getAttributeName, baseQualityAttributeVo.getAttributeName())
				.count();
		ExceptionUtil.check(nameCount > 0, "500", "属性名称已存在，不可重复提交");
		
		BaseQualityAttributeEntity baseQualityAttribute = new BaseQualityAttributeEntity();
        BeanUtil.copyProperties(baseQualityAttributeVo, baseQualityAttribute);
		save(baseQualityAttribute);
		if (CollectionUtils.isNotEmpty(baseQualityAttributeVo.getBaseQualityAttributeValueList())) {
			baseQualityAttributeVo.getBaseQualityAttributeValueList().forEach(baseQualityAttributeValue -> {
				baseQualityAttributeValue.setAttributeId(baseQualityAttribute.getId());
			});
			baseQualityAttributeValueService.saveBatch(baseQualityAttributeVo.getBaseQualityAttributeValueList());
		}
        return Boolean.TRUE;
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean updateBaseQualityAttribute(BaseQualityAttributeVo baseQualityAttributeVo) {
		BaseQualityAttributeEntity baseQualityAttribute = getById(baseQualityAttributeVo.getId());
		ExceptionUtil.check(baseQualityAttribute == null, "500", "质量属性表不存在");
		
		// 1. 必填校验
		ExceptionUtil.check(StrUtil.isBlank(baseQualityAttributeVo.getAttributeName()), "500", "属性名称不能为空");
		
		// 2. 属性名称长度校验
		if (baseQualityAttributeVo.getAttributeName().length() > 30) {
			ExceptionUtil.check(true, "500", "属性名称长度不能超过30个字符");
		}
		
		// 3. 属性名称唯一性校验（排除当前记录）
		Long nameCount = this.lambdaQuery()
				.eq(BaseQualityAttributeEntity::getAttributeName, baseQualityAttributeVo.getAttributeName())
				.ne(BaseQualityAttributeEntity::getId, baseQualityAttributeVo.getId())
				.count();
		ExceptionUtil.check(nameCount > 0, "500", "属性名称已存在，不可重复提交");
		
		List<BaseQualityAttributeValueEntity> oldBaseQualityAttributeValueList = Optional.ofNullable(baseQualityAttributeValueService.list(
						Wrappers.<BaseQualityAttributeValueEntity>lambdaQuery()
								.eq(BaseQualityAttributeValueEntity::getAttributeId, baseQualityAttribute.getId())
				))
				.orElse(Collections.emptyList());

		List<BaseQualityAttributeValueEntity> newBaseQualityAttributeValueList = Optional.ofNullable(baseQualityAttributeVo.getBaseQualityAttributeValueList())
				.orElse(Collections.emptyList());

		Set<Long> newBaseQualityAttributeValueIds = newBaseQualityAttributeValueList.stream()
				.map(BaseQualityAttributeValueEntity::getId)
				.filter(Objects::nonNull)
				.collect(Collectors.toSet());

		List<Long> idsBaseQualityAttributeValueToDelete = oldBaseQualityAttributeValueList.stream()
				.filter(old -> !newBaseQualityAttributeValueIds.contains(old.getId()))
				.map(BaseQualityAttributeValueEntity::getId)
				.collect(Collectors.toList());

		if (!idsBaseQualityAttributeValueToDelete.isEmpty()) {
			baseQualityAttributeValueService.removeByIds(idsBaseQualityAttributeValueToDelete);
		}
		for (BaseQualityAttributeValueEntity baseQualityAttributeValue : newBaseQualityAttributeValueList) {
			baseQualityAttributeValue.setAttributeId(baseQualityAttribute.getId());
		}
		baseQualityAttributeValueService.saveOrUpdateBatch(baseQualityAttributeVo.getBaseQualityAttributeValueList());
		BeanUtil.copyProperties(baseQualityAttributeVo, baseQualityAttribute);
        return updateById(baseQualityAttribute);
	}
	
	@Override
	public BaseQualityAttributeEntity getByIdBaseQualityAttribute(Long id) {
		BaseQualityAttributeEntity baseQualityAttribute = getById(id);
		if (baseQualityAttribute != null) {
			baseQualityAttribute.setBaseQualityAttributeValueList(baseQualityAttributeValueService.list(Wrappers.<BaseQualityAttributeValueEntity>lambdaQuery().eq(BaseQualityAttributeValueEntity::getAttributeId, baseQualityAttribute.getId())));
		}
		return baseQualityAttribute;
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean removeByIdsBaseQualityAttribute(List<Long> ids) {
		List<BaseQualityAttributeEntity> baseQualityAttribute = lambdaQuery()
				.in(BaseQualityAttributeEntity::getId, ids)
				.list();
		if (CollectionUtils.isNotEmpty(baseQualityAttribute)) {
			removeByIds(ids);
			List<Long> baseQualityAttributeValueBaseQualityAttributeId = baseQualityAttribute.stream().map(BaseQualityAttributeEntity::getId).toList();
			baseQualityAttributeValueService.remove(Wrappers.<BaseQualityAttributeValueEntity>lambdaQuery().in(BaseQualityAttributeValueEntity::getAttributeId, baseQualityAttributeValueBaseQualityAttributeId));
		}
		return true;
	}
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean switchStatus(Long id) {
		BaseQualityAttributeEntity baseMaterialCategory = getById(id);
		ExceptionUtil.check(baseMaterialCategory == null, "500", "质量属性不存在");
		if (CommonSwitchEnum.ENABLED.name().equals(baseMaterialCategory.getStatus())) {
			baseMaterialCategory.setStatus(CommonSwitchEnum.DISABLED.name());
		} else {
			baseMaterialCategory.setStatus(CommonSwitchEnum.ENABLED.name());
		}
		this.updateById(baseMaterialCategory);
		return true;
	}

	@Override
	public void template(HttpServletResponse response) {
		// 创建目录及导入模板文件
		isExcelExist();
		// 读取导入模板
		FileInputStream inputStream = null;
		ServletOutputStream outputStream = null;
		try {
			String oriFileName = "质量属性表导入模板";
			response.setContentType("application/vnd.ms-excel");
			response.setCharacterEncoding("utf-8");
			String fileName = URLEncoder.encode(oriFileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
			response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
			Path result = ExcelTemplateGenerator.getResult();
			Path filePath = result.resolve("质量属性表baseQualityAttribute.xlsx");
			// 绝对路径
			File file = new File(filePath.toString());
			inputStream = new FileInputStream(file);

			// 获取输出流
			outputStream = response.getOutputStream();

			// 将输入流的内容写入到输出流中
			byte[] buffer = new byte[1024];
			int bytesRead;
			while ((bytesRead = inputStream.read(buffer)) != -1) {
				outputStream.write(buffer, 0, bytesRead);
			}

			// 确保输出流的数据完全写入
			outputStream.flush();
			log.info("模板下载成功");
		} catch (IOException e) {
			System.out.println("e = " + e.getMessage());
			log.info("模板下载失败");
		} finally {
			// 关闭流
			try {
				if (inputStream != null) {
					inputStream.close();
				}
				if (outputStream != null) {
					outputStream.close();
				}
			} catch (IOException e) {
				log.error("关闭流时发生错误: {}", e.getMessage());
			}
		}
	}

	@Override
	@Transactional
	public void uploadData(MultipartFile file, HttpServletResponse response) {
		try {
			List<BaseQualityAttributeReq> importGoodsList = new ExcelAnalysisUtil<BaseQualityAttributeReq>()
					.readFile(file, BaseQualityAttributeReq.class, 1);
			if (CollectionUtils.isEmpty(importGoodsList)) {
				writeImportResponse(response, 1, "导入数据为空");
				return;
			}

			// 1. 检查导入数据中是否有重复的属性名称
			List<String> importAttributeNames = importGoodsList.stream()
					.map(BaseQualityAttributeReq::getAttributeName)
					.filter(StrUtil::isNotBlank)
					.toList();
			
			if (importAttributeNames.size() != importAttributeNames.stream().distinct().count()) {
				writeImportResponse(response, 1, "导入数据中存在重复的属性名称");
				return;
			}

			// 2. 检查与数据库中已有属性名称的重复性
			Long existingNameCount = this.lambdaQuery()
					.in(BaseQualityAttributeEntity::getAttributeName, importAttributeNames)
					.count();
			if (existingNameCount > 0) {
				writeImportResponse(response, 1, "属性名称已存在，请检查导入数据");
				return;
			}

			// 3. 字段校验
			StringBuilder errorMsg = new StringBuilder();
			for (int i = 0; i < importGoodsList.size(); i++) {
				BaseQualityAttributeReq req = importGoodsList.get(i);
				int rowNum = i + 2;
				
				// 必填校验
				if (StrUtil.isBlank(req.getAttributeName())) {
					errorMsg.append("第").append(rowNum).append("行：属性名称不能为空；");
				}
				
				// 长度校验
				if (StrUtil.isNotBlank(req.getAttributeName()) && req.getAttributeName().length() > 30) {
					errorMsg.append("第").append(rowNum).append("行：属性名称长度不能超过30个字符；");
				}
			}
			
			// 如果有校验错误，直接返回
			if (!errorMsg.isEmpty()) {
				writeImportResponse(response, 1, errorMsg.toString());
				return;
			}

			// 转换为实体对象并处理分类名称转ID
			List<BaseQualityAttributeEntity> baseQualityAttributeEntities = new ArrayList<>();
			for (BaseQualityAttributeReq req : importGoodsList) {
				BaseQualityAttributeEntity entity = new BaseQualityAttributeEntity();
				BeanUtil.copyProperties(req, entity);

				baseQualityAttributeEntities.add(entity);
			}

			// 保存数据
			this.saveOrUpdateBatch(baseQualityAttributeEntities);
			writeImportResponse(response, 0, "导入成功，共导入 " + baseQualityAttributeEntities.size() + " 条数据");
		} catch (IOException e) {
			log.info("文件解析失败");
			throw new RuntimeException(e);
		}
	}

	private void writeImportResponse(HttpServletResponse response, int code, String errorMsg) throws IOException {
		JSONObject obj = new JSONObject();
		response.setContentType("text/html;charset=utf-8");
		obj.put("code", code);
		obj.put("msg", errorMsg);
		PrintWriter writer = response.getWriter();
		writer.write(obj.toJSONString());
		writer.close();
	}

	private static void isExcelExist() {
		try {
			Path result = ExcelTemplateGenerator.getResult();

			File file = new File(result.toString());
			if (file.exists()) {
				log.info("excel文件夹路径存在");
			} else {
				// 创建文件夹
				if (file.mkdirs()) {
					log.info("目录已创建: {}" , file);
				} else {
					log.info("创建目录失败: {}", file);
					throw new RuntimeException("创建目录失败: " + file);
				}
			}
			// 设置 Excel 文件的完整路径
			Path filePath = result.resolve("质量属性表baseQualityAttribute.xlsx");
			// 生成模板文件
			ExcelTemplateGenerator.createExcelTemplate(filePath.toString(), BaseQualityAttributeReq.class);
		} catch (UnsupportedEncodingException e) {
			log.info("编码失败");
		}
	}

}