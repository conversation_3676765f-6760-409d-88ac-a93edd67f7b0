package com.ylz.saas.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/6/18 18:00
 * @Description
 */
@AllArgsConstructor
@Getter
public enum ProjectProgressStatusEnum {
    INIT("初始"),

//    关于邀请的枚举 不参与到保存中 仅因为前端需要
    TO_INVITE("待发邀请"),
    INVITE("已发布邀请"),


    TO_NOTICE("待发公告"),
    NOTICE("已发布公告"),
    TENDER_DOC("已发布文件"),
    REGISTER("报名中"),
    QUOTING("报价中"),
    TO_BID_OPEN("待开标"),
    BID_OPENED("已开标"),
    END_OPENED("开标结束"),
    EVALUATING("评标中"),
    EVALUATED("已评标"),
    AWARDED("已定标"),
    BID_WON_PUBLICITY("已发中标公示"),
    BID_WON_NOTICE("已发中标公告"),
    CONTRACTED("已签合同"),
    COMPLETED("已完成"),
    FAILED("已流标");

    private final String desc;


    /**
     * 获取转换后的邀请状态 如果是null就不做处理
     * @return
     */
    public static ProjectProgressStatusEnum getInviteStatus(ProjectProgressStatusEnum sourceStatus) {
        return switch (sourceStatus) {
            case TO_INVITE -> TO_NOTICE;
            case INVITE -> NOTICE;
            default -> null;
        };

    }
    /**
     * 获取转换后的邀请状态 如果是null就不做处理
     * @return
     */
    public static ProjectProgressStatusEnum getPublicityStatus(ProjectProgressStatusEnum sourceStatus) {
        return switch (sourceStatus) {
            case TO_NOTICE -> TO_NOTICE;
            case NOTICE -> NOTICE;
            default -> null;
        };

    }
    /**
     * 获取转换后的邀请状态 如果是null就不做处理
     * @return
     */
    public static ProjectProgressStatusEnum setInviteStatus(ProjectProgressStatusEnum sourceStatus) {
        return switch (sourceStatus) {
            case TO_NOTICE -> TO_INVITE;
            case NOTICE -> INVITE;
            default -> sourceStatus;
        };

    }
}
