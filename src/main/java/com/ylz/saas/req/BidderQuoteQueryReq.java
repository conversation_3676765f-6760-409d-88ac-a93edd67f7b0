package com.ylz.saas.req;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/18 13:52
 * @Description
 */
@Data
public class BidderQuoteQueryReq {

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;

    /**
     * 标段ID（可选，用于兼容旧接口）
     */
    private Long sectionId;

    /**
     * 报价轮次
     */
    @Min(value = 1, message = "报价轮次不能小于1")
    private Integer roundNo;

    /**
     * 是否已中标
     */
    private Boolean awarded;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 批量物料编码
     */
    private List<String> materialCodeList;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 租户供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 是否推荐中标供应商
     */
    private Integer isRecommendedWinner;

}
