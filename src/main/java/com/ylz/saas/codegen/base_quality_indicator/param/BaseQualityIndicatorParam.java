package com.ylz.saas.codegen.base_quality_indicator.param;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ylz.saas.codegen.base_quality_indicator.entity.BaseQualityIndicatorEntity;
import com.ylz.saas.common.data.mybatis.helper.Operator;
import com.ylz.saas.common.data.mybatis.helper.QueryWrapperHelper;
import com.ylz.saas.common.data.mybatis.wrapper.CriteriaField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * mybatis-plus的查询帮助类
 * 
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseQualityIndicatorParam {

	/**
  	 * 主键ID
   	 */
 	@CriteriaField(field = "id", operator = Operator.LIKE)
    private Long id;
	/**
  	 * 组织ID
   	 */
 	@CriteriaField(field = "deptId", operator = Operator.LIKE)
    private Long deptId;
	/**
	 * 是否适用与部门名称字段（用于模糊查询）
	 */
	@CriteriaField(ignore = true)
	private String locationName;
	/**
  	 * 指标编码
   	 */
 	@CriteriaField(field = "indicatorCode", operator = Operator.LIKE)
    private String indicatorCode;
	/**
  	 * 指标名称
   	 */
 	@CriteriaField(field = "indicatorName", operator = Operator.LIKE)
    private String indicatorName;
	/**
  	 * 适用物料编码
   	 */
 	@CriteriaField(field = "materialCode", operator = Operator.LIKE)
    private String materialCode;
	/**
  	 * 适用物料名称（用于模糊查询）
   	 */
 	@CriteriaField(ignore = true)
    private String materialName;
	/**
  	 * 指标描述
   	 */
 	@CriteriaField(field = "description", operator = Operator.LIKE)
    private String description;
	/**
  	 * 生效日期_开始
   	 */
	@CriteriaField(field = "releaseDate", operator = Operator.GE, filterBlank = true)
	private LocalDate releaseDateStart;
	/**
  	 * 生效日期_结束
   	 */
    @CriteriaField(field = "releaseDate", operator = Operator.LE, filterBlank = true)
	private LocalDate releaseDateEnd;
	/**
  	 * 失效日期_开始
   	 */
	@CriteriaField(field = "expiryDate", operator = Operator.GE, filterBlank = true)
	private LocalDate expiryDateStart;
	/**
  	 * 失效日期_结束
   	 */
    @CriteriaField(field = "expiryDate", operator = Operator.LE, filterBlank = true)
	private LocalDate expiryDateEnd;
	/**
  	 * 是否适用于所有部门
   	 */
 	@CriteriaField(field = "applyAllLocations", operator = Operator.EQ)
    private String applyAllLocations;
	/**
  	 * 状态
   	 */
 	@CriteriaField(field = "status", operator = Operator.IN)
    private List<String> statusList;
	/**
  	 * 创建人名称
   	 */
 	@CriteriaField(field = "createByName", operator = Operator.LIKE)
    private String createByName;
	/**
  	 * 修改人名称
   	 */
 	@CriteriaField(field = "updateByName", operator = Operator.LIKE)
    private String updateByName;
	/**
  	 * 创建人
   	 */
 	@CriteriaField(field = "createBy", operator = Operator.LIKE)
    private String createBy;
	/**
  	 * 创建时间_开始
   	 */
	@CriteriaField(field = "createTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime createTimeStart;
	/**
  	 * 创建时间_结束
   	 */
    @CriteriaField(field = "createTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime createTimeEnd;
	/**
  	 * 修改人
   	 */
 	@CriteriaField(field = "updateBy", operator = Operator.LIKE)
    private String updateBy;
	/**
  	 * 修改时间_开始
   	 */
	@CriteriaField(field = "updateTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime updateTimeStart;
	/**
  	 * 修改时间_结束
   	 */
    @CriteriaField(field = "updateTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime updateTimeEnd;
	/**
  	 * 删除标识（0-正常、1-删除）
   	 */
	@CriteriaField(field = "delFlag", operator = Operator.EQ)
	private int delFlag;
	/**
  	 * 租户ID
   	 */
 	@CriteriaField(field = "tenantId", operator = Operator.LIKE)
    private Long tenantId;
	/**
  	 * 创建人ID
   	 */
 	@CriteriaField(field = "createById", operator = Operator.LIKE)
    private Long createById;
	/**
  	 * 更新人ID
   	 */
 	@CriteriaField(field = "updateById", operator = Operator.LIKE)
    private Long updateById;

    /**
    * 构建查询
    */
    public QueryWrapper<BaseQualityIndicatorEntity> toWrapper() {
        QueryWrapper<BaseQualityIndicatorEntity> queryWrapper = QueryWrapperHelper.fromBean(this, BaseQualityIndicatorEntity.class)
            .orderByDesc("id");
       return queryWrapper;
    }
}