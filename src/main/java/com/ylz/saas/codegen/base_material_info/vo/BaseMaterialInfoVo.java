package com.ylz.saas.codegen.base_material_info.vo;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 物料基础信息表
 *
 * <AUTHOR>
 * @date 2025-06-03 18:00:37
 */
@Data
public class BaseMaterialInfoVo implements Serializable {

    /**
  	 * 主键ID
   	 */
    private Long id;

    /**
  	 * 创建人ID
   	 */
    private Long createById;

    /**
  	 * 组织ID
   	 */
    private Long deptId;

    /**
  	 * 更新人ID
   	 */
    private Long updateById;

    /**
  	 * 物料编码
   	 */
    @NotBlank(message = "物料编码不能为空")
    private String materialCode;

    /**
  	 * 物料名称
   	 */
    @NotBlank(message = "物料名称不能为空")
    private String materialName;

    /**
  	 * 物料简称
   	 */
    private String materialSimpleName;

    /**
  	 * 规格/型号
   	 */
    private String spec;

    /**
  	 * 物料所属分类ID
   	 */
    private Long materialCategoryId;

    /**
     * 物料分类名称（关联查询字段）
     */
    private String materialCategoryName;

    /**
  	 * 计量单位
   	 */
    private String unit;

    /**
  	 * 来源标识
   	 */
    private String dataSource;

    /**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
    private String status;

    /**
  	 * 备注
   	 */
    private String remark;

    /**
  	 * 创建人名称
   	 */
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    private String updateByName;

    /**
  	 * 创建人
   	 */
    private String createBy;

    /**
  	 * 创建时间
   	 */
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
    private String updateBy;

    /**
  	 * 修改时间
   	 */
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    private Long tenantId;

    /**
  	 * 组织名称（关联查询字段）
   	 */
    private String deptName;

    /**
     * 物料分类顶级名称（关联查询字段）
     */
    private String materialCategoryRoot;

    /**
     * 组织ID列表（用于新增和修改时选择多个组织）
     */
    private List<Long> deptIds;

    /**
     * 关联的组织列表（用于回显）
     */
    private List<DeptInfo> deptList;

    /**
     * 组织信息内部类
     */
    @Data
    public static class DeptInfo implements Serializable {
        /**
         * 组织ID
         */
        private Long deptId;

        /**
         * 组织名称
         */
        private String deptName;
    }
}