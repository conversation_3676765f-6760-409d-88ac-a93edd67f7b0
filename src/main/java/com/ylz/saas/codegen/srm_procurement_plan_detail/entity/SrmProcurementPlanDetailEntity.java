package com.ylz.saas.codegen.srm_procurement_plan_detail.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ylz.saas.codegen.base_service_type_field.entity.BaseServiceTypeFieldEntity;
import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.codegen.srm_procurement_plan_field_value.entity.SrmProcurementPlanFieldValueEntity;
import com.ylz.saas.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 采购计划明细表
 *
 * <AUTHOR>
 * @date 2025-06-03 21:00:11
 */
@Data
@TableName("srm_procurement_plan_detail")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "采购计划明细表")
@TenantTable
public class SrmProcurementPlanDetailEntity extends Model<SrmProcurementPlanDetailEntity> {


    /**
  	 * 主键ID
   	 */
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

    /**
  	 * 组织ID
   	 */
    @Schema(description="组织ID")
    private Long deptId;

    /**
  	 * 采购计划ID
   	 */
    @Schema(description="采购计划ID")
    private Long planId;

    /**
  	 * 服务类型ID
   	 */
    @Schema(description="服务类型ID")
    private Long serviceTypeId;

    /**
     * 采招（寻源）方式
     */
    @Schema(description="寻源方式")
    private BaseServiceTypeFieldService.BuyWayEnum buyWay;

    /**
     * 需求行号
     */
    @Schema(description="需求行号")
    private String requireNo;

    /**
  	 * 物料编码
   	 */
    @Schema(description="物料编码")
    private String materialCode;

    /**
  	 * 物料名称
   	 */
    @Schema(description="物料名称")
    private String materialName;

    /**
  	 * 规格型号
   	 */
    @Schema(description="规格型号")
    private String specModel;

    /**
  	 * 单位
   	 */
    @Schema(description="单位")
    private String unit;

    /**
  	 * 需求数量
   	 */
    @Schema(description="需求数量")
    private BigDecimal requiredQuantity;

    /**
     * 牧场id
     */
    private Long usageLocationId;

    /**
     * 质量指标id
     */
    private Long qualityIndicatorId;

    /**
  	 * 备注
   	 */
    @Schema(description="备注")
    private String note;

    /**
  	 * 创建人名称
   	 */
    @Schema(description="创建人名称")
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    @Schema(description="修改人名称")
    private String updateByName;

    /**
  	 * 创建人
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
  	 * 创建时间
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

    /**
  	 * 修改时间
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标识（0-正常、1-删除）")
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    @Schema(description="租户ID")
    private Long tenantId;

    /**
     * 采购计划编码
     */
    @ExcelIgnore
    @TableField(exist = false)
    private String planCode;

    /**
     * 采购计划名称
     */
    @ExcelIgnore
    @TableField(exist = false)
    private String planName;

    /**
     * 申请时间
     */
    @ExcelIgnore
    @TableField(exist = false)
    private LocalDate applyDate;

    /**
     * 服务类型字段集合
     */
    @ExcelIgnore
    @TableField(exist = false)
    private List<BaseServiceTypeFieldEntity> baseServiceTypeFieldList;

	/**
	* 子表集合:srm_procurement_plan_field_value
	*/
	@ExcelIgnore
	@TableField(exist = false)
	private List<SrmProcurementPlanFieldValueEntity> srmProcurementPlanFieldValueList;

    /**
     * 牧场所属区域
     */
    @ExcelIgnore
    @TableField(exist = false)
    private String usageLocationRegion;

    /**
     * 牧场地址
     */
    @ExcelIgnore
    @TableField(exist = false)
    private String usageLocationAddress;

    /**
     * 牧场名称
     */
    @ExcelIgnore
    @TableField(exist = false)
    private String usageLocationName;

    /**
     * 质量指标名称
     */
    @ExcelIgnore
    @TableField(exist = false)
    private String qualityIndicatorName;

}