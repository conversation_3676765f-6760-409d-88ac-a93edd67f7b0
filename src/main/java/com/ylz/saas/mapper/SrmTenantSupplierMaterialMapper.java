package com.ylz.saas.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.entity.SrmTenantSupplierMaterial;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tenant_supplier_material(租户供应商主营物料表)】的数据库操作Mapper
* @createDate 2025-06-10 18:08:09
* @Entity com.ylz.saas.entity.SrmTenantSupplierMaterial
*/
@Mapper
public interface SrmTenantSupplierMaterialMapper extends SaasBaseMapper<SrmTenantSupplierMaterial> {

    List<SrmTenantSupplierMaterial> getMaterialsBySupplierId(@Param("supplierId") Long supplierId);

    /**
     * 分页查询供应商主营物料列表（联查物料详细信息）
     * @param supplierId 供应商ID
     * @param page 分页参数
     * @return 分页物料列表
     */
    Page<SrmTenantSupplierMaterial> getMaterialsBySupplierIdPage(@Param("supplierId") Long supplierId, Page<SrmTenantSupplierMaterial> page);

    /**
     * 物理删除供应商主营物料
     * @param id 物料关联ID
     * @return 删除的记录数
     */
    int physicalDeleteById(@Param("id") Long id);

}




