package com.ylz.saas.controller;

import com.ylz.saas.common.core.util.R;
import com.ylz.saas.req.SrmTenderEvaluationProgressReq;
import com.ylz.saas.req.SrmTenderEvaluationStandardReq;
import com.ylz.saas.resp.SrmTenderEvaluationProgressResp;
import com.ylz.saas.resp.SrmTenderEvaluationStandardResp;
import com.ylz.saas.service.SrmTenderEvaluationStandardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 评标详情标准控制器
 * <AUTHOR>
 * @createDate 2025-07-10
 */
@RestController
@RequestMapping("/srmTenderBidEvaluationStandard")
@Tag(name = "评标专家评标标准", description = "评标专家评标标准")
@Slf4j
@AllArgsConstructor
public class SrmTenderBidEvaluationStandardController {

    private final SrmTenderEvaluationStandardService srmTenderEvaluationStandardService;

    /**
     * 查询评标进度（支持查询指定报价轮次的评标进度）
     */
    @PostMapping("/getEvaluationProgress")
    @Operation(summary = "查询评标进度", description = "查询指定报价轮次的评标进度，currentRound为必填参数")
    public R<SrmTenderEvaluationProgressResp> getEvaluationProgress(@RequestBody @Validated SrmTenderEvaluationProgressReq req) {
        SrmTenderEvaluationProgressResp result = srmTenderEvaluationStandardService.getEvaluationProgress(req);
        return R.ok(result, "查询成功");
    }

    /**
     * 新增评标标准
     */
    @PostMapping("/saveEvaluationStandard")
    @Operation(summary = "新增评标标准", description = "新增评标标准信息")
    public R<Boolean> saveEvaluationStandard(@RequestBody @Validated SrmTenderEvaluationStandardReq req) {
        boolean result = srmTenderEvaluationStandardService.saveEvaluationStandard(req);
        return result ? R.ok(true, "新增成功") : R.failed("新增失败");
    }

    /**
     * 修改评标标准
     */
    @PostMapping("/updateEvaluationStandard")
    @Operation(summary = "修改评标标准", description = "修改评标标准信息")
    public R<Boolean> updateEvaluationStandard(@RequestBody @Validated SrmTenderEvaluationStandardReq req) {
        boolean result = srmTenderEvaluationStandardService.updateEvaluationStandard(req);
        return result ? R.ok(true, "修改成功") : R.failed("修改失败");
    }

    /**
     * 根据ID查询评标标准详情
     */
    @PostMapping("/getByIdEvaluationStandard")
    @Operation(summary = "查询评标标准详情", description = "根据ID查询评标标准详情")
    public R<SrmTenderEvaluationStandardResp> getByIdEvaluationStandard(@RequestParam Long id) {
        SrmTenderEvaluationStandardResp result = srmTenderEvaluationStandardService.getByIdEvaluationStandard(id);
        return R.ok(result, "查询成功");
    }

    /**
     * 根据标段ID查询评标标准列表
     */
    @PostMapping("/listBySectionId")
    @Operation(summary = "查询评标标准列表", description = "根据标段ID查询评标标准列表")
    public R<List<SrmTenderEvaluationStandardResp>> listBySectionId(@RequestParam Long sectionId) {
        List<SrmTenderEvaluationStandardResp> result = srmTenderEvaluationStandardService.listBySectionId(sectionId);
        return R.ok(result, "查询成功");
    }

    /**
     * 根据标段ID和招标公告ID查询评标标准列表
     */
    @PostMapping("/listBySectionIdAndNoticeId")
    @Operation(summary = "查询评标标准列表", description = "根据标段ID和招标公告ID查询评标标准列表")
    public R<List<SrmTenderEvaluationStandardResp>> listBySectionIdAndNoticeId(@RequestParam Long sectionId, @RequestParam Long noticeId) {
        List<SrmTenderEvaluationStandardResp> result = srmTenderEvaluationStandardService.listBySectionIdAndNoticeId(sectionId, noticeId);
        return R.ok(result, "查询成功");
    }

    /**
     * 批量保存评标标准
     */
    @PostMapping("/batchSaveEvaluationStandards")
    @Operation(summary = "批量保存评标标准", description = "批量保存评标标准信息")
    public R<Boolean> batchSaveEvaluationStandards(@RequestBody @Validated List<SrmTenderEvaluationStandardReq> standardList) {
        boolean result = srmTenderEvaluationStandardService.batchSaveEvaluationStandards(standardList);
        return result ? R.ok(true, "保存成功") : R.failed("保存失败");
    }

    /**
     * 删除评标标准
     */
    @PostMapping("/removeEvaluationStandards")
    @Operation(summary = "删除评标标准", description = "删除评标标准信息")
    public R<Boolean> removeEvaluationStandards(@RequestBody List<Long> ids) {
        boolean result = srmTenderEvaluationStandardService.removeByIdsEvaluationStandard(ids);
        return result ? R.ok(true, "删除成功") : R.failed("删除失败");
    }
}
