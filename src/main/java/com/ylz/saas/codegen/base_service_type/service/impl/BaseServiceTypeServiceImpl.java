package com.ylz.saas.codegen.base_service_type.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Joiner;
import com.google.common.hash.Hashing;
import com.ylz.saas.codegen.base_service_type.entity.BaseServiceTypeEntity;
import com.ylz.saas.codegen.base_service_type.mapper.BaseServiceTypeMapper;
import com.ylz.saas.codegen.base_service_type.service.BaseServiceTypeService;
import com.ylz.saas.codegen.base_service_type.vo.BaseServiceTypeVo;
import com.ylz.saas.codegen.base_service_type_field.entity.BaseServiceTypeFieldEntity;
import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.codegen.srm_procurement_plan.entity.SrmProcurementPlanEntity;
import com.ylz.saas.codegen.srm_procurement_plan.service.SrmProcurementPlanService;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.core.exception.GlobalResultCode;
import com.ylz.saas.common.data.tenant.TenantContextHolder;
import com.ylz.saas.common.sequence.generator.impl.DefaultCodeGenerator;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.enums.FixedFieldEnum;
import com.ylz.saas.service.SrmProcurementProjectService;
import com.ylz.saas.util.SqlValidator;
import com.ylz.saas.vo.LabelAndValueVo;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.ylz.saas.codegen.base_service_type.controller.BaseServiceTypeController.*;

/**
 * 服务类型表
 *
 * <AUTHOR>
 * @date 2025-06-03 21:25:49
 */
@Service
@Slf4j
public class BaseServiceTypeServiceImpl extends ServiceImpl<BaseServiceTypeMapper, BaseServiceTypeEntity> implements BaseServiceTypeService {

    @Autowired
    private BaseServiceTypeFieldService baseServiceTypeFieldService;
    @Autowired
    private SrmProcurementPlanService srmProcurementPlanService;
    @Autowired
    RedisTemplate redisTemplate;
    private DefaultCodeGenerator defaultCodeGenerator;

    public static final String BASE_SERVICE_TYPE_PREFIX = "BST";
    public static final String BASE_SERVICE_TYPE_FIELD_PREFIX = "BSTF";
    @Autowired
    @Lazy
    private SrmProcurementProjectService srmProcurementProjectService;

    @PostConstruct
    public void init() {
        defaultCodeGenerator = new DefaultCodeGenerator(redisTemplate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBaseServiceType(BaseServiceTypeVo baseServiceTypeVo) {
        // 业务类型编码 = 业务类型名Base64
        if (StringUtils.isBlank(baseServiceTypeVo.getTypeCode())) {
            final String tyeCode = defaultCodeGenerator.generateWithPrefixCode(BASE_SERVICE_TYPE_PREFIX);
            baseServiceTypeVo.setTypeCode(tyeCode);
        }
        // 业务类型名称是否重复校验
        final BaseServiceTypeEntity repeatOne = getOne(Wrappers.lambdaQuery(BaseServiceTypeEntity.class).eq(BaseServiceTypeEntity::getTypeName, baseServiceTypeVo.getTypeName()));
        ExceptionUtil.check(Objects.nonNull(repeatOne), "50010", "类型名字重复");

        BaseServiceTypeEntity baseServiceType = new BaseServiceTypeEntity();
        BeanUtil.copyProperties(baseServiceTypeVo, baseServiceType);
        save(baseServiceType);

        return Boolean.TRUE;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean upsertTypeFields(BaseServiceTypeVo baseServiceTypeVo) {
        final Long tenantId = TenantContextHolder.getTenantId();
        // 插入或更新服务类型字段子表记录
        List<BaseServiceTypeFieldEntity> oldBaseServiceTypeFieldList = Optional.ofNullable(baseServiceTypeFieldService.list(Wrappers.<BaseServiceTypeFieldEntity>lambdaQuery().eq(BaseServiceTypeFieldEntity::getServiceTypeId, baseServiceTypeVo.getId()))).orElse(Collections.emptyList());

        // 如果旧列表为空，那么直接插入
        if (!oldBaseServiceTypeFieldList.isEmpty()) {
            // 不为空先删除后插入
            baseServiceTypeFieldService.remove(Wrappers.lambdaQuery(BaseServiceTypeFieldEntity.class).eq(BaseServiceTypeFieldEntity::getServiceTypeId, baseServiceTypeVo.getId()));
        }
        final HashSet<String> nameSet = new HashSet<>();
        AtomicBoolean ifRepeat = new AtomicBoolean(false);
        baseServiceTypeVo.getBaseServiceTypeFieldList().forEach(baseServiceTypeField -> {
            baseServiceTypeField.setServiceTypeId(baseServiceTypeVo.getId());
            // 如果是更新这里会将记录id带入需要清除
            baseServiceTypeField.setId(null);
            // 为生成字段编码手动填入tenantId
            baseServiceTypeField.setTenantId(tenantId);
            baseServiceTypeField.setDeptId(baseServiceTypeVo.getDeptId());
            // 批量插入字段前需要效验字段名是否重复
            if (nameSet.contains(baseServiceTypeField.getFieldName())) {
                ifRepeat.set(true);
                log.info("字段 {} 重复", baseServiceTypeField.getFieldName());
            } else {
                nameSet.add(baseServiceTypeField.getFieldName());
            }
            if (StringUtils.isNotBlank(baseServiceTypeField.getFieldCode())) {
                FixedFieldEnum fieldEnum = FixedFieldEnum.getByCode(baseServiceTypeField.getFieldCode());
                ExceptionUtil.checkNonNull(fieldEnum,"固定字段code错误");
            } else {
                final String fieldCode = generateWithPrefixCode(baseServiceTypeField, BASE_SERVICE_TYPE_FIELD_PREFIX);
                baseServiceTypeField.setFieldCode(fieldCode);
            }
            // 验证表单类型字段的 SQL 语句是否是 SELECT 语句
            checkBaseServiceTypeFieldFormTypeSQLValid(baseServiceTypeField);
        });
        ExceptionUtil.check(ifRepeat.get(), "50020", "字段名称已存在，不可重复提交");
        baseServiceTypeFieldService.saveBatch(baseServiceTypeVo.getBaseServiceTypeFieldList());
        return Boolean.TRUE;
    }

    /**
     * 验证 BaseServiceTypeFieldEntity 业务类型字段子表记录是否有效
     * 1. 主要校验当字段类型是 表单类型时, 指定的 backupValue 枚举字段存储的是否是有效的 select SQL语句
     * 2. 校验错误时, 抛出异常, 让事务回滚
     * @return
     */
    private boolean checkBaseServiceTypeFieldFormTypeSQLValid(BaseServiceTypeFieldEntity fieldObj) {
        if (BaseServiceTypeFieldService.FieldTypeEnum.LINK_FORM.equals(fieldObj.getFieldType())) {
            String backupValue = fieldObj.getBackupValue();
            if (StrUtil.isBlank(backupValue)) {
                ExceptionUtil.check(true, GlobalResultCode.INVALID_PARAMS, "SQL 语句未填!");
                return false;
            }
            ExceptionUtil.check(!SqlValidator.isSelectOnly(backupValue), GlobalResultCode.INVALID_PARAMS, "只能填写 select 语句!");
            fieldObj.setEnumValues(null);
        }
        return true;
    }


    private String generateWithPrefixCode(BaseServiceTypeFieldEntity baseServiceTypeField, String baseServiceTypeFieldPrefix) {
        ExceptionUtil.check(Objects.isNull(baseServiceTypeField.getTenantId())
                                    || Objects.isNull(baseServiceTypeField.getServiceTypeId())
                                    || Objects.isNull(baseServiceTypeField.getBuyWay())
                                    || StringUtils.isBlank(baseServiceTypeField.getFieldName()), "500203", "字段缺失，无法生成字段编码");
        final String union = Joiner.on("").join(baseServiceTypeField.getTenantId(), baseServiceTypeField.getServiceTypeId(), baseServiceTypeField.getBuyWay(), baseServiceTypeField.getFieldName());
//        return baseServiceTypeFieldPrefix + union.hashCode();
        long hash = Hashing.murmur3_128().hashString(union, StandardCharsets.UTF_8).asInt();
        return baseServiceTypeFieldPrefix + Math.abs(hash) + baseServiceTypeField.getServiceTypeId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBaseServiceType(BaseServiceTypeVo baseServiceTypeVo) {
        BaseServiceTypeEntity baseServiceType = getById(baseServiceTypeVo.getId());
        ExceptionUtil.check(baseServiceType == null, "500", "服务类型表不存在");
        // 租户 + 业务类型名 唯一效验
        final BaseServiceTypeEntity repeatOne = getOne(Wrappers.lambdaQuery(BaseServiceTypeEntity.class).eq(BaseServiceTypeEntity::getTypeName, baseServiceTypeVo.getTypeName()));
        if (Objects.nonNull(repeatOne) && baseServiceTypeVo.getId() != repeatOne.getId()) {
            ExceptionUtil.check(Objects.nonNull(repeatOne), "50010", "类型名字重复");
        }
        BeanUtil.copyProperties(baseServiceTypeVo, baseServiceType);
        return updateById(baseServiceType);
    }

    @Override
    public BaseServiceTypeEntity getByIdBaseServiceType(Long id) {
        BaseServiceTypeEntity baseServiceType = getById(id);
        if (baseServiceType != null) {
            List<BaseServiceTypeFieldEntity> fieldEntities = baseServiceTypeFieldService.list(Wrappers.<BaseServiceTypeFieldEntity>lambdaQuery().eq(BaseServiceTypeFieldEntity::getServiceTypeId, baseServiceType.getId()).orderByAsc(BaseServiceTypeFieldEntity::getSort));
            for (BaseServiceTypeFieldEntity fieldEntity : fieldEntities) {
                if (BaseServiceTypeFieldService.FieldTypeEnum.LINK_FORM.equals(fieldEntity.getFieldType())) {
                    this.updateLinkFormFieldData(fieldEntity);
                }
            }
            baseServiceType.setBaseServiceTypeFieldList(fieldEntities);
        }
        return baseServiceType;
    }


    @Override
    public BaseServiceTypeEntity getByIdAndBuyWay(Long id, BaseServiceTypeFieldService.BuyWayEnum buyWay) {
        BaseServiceTypeEntity baseServiceType = getById(id);
        if (baseServiceType != null) {
            // 这里的服务类型字段是由 服务类型 + 寻源方式 确定
            baseServiceType.setBaseServiceTypeFieldList(baseServiceTypeFieldService.list(Wrappers.<BaseServiceTypeFieldEntity>lambdaQuery().eq(BaseServiceTypeFieldEntity::getServiceTypeId, baseServiceType.getId()).and(e -> e.eq(BaseServiceTypeFieldEntity::getBuyWay, buyWay)).orderByAsc(BaseServiceTypeFieldEntity::getSort)));
            List<BaseServiceTypeFieldEntity> fieldList = baseServiceType.getBaseServiceTypeFieldList();
            for (BaseServiceTypeFieldEntity field : fieldList) {
                // 这里需要将 表单类型的字段的 enumValues 字段替换成具体 SQL 查询结果数据集
                if (BaseServiceTypeFieldService.FieldTypeEnum.LINK_FORM.equals(field.getFieldType())) {
                    this.updateLinkFormFieldData(field);
                }
            }
        }
        return baseServiceType;
    }

    private void updateLinkFormFieldData(BaseServiceTypeFieldEntity field) {
        List<LabelAndValueVo> keyValuePairs = baseServiceTypeFieldService.convertServiceTypeFieldToSqlDataList(field);
        field.setEnumValues(JSONArray.toJSONString(keyValuePairs));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIdsBaseServiceType(List<Long> ids) {
        boolean planExists = srmProcurementPlanService.exists(Wrappers.<SrmProcurementPlanEntity>lambdaQuery().in(SrmProcurementPlanEntity::getServiceTypeId, ids));
        ExceptionUtil.check(planExists, GlobalResultCode.INVALID_STATE, "此业务类型采购计划已使用，不可删除");

        boolean projectExists = srmProcurementProjectService.lambdaQuery().in(SrmProcurementProject::getServiceTypeId, ids).exists();
        ExceptionUtil.check(projectExists, GlobalResultCode.INVALID_STATE, "此业务类型采购项目已使用，不可删除");

        List<BaseServiceTypeEntity> baseServiceType = lambdaQuery().in(BaseServiceTypeEntity::getId, ids).list();
        if (CollectionUtils.isNotEmpty(baseServiceType)) {
            // 批量物理删除主表数据
            removeBatchByIds(baseServiceType);
            // 批量物理删除子表数据
            final List<BaseServiceTypeFieldEntity> typeFieldEntities = baseServiceTypeFieldService.
                    list(Wrappers.<BaseServiceTypeFieldEntity>lambdaQuery()
                                 .in(BaseServiceTypeFieldEntity::getServiceTypeId, ids));
            List<Long> fieldIds = typeFieldEntities.stream().map(BaseServiceTypeFieldEntity::getId).toList();
            List<Long> refFieldIds = baseServiceTypeFieldService.verifyRemoveFieldById(fieldIds);
            ExceptionUtil.check(CollectionUtils.isNotEmpty(refFieldIds), GlobalResultCode.INVALID_STATE, "业务类型的动态字段，被采购计划、采购项目、报价引用了不能删除");
            baseServiceTypeFieldService.removeBatchByIds(typeFieldEntities);
        }
        return true;
    }

    @Override
    public List<BaseServiceTypeEntity> getEnabled() {
        return list(Wrappers.lambdaQuery(BaseServiceTypeEntity.class).eq(BaseServiceTypeEntity::getStatus, StatusEnum.ENABLED).and(e -> e.eq(BaseServiceTypeEntity::getDelFlag, NO_DEL_FLAG)));
    }

}