package com.ylz.saas.resp;

import com.ylz.saas.enums.SignatureStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 评标汇总签名进度响应类
 * <AUTHOR>
 * @createDate 2025-07-11
 */
@Data
@Schema(description = "评标汇总签名进度响应类")
public class SrmTenderEvaluationSignatureResp implements Serializable {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 标段ID
     */
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 评标委员会ID
     */
    @Schema(description = "评标委员会ID")
    private Long evaluationId;

    /**
     * 评标人员用户ID
     */
    @Schema(description = "评标人员用户ID")
    private Long userId;

    /**
     * 评标人员姓名
     */
    @Schema(description = "评标人员姓名")
    private String userName;

    /**
     * 签名状态
     */
    @Schema(description = "签名状态")
    private SignatureStatusEnum signatureStatus;

    /**
     * 签名状态描述
     */
    @Schema(description = "签名状态描述")
    private String signatureStatusDesc;

    /**
     * 签名时间
     */
    @Schema(description = "签名时间")
    private LocalDateTime signatureTime;

    /**
     * 签名意见
     */
    @Schema(description = "签名意见")
    private String signatureComment;

    /**
     * 专家编码
     */
    @Schema(description = "专家编码")
    private String expertCode;

    /**
     * 专家类别
     */
    @Schema(description = "专家类别")
    private String expertCategory;

    /**
     * 专家类型
     */
    @Schema(description = "专家类型")
    private String expertType;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String contactPhone;

    private static final long serialVersionUID = 1L;
}
