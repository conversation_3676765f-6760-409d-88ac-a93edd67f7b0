package com.ylz.saas.codegen.base_quality_indicator_attribute_value.param;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ylz.saas.codegen.base_quality_indicator_attribute_value.entity.BaseQualityIndicatorAttributeValueEntity;
import com.ylz.saas.common.data.mybatis.helper.Operator;
import com.ylz.saas.common.data.mybatis.helper.QueryWrapperHelper;
import com.ylz.saas.common.data.mybatis.wrapper.CriteriaField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * mybatis-plus的查询帮助类
 * 
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BaseQualityIndicatorAttributeValueParam {

	/**
  	 * 主键ID
   	 */
 	@CriteriaField(field = "id", operator = Operator.LIKE)
    private Long id;
	/**
  	 * 组织ID
   	 */
 	@CriteriaField(field = "deptId", operator = Operator.LIKE)
    private Long deptId;
	/**
  	 * 质量指标ID
   	 */
 	@CriteriaField(field = "qualityIndicatorId", operator = Operator.LIKE)
    private Long qualityIndicatorId;
	/**
  	 * 质量属性ID
   	 */
 	@CriteriaField(field = "attributeId", operator = Operator.LIKE)
    private String attributeId;
	/**
  	 * 质量属性值ID
   	 */
 	@CriteriaField(field = "attributeValueId", operator = Operator.LIKE)
    private String attributeValueId;
	/**
  	 * 属性值内容
   	 */
 	@CriteriaField(field = "valueContent", operator = Operator.LIKE)
    private String valueContent;
	/**
  	 * 拒收标准
   	 */
 	@CriteriaField(field = "rejectionStandard", operator = Operator.LIKE)
    private String rejectionStandard;
	/**
  	 * 扣款标准
   	 */
 	@CriteriaField(field = "penaltyStandard", operator = Operator.LIKE)
    private String penaltyStandard;
	/**
  	 * 排序
   	 */
 	@CriteriaField(field = "sortOrder", operator = Operator.LIKE)
    private Integer sortOrder;
	/**
  	 * 创建人名称
   	 */
 	@CriteriaField(field = "createByName", operator = Operator.LIKE)
    private String createByName;
	/**
  	 * 修改人名称
   	 */
 	@CriteriaField(field = "updateByName", operator = Operator.LIKE)
    private String updateByName;
	/**
  	 * 创建人
   	 */
 	@CriteriaField(field = "createBy", operator = Operator.LIKE)
    private String createBy;
	/**
  	 * 创建时间_开始
   	 */
	@CriteriaField(field = "createTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime createTimeStart;
	/**
  	 * 创建时间_结束
   	 */
    @CriteriaField(field = "createTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime createTimeEnd;
	/**
  	 * 修改人
   	 */
 	@CriteriaField(field = "updateBy", operator = Operator.LIKE)
    private String updateBy;
	/**
  	 * 修改时间_开始
   	 */
	@CriteriaField(field = "updateTime", operator = Operator.GE, filterBlank = true)
	private LocalDateTime updateTimeStart;
	/**
  	 * 修改时间_结束
   	 */
    @CriteriaField(field = "updateTime", operator = Operator.LE, filterBlank = true)
	private LocalDateTime updateTimeEnd;
	/**
  	 * 租户ID
   	 */
 	@CriteriaField(field = "tenantId", operator = Operator.LIKE)
    private Long tenantId;
	/**
  	 * 创建人ID
   	 */
 	@CriteriaField(field = "createById", operator = Operator.LIKE)
    private Long createById;
	/**
  	 * 更新人ID
   	 */
 	@CriteriaField(field = "updateById", operator = Operator.LIKE)
    private Long updateById;

    /**
    * 构建查询
    */
    public QueryWrapper<BaseQualityIndicatorAttributeValueEntity> toWrapper() {
        QueryWrapper<BaseQualityIndicatorAttributeValueEntity> queryWrapper = QueryWrapperHelper.fromBean(this, BaseQualityIndicatorAttributeValueEntity.class)
            .orderByDesc("id");
       return queryWrapper;
    }
}