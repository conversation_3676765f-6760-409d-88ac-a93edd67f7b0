<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmTenderOnlineQaMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmTenderOnlineQa">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="projectId" column="project_id" jdbcType="BIGINT"/>
            <result property="noticeId" column="notice_id" jdbcType="BIGINT"/>
            <result property="tenantSupplierId" column="tenant_supplier_id" jdbcType="BIGINT"/>
            <result property="supplierName" column="supplier_name" jdbcType="VARCHAR"/>
            <result property="questionTitle" column="question_title" jdbcType="VARCHAR"/>
            <result property="questionContent" column="question_content" jdbcType="VARCHAR"/>
            <result property="submitTime" column="submit_time" jdbcType="TIMESTAMP"/>
            <result property="submitBy" column="submit_by" jdbcType="VARCHAR"/>
            <result property="answerContent" column="answer_content" jdbcType="VARCHAR"/>
            <result property="answerStatus" column="answer_status" jdbcType="VARCHAR"/>
            <result property="answerTime" column="answer_time" jdbcType="TIMESTAMP"/>
            <result property="answerBy" column="answer_by" jdbcType="VARCHAR"/>
            <result property="answerByName" column="answer_by_name" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
        <select id="pageQuestion" resultType="com.ylz.saas.entity.SrmTenderOnlineQa">
            select id,tenant_id,dept_id,
                   project_id,notice_id,tenant_supplier_id,
                   supplier_name,question_title,question_content,
                   submit_time,submit_by,answer_content,
                   answer_status,answer_time,answer_by,
                   answer_by_name,del_flag,create_by,
                   create_by_name,create_time,update_by,
                   update_by_name,update_time from srm_tender_online_qa
            <where>
                <if test="req.projectId != null">
                    and project_id = #{req.projectId}
                </if>
                <if test="req.noticeId != null">
                    and notice_id = #{req.noticeId}
                </if>
                <if test="req.tenantSupplierId != null">
                    and tenant_supplier_id = #{req.tenantSupplierId}
                </if>
                <if test="req.supplierName != null and req.supplierName != ''">
                    and supplier_name like concat('%',#{req.supplierName},'%')
                </if>
                <if test="req.questionTitle != null and req.questionTitle != ''">
                    and question_title like concat('%',#{req.questionTitle},'%')
                </if>
                <if test="req.questionContent != null and req.questionContent != ''">
                    and question_content like concat('%',#{req.questionContent},'%')
                </if>
                <if test="req.answerStatus != null">
                    and answer_status = #{req.answerStatus}
                </if>
            </where>
            order by answer_status asc
        </select>


</mapper>
