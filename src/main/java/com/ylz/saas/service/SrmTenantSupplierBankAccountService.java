package com.ylz.saas.service;

import com.ylz.saas.entity.SrmTenantSupplierBankAccount;
import com.ylz.saas.vo.SupplierBankAccountInfoVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tenant_supplier_bank_account(租户供应商银行账户表)】的数据库操作Service
* @createDate 2025-06-10 18:08:09
*/
public interface SrmTenantSupplierBankAccountService extends IService<SrmTenantSupplierBankAccount> {



    /**
     * 保存或更新供应商银行账户信息
     *
     * @param supplierBankAccountInfoVo 供应商银行账户信息VO
     * @return 是否保存成功
     */
    boolean saveOrUpdateSupplierBankAccountInfo(SupplierBankAccountInfoVo supplierBankAccountInfoVo);

    /**
     * 根据供应商ID查询银行账户列表
     *
     * @param supplierId 供应商ID
     * @return 银行账户列表
     */
    List<SrmTenantSupplierBankAccount> getBankAccountsBySupplierId(Long supplierId);



    /**
     * 删除供应商银行账户信息
     *
     * @param id 银行账户ID
     * @return 是否删除成功
     */
    boolean deleteSupplierBankAccount(Long id);

    /**
     * 根据供应商ID分页查询银行账户列表
     *
     * @param supplierId 供应商ID
     * @param page 分页参数
     * @return 分页银行账户列表
     */
    Page<SrmTenantSupplierBankAccount> getBankAccountsBySupplierIdPage(Long supplierId, Page<SrmTenantSupplierBankAccount> page);

}
