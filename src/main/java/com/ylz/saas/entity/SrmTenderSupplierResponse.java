package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ylz.saas.common.core.util.TenantTable;

import com.ylz.saas.enums.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 供应商响应表
 * @TableName srm_tender_supplier_response
 */
@TenantTable
@TableName(value ="srm_tender_supplier_response")
@Data
public class SrmTenderSupplierResponse {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 采购立项ID
     */
    private Long projectId;

    /**
     * 标段ID
     */
    private Long sectionId;

    /**
     * 招标公告ID
     */
    private Long noticeId;

    /**
     * 租户供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 响应方式(SELF-自主报名、INVITE-邀请报名)
     */
    private TenderResponseTypeEnum responseType;

    /**
     * 响应时间
     */
    private LocalDateTime responseTime;

    /**
     * 响应IP
     */
    private String responseIp;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 联系邮箱
     */
    private String contactEmail;

    /**
     * 邀请回执
     */
    private String inviteReceipt;

    /**
     * 报名状态
     */
    private TenderSupplierRegisterStatusEnum registerStatus;

    /**
     * 保证金状态
     */
    private DepositStatusEnum depositStatus;

    /**
     * 标书费状态
     */
    private TenderFeeStatusEnum tenderFeeStatus;

    /**
     * 标书文件下载状态
     */
    private DownloadStatusEnum bidFileDownloadStatus;

    /**
     * 报价状态
     */
    private QuoteStatusEnum quoteStatus;

    /**
     * 当前报价轮次
     */
    private Integer currentRound;

    /**
     * 最新报价时间
     */
    private LocalDateTime newestQuoteTime;

    /**
     * 最新报价IP
     */
    private String newestQuoteIp;

    /**
     * 报名审核时间
     */
    private LocalDateTime registerReviewTime;

    /**
     * 保证金审核时间
     */
    private LocalDateTime depositReviewTime;

    /**
     * 标书费审核时间
     */
    private LocalDateTime tenderFeeReviewTime;

    /**
     * 报名驳回原因
     */
    private String registerRejectReason;

    /**
     * 保证金驳回原因
     */
    private String depositRejectReason;

    /**
     * 标书费驳回原因
     */
    private String tenderFeeRejectReason;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 投标要求响应
     */
    @TableField(exist = false)
    private List<SrmTenderSupplierBidderResponse> srmTenderSupplierBidderResponseList;

    /**
     * 保证金支付时间
     */
    @TableField(exist = false)
    private LocalDateTime depositTime;

    /**
     * 投标保证金支付信息
     */
    @TableField(exist = false)
    private String depositRespContent;

    /**
     * 标书费支付时间
     */
    @TableField(exist = false)
    private LocalDateTime tenderFeeTime;

    /**
     * 标书费支付信息
     */
    @TableField(exist = false)
    private String tenderFeeRespContent;

    /**
     * 租户供应商编码
     */
    @TableField(exist = false)
    private String tenantSupplierCode;


    /**
     * 邀请时间
     */
    @TableField(exist = false)
    private LocalDateTime inviteTime;

    /**
     * 邀请状态
     */
    @TableField(exist = false)
    private InviteStatusEnum inviteStatus;

    /**
     * 回执时间
     */
    @TableField(exist = false)
    private LocalDateTime inviteResponseTime;

    /**
     * 是否推荐中标(0-否、1-是)
     */
    private Integer isRecommendedWinner;

    /**
     * 中标候选顺序
     */
    private WinnerCandidateOrderEnum winnerCandidateOrder;

    /**
     * 保存中标金额
     */
    private BigDecimal quoteAmount;

    /**
     * 总分
     */
    private BigDecimal totalScore;

    /**
     * 是否中标(0-否、1-是)
     */
    private Integer isWin;

    /**
     * 报价附件列表
     */
    @TableField(exist = false)
    private List<SrmProjectAttachment> quoteAttachmentList;

    /**
     * 邀请附件列表
     */
    @TableField(exist = false)
    private List<SrmProjectAttachment> inviteAttachmentList;

    /**
     * 合同附件列表
     */
    @TableField(exist = false)
    private List<SrmProjectAttachment> contractAttachmentList;
}