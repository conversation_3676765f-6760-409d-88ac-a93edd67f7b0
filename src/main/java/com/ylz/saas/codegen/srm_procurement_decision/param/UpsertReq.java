package com.ylz.saas.codegen.srm_procurement_decision.param;

import cn.hutool.core.collection.CollUtil;
import com.ylz.saas.codegen.srm_procurement_decision.entity.SrmProcurementDecisionEntity;
import com.ylz.saas.codegen.srm_procurement_decision.enums.DecisionType;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.enums.ApprovalType;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpsertReq implements Serializable {
    @Serial
    private static final long serialVersionUID = -4050640794469607503L;
    /**
     * 主键ID
     */
    private Long id;
    @NotBlank(message = "申请标题不能为空")
    @Size(max = 30, message = "申请标题长度限制{max}字符")
    private String title;
    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    private Long projectId;
    /**
     * 项目编号
     */
    @NotBlank(message = "采购立项编码不能为空")
    private String projectCode;
    /**
     * 项目名称
     */
    @NotBlank(message = "采购立项名称不能为空")
    private String projectName;
    /**
     * 关联计划列表[(编号、名称)]
     */
    private List<SrmProcurementDecisionEntity.RelationPlanVO> relationPlanList;
    /**
     * 决策（申请）类型
     */
    @NotNull(message = "决策（申请）类型不能为空")
    private DecisionType decisionType;
    /**
     * 采购招标预算/控制价
     */
    @NotNull(message = "决策（申请）类型不能为空")
    @DecimalMax(value = "9999999999", message = "采购招标预算/控制价限制{value}")
    private BigDecimal controlPriceAmount;
    /**
     * 申请人ID（列表回显）
     */
    @NotNull(message = "申请人ID不能为空")
    private Long applicant;
    /**
     * 申请人姓名
     */
    @NotBlank(message = "申请人姓名不能为空")
    private String applicantName;
    /**
     * 申请部门ID（列表回显）
     */
    @NotNull(message = "申请部门ID不能为空")
    private Long applyDeptId;
    /**
     * 申请日期
     */
    @NotNull(message = "申请日期不能为空")
    private LocalDate applyDate;
    /**
     * 审批（发起）类型 (存 srm_process_instance)
     */
    @NotNull(message = "审批（发起）类型不能为空")
    private ApprovalType approvalType;
    /**
     * 指定审批人 [审批人id] (存 srm_process_instance)
     */
    private List<String> assignedApprover;
    /**
     * 申请原因
     */
    @NotBlank(message = "申请原因不能为空")
    @Size(max = 200, message = "申请标题长度限制{max}字符")
    private String applyReason;
    /**
     * 申请附件 [url] (存 srm_project_attachment)
     */
    private List<AttachmentBO> attachments;

    public SrmProcurementDecisionEntity buildOne() {
        List<String> _attachments = null;
        if (CollUtil.isNotEmpty(attachments)) {
            // 附件
            _attachments = attachments.stream().map(AttachmentBO::getFilePath).toList();
        }

        // 可编辑8个字段
        return SrmProcurementDecisionEntity.builder()
                .id(id)
                .title(title)
                .decisionType(decisionType)
                .controlPriceAmount(controlPriceAmount)
                .applyDate(applyDate)
                .approvalType(approvalType)
                .assignedApprover(approvalType.equals(ApprovalType.CUSTOM) ? assignedApprover : null)
                .applyReason(applyReason)
                .attachments(_attachments)
                .build();
    }

    public void validate() {
        if (approvalType.equals(ApprovalType.CUSTOM) && CollUtil.isEmpty(assignedApprover)) {
            ExceptionUtil.check(true, "500", "审批类型为指定审批人时，审批人不能为空！");
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AttachmentBO implements Serializable {
        /**
         * 文件名称
         */
        private String fileName;
        /**
         * 文件路径
         */
        private String filePath;
    }

}