package com.ylz.saas.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 招募公告终止请求类
 *
 * <AUTHOR>
 * @date 2025-08-07
 */
@Data
@Schema(description = "招募公告终止请求类")
public class SrmRecruitSupplierTerminateReq implements Serializable {

    /**
     * 招募公告ID
     */
    @NotNull(message = "招募公告ID不能为空")
    @Schema(description = "招募公告ID", required = true)
    private Long recruitId;

    /**
     * 终止原因
     */
    @NotBlank(message = "终止原因不能为空")
    @Schema(description = "终止原因", required = true)
    private String terminationReason;

    private static final long serialVersionUID = 1L;
}
