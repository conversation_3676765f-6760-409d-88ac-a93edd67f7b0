package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.admin.api.entity.SysUser;
import com.ylz.saas.admin.service.SysUserService;
import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.entity.SrmProjectMember;
import com.ylz.saas.entity.SrmTenderNoticeChange;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.AttachmentTypeEnum;
import com.ylz.saas.enums.ChangeTypeEnum;
import com.ylz.saas.enums.ProjectMemberRoleEnum;
import com.ylz.saas.enums.ProjectMemberTypeEnum;
import com.ylz.saas.mapper.SrmAllNoticeTabMapper;
import com.ylz.saas.req.SrmAllNoticePageReq;
import com.ylz.saas.resp.SrmAllInvitePageResp;
import com.ylz.saas.resp.SrmAllNoticePageResp;
import com.ylz.saas.resp.SrmAllPublicNoticePageResp;
import com.ylz.saas.resp.SrmAllPublicityPageResp;
import com.ylz.saas.service.SrmAllNoticeTabService;
import com.ylz.saas.service.SrmProjectAttachmentService;
import com.ylz.saas.service.SrmProjectMemberService;
import com.ylz.saas.service.SrmTenderNoticeChangeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 统一公告服务实现类
 * 通过XML文件实现复杂的SQL查询逻辑
 */
@Service
@RequiredArgsConstructor
public class SrmAllNoticeTabServiceImpl implements SrmAllNoticeTabService {

    private final SrmAllNoticeTabMapper srmAllNoticeTabMapper;
    private final SrmProjectAttachmentService srmProjectAttachmentService;
    private final SrmProjectMemberService srmProjectMemberService;
    private final SysUserService sysUserService;
    private final SrmTenderNoticeChangeService srmTenderNoticeChangeService;

    @Override
    public Page<SrmAllNoticePageResp> tenderNoticePage(SrmAllNoticePageReq req) {
        // 创建分页对象
        Page<SrmAllNoticePageResp> page = new Page<>(req.getCurrent(), req.getSize());

        // 通过XML SQL查询招标公告分页数据
        Page<SrmAllNoticePageResp> result = srmAllNoticeTabMapper.selectTenderNoticePage(page, req);

        // 批量设置项目责任人为发布人
        if (!CollectionUtils.isEmpty(result.getRecords())) {
            setProjectLeadersAsPublisher(result.getRecords());
            loadAttachments(result.getRecords(), AttachmentTypeEnum.NOTICE);
            // 查询变更信息，判断公告是否是变更后的，如果是，则在响应体中设置表更后的标识（字段使用isChange）;
            // 查询变更信息并设置变更标识
            setNoticeChangeFlag(result.getRecords());

        }

        return result;
    }

    @Override
    public Page<SrmAllInvitePageResp> invitePage(SrmAllNoticePageReq req) {
        // 创建分页对象
        Page<SrmAllInvitePageResp> page = new Page<>(req.getCurrent(), req.getSize());

        // 通过XML SQL查询邀请函分页数据
        Page<SrmAllInvitePageResp> result = srmAllNoticeTabMapper.selectInvitePage(page, req);

        // 批量设置项目责任人为发布人
        if (!CollectionUtils.isEmpty(result.getRecords())) {
            setProjectLeadersAsPublisher(result.getRecords());
            loadAttachments(result.getRecords(), AttachmentTypeEnum.NOTICE);
            // 查询变更信息并设置变更标识
            setInviteChangeFlag(result.getRecords());
        }

        return result;
    }

    private void setInviteChangeFlag(List<SrmAllInvitePageResp> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        try {
            // 收集所有公告ID
            Set<Long> noticeIds = records.stream()
                    .map(SrmAllInvitePageResp::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            if (CollectionUtils.isEmpty(noticeIds)) {
                return;
            }

            // 查询存在变更记录的公告ID
            Set<Long> changedNoticeIds = srmTenderNoticeChangeService.lambdaQuery()
                    .in(SrmTenderNoticeChange::getNoticeId, noticeIds)
                    .select(SrmTenderNoticeChange::getNoticeId)
                    .list()
                    .stream()
                    .map(SrmTenderNoticeChange::getNoticeId)
                    .collect(Collectors.toSet());

            // 为每条记录设置变更标识
            for (SrmAllInvitePageResp record : records) {
                if (record.getId() != null) {
                    record.setIsChange(changedNoticeIds.contains(record.getId()));
                } else {
                    record.setIsChange(false);
                }
            }
        } catch (Exception e) {
            // 变更标识设置失败不影响主要数据返回，仅记录日志
            // log.warn("设置公告变更标识失败", e);
        }
    }

    @Override
    public Page<SrmAllPublicityPageResp> publicityPage(SrmAllNoticePageReq req) {
        // 创建分页对象
        Page<SrmAllPublicityPageResp> page = new Page<>(req.getCurrent(), req.getSize());

        // 通过XML SQL查询公示分页数据
        Page<SrmAllPublicityPageResp> result = srmAllNoticeTabMapper.selectPublicityPage(page, req);

        // 批量设置项目责任人为发布人
        if (!CollectionUtils.isEmpty(result.getRecords())) {
            setProjectLeadersAsPublisher(result.getRecords());
            loadAttachments(result.getRecords(), AttachmentTypeEnum.PUBLICITY_ATTACHMENT);
        }

        return result;
    }

    @Override
    public Page<SrmAllPublicNoticePageResp> publicNoticePage(SrmAllNoticePageReq req) {
        // 创建分页对象
        Page<SrmAllPublicNoticePageResp> page = new Page<>(req.getCurrent(), req.getSize());

        // 通过XML SQL查询公告分页数据
        Page<SrmAllPublicNoticePageResp> result = srmAllNoticeTabMapper.selectPublicNoticePage(page, req);

        // 批量设置项目责任人为发布人
        if (!CollectionUtils.isEmpty(result.getRecords())) {
            setProjectLeadersAsPublisher(result.getRecords());
            loadAttachments(result.getRecords(), AttachmentTypeEnum.NOTICE);
        }

        return result;
    }

    /**
     * 设置公告变更标识
     */
    private void setNoticeChangeFlag(List<SrmAllNoticePageResp> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        try {
            // 收集所有公告ID
            Set<Long> noticeIds = records.stream()
                    .map(SrmAllNoticePageResp::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            if (CollectionUtils.isEmpty(noticeIds)) {
                return;
            }

            // 查询存在变更记录的公告ID
            Set<Long> changedNoticeIds = srmTenderNoticeChangeService.lambdaQuery()
                    .in(SrmTenderNoticeChange::getNoticeId, noticeIds)
                    .eq(SrmTenderNoticeChange::getApprovedStatus, ApproveStatusEnum.APPROVE)
                    .eq(SrmTenderNoticeChange::getChangeType, ChangeTypeEnum.CHANGE_NOTICE)
                    .select(SrmTenderNoticeChange::getNoticeId)
                    .list()
                    .stream()
                    .map(SrmTenderNoticeChange::getNoticeId)
                    .collect(Collectors.toSet());

            // 为每条记录设置变更标识
            for (SrmAllNoticePageResp record : records) {
                if (record.getId() != null) {
                    record.setIsChange(changedNoticeIds.contains(record.getId()));
                } else {
                    record.setIsChange(false);
                }
            }
        } catch (Exception e) {
            // 变更标识设置失败不影响主要数据返回，仅记录日志
            // log.warn("设置公告变更标识失败", e);
        }
    }

    /**
     * 批量设置项目责任人为发布人
     * @param records 记录列表
     */
    private <T> void setProjectLeadersAsPublisher(List<T> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        try {
            // 收集所有项目ID
            Set<Long> projectIds = records.stream()
                    .map(this::getProjectId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            if (CollectionUtils.isEmpty(projectIds)) {
                return;
            }

            // 批量查询项目负责人
            List<SrmProjectMember> projectLeaders = srmProjectMemberService.lambdaQuery()
                    .eq(SrmProjectMember::getMemberType, ProjectMemberTypeEnum.PROJECT_MEMBER)
                    .eq(SrmProjectMember::getRole, ProjectMemberRoleEnum.PROJECT_LEADER)
                    .in(SrmProjectMember::getBusinessId, projectIds)
                    .eq(SrmProjectMember::getDelFlag, 0)
                    .list();

            if (CollectionUtils.isEmpty(projectLeaders)) {
                return;
            }

            // 获取所有负责人的用户ID
            Set<Long> userIds = projectLeaders.stream()
                    .map(SrmProjectMember::getUserId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            if (CollectionUtils.isEmpty(userIds)) {
                return;
            }

            // 批量查询用户信息
            List<SysUser> users = sysUserService.lambdaQuery()
                    .in(SysUser::getUserId, userIds)
                    .list();

            if (CollectionUtils.isEmpty(users)) {
                return;
            }

            // 构建用户ID到用户名的映射
            Map<Long, String> userIdToNameMap = users.stream()
                    .collect(Collectors.toMap(SysUser::getUserId, SysUser::getName));

            // 构建项目ID到负责人姓名的映射
            Map<Long, String> projectIdToLeaderNameMap = projectLeaders.stream()
                    .filter(leader -> userIdToNameMap.containsKey(leader.getUserId()))
                    .collect(Collectors.toMap(
                            SrmProjectMember::getBusinessId,
                            leader -> userIdToNameMap.get(leader.getUserId()),
                            (existing, replacement) -> existing // 如果有重复，保留第一个
                    ));

            // 设置发布人信息
            for (T record : records) {
                Long projectId = getProjectId(record);
                if (projectId != null && projectIdToLeaderNameMap.containsKey(projectId)) {
                    String leaderName = projectIdToLeaderNameMap.get(projectId);
                    setPublisher(record, leaderName);
                }
            }
        } catch (Exception e) {
            // 设置项目负责人失败不影响主要数据的返回，只记录日志
            // log.warn("批量设置项目责任人失败", e);
        }
    }

    /**
     * 获取项目ID
     */
    private <T> Long getProjectId(T record) {
        if (record instanceof SrmAllNoticePageResp) {
            return ((SrmAllNoticePageResp) record).getProjectId();
        } else if (record instanceof SrmAllInvitePageResp) {
            return ((SrmAllInvitePageResp) record).getProjectId();
        } else if (record instanceof SrmAllPublicityPageResp) {
            return ((SrmAllPublicityPageResp) record).getProjectId();
        } else if (record instanceof SrmAllPublicNoticePageResp) {
            return ((SrmAllPublicNoticePageResp) record).getProjectId();
        }
        return null;
    }

    /**
     * 设置发布人
     */
    private <T> void setPublisher(T record, String publisherName) {
        if (record instanceof SrmAllNoticePageResp) {
            ((SrmAllNoticePageResp) record).setPublisher(publisherName);
        } else if (record instanceof SrmAllInvitePageResp) {
            ((SrmAllInvitePageResp) record).setPublisher(publisherName);
        } else if (record instanceof SrmAllPublicityPageResp) {
            ((SrmAllPublicityPageResp) record).setPublisher(publisherName);
        } else if (record instanceof SrmAllPublicNoticePageResp) {
            ((SrmAllPublicNoticePageResp) record).setPublisher(publisherName);
        }
    }

    /**
     * 批量加载附件信息
     * @param records 记录列表
     * @param attachmentType 附件类型
     */
    private <T> void loadAttachments(List<T> records, AttachmentTypeEnum attachmentType) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        try {
            // 收集所有需要查询附件的业务ID
            List<Long> businessIds = new ArrayList<>();

            for (T record : records) {
                Long businessId = getBusinessId(record);
                if (businessId != null) {
                    businessIds.add(businessId);
                }
            }

            if (CollectionUtils.isEmpty(businessIds)) {
                return;
            }

            // 批量查询附件信息
            List<SrmProjectAttachment> attachments = srmProjectAttachmentService.lambdaQuery()
                    .eq(SrmProjectAttachment::getDelFlag, 0)
                    .eq(SrmProjectAttachment::getBusinessType, attachmentType)
                    .in(SrmProjectAttachment::getBusinessId, businessIds)
                    .list();

            if (CollectionUtils.isEmpty(attachments)) {
                return;
            }

            // 按业务ID分组
            Map<Long, List<SrmProjectAttachment>> attachmentMap = attachments.stream()
                    .collect(Collectors.groupingBy(SrmProjectAttachment::getBusinessId));

            // 设置附件信息到对应的记录中
            for (T record : records) {
                Long businessId = getBusinessId(record);
                if (businessId != null) {
                    List<SrmProjectAttachment> recordAttachments = attachmentMap.get(businessId);
                    setAttachmentList(record, recordAttachments != null ? recordAttachments : new ArrayList<>());
                }
            }
        } catch (Exception e) {
            // 附件加载失败不影响主要数据的返回，只记录日志
            // log.warn("批量加载附件信息失败", e);
        }
    }

    /**
     * 获取业务ID
     */
    private <T> Long getBusinessId(T record) {
        if (record instanceof SrmAllNoticePageResp) {
            return ((SrmAllNoticePageResp) record).getId();
        } else if (record instanceof SrmAllInvitePageResp) {
            return ((SrmAllInvitePageResp) record).getId();
        } else if (record instanceof SrmAllPublicityPageResp) {
            return ((SrmAllPublicityPageResp) record).getEvaluationResultId();
        } else if (record instanceof SrmAllPublicNoticePageResp) {
            return ((SrmAllPublicNoticePageResp) record).getId();
        }
        return null;
    }

    /**
     * 设置附件列表
     */
    private <T> void setAttachmentList(T record, List<SrmProjectAttachment> attachments) {
        if (record instanceof SrmAllNoticePageResp) {
            ((SrmAllNoticePageResp) record).setAttachmentList(attachments);
        } else if (record instanceof SrmAllInvitePageResp) {
            ((SrmAllInvitePageResp) record).setAttachmentList(attachments);
        } else if (record instanceof SrmAllPublicityPageResp) {
            ((SrmAllPublicityPageResp) record).setAttachmentList(attachments);
        } else if (record instanceof SrmAllPublicNoticePageResp) {
            ((SrmAllPublicNoticePageResp) record).setAttachmentList(attachments);
        }
    }
}
