package com.ylz.saas.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 评标专家打分分页查询请求类
 */
@Data
@Schema(description = "评标专家打分分页查询请求类")
public class SrmTenderBidEvaluationScoringPageReq implements Serializable {

    /**
     * 当前页
     */
    @Schema(description = "当前页")
    private Long current = 1L;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小")
    private Long size = 10L;

    /**
     * 采购立项ID
     */
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 标段ID
     */
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 专家用户id
     */
    @Schema(description = "专家用户id")
    private Long userId;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id")
    private Long tenantSupplierId;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private String status;

    /**
     * 类型（评审项、评分项）
     */
    @Schema(description = "类型（评审项、评分项）")
    private String type;

    private static final long serialVersionUID = 1L;
}
