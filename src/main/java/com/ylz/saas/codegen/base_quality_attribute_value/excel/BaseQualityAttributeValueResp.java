package com.ylz.saas.codegen.base_quality_attribute_value.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 质量属性值表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:42:43
 */
@Data
public class BaseQualityAttributeValueResp implements Serializable {

    /**
  	 * 主键ID
   	 */
	@ExcelProperty(value = "主键ID")
    private Long id;



    /**
  	 * 属性值内容
   	 */
	@ExcelProperty(value = "属性值内容")
    private String valueContent;

    /**
  	 * 适用物料编码
   	 */
	@ExcelProperty(value = "适用物料编码")
    private String materialCode;

    /**
  	 * 拒收标准
   	 */
	@ExcelProperty(value = "拒收标准")
    private String rejectionStandard;

    /**
  	 * 扣款标准
   	 */
	@ExcelProperty(value = "扣款标准")
    private String penaltyStandard;




    /**
  	 * 创建人
   	 */
	@ExcelProperty(value = "创建人")
    private String createBy;

    /**
  	 * 创建时间
   	 */
	@ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
	@ExcelProperty(value = "修改人")
    private String updateBy;

    /**
  	 * 修改时间
   	 */
	@ExcelProperty(value = "修改时间")
    private LocalDateTime updateTime;



}