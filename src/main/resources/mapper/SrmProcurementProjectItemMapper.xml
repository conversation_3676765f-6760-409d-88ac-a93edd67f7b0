<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylz.saas.mapper.SrmProcurementProjectItemMapper">

    <resultMap id="BaseResultMap" type="com.ylz.saas.entity.SrmProcurementProjectItem">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="deptId" column="dept_id" jdbcType="BIGINT"/>
            <result property="projectId" column="project_id" jdbcType="BIGINT"/>
            <result property="sectionId" column="section_id" jdbcType="BIGINT"/>
            <result property="serviceTypeId" column="service_type_id" jdbcType="BIGINT"/>
            <result property="sourcingType" column="sourcing_type" jdbcType="VARCHAR"/>
            <result property="materialCode" column="material_code" jdbcType="VARCHAR"/>
            <result property="materialName" column="material_name" jdbcType="VARCHAR"/>
            <result property="specModel" column="spec_model" jdbcType="VARCHAR"/>
            <result property="unit" column="unit" jdbcType="VARCHAR"/>
            <result property="requiredQuantity" column="required_quantity" jdbcType="DECIMAL"/>
            <result property="unitPrice" column="unit_price" jdbcType="DECIMAL"/>
            <result property="totalPrice" column="total_price" jdbcType="DECIMAL"/>
            <result property="listPrice" column="list_price" jdbcType="DECIMAL"/>
            <result property="budgetPrice" column="budget_price" jdbcType="DECIMAL"/>
            <result property="qualityIndicatorId" column="quality_indicator_id" jdbcType="BIGINT"/>
            <result property="paymentMethodId" column="payment_method_id" jdbcType="BIGINT"/>
            <result property="usageLocationId" column="usage_location_id" jdbcType="BIGINT"/>
            <result property="supplyPeriod" column="supply_period" jdbcType="VARCHAR"/>
            <result property="planId" column="plan_id" jdbcType="BIGINT"/>
            <result property="note" column="note" jdbcType="VARCHAR"/>
            <result property="delFlag" column="del_flag" jdbcType="TINYINT"/>
            <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createByName" column="create_by_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateById" column="update_by_id" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateByName" column="update_by_name" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,dept_id,
        project_id,section_id,service_type_id,
        sourcing_type,material_code,material_name,
        spec_model,unit,required_quantity,
        unit_price,total_price,list_price,
        budget_price,quality_indicator_id,payment_method_id,
        usage_location_id,supply_period,plan_id,
        note,del_flag,create_by_id,
        create_by,create_by_name,create_time,
        update_by_id,update_by,update_by_name,
        update_time
    </sql>
</mapper>
