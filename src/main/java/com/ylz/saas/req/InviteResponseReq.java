package com.ylz.saas.req;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @Date 2025/6/27 16:11
 * @Description
 */
@Data
public class InviteResponseReq {

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    private Long noticeId;

    /**
     * 招标标段ID
     */
    @NotNull(message = "招标标段ID不能为空")
    private Long sectionId;

    /**
     * 联系人
     */
    @NotBlank(message = "联系人不能为空")
    @Length(max = 50, message = "联系人长度不能超过50")
    private String concatName;

    /**
     * 联系人电话
     */
    @NotBlank(message = "联系人电话不能为空")
    @Length(max = 50, message = "联系人电话长度不能超过50")
    private String concatPhone;

    /**
     * 联系人邮箱
     */
    @Length(max = 50, message = "联系人邮箱长度不能超过50")
    private String concatEmail;

    /**
     * 回执内容
     */
    @NotBlank(message = "回执内容不能为空")
    private String responseContent;

    /**
     * 回执附件名称
     */
    private String attachmentName;

    /**
     * 回执附件URL
     */
    private String attachmentUrl;

}
