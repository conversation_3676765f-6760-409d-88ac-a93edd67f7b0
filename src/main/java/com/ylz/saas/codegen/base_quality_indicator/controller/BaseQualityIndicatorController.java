package com.ylz.saas.codegen.base_quality_indicator.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.codegen.base_quality_indicator.entity.BaseQualityIndicatorEntity;
import com.ylz.saas.codegen.base_quality_indicator.param.BaseQualityIndicatorParam;
import com.ylz.saas.codegen.base_quality_indicator.service.BaseQualityIndicatorService;
import com.ylz.saas.codegen.base_quality_indicator.vo.BaseQualityIndicatorVo;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 质量指标表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:00
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/baseQualityIndicator" )
@Tag(description = "baseQualityIndicator" , name = "质量指标表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class BaseQualityIndicatorController {

    private final  BaseQualityIndicatorService baseQualityIndicatorService;
    /**
     * 启用/禁用
     * @return R
     */
    @Operation(summary = "启用/禁用" , description = "启用/禁用" )
    @PostMapping("/switch/{id}")
    public R switchStatus(@PathVariable("id") Long id) {
        return R.ok(baseQualityIndicatorService.switchStatus(id));
    }

    /**
     * 分页查询
     * @param page 分页对象
     * @param param 质量指标表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @PostMapping("/page" )
    public R getBaseQualityIndicatorPage(@RequestBody BaseQualityIndicatorParam param, Page page) {
        return R.ok(baseQualityIndicatorService.pageInfo(page, param));
    }

    /**
     * 通过id查询质量指标表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(baseQualityIndicatorService.getByIdBaseQualityIndicator(id));
    }

    /**
     * 新增质量指标表
     * @param baseQualityIndicatorVo 质量指标表
     * @return R
     */
    @Operation(summary = "新增质量指标表" , description = "新增质量指标表" )
    @SysLog("新增质量指标表" )
    @PostMapping("/save")
    public R save(@Valid @RequestBody BaseQualityIndicatorVo baseQualityIndicatorVo) {
        return R.ok(baseQualityIndicatorService.saveBaseQualityIndicator(baseQualityIndicatorVo));
    }

    /**
     * 修改质量指标表
     * @param baseQualityIndicatorVo 质量指标表
     * @return R
     */
    @Operation(summary = "修改质量指标表" , description = "修改质量指标表" )
    @SysLog("修改质量指标表" )
    @PostMapping("/update")
    public R updateById(@Valid @RequestBody BaseQualityIndicatorVo baseQualityIndicatorVo) {
        return R.ok(baseQualityIndicatorService.updateBaseQualityIndicator(baseQualityIndicatorVo));
    }

    /**
     * 通过id删除质量指标表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除质量指标表" , description = "通过id删除质量指标表" )
    @SysLog("通过id删除质量指标表" )
    @GetMapping("remove")
    public R removeById(@RequestParam List<Long> ids) {
		boolean remove = baseQualityIndicatorService.removeByIdsBaseQualityIndicator(ids);
		return R.ok(remove);
    }

}