package com.ylz.saas.resp;

import com.ylz.saas.entity.SrmTenderBidEvaluationScoring;
import com.ylz.saas.enums.ApproveStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 专家打分查询响应类
 */
@Data
@Schema(description = "专家打分查询响应类")
public class SrmTenderBidEvaluationScoringQueryResp implements Serializable {

    /**
     * 供应商回应id
     */
    @Schema(description = "供应商回应id")
    private Long responseId;
    /**
     * 评分id
     */
    @Schema(description = "评分id")
    private Long scoringId;


    /**
     * 评标标准ID
     */
    @Schema(description = "评标标准ID")
    private Long standardId;

    /**
     * 采购立项ID
     */
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 招标公告ID
     */
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 标段ID
     */
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 专家用户ID
     */
    @Schema(description = "专家用户ID")
    private Long userId;

    /**
     * 专家姓名
     */
    @Schema(description = "专家姓名")
    private String expertName;

    /**
     * 专家编码
     */
    @Schema(description = "专家编码")
    private String expertCode;

    /**
     * 专家分类
     */
    @Schema(description = "专家分类")
    private String expertCategory;

    /**
     * 专家角色（EVALUATION_LEADER-评标组长，EVALUATION_MEMBER-评标成员）
     */
    @Schema(description = "专家角色（EVALUATION_LEADER-评标组长，EVALUATION_MEMBER-评标成员）")
    private String role;

    /**
     * 供应商ID
     */
    @Schema(description = "供应商ID")
    private Long tenantSupplierId;

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    private String supplierName;

    /**
     * 节点名称
     */
    @Schema(description = "节点名称")
    private String nodeName;

    /**
     * 评审项名称
     */
    @Schema(description = "评审项名称")
    private String itemName;

    /**
     * 评审项描述
     */
    @Schema(description = "评审项描述")
    private String itemDescription;

    /**
     * 总分
     */
    private Integer totalScore;

    /**
     * 满分分值
     */
    @Schema(description = "满分分值")
    private Integer maxScore;

    /**
     * 最小分值
     */
    @Schema(description = "最小分值")
    private Integer minScore;

    /**
     * 权重
     */
    @Schema(description = "权重")
    private Double weight;

    /**
     * 分值（评分项）
     */
    @Schema(description = "分值（评分项）")
    private Integer score;

    /**
     * 状态：待提交 已提交（评审完成）
     */
    @Schema(description = "状态：待提交 已提交（评审完成）")
    private SrmTenderBidEvaluationScoring.SubmitStatus status;

    /**
     * 是否符合（评审项）
     */
    @Schema(description = "是否符合（评审项）")
    private Integer isConform;

    /**
     * 类型（评审项、评分项）
     */
    @Schema(description = "类型（评审项、评分项）")
    private String type;

    /**
     * 评分类型（专家评分、组长汇总）
     */
    @Schema(description = "评分类型（专家评分、组长汇总）")
    private SrmTenderBidEvaluationScoring.ScoringType scoringType;

    /**
     * 评审结论
     */
    @Schema(description = "评审结论")
    private String conclusion;

    /**
     * 是否已打分
     */
    @Schema(description = "是否已打分")
    private Boolean isScored;

    /**
     * 供应商报价状态
     */
    @Schema(description = "供应商报价状态")
    private String quoteStatus;

    /**
     * 打分创建时间
     */
    @Schema(description = "打分创建时间")
    private LocalDateTime scoringCreateTime;

    /**
     * 打分修改时间
     */
    @Schema(description = "打分修改时间")
    private LocalDateTime scoringUpdateTime;

    private static final long serialVersionUID = 1L;
}
