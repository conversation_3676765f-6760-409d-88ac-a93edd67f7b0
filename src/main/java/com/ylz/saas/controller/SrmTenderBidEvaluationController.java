package com.ylz.saas.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.req.SrmTenderBidEvaluationReq;
import com.ylz.saas.req.SrmTenderBidEvaluationScoringBatchReq;
import com.ylz.saas.req.SrmTenderBidEvaluationScoringQueryReq;
import com.ylz.saas.req.SrmTenderEvaluationSummaryReq;
import com.ylz.saas.req.SrmTenderEvaluationSummaryQueryReq;
import com.ylz.saas.req.SrmTenderOfflineEvaluationSummaryQueryReq;
import com.ylz.saas.req.SrmTenderOfflineEvaluationSummaryReq;
import com.ylz.saas.req.SrmTenderSupplierReviewDetailQueryReq;
import com.ylz.saas.req.SrmTenderLeaderSummaryReviewReq;
import com.ylz.saas.req.SrmTenderSectionPageReq;
import com.ylz.saas.resp.SrmTenderBidEvaluationResp;
import com.ylz.saas.resp.SrmTenderBidEvaluationScoringTableResp;
import com.ylz.saas.resp.SrmTenderNodeEvaluationSummaryResp;
import com.ylz.saas.resp.SrmTenderEvaluationSummaryQueryResp;
import com.ylz.saas.resp.SrmTenderOfflineEvaluationSummaryQueryResp;
import com.ylz.saas.resp.SrmTenderSupplierReviewDetailQueryResp;
import com.ylz.saas.resp.SrmTenderSectionPageResp;
import com.ylz.saas.service.SrmTenderBidEvaluationScoringService;
import com.ylz.saas.service.SrmTenderBidEvaluationService;
import com.ylz.saas.service.SrmTenderEvaluationSignatureService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 评标委员会控制器
 * <AUTHOR>
 * @createDate 2025-07-09
 */
@RestController
@RequestMapping("/srmTenderBidEvaluation")
@Tag(name = "评标委员会管理", description = "评标委员会相关接口")
@Slf4j
@AllArgsConstructor
public class SrmTenderBidEvaluationController {


    private final SrmTenderBidEvaluationService srmTenderBidEvaluationService;
    private final SrmTenderBidEvaluationScoringService srmTenderBidEvaluationScoringService;
    private final SrmTenderEvaluationSignatureService srmTenderEvaluationSignatureService;




//以下是评标委员为接口

    /**
     * 新增评标委员会
     */
    @PostMapping("/save")
    @Operation(summary = "新增评标委员会", description = "新增评标委员会信息")
    public R<Boolean> save(@RequestBody @Validated SrmTenderBidEvaluationReq req) {
        boolean result = srmTenderBidEvaluationService.saveBidEvaluation(req);
        return result ? R.ok(true, "新增成功") : R.failed("新增失败");
    }

    /**
     * 修改评标委员会
     */
    @PostMapping("/update")
    @Operation(summary = "修改评标委员会", description = "修改评标委员会信息")
    public R<Boolean> update(@RequestBody @Validated SrmTenderBidEvaluationReq req) {
        if (req.getId() == null) {
            return R.failed("ID不能为空");
        }
        boolean result = srmTenderBidEvaluationService.updateBidEvaluation(req);
        return result ? R.ok(true, "修改成功") : R.failed("修改失败");
    }


    /**
     * 根据标段ID查询评标委员会
     */
    @GetMapping("/getBySectionId/{sectionId}")
    @Operation(summary = "根据标段ID查询评标委员会", description = "根据标段ID查询当前轮次的评标委员会")
    public R<SrmTenderBidEvaluationResp> getBySectionId(@PathVariable Long sectionId) {
        SrmTenderBidEvaluationResp result = srmTenderBidEvaluationService.getBySectionId(sectionId);
        return R.ok(result);
    }

    /**
     * 分页查询项目标段列表
     */
    @PostMapping("/pageSections")
    @Operation(summary = "分页查询项目标段列表", description = "查询某项目下的所有标段，包含专家抽取状态、操作信息和评标完成状态")
    public R<IPage<SrmTenderSectionPageResp>> pageSections(@RequestBody @Validated SrmTenderSectionPageReq req) {
        IPage<SrmTenderSectionPageResp> result = srmTenderBidEvaluationService.pageSections(req);
        return R.ok(result, "查询成功");
    }

//以下是评标打分接口






    /**
     * 查询专家对某节点的项目打分情况（表格格式）
     */
    @PostMapping("/getExpertScoringTableByNode")
    @Operation(summary = "查询专家对某节点的项目打分情况（表格格式）", description = "根据节点名称和专家ID查询指定专家的打分情况，返回表格格式数据，包含合计行。userId必传")
    public R<SrmTenderBidEvaluationScoringTableResp> getExpertScoringTableByNode(@RequestBody @Validated SrmTenderBidEvaluationScoringQueryReq req) {
        SrmTenderBidEvaluationScoringTableResp result = srmTenderBidEvaluationScoringService.getExpertScoringTableByNode(req);
        return R.ok(result, "查询成功");
    }

    /**
     * 撤回评标结果
     */
    @PostMapping("/revokeScoringResult")
    @Operation(summary = "撤回评标结果", description = "撤回评标结果")
    public R revokeScoringResult(@RequestBody @Validated SrmTenderBidEvaluationScoringQueryReq req) {
        return R.ok(srmTenderBidEvaluationScoringService.revokeScoringResult(req));
    }

    /**
     * 查询某节点下所有专家对各个供应商的打分/评审情况汇总
     */
    @PostMapping("/getNodeEvaluationSummary")
    @Operation(summary = "查询节点评审汇总", description = "查询某节点下所有专家对各个供应商的打分/评审情况汇总。评审节点返回通过/不通过状态，评分节点返回分数、平均分、权重和实际得分")
    public R<SrmTenderNodeEvaluationSummaryResp> getNodeEvaluationSummary(@RequestBody @Validated SrmTenderBidEvaluationScoringQueryReq req) {
        SrmTenderNodeEvaluationSummaryResp result = srmTenderBidEvaluationScoringService.getNodeEvaluationSummary(req);
        return R.ok(result, "查询成功");
    }
    /**
     * 批量保存专家打分
     */
    @PostMapping("/batchSaveScoring")
    @Operation(summary = "批量保存专家打分", description = "批量保存专家打分信息，支持新增和修改。当ID为空或0时新增，当ID不为空且大于0时修改")
    public R<Boolean> batchSaveScoring(@RequestBody @Validated SrmTenderBidEvaluationScoringBatchReq req) {
        boolean result = srmTenderBidEvaluationScoringService.batchSubmitScoring(req);
        return result ? R.ok(true, "批量保存成功") : R.failed("批量保存失败");
    }


//评标汇总相关接口
    /**
     * 评标汇总
     */
    @PostMapping("/summaryEvaluation")
    @Operation(summary = "评标汇总（新版）", description = "进行评标汇总，生成评标报告，保存中标候选人顺序和推荐中标信息，支持小组长评分项汇总批量操作，并更新汇总状态为已汇总")
    public R<Boolean> summaryEvaluation(@RequestBody @Validated SrmTenderEvaluationSummaryReq req) {
        boolean result = srmTenderBidEvaluationService.summaryEvaluation(req);
        return result ? R.ok(true, "汇总成功") : R.failed("汇总失败");
    }

    /**
     * 查询评标汇总供应商信息
     */
    @PostMapping("/queryEvaluationSummary")
    @Operation(summary = "查询评标汇总供应商信息", description = "根据标段查询所有供应商的评标汇总信息，包括评审项汇总、评分项分数、投标价格、排名等。评分项计算逻辑：按节点汇总，优先使用组长修改的节点总分，否则使用(专家分数总和/专家数量)×权重")
    public R<SrmTenderEvaluationSummaryQueryResp> queryEvaluationSummary(@RequestBody @Validated SrmTenderEvaluationSummaryQueryReq req) {
        SrmTenderEvaluationSummaryQueryResp result = srmTenderBidEvaluationService.queryEvaluationSummary(req);
        return R.ok(result, "查询成功");
    }





    /**
     * 评标人员签名
     */
    @PostMapping("/signEvaluation")
    @Operation(summary = "评标人员签名", description = "评标人员对汇总结果进行签名确认")
    public R<Boolean> signEvaluation(@RequestParam Long evaluationId,
                                   @RequestParam Long userId,
                                   @RequestParam(required = false) String signatureComment) {
        boolean result = srmTenderEvaluationSignatureService.signEvaluation(evaluationId, userId, signatureComment);
        return result ? R.ok(true, "签名成功") : R.failed("签名失败");
    }

    /**
     * 查询线下评标汇总供应商信息
     */
    @PostMapping("/queryOfflineEvaluationSummary")
    @Operation(summary = "查询线下评标汇总供应商信息", description = "根据标段查询线下评标的供应商汇总信息，包括供应商名称、评标总分、投标价格、排名等")
    public R<SrmTenderOfflineEvaluationSummaryQueryResp> queryOfflineEvaluationSummary(@RequestBody @Validated SrmTenderOfflineEvaluationSummaryQueryReq req) {
        SrmTenderOfflineEvaluationSummaryQueryResp result = srmTenderBidEvaluationService.queryOfflineEvaluationSummary(req);
        return R.ok(result, "查询成功");
    }

    /**
     * 线下评标汇总保存（线下评标）
     */
    @PostMapping("/summaryOfflineEvaluation")
    @Operation(summary = "线下评标汇总保存", description = "保存线下评标汇总信息，包括手动输入的评标总分、投标价格、中标候选人信息，并上传评标结果报告")
    public R<Boolean> summaryOfflineEvaluation(@RequestBody @Validated SrmTenderOfflineEvaluationSummaryReq req) {
        boolean result = srmTenderBidEvaluationService.summaryOfflineEvaluation(req);
        return result ? R.ok(true, "保存成功") : R.failed("保存失败");
    }

    /**
     * 查询供应商评审项详情
     */
    @PostMapping("/querySupplierReviewDetail")
    @Operation(summary = "查询供应商评审项详情", description = "查询所有评标人对某个供应商评审项的评审情况，表头为专家名称、各个评审项节点名称，内容显示符合或者不符合，并且回显评审备注")
    public R<SrmTenderSupplierReviewDetailQueryResp> querySupplierReviewDetail(@RequestBody @Validated SrmTenderSupplierReviewDetailQueryReq req) {
        SrmTenderSupplierReviewDetailQueryResp result = srmTenderBidEvaluationService.querySupplierReviewDetail(req);
        return R.ok(result, "查询成功");
    }


    /**
     * 组长保存汇总评审结果
     */
    @PostMapping("/leaderSaveSummaryReview")
    @Operation(summary = "组长保存汇总评审结果", description = "组长保存指定供应商的评审项汇总结果和评分项节点分数修改，支持按节点修改评分项总分，作为特殊的组长结论，一次只处理一个供应商")
    public R<Boolean> leaderSaveSummaryReview(@RequestBody @Validated SrmTenderLeaderSummaryReviewReq req) {
        boolean result = srmTenderBidEvaluationService.leaderSaveSummaryReview(req);
        return result ? R.ok(true, "保存成功") : R.failed("保存失败");
    }


    /**
     * 获取项目下所有标段是否完成评标
     * 如果标段没有配置评标标准，则直接返回true（认为评标已完成）
     * 如果标段配置了评标标准，则检查所有专家是否都已完成评标
     */
    @GetMapping("/getNoticeEvaluationCompleteStatus/{noticeId}")
    @Operation(summary = "获取评标完成状态", description = "检查项目下所有标段是否完成评标，未配置评标标准的标段视为已完成")
    public R<Boolean> getNoticeEvaluationCompleteStatus(@PathVariable Long noticeId) {
        return R.ok(srmTenderBidEvaluationService.getNoticeEvaluationCompleteStatus(noticeId));
    }

}
