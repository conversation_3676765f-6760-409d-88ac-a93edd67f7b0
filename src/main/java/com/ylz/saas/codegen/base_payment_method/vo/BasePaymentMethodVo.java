package com.ylz.saas.codegen.base_payment_method.vo;

import com.ylz.saas.codegen.base_payment_method_period.entity.BasePaymentMethodPeriodEntity;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 支付方式表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:34
 */
@Data
public class BasePaymentMethodVo implements Serializable {

    /**
  	 * 主键ID
   	 */
    private Long id;

    /**
  	 * 组织ID
   	 */
    private Long deptId;

    /**
  	 * 支付方式编码
   	 */
    private String methodCode;

    /**
  	 * 支付方式名称
   	 */
    private String methodName;

    /**
  	 * 应用业务类型
   	 */
    private String applicationType;

    /**
  	 * 是否有账期（YES-有账期、NO-无账期）
   	 */
    private String period;

    /**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
    private String status;

    /**
  	 * 创建人名称
   	 */
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    private String updateByName;

    /**
  	 * 创建人
   	 */
    private String createBy;

    /**
  	 * 创建时间
   	 */
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
    private String updateBy;

    /**
  	 * 修改时间
   	 */
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
    private Long createById;

    /**
  	 * 更新人ID
   	 */
    private Long updateById;
	/**
	* 子表集合:base_payment_method_period
	*/
	private List<BasePaymentMethodPeriodEntity> basePaymentMethodPeriodList;
}