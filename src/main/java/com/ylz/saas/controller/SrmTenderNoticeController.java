package com.ylz.saas.controller;

import com.ylz.saas.common.core.util.R;
import com.ylz.saas.entity.SrmTenderNotice;
import com.ylz.saas.req.SrmTenderNoticeAddReq;
import com.ylz.saas.req.SrmTenderNoticeReviewReq;
import com.ylz.saas.req.SrmTenderSupplierInviteReq;
import com.ylz.saas.resp.SrmTenderNoticeAddResp;
import com.ylz.saas.service.SrmTenderNoticeService;
import com.ylz.saas.service.SrmTenderSupplierInviteService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 招标公告控制层
 *
 * <AUTHOR>
 * @since 2025-06-10 14:11:33
 */
@RestController
@RequestMapping("/srmTenderNotice")
public class SrmTenderNoticeController {

    @Resource
    private SrmTenderNoticeService srmTenderNoticeService;

    @Resource
    private SrmTenderSupplierInviteService supplierInviteService;

    /**
     * 新增招标公告（同时具备编辑功能）
     *
     * @param req 筛选条件
     * @return 查询结果
     */
    @PostMapping("/add")
    public R<String> addTenderNotice(@RequestBody @Validated SrmTenderNoticeAddReq req) {
        String noticeId = srmTenderNoticeService.addTenderNotice(req);
        return R.ok(noticeId);
    }

    /**
     * 招标公告，提交审核
     *
     * @param noticeId 筛选条件
     */
    @PostMapping("/toReview/{noticeId}")
    public R<Void> addTenderNoticeToReview(@PathVariable("noticeId") String noticeId) {
        srmTenderNoticeService.addTenderNoticeToReview(noticeId);
        return R.ok();
    }

    /**
     * 招标公告详情
     *
     * @param noticeId
     * @return
     */
    @PostMapping("/detail/{noticeId}")
    public R<SrmTenderNoticeAddResp> tenderNoticeDetail(@PathVariable("noticeId") String noticeId) {
        SrmTenderNoticeAddResp resp = srmTenderNoticeService.tenderNoticeDetail(noticeId);
        return R.ok(resp);
    }

    /**
     * 审核招标公告
     *
     * @param req
     * @return
     */
    @PostMapping("/review")
    public R<Void> addTenderNoticeReview(@RequestBody @Validated SrmTenderNoticeReviewReq req) {
        srmTenderNoticeService.addTenderNoticeReview(req);
        return R.ok();
    }


    /**
     * 获取当前项目有效的招标公告
     */
    @GetMapping("/getEffectNotice/{projectId}")
    public R<SrmTenderNotice> getEffectNotice(@PathVariable Long projectId){
        return R.ok(srmTenderNoticeService.getEffectNotice(projectId));
    }

    /**
     * 邀请供应商
     * @param req
     */
    @PostMapping("/inviteSupplier")
    public R<Void> inviteSupplier(@RequestBody @Validated SrmTenderSupplierInviteReq req){
        supplierInviteService.supplierInviteList(req);
        return R.ok();
    }

}

