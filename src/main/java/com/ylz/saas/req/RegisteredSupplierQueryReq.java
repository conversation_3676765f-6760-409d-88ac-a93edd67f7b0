package com.ylz.saas.req;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/25 19:49
 * @Description
 */
@Data
public class RegisteredSupplierQueryReq {

    /**
     * 公告id
     */
    @NotNull(message = "公告id不能为空")
    private Long noticeId;

    /**
     * 标段id
     */
    @NotNull(message = "标段id不能为空")
    private Long sectionId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 报名状态
     */
    private String registerStatus;

    /**
     * 保证金缴纳状态
     */
    private List<String> depositStatusList;

    /**
     * 回执状态
     */
    private List<String> inviteStatusList;

    /**
     * 标书费缴纳状态
     */
    private List<String> tenderFeeStatusList;

}
