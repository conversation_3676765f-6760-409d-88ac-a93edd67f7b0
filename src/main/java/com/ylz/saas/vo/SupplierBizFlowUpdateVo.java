package com.ylz.saas.vo;

import com.ylz.saas.enums.SupplierApprovalStatus;
import lombok.Data;

import java.io.Serializable;

/**
 * 供应商业务审批流执行后的更新方法实体
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
public class SupplierBizFlowUpdateVo implements Serializable {

    /**
     * 租户供应商主键ID
     */
    private Long id;

    /**
     * 当前审批人
     */
    private String currentApprover;

    /**
     * 审批状态
     */
    private SupplierApprovalStatus approvalStatus;
}
