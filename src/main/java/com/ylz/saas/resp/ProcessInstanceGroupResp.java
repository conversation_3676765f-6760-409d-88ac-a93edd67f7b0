package com.ylz.saas.resp;

import com.ylz.saas.entity.SrmProcessInstance;
import com.ylz.saas.service.SrmProcessConfigService;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 流程实例分组响应对象
 * 用于按bizId和type分组返回流程实例数据
 * <AUTHOR>
 * @Date 2025-07-08
 * @Description
 */
@Data
public class ProcessInstanceGroupResp {

    /**
     * 按bizId分组的流程实例映射
     * Key: bizId, Value: 该bizId下按type分组的流程实例
     */
    private Map<Long, Map<SrmProcessConfigService.BizTypeEnum, List<SrmProcessInstance>>> bizIdGroupMap;

    /**
     * 按type分组的流程实例映射
     * Key: type, Value: 该type下按bizId分组的流程实例
     */
    private Map<SrmProcessConfigService.BizTypeEnum, Map<Long, List<SrmProcessInstance>>> typeGroupMap;

    /**
     * 所有流程实例的平铺列表
     */
    private List<SrmProcessInstance> allInstances;

    /**
     * 构造函数
     */
    public ProcessInstanceGroupResp() {
        this.bizIdGroupMap = new HashMap<>();
        this.typeGroupMap = new HashMap<>();
        this.allInstances = new ArrayList<>();
    }

    /**
     * 从流程实例列表构建分组响应对象
     * @param instances 流程实例列表
     * @return 分组响应对象
     */
    public static ProcessInstanceGroupResp fromInstances(List<SrmProcessInstance> instances) {
        ProcessInstanceGroupResp resp = new ProcessInstanceGroupResp();
        resp.allInstances = instances;

        // 按bizId分组，然后按type分组
        resp.bizIdGroupMap = instances.stream()
                .collect(Collectors.groupingBy(
                        SrmProcessInstance::getBizId,
                        Collectors.groupingBy(SrmProcessInstance::getType)
                ));

        // 按type分组，然后按bizId分组
        resp.typeGroupMap = instances.stream()
                .collect(Collectors.groupingBy(
                        SrmProcessInstance::getType,
                        Collectors.groupingBy(SrmProcessInstance::getBizId)
                ));

        return resp;
    }

    /**
     * 通过bizId获取该业务下的所有流程实例
     * @param bizId 业务ID
     * @return 流程实例映射（按type分组）
     */
    public Map<SrmProcessConfigService.BizTypeEnum, List<SrmProcessInstance>> getInstancesByBizId(Long bizId) {
        return bizIdGroupMap.getOrDefault(bizId, new HashMap<>());
    }

    /**
     * 通过type获取该类型下的所有流程实例
     * @param type 业务类型
     * @return 流程实例映射（按bizId分组）
     */
    public Map<Long, List<SrmProcessInstance>> getInstancesByType(SrmProcessConfigService.BizTypeEnum type) {
        return typeGroupMap.getOrDefault(type, new HashMap<>());
    }

    /**
     * 通过指定的bizId和type获取流程实例列表
     * @param bizId 业务ID
     * @param type 业务类型
     * @return 流程实例列表
     */
    public List<SrmProcessInstance> getInstancesByBizIdAndType(Long bizId, SrmProcessConfigService.BizTypeEnum type) {
        return bizIdGroupMap.getOrDefault(bizId, new HashMap<>())
                .getOrDefault(type, new ArrayList<>());
    }

    /**
     * 检查指定的bizId和type组合是否存在流程实例
     * @param bizId 业务ID
     * @param type 业务类型
     * @return 是否存在
     */
    public boolean hasInstances(Long bizId, SrmProcessConfigService.BizTypeEnum type) {
        return !getInstancesByBizIdAndType(bizId, type).isEmpty();
    }

    /**
     * 获取指定bizId和type组合的流程实例数量
     * @param bizId 业务ID
     * @param type 业务类型
     * @return 实例数量
     */
    public int getInstanceCount(Long bizId, SrmProcessConfigService.BizTypeEnum type) {
        return getInstancesByBizIdAndType(bizId, type).size();
    }

    /**
     * 获取所有bizId集合
     * @return bizId集合
     */
    public java.util.Set<Long> getAllBizIds() {
        return bizIdGroupMap.keySet();
    }

    /**
     * 获取所有type集合
     * @return type集合
     */
    public java.util.Set<SrmProcessConfigService.BizTypeEnum> getAllTypes() {
        return typeGroupMap.keySet();
    }

    /**
     * 获取总的流程实例数量
     * @return 总数量
     */
    public int getTotalInstanceCount() {
        return allInstances.size();
    }
}
