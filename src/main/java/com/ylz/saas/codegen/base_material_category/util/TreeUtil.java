package com.ylz.saas.codegen.base_material_category.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.ylz.saas.codegen.base_material_category.entity.BaseMaterialCategoryEntity;
import com.ylz.saas.codegen.base_material_category.vo.BaseMaterialCategoryTreeVo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 树形结构工具类
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
public class TreeUtil {

    /**
     * 构建物料分类树形结构
     *
     * @param categoryList 物料分类列表
     * @return 树形结构列表
     */
    public static List<BaseMaterialCategoryTreeVo> buildMaterialCategoryTree(List<BaseMaterialCategoryEntity> categoryList) {
        if (CollectionUtil.isEmpty(categoryList)) {
            return new ArrayList<>();
        }

        // 转换为TreeVo
        List<BaseMaterialCategoryTreeVo> treeVoList = categoryList.stream()
                .map(entity -> {
                    BaseMaterialCategoryTreeVo treeVo = new BaseMaterialCategoryTreeVo();
                    BeanUtil.copyProperties(entity, treeVo);
                    treeVo.setChildren(new ArrayList<>());
                    treeVo.setHasChildren(false);
                    treeVo.setLevel(0);
                    return treeVo;
                })
                .collect(Collectors.toList());

        // 按父级编码分组
        Map<String, List<BaseMaterialCategoryTreeVo>> parentCodeMap = treeVoList.stream()
                .filter(vo -> StrUtil.isNotBlank(vo.getParentCategoryCode()))
                .collect(Collectors.groupingBy(BaseMaterialCategoryTreeVo::getParentCategoryCode));

        // 找出根节点（没有父级编码或父级编码为空的节点）
        List<BaseMaterialCategoryTreeVo> rootNodes = treeVoList.stream()
                .filter(vo -> StrUtil.isBlank(vo.getParentCategoryCode()))
                .collect(Collectors.toList());

        // 递归构建树形结构
        for (BaseMaterialCategoryTreeVo rootNode : rootNodes) {
            buildChildren(rootNode, parentCodeMap, 0);
            // 设置顶级分类名称
            setMaterialCategoryRoot(rootNode, rootNode.getCategoryName());
        }

        return rootNodes;
    }

    /**
     * 递归构建子节点
     *
     * @param parentNode    父节点
     * @param parentCodeMap 父级编码映射
     * @param level         当前层级
     */
    private static void buildChildren(BaseMaterialCategoryTreeVo parentNode, 
                                    Map<String, List<BaseMaterialCategoryTreeVo>> parentCodeMap, 
                                    int level) {
        parentNode.setLevel(level);
        
        List<BaseMaterialCategoryTreeVo> children = parentCodeMap.get(parentNode.getCategoryCode());
        if (CollectionUtil.isNotEmpty(children)) {
            parentNode.setChildren(children);
            parentNode.setHasChildren(true);
            
            // 递归构建子节点的子节点
            for (BaseMaterialCategoryTreeVo child : children) {
                buildChildren(child, parentCodeMap, level + 1);
            }
        }
    }

    /**
     * 获取所有子节点的编码（包括子节点的子节点）
     *
     * @param parentCode   父级编码
     * @param categoryList 所有分类列表
     * @return 子节点编码列表
     */
    public static List<String> getAllChildrenCodes(String parentCode, List<BaseMaterialCategoryEntity> categoryList) {
        List<String> result = new ArrayList<>();
        if (StrUtil.isBlank(parentCode) || CollectionUtil.isEmpty(categoryList)) {
            return result;
        }

        // 找出直接子节点
        List<String> directChildren = categoryList.stream()
                .filter(entity -> parentCode.equals(entity.getParentCategoryCode()))
                .map(BaseMaterialCategoryEntity::getCategoryCode)
                .collect(Collectors.toList());

        result.addAll(directChildren);

        // 递归找出子节点的子节点
        for (String childCode : directChildren) {
            result.addAll(getAllChildrenCodes(childCode, categoryList));
        }

        return result;
    }

    /**
     * 获取所有父节点的编码（包括父节点的父节点）
     *
     * @param childCode    子节点编码
     * @param categoryList 所有分类列表
     * @return 父节点编码列表
     */
    public static List<String> getAllParentCodes(String childCode, List<BaseMaterialCategoryEntity> categoryList) {
        List<String> result = new ArrayList<>();
        if (StrUtil.isBlank(childCode) || CollectionUtil.isEmpty(categoryList)) {
            return result;
        }

        // 创建编码到实体的映射
        Map<String, BaseMaterialCategoryEntity> codeMap = categoryList.stream()
                .collect(Collectors.toMap(BaseMaterialCategoryEntity::getCategoryCode, entity -> entity, (e1, e2) -> e1));

        BaseMaterialCategoryEntity current = codeMap.get(childCode);
        while (current != null && StrUtil.isNotBlank(current.getParentCategoryCode())) {
            result.add(current.getParentCategoryCode());
            current = codeMap.get(current.getParentCategoryCode());
        }

        return result;
    }

    /**
     * 递归设置顶级分类名称
     *
     * @param node 当前节点
     * @param rootCategoryName 顶级分类名称
     */
    private static void setMaterialCategoryRoot(BaseMaterialCategoryTreeVo node, String rootCategoryName) {
        if (node == null) {
            return;
        }

        // 设置当前节点的顶级分类名称
        node.setMaterialCategoryRoot(rootCategoryName);

        // 递归设置子节点的顶级分类名称
        if (CollectionUtil.isNotEmpty(node.getChildren())) {
            for (BaseMaterialCategoryTreeVo child : node.getChildren()) {
                setMaterialCategoryRoot(child, rootCategoryName);
            }
        }
    }
}
