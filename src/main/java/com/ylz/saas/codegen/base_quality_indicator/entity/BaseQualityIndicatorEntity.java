package com.ylz.saas.codegen.base_quality_indicator.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ylz.saas.codegen.base_quality_indicator_attribute_value.entity.BaseQualityIndicatorAttributeValueEntity;
import com.ylz.saas.codegen.base_quality_indicator_location.entity.BaseQualityIndicatorLocationEntity;
import com.ylz.saas.codegen.base_usage_location.entity.BaseUsageLocationEntity;
import com.ylz.saas.common.core.util.TenantTable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;


/**
 * 质量指标表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:00
 */
@Data
@TableName("base_quality_indicator")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "质量指标表")
@TenantTable
public class BaseQualityIndicatorEntity extends Model<BaseQualityIndicatorEntity> {


    /**
  	 * 主键ID
   	 */
    @TableId(type = IdType.AUTO)
    @Schema(description="主键ID")
    private Long id;

    /**
  	 * 组织ID
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="组织ID")
    private Long deptId;

    /**
     * 组织名称
     */
    @Schema(description="组织名称")
    @TableField(exist = false)
    private String deptName;

    /**
  	 * 指标编码
   	 */
    @Schema(description="指标编码")
    private String indicatorCode;

    /**
  	 * 指标名称
   	 */
    @Schema(description="指标名称")
    private String indicatorName;

    /**
  	 * 适用物料编码
   	 */
    @Schema(description="适用物料编码")
    private String materialCode;

    /**
     * 适用物料名称
     */
    @Schema(description="适用物料名称")
    @TableField(exist = false)
    private String materialName;

    /**
  	 * 指标描述
   	 */
    @Schema(description="指标描述")
    private String description;

    /**
  	 * 生效日期
   	 */
    @Schema(description="生效日期")
    private LocalDate releaseDate;

    /**
  	 * 失效日期
   	 */
    @Schema(description="失效日期")
    private LocalDate expiryDate;

    /**
  	 * 是否适用于所有部门（0指定部门 1所有部门)
   	 */
    @Schema(description="是否适用于所有部门（0指定部门 1所有部门)")
    private String applyAllLocations;

    /**
  	 * 状态
   	 */
    @Schema(description="状态")
    private String status;

    /**
  	 * 创建人名称
   	 */
    @Schema(description="创建人名称")
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    @Schema(description="修改人名称")
    private String updateByName;

    /**
  	 * 创建人
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人")
    private String createBy;

    /**
  	 * 创建时间
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建时间")
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改人")
    private String updateBy;

    /**
  	 * 修改时间
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="修改时间")
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    @Schema(description="删除标识（0-正常、1-删除）")
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    @Schema(description="租户ID")
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
  	@TableField(fill = FieldFill.INSERT)
    @Schema(description="创建人ID")
    private Long createById;

    /**
  	 * 更新人ID
   	 */
  	@TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description="更新人ID")
    private Long updateById;
	/**
	* 子表集合:base_quality_indicator_attribute_value
	*/
	@ExcelIgnore
	@TableField(exist = false)
	private List<BaseQualityIndicatorAttributeValueEntity> baseQualityIndicatorAttributeValueList;

	/**
	* 关联的使用部门集合:base_quality_indicator_location
	*/
//	@ExcelIgnore
//	@TableField(exist = false)
//	private List<BaseQualityIndicatorLocationEntity> baseQualityIndicatorLocationList;

	/**
	* 关联的使用部门对象集合
	*/
	@ExcelIgnore
	@TableField(exist = false)
	private List<String> locationNameList;
}