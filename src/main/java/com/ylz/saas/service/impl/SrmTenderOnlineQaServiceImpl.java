package com.ylz.saas.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.common.core.exception.ExceptionUtil;
import com.ylz.saas.common.security.service.SaasUser;
import com.ylz.saas.common.security.util.SecurityUtils;
import com.ylz.saas.entity.SrmTenantSupplierContact;
import com.ylz.saas.entity.SrmTenderOnlineQa;
import com.ylz.saas.enums.AnswerStatusEnum;
import com.ylz.saas.mapper.SrmTenderOnlineQaMapper;
import com.ylz.saas.req.SrmTenderOnlineQaAddReq;
import com.ylz.saas.req.SrmTenderOnlineQaPageReq;
import com.ylz.saas.req.SrmTenderOnlineQaReplyReq;
import com.ylz.saas.resp.SrmTenderOnlineQaPageResp;
import com.ylz.saas.service.SrmTenantSupplierContactService;
import com.ylz.saas.service.SrmTenderOnlineQaService;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_tender_online_qa(在线问答表)】的数据库操作Service实现
* @createDate 2025-06-10 14:22:17
*/
@Service
@AllArgsConstructor
public class SrmTenderOnlineQaServiceImpl extends ServiceImpl<SrmTenderOnlineQaMapper, SrmTenderOnlineQa>
    implements SrmTenderOnlineQaService{


    private final SrmTenantSupplierContactService srmTenantSupplierContactService;

    @Override
    public void addQuestion(SrmTenderOnlineQaAddReq req) {
        SaasUser user = SecurityUtils.getUser();
        SrmTenantSupplierContact contact = srmTenantSupplierContactService.getByUserAccount(user.getUsername());
        if(contact == null){
            ExceptionUtil.check(true,"500","当前用户不是供应商用户！");
        }
        SrmTenderOnlineQa qa = new SrmTenderOnlineQa();
        qa.setProjectId(req.getProjectId());
        qa.setNoticeId(req.getNoticeId());
        qa.setTenantSupplierId(contact.getTenantSupplierId());
        qa.setSupplierName(contact.getSupplierName());
        qa.setQuestionTitle(req.getQuestionTitle());
        qa.setQuestionContent(req.getQuestionContent());
        qa.setDelFlag(0);
        LocalDateTime currentTime = LocalDateTime.now();
        qa.setSubmitTime(currentTime);
        qa.setSubmitBy(user.getName());
        // 待回复
        qa.setAnswerStatus(AnswerStatusEnum.PENDING);
        save(qa);
    }

    @Override
    public void replyQuestion(SrmTenderOnlineQaReplyReq req) {
        SaasUser user = SecurityUtils.getUser();
        SrmTenderOnlineQa qa = getById(req.getId());
        ExceptionUtil.checkNonNull(qa, "问答信息不存在");
        String answerContent = qa.getAnswerContent();
        if  (StringUtils.isNotBlank(answerContent)) {
            ExceptionUtil.checkNonNull(null, "问答已回复！");
        }
        AnswerStatusEnum answerStatus = qa.getAnswerStatus();
        if(answerStatus == AnswerStatusEnum.REPLIED){
            ExceptionUtil.checkNonNull(null, "问答状态已回复！");
        }
        qa.setAnswerContent(req.getAnswerContent());
        qa.setAnswerTime(LocalDateTime.now());
        qa.setAnswerBy(user.getName());
        qa.setAnswerByName(user.getName());
        qa.setAnswerStatus(AnswerStatusEnum.REPLIED);

        updateById(qa);
    }

    @Override
    public Page<SrmTenderOnlineQaPageResp> findQuestion(SrmTenderOnlineQaPageReq req) {
        Page<SrmTenderOnlineQa> pageInfo = baseMapper.pageQuestion(req);
        Page<SrmTenderOnlineQaPageResp> pageLast = new Page<>();
        BeanUtils.copyProperties(pageInfo, pageLast);
        List<SrmTenderOnlineQa> records = pageInfo.getRecords();
        if(CollectionUtil.isEmpty(records)){
            return pageLast;
        }
        List<SrmTenderOnlineQaPageResp> lastRecords = records.stream().map(info -> {
            SrmTenderOnlineQaPageResp pageResp = new SrmTenderOnlineQaPageResp();
            BeanUtils.copyProperties(info, pageResp);
            return pageResp;
        }).toList();
        pageLast.setRecords(lastRecords);
        return pageLast;
    }
}




