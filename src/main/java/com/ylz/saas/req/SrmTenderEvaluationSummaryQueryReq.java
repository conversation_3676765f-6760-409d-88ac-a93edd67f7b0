package com.ylz.saas.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 评标汇总查询请求类
 * <AUTHOR>
 * @createDate 2025-07-11
 */
@Data
@Schema(description = "评标汇总查询请求类")
public class SrmTenderEvaluationSummaryQueryReq implements Serializable {

    /**
     * 标段ID
     */
    @NotNull(message = "标段ID不能为空")
    @Schema(description = "标段ID")
    private Long sectionId;

    /**
     * 招标公告ID
     */
    @NotNull(message = "招标公告ID不能为空")
    @Schema(description = "招标公告ID")
    private Long noticeId;

    /**
     * 采购立项ID
     */
    @NotNull(message = "采购立项ID不能为空")
    @Schema(description = "采购立项ID")
    private Long projectId;

    /**
     * 评标委员会ID
     */
    @NotNull(message = "评标委员会ID不能为空")
    @Schema(description = "评标委员会ID")
    private Long evaluationId;

    /**
     * 报价轮次
     */
    @NotNull(message = "报价轮次")
    @Schema(description = "报价轮次")
    private Integer currentRound;
    private static final long serialVersionUID = 1L;
}
