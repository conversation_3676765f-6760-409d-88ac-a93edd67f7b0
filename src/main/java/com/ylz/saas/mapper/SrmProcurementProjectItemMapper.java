package com.ylz.saas.mapper;

import com.ylz.saas.common.data.datascope.annotation.DataPermission;
import com.ylz.saas.entity.SrmProcurementProjectItem;
import com.ylz.saas.common.data.datascope.SaasBaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【srm_procurement_project_item(采购立项物资明细表)】的数据库操作Mapper
* @createDate 2025-06-11 11:21:09
* @Entity com.ylz.saas.entity.SrmProcurementProjectItem
*/
@Mapper
//@DataPermission(userEnabled = true, dataPermissionRuleList = "[{\"dataTypeCode\":\"PROJECT\",\"targetField\":\"project_id\",\"dataSourceField\":\"ID\"}]")
public interface SrmProcurementProjectItemMapper extends SaasBaseMapper<SrmProcurementProjectItem> {

}




