package com.ylz.saas.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.entity.SrmTenderNotice;
import com.ylz.saas.req.SrmTenderNoticeAddReq;
import com.ylz.saas.req.SrmTenderNoticeReviewReq;
import com.ylz.saas.req.TenderNoticeApproveReq;
import com.ylz.saas.resp.SrmTenderNoticeAddResp;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;

/**
* <AUTHOR>
* @description 针对表【srm_tender_notice(招标公告表)】的数据库操作Service
* @createDate 2025-06-10 14:13:56
*/
@Validated
public interface SrmTenderNoticeService extends IService<SrmTenderNotice> {

    String addTenderNotice(SrmTenderNoticeAddReq req);

    /**
     * 招标审核
     * @param req
     */
    void addTenderNoticeReview(SrmTenderNoticeReviewReq req);

    /**
     * 招标审核，通过
     * @param req
     */
    void addTenderNoticeReviewPass(@Valid TenderNoticeApproveReq req);


    /**
     * 招标审核，拒绝
     * @param req
     */
    void addTenderNoticeReviewReject(@Valid TenderNoticeApproveReq req);

    /**
     * 招标详情
     *
     * @param noticeId
     * @return
     */
    SrmTenderNoticeAddResp tenderNoticeDetail(String noticeId);

    SrmTenderNotice getEffectNotice(Long projectId);

    /**
     * 招标公告，提交审核
     * @param noticeId
     */
    void addTenderNoticeToReview(String noticeId);

    /**
     * 公告审批锁定修改为审批中
     *
     * @param noticeId
     */
    void noticeApprovingHook(String noticeId);

}
