
package com.ylz.saas.req;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;


@Data
public class BaseAnnouncementTemplateAddReq {

    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    private String templateName;

    /**
     * 模板场景ID
     */
    private Long templateSceneId;

    /**
     * 类型
     */
    private String type;

    /**
     * 版本号
     */
    private String version;

    /**
     * 模板说明
     */
    private String description;

    /**
     * 图标
     */
    private String iconUrl;

    /**
     * 模板内容
     */
    private String content;

    /**
     * 是否默认
     */
    private Integer isDefault;

}