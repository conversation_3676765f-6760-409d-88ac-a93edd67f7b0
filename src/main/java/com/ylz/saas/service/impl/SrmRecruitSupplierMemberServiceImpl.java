package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmRecruitSupplierMember;
import com.ylz.saas.mapper.SrmRecruitSupplierMemberMapper;
import com.ylz.saas.service.SrmRecruitSupplierMemberService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 供应商招募成员表Service实现类
 *
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
@Slf4j
@AllArgsConstructor
public class SrmRecruitSupplierMemberServiceImpl extends ServiceImpl<SrmRecruitSupplierMemberMapper, SrmRecruitSupplierMember> implements SrmRecruitSupplierMemberService {

}
