package com.ylz.saas.test.vo;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 单表测试页面
 *
 * <AUTHOR>
 * @date 2025-05-19 15:35:40
 */
@Data
public class SingleTableVo implements Serializable {

    /**
  	 * 主键
   	 */
    private Long id;

    /**
  	 * 名称
   	 */
    private String name;

    /**
  	 * 编码
   	 */
    private String code;

    /**
  	 * 创建人
   	 */
    private String createBy;

    /**
  	 * 创建时间
   	 */
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
    private String updateBy;

    /**
  	 * 修改时间
   	 */
    private LocalDateTime updateTime;

    /**
  	 * 删除标记
   	 */
    private String delFlag;

    /**
  	 * 租户ID
   	 */
    private Long tenantId;
}