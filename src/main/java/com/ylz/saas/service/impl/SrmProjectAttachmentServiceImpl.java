package com.ylz.saas.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.service.SrmProjectAttachmentService;
import com.ylz.saas.mapper.SrmProjectAttachmentMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【srm_project_attachment(附件表)】的数据库操作Service实现
* @createDate 2025-06-10 18:23:46
*/
@Service
public class SrmProjectAttachmentServiceImpl extends ServiceImpl<SrmProjectAttachmentMapper, SrmProjectAttachment>
    implements SrmProjectAttachmentService{

}




