package com.ylz.saas.dto;

import lombok.Data;

/**
 * 标段签名进度DTO
 * <AUTHOR>
 * @createDate 2025-07-11
 */
@Data
public class SectionSignatureProgressDto {
    
    /**
     * 标段ID
     */
    private Long sectionId;
    
    /**
     * 已签名人数
     */
    private Integer signedCount;
    
    /**
     * 总人数
     */
    private Integer totalCount;
    
    /**
     * 获取进度文本
     * @return 格式：已签名人数/总人数
     */
    public String getProgressText() {
        if (totalCount == null || totalCount == 0) {
            return "0/0";
        }
        return (signedCount != null ? signedCount : 0) + "/" + totalCount;
    }
}
