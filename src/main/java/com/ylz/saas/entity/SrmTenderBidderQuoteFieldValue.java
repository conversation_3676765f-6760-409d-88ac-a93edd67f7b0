package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import java.time.LocalDateTime;

import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import lombok.Data;
import com.ylz.saas.common.core.util.TenantTable;

/**
 * 投标报价字段值表
 * @TableName srm_tender_bidder_quote_field_value
 */
@TenantTable
@TableName(value ="srm_tender_bidder_quote_field_value")
@Data
public class SrmTenderBidderQuoteFieldValue {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 采购立项ID
     */
    private Long projectId;

    /**
     * 招标公告ID
     */
    private Long noticeId;

    /**
     * 标段ID
     */
    private Long sectionId;

    /**
     * 采购立项明细ID
     */
    private Long projectItemId;

    /**
     * 租户供应商ID
     */
    private Long tenantSupplierId;

    /**
     * 报价明细ID
     */
    private Long quoteItemId;

    /**
     * 服务类型ID
     */
    private Long serviceTypeId;

    /**
     * 字段编码
     */
    private String fieldCode;

    /**
     * 字段类型（string-字符串、int-整数、decimal-小数、date-日期、datetime-日期时间、enum-枚举）
     */
    private BaseServiceTypeFieldService.FieldTypeEnum fieldType;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 枚举值（JSON格式，当field_type为enums时使用）
     */
    private String enumValues;

    /**
     * 字段值
     */
    private String fieldValue;

    /**
     * 删除标识（0-正常、1-删除）
     */
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}