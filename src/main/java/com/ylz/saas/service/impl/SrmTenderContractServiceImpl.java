package com.ylz.saas.service.impl;

import cn.smallbun.screw.core.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylz.saas.codegen.base_payment_method.service.BasePaymentMethodService;
import com.ylz.saas.codegen.base_payment_method_period.service.BasePaymentMethodPeriodService;
import com.ylz.saas.codegen.base_usage_location.entity.BaseUsageLocationEntity;
import com.ylz.saas.codegen.base_usage_location.service.BaseUsageLocationService;
import com.ylz.saas.entity.SrmProcessInstance;
import com.ylz.saas.entity.SrmProcurementProject;
import com.ylz.saas.entity.SrmProcurementProjectSection;
import com.ylz.saas.entity.SrmProjectAttachment;
import com.ylz.saas.entity.SrmTenderBidderQuoteItem;
import com.ylz.saas.entity.SrmTenderContract;
import com.ylz.saas.entity.SrmTenderEvaluationResult;
import com.ylz.saas.enums.ApproveStatusEnum;
import com.ylz.saas.enums.AttachmentTypeEnum;
import com.ylz.saas.enums.ContractStatusEnum;
import com.ylz.saas.mapper.SrmTenderContractMapper;
import com.ylz.saas.req.ProcessInstanceStartReq;
import com.ylz.saas.req.SrmTenderContractQueryReq;
import com.ylz.saas.req.SrmTenderContractSaveReq;
import com.ylz.saas.resp.AwardedSupplierResp;
import com.ylz.saas.resp.SrmTenderContractDetailResp;
import com.ylz.saas.resp.SrmTenderContractQueryResp;
import com.ylz.saas.service.ApproveRejectHookService;
import com.ylz.saas.service.SrmProcessConfigService;
import com.ylz.saas.service.SrmProcessInstanceService;
import com.ylz.saas.service.SrmProcurementProjectPaymentService;
import com.ylz.saas.service.SrmProcurementProjectSectionService;
import com.ylz.saas.service.SrmProcurementProjectService;
import com.ylz.saas.service.SrmProjectAttachmentService;
import com.ylz.saas.service.SrmTenderBidderQuoteItemService;
import com.ylz.saas.service.SrmTenderContractService;
import com.ylz.saas.service.SrmTenderEvaluationResultService;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【srm_tender_contract(合同表)】的数据库操作Service实现
* @createDate 2025-07-21 17:26:10
*/
@Service
@Slf4j
@AllArgsConstructor
public class SrmTenderContractServiceImpl extends ServiceImpl<SrmTenderContractMapper, SrmTenderContract>
    implements SrmTenderContractService ,ApproveRejectHookService {
    private final SrmTenderEvaluationResultService evaluationResultService;
    private final SrmProjectAttachmentService projectAttachmentService;
    private final SrmTenderBidderQuoteItemService bidQuoteDetailService;
    private final BaseUsageLocationService baseUsageLocationService;
    private final SrmProcurementProjectService procurementProjectService;
    private final SrmProcurementProjectSectionService procurementProjectSectionService;
    private final SrmProcurementProjectPaymentService procurementProjectPaymentService;
    private final BasePaymentMethodPeriodService paymentMethodPeriodService;
    private final BasePaymentMethodService paymentMethodService;
    @Resource
    private SrmProcessInstanceService srmProcessInstanceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveContract(SrmTenderContractSaveReq req) {
        // 1. 查询是否已存在合同（未删除 且状态不为 已废除）
        LambdaQueryWrapper<SrmTenderContract> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SrmTenderContract::getSectionId, req.getSectionId())
                     .eq(SrmTenderContract::getDelFlag, 0)
                     .ne(SrmTenderContract::getContractStatus, ContractStatusEnum.REVOKED)
                     .ne(SrmTenderContract::getContractStatus, ContractStatusEnum.INVALID);

        long count = this.count(queryWrapper);
        if (count > 0) {
            throw new RuntimeException("该标段合同已存在，无法重复创建");
        }

        // 2. 复制 req 值到实体类
        SrmTenderContract contract = new SrmTenderContract();
        try {
            BeanUtils.copyProperties(contract, req);
        } catch (IllegalAccessException | InvocationTargetException e) {
            throw new RuntimeException("属性拷贝失败", e);
        }

        // 3. 存入其他的值
        // 3.1 通过 供方id + 标段id 查询 evaluationResultId
        LambdaQueryWrapper<SrmTenderEvaluationResult> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SrmTenderEvaluationResult::getSectionId, req.getSectionId())
                .eq(SrmTenderEvaluationResult::getTenantSupplierId, req.getTenantSupplierId())
                .last("LIMIT 1"); // 强制限制一条

        SrmTenderEvaluationResult result = evaluationResultService.getOne(wrapper, false); // 第二个参数false不抛出异常

        if (result != null) {
            contract.setEvaluationResultId(result.getId());
            // 3.2 通过 evaluationResultId 查询 noticeId
            contract.setNoticeId(result.getNoticeId());
        } else {
            throw new RuntimeException("未找到对应的评标结果");
        }

        // 3.3 生成 contractCode：HT-20250722001（年月日 + 3位流水号）
        String contractCode = generateContractCode();
        contract.setContractCode(contractCode);

        // 3.4 设置合同状态与审批状态
        contract.setContractStatus(ContractStatusEnum.NEW); // 新建状态
//        contract.setApprovalStatus(ApproveStatusEnum.APPROVING); // 审批中

        // 3.5 根据合同清单修改投标报价明细数量与总价
        if (req.getContractItems() != null && !req.getContractItems().isEmpty()) {
            for (SrmTenderContractSaveReq.ContractItem item : req.getContractItems()) {
                // 更新投标报价明细中的数量和总价
                SrmTenderBidderQuoteItem quoteItem = bidQuoteDetailService.getById(item.getId());
                if (quoteItem != null) {
                    // 直接使用前端传入的合同数量和合同总价
                    quoteItem.setContractQuantity(item.getContractQuantity());
                    quoteItem.setContractAmount(item.getContractAmount());
                    bidQuoteDetailService.updateById(quoteItem);
                }
            }
        }
        // 3.6 del_flag = 0
        contract.setDelFlag(0);


        // 4. 保存到数据库
        this.save(contract);

        // 5. 保存附件
//        SrmProjectAttachment newAttachment = req.getContractAttachment();
//        newAttachment.setBusinessType(AttachmentTypeEnum.TENDER_CONTRACT);
//        newAttachment.setBusinessId(contract.getId());
//        newAttachment.setFileName(newAttachment.getFileName());
//        newAttachment.setFilePath(newAttachment.getFilePath());
//        projectAttachmentService.save(newAttachment);
        if (req.getContractAttachment() != null && !req.getContractAttachment().isEmpty()) {
            for (SrmProjectAttachment attachment : req.getContractAttachment()) {
                if (attachment != null) {
                    attachment.setBusinessType(AttachmentTypeEnum.TENDER_CONTRACT);
                    attachment.setProjectId(req.getProjectId());
                    attachment.setBusinessId(contract.getId());
                    attachment.setFileName(attachment.getFileName());
                    attachment.setFilePath(attachment.getFilePath());
                    projectAttachmentService.save(attachment);
                }
            }
        }
        // 6. 提交审批
        ProcessInstanceStartReq startReq = new ProcessInstanceStartReq();
        startReq.setBizKey(contractCode);
        startReq.setArgs(List.of(contractCode , contractCode));
        startReq.setBizType(SrmProcessConfigService.BizTypeEnum.SRM_TENDER_CONTRACT_AUDIT);
        srmProcessInstanceService.startProcessInstance(startReq);
        log.info("业务流程发起完成！");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateContract(SrmTenderContractSaveReq req) {
        SrmTenderContract contract = this.handlerUpdateContract(req);

        // 9. 提交审批
        ProcessInstanceStartReq startReq = new ProcessInstanceStartReq();
        String code = contract.getContractCode();
        startReq.setBizKey(code);
        startReq.setArgs(List.of(code , code));
        startReq.setBizType(SrmProcessConfigService.BizTypeEnum.SRM_TENDER_CONTRACT_AUDIT);
        srmProcessInstanceService.startProcessInstance(startReq);
        log.info("业务流程发起完成！");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SrmTenderContract handlerUpdateContract(SrmTenderContractSaveReq req) {
        // 1. 校验合同是否存在
        SrmTenderContract contract = this.getById(req.getId());
        if (contract == null) {
            throw new RuntimeException("合同不存在");
        }
        // 2.如果状态为供方退回且待审批 存json信息
        if (contract.getApprovalStatus() ==ApproveStatusEnum.APPROVE && contract.getContractStatus() == ContractStatusEnum.SUPPLIER_REJECTED ) {
            SrmTenderContractDetailResp resp = this.queryDetail(getById(req.getId()).getContractCode());
            contract.setSupplierRejectedContractInfo(JSON.toJSONString(resp));
        }

        // 3. 复制 req 值到实体类
        try {
            BeanUtils.copyProperties(contract, req);
        } catch (IllegalAccessException | InvocationTargetException e) {
            throw new RuntimeException("属性拷贝失败", e);
        }


        // 4. 更新合同状态（1.审批不通过->新建 2.合同生效->不变 3.供应商退回->不变）
        if (contract.getApprovalStatus() != null && contract.getApprovalStatus() == ApproveStatusEnum.APPROVE_REJECT) {
            contract.setContractStatus(ContractStatusEnum.NEW);
        }
        // 5. 更新审批状态（改为审批中）
//        contract.setApprovalStatus(ApproveStatusEnum.APPROVING);

        // 6. 更新合同附件（如果有新附件）
        if (req.getContractAttachment() != null && !req.getContractAttachment().isEmpty()) {
            // 6.1 删除旧附件
            // 查询旧未删除附件
            LambdaQueryWrapper<SrmProjectAttachment> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(SrmProjectAttachment::getBusinessId, contract.getId())
                    .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.TENDER_CONTRACT)
                    .eq(SrmProjectAttachment::getDelFlag, 0);

            // 将旧附件标记为已删除
            List<SrmProjectAttachment> oldAttachments = projectAttachmentService.list(deleteWrapper);
            if (oldAttachments != null && !oldAttachments.isEmpty()) {
                for (SrmProjectAttachment oldAttachment : oldAttachments) {
                    oldAttachment.setDelFlag(1);
                    projectAttachmentService.updateById(oldAttachment);
                }
            }

            // 6.2 保存新附件列表
            for (SrmProjectAttachment newAttachment : req.getContractAttachment()) {
                if (newAttachment != null) {
                    newAttachment.setBusinessType(AttachmentTypeEnum.TENDER_CONTRACT);
                    newAttachment.setProjectId(req.getProjectId());
                    newAttachment.setBusinessId(contract.getId());
                    newAttachment.setFileName(newAttachment.getFileName());
                    newAttachment.setFilePath(newAttachment.getFilePath());
                    projectAttachmentService.save(newAttachment);
                }
            }
        } else {
            // 如果新附件列表为空，则删除所有旧附件
            LambdaQueryWrapper<SrmProjectAttachment> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(SrmProjectAttachment::getBusinessId, contract.getId())
                    .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.TENDER_CONTRACT)
                    .eq(SrmProjectAttachment::getDelFlag, 0);

            List<SrmProjectAttachment> oldAttachments = projectAttachmentService.list(deleteWrapper);
            if (oldAttachments != null && !oldAttachments.isEmpty()) {
                for (SrmProjectAttachment oldAttachment : oldAttachments) {
                    oldAttachment.setDelFlag(1);
                    projectAttachmentService.updateById(oldAttachment);
                }
            }
        }


        // 7. 更新合同清单和投标报价明细
        if (req.getContractItems() != null && !req.getContractItems().isEmpty()) {
            // 7.1 先将该标段下所有投标报价明细的 contractQuantity 和 contractAmount 设置为 null

            // 获取标段ID
            Long sectionId = req.getSectionId();
            if (sectionId == null) {
                throw new RuntimeException("标段ID不能为空");
            }

            // 查询该标段下的所有投标报价明细
            LambdaQueryWrapper<SrmTenderBidderQuoteItem> clearWrapper = new LambdaQueryWrapper<>();
            clearWrapper.eq(SrmTenderBidderQuoteItem::getSectionId, sectionId)
                    .eq(SrmTenderBidderQuoteItem::getTenantSupplierId, contract.getTenantSupplierId());

            List<SrmTenderBidderQuoteItem> allItems = bidQuoteDetailService.list(clearWrapper);

            // 清空 contractQuantity 和 contractAmount
            if (allItems != null && !allItems.isEmpty()) {
                for (SrmTenderBidderQuoteItem item : allItems) {
                    item.setContractQuantity(null);
                    item.setContractAmount(null);
                    bidQuoteDetailService.updateById(item);
                }
            }

            // 7.2 再根据当前合同明细设置新的 contractQuantity 和 contractAmount
            for (SrmTenderContractSaveReq.ContractItem item : req.getContractItems()) {
                SrmTenderBidderQuoteItem quoteItem = bidQuoteDetailService.getById(item.getId());
                if (quoteItem != null) {
                    quoteItem.setContractQuantity(item.getContractQuantity());
                    quoteItem.setContractAmount(item.getContractAmount());
                    bidQuoteDetailService.updateById(quoteItem);
                }
            }
        }


        // 8. 保存合同更新
        this.updateById(contract);
        return contract;
    }

    @Override
    public Page<SrmTenderContractQueryResp> query(Page<SrmTenderContractQueryResp> page, SrmTenderContractQueryReq req ) {
        if (req.getIsPurchase()) {
            Page<SrmTenderContractQueryResp> resultPage = this.baseMapper.selectContractPage(page, req);
            List<SrmTenderContractQueryResp> records = resultPage.getRecords();
            setProcessInstanceTypeByBizKey( records);
            resultPage.setRecords(records);
            return resultPage;
        }
        else{
            // 先正常查询
            Page<SrmTenderContractQueryResp> resultPage = this.baseMapper.selectContractPageBySupplier(page, req);
            // 对满足条件的记录，用supplier_rejected_contract_info中的数据覆盖
            List<SrmTenderContractQueryResp> records = resultPage.getRecords();
            for (SrmTenderContractQueryResp record : records) {
                if (record.getSupplierRejectedContractInfo() != null
                        && (record.getApprovalStatus().name().equals("APPROVING") || record.getApprovalStatus().name().equals("APPROVE_REJECT"))
                        && record.getContractStatus().name().equals("SUPPLIER_REJECTED")) {

                    // 解析JSON并覆盖相关字段
                    JSONObject json = JSON.parseObject(record.getSupplierRejectedContractInfo());
                    if (json.containsKey("id")) {
                        record.setId(json.getLong("id"));
                    }
                    if (json.containsKey("contractCode")) {
                        record.setContractCode(json.getString("contractCode"));
                    }
                    if (json.containsKey("contractName")) {
                        record.setContractName(json.getString("contractName"));
                    }
                    if (json.containsKey("signDate")) {
                        record.setSignDate(LocalDateTime.parse(json.getString("signDate")));
                    }
                    if (json.containsKey("totalAmount")) {
                        record.setTotalAmount(new BigDecimal(json.getString("totalAmount")));
                    }
                    if (json.containsKey("purchaseDeptName")) {
                        record.setPurchaseDeptName(json.getString("purchaseDeptName"));
                    }
                    if (json.containsKey("supplierName")) {
                        record.setSupplierName(json.getString("supplierName"));
                    }
                    if (json.containsKey("projectName")) {
                        record.setProjectName(json.getString("projectName"));
                    }
                    if (json.containsKey("sectionName")) {
                        record.setSectionName(json.getString("sectionName"));
                    }
                    if (json.containsKey("contractStatus")) {
                        record.setContractStatus(ContractStatusEnum.valueOf(json.getString("contractStatus")));
                    }
                    if (json.containsKey("approvalStatus")) {
                        record.setApprovalStatus(ApproveStatusEnum.valueOf(json.getString("approvalStatus")));
                    }
                    if (json.containsKey("createBy")) {
                        record.setCreateBy(json.getString("createBy"));
                    }
                    if (json.containsKey("createByName")) {
                        record.setCreateByName(json.getString("createByName"));
                    }
                    if (json.containsKey("createTime")) {
                        record.setCreateTime(LocalDateTime.parse(json.getString("createTime")));
                    }
                    if (json.containsKey("updateBy")) {
                        record.setUpdateBy(json.getString("updateBy"));
                    }
                    if (json.containsKey("updateByName")) {
                        record.setUpdateByName(json.getString("updateByName"));
                    }
                    if (json.containsKey("updateTime")) {
                        record.setUpdateTime(LocalDateTime.parse(json.getString("updateTime")));
                    }
                }
            }

            resultPage.setRecords(records);
            return resultPage;
        }
    }

    @Override
    public void delete(Long id) {
        // 查询合同是否存在
        SrmTenderContract contract = this.getById(id);
        if (contract == null) {
            throw new RuntimeException("合同不存在");
        }
        // 将合同相关附件的删除标记置为1
        LambdaQueryWrapper<SrmProjectAttachment> attachmentWrapper = new LambdaQueryWrapper<>();
        attachmentWrapper.eq(SrmProjectAttachment::getBusinessId, contract.getId())
                .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.TENDER_CONTRACT)
                .eq(SrmProjectAttachment::getDelFlag, 0);

        List<SrmProjectAttachment> attachments = projectAttachmentService.list(attachmentWrapper);
        for (SrmProjectAttachment attachment : attachments) {
            attachment.setDelFlag(1);
            projectAttachmentService.updateById(attachment);
        }

        this.removeById(contract.getId());

    }

    @Override
    public SrmTenderContractDetailResp queryDetail(String contractCode) {
        // 1. 查询合同基本信息
        LambdaQueryWrapper<SrmTenderContract> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SrmTenderContract::getContractCode, contractCode);
        SrmTenderContract contract = this.getOne(queryWrapper);

        if (contract == null) {
            throw new RuntimeException("合同不存在");
        }

        SrmTenderContractDetailResp resp = new SrmTenderContractDetailResp();
        try {
            BeanUtils.copyProperties(resp, contract);
        } catch (IllegalAccessException | InvocationTargetException e) {
            throw new RuntimeException("属性拷贝失败", e);
        }

        // 2. 查询其他信息
        // 2.1 采购立项名称srm_procurement_project
        if (contract.getProjectId() != null) {
            resp.setProjectName(procurementProjectService.getById(contract.getProjectId()).getProjectName());
        }
        // 2.2 标段名称srm_procurement_project_section
        if (contract.getSectionId() != null) {
            resp.setSectionName(procurementProjectSectionService.getById(contract.getSectionId()).getSectionName());
        }
        // 2.3 采购联系人名称srm_project_member（前端处理）

        // 3. 查询投标报价明细（合同清单）
        LambdaQueryWrapper<SrmTenderBidderQuoteItem> quoteItemWrapper = new LambdaQueryWrapper<>();
        quoteItemWrapper.eq(SrmTenderBidderQuoteItem::getSectionId, contract.getSectionId())
                .eq(SrmTenderBidderQuoteItem::getTenantSupplierId, contract.getTenantSupplierId())
                .isNotNull(SrmTenderBidderQuoteItem::getContractQuantity)
                .isNotNull(SrmTenderBidderQuoteItem::getContractAmount);

        List<SrmTenderBidderQuoteItem> quoteItems = bidQuoteDetailService.list(quoteItemWrapper);

        List<SrmTenderContractDetailResp.ContractItem> contractItems = quoteItems.stream()
                .map(item -> {
                    SrmTenderContractDetailResp.ContractItem contractItem = new SrmTenderContractDetailResp.ContractItem();

                    // 3.1 id、requireNo、materialCode、materialName、specModel、unit、contractQuantity、contractAmount
                    contractItem.setId(item.getId());
                    contractItem.setRequireNo(item.getRequireNo());
                    contractItem.setMaterialCode(item.getMaterialCode());
                    contractItem.setMaterialName(item.getMaterialName());
                    contractItem.setSpecModel(item.getSpecModel());
                    contractItem.setUnit(item.getUnit());
                    contractItem.setContractQuantity(item.getContractQuantity());
                    contractItem.setContractAmount(item.getContractAmount());

                    // 3.2 根据usageLocationId查base_usage_location表里的locationName、province、city、district、address
                    if (item.getUsageLocationId() != null) {
                        BaseUsageLocationEntity usageLocation = baseUsageLocationService.getById(item.getUsageLocationId());
                        if (usageLocation != null) {
                            contractItem.setLocationName(usageLocation.getLocationName());
                            contractItem.setProvince(usageLocation.getProvince());
                            contractItem.setCity(usageLocation.getCity());
                            contractItem.setDistrict(usageLocation.getDistrict());
                            contractItem.setAddress(usageLocation.getAddress());
                        }
                    }
                    // 3.3 查询支付方式（从明细中获取）
                    if (item.getProcurementProjectPaymentId() != null) {
                        contractItem.setPaymentMethodId(item.getProcurementProjectPaymentId());
                    }


                    return contractItem;
                }).toList();
        resp.setContractItems(contractItems);

        // 4. 查询合同附件列表
        LambdaQueryWrapper<SrmProjectAttachment> attachmentWrapper = new LambdaQueryWrapper<>();
        attachmentWrapper.eq(SrmProjectAttachment::getBusinessId, contract.getId())
                .eq(SrmProjectAttachment::getBusinessType, AttachmentTypeEnum.TENDER_CONTRACT)
                .eq(SrmProjectAttachment::getDelFlag, 0);
        List<SrmProjectAttachment> attachments = projectAttachmentService.list(attachmentWrapper);
        resp.setContractAttachment(attachments);


        return resp;
    }

    @Override
    public void revoke(Long id) {
        // 查询合同是否存在
        SrmTenderContract contract = this.getById(id);
        if (contract == null) {
            throw new RuntimeException("合同不存在");
        }

        // 设置合同状态为作废
        contract.setContractStatus(ContractStatusEnum.REVOKED);

        // 更新合同状态
        this.updateById(contract);
    }

    @Override
    public void approveReject(Long id) {
        // 查询合同是否存在
        SrmTenderContract contract = this.getById(id);
        if (contract == null) {
            throw new RuntimeException("合同不存在");
        }

        // 设置审批状态为审批不通过
        contract.setApprovalStatus(ApproveStatusEnum.APPROVE_REJECT);

        // 更新状态
        this.updateById(contract);
    }

    @Override
    public void confirmSign(Long id) {
        // 查询合同是否存在
        SrmTenderContract contract = this.getById(id);
        if (contract == null) {
            throw new RuntimeException("合同不存在");
        }
        //签章

        // 设置合同状态为合同生效
        contract.setContractStatus(ContractStatusEnum.EFFECTIVE);

        // 签章

        // 更新状态
        this.updateById(contract);
    }

    @Override
    public void contractReject(Long id) {
        // 查询合同是否存在
        SrmTenderContract contract = this.getById(id);
        if (contract == null) {
            throw new RuntimeException("合同不存在");
        }

        // 设置合同状态为供应商退回
        contract.setContractStatus(ContractStatusEnum.SUPPLIER_REJECTED);

        // 更新状态
        this.updateById(contract);
    }

    @Override
    public void approveRevoke(String contractCode) {
        // 查询合同是否存在
        LambdaQueryWrapper<SrmTenderContract> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SrmTenderContract::getContractCode, contractCode);
        SrmTenderContract contract = this.getOne(queryWrapper);
        if (contract == null) {
            throw new RuntimeException("合同不存在");
        }

        // 设置审批状态为审批撤销
        contract.setApprovalStatus(ApproveStatusEnum.APPROVE_REVOKE);

        // 更新状态
        this.updateById(contract);
    }

    @Override
    public void approve(Long id , String type) {
        // 查询合同是否存在
        SrmTenderContract contract = this.getById(id);
        if (contract == null) {
            throw new RuntimeException("合同不存在");
        }

        // 设置审批状态为审批通过
        contract.setApprovalStatus(ApproveStatusEnum.APPROVE);
        // 生效后更改/退回后更改，审批通过
        if (contract.getContractStatus() == ContractStatusEnum.EFFECTIVE || contract.getContractStatus() == ContractStatusEnum.SUPPLIER_REJECTED || contract.getContractStatus() == ContractStatusEnum.NEW) {
            contract.setContractStatus(ContractStatusEnum.PENDING_SUPPLIER);
        }
        // 使用UpdateWrapper强制更新supplier_rejected_contract_info字段，即使为null值
        UpdateWrapper<SrmTenderContract> updateWrapper =new UpdateWrapper<>();
        updateWrapper.eq("id", contract.getId());

        // 如果type为作废REVOKE则状态改为已作废
        if ("REVOKE".equals(type)) {
            contract.setContractStatus(ContractStatusEnum.REVOKED);
        }

        // 如果supplier_rejected_contract_info有内容，则置为空
        if (contract.getSupplierRejectedContractInfo() != null && !contract.getSupplierRejectedContractInfo().isEmpty()) {
            updateWrapper.set("supplier_rejected_contract_info", null);
            // 同时更新contract对象，保持一致性
            contract.setSupplierRejectedContractInfo(null);
        }
        // 更新状态
        this.update(contract, updateWrapper);
    }

    @Override
    public void contractStatusCheck() {
        // 查询所有生效中的合同
        LambdaQueryWrapper<SrmTenderContract> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SrmTenderContract::getContractStatus, ContractStatusEnum.EFFECTIVE);
        queryWrapper.eq(SrmTenderContract::getDelFlag, 0);

        List<SrmTenderContract> contracts = list(queryWrapper);

        LocalDateTime now = LocalDateTime.now();

        for (SrmTenderContract contract : contracts) {
            // 检查是否超过有效期终止时间
            if (contract.getEffectEndDate() != null &&
                    contract.getEffectEndDate().isBefore(now)) {
                // 设置为已失效状态
                contract.setContractStatus(ContractStatusEnum.INVALID);
                updateById(contract);
            }
        }
    }

    @Override
    public void submitApproval(Long id) {
        // 查询合同是否存在
        SrmTenderContract contract = this.getById(id);
        if (contract == null) {
            throw new RuntimeException("合同不存在");
        }
        contract.setApprovalStatus(ApproveStatusEnum.APPROVING);
        // 提交审批
        ProcessInstanceStartReq startReq = new ProcessInstanceStartReq();
        String contractCode = getById(id).getContractCode();
        startReq.setBizKey(contractCode);
        startReq.setArgs(List.of(contractCode , contractCode));
        startReq.setBizType(SrmProcessConfigService.BizTypeEnum.SRM_TENDER_CONTRACT_AUDIT);
        srmProcessInstanceService.startProcessInstance(startReq);
        log.info("业务流程发起完成！");

        // 更新状态
        this.updateById(contract);
    }

    @Override
    public void submitApprovalByCode(String contractCode) {
        // 查询合同是否存在
        LambdaQueryWrapper<SrmTenderContract> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SrmTenderContract::getContractCode, contractCode);
        SrmTenderContract contract = this.getOne(queryWrapper);

        if (contract == null) {
            throw new RuntimeException("合同不存在");
        }

        // 提交审批，设置审批状态为审批中
        contract.setApprovalStatus(ApproveStatusEnum.APPROVING);

        // 更新状态
        this.updateById(contract);
    }

    @Override
    public String queryOldContract(String contractCode) {
        LambdaQueryWrapper<SrmTenderContract> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SrmTenderContract::getContractCode, contractCode);

        SrmTenderContract contract = getOne(queryWrapper);

        if (contract != null) {
            return contract.getSupplierRejectedContractInfo();
        }
        return null;
    }

    @Override
    public List<AwardedSupplierResp> filterAwardedSuppliersWithExistingContract(List<AwardedSupplierResp> awardedSuppliers) {
        // 过滤掉已经有合同的标段和供应商组合
        return awardedSuppliers.stream().filter(supplier ->
                !hasExistingContract(supplier.getSectionId(), supplier.getTenantSupplierId())
        ).collect(Collectors.toList());
    }


    /**
         * 生成合同编号，格式：HT-20250722001（年月日 + 3位流水号）
         */

    private String generateContractCode () {
        // 获取当前日期字符串，格式：yyyyMMdd
        String datePrefix = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")); // 如 20250722

        // 查询当天最大流水号（包括已删除记录）
        Integer maxSequence = getBaseMapper().selectMaxContractSequence(datePrefix);
        if (maxSequence == null) {
            maxSequence = 0;
        }

        // 流水号递增
        int nextSequence = maxSequence + 1;

        // 格式化为3位，不足补0
        String sequenceStr = String.format("%03d", nextSequence);

        // 返回最终合同编号
        return "HT-" + datePrefix + sequenceStr;
    }


    @Override
    public boolean match(SrmProcessConfigService.BizTypeEnum bizType) {
        return bizType == SrmProcessConfigService.BizTypeEnum.SRM_TENDER_CONTRACT_AUDIT ||
               bizType == SrmProcessConfigService.BizTypeEnum.SRM_TENDER_CONTRACT_REVOKE_AUDIT;
    }


    @Override
    public void approveRejectHook(String bizKey) {
        // 根据合同编号查询合同
        LambdaQueryWrapper<SrmTenderContract> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SrmTenderContract::getContractCode, bizKey);
        SrmTenderContract contract = this.getOne(queryWrapper);

        // 如果合同存在，则调用revoke方法
        if (contract != null) {
            contract.setApprovalStatus(ApproveStatusEnum.APPROVE_REJECT);
        }
    }
    /**
     * 根据合同编码设置流程实例类型
     * @param records 合同记录列表
     */
    private void setProcessInstanceTypeByBizKey(List<SrmTenderContractQueryResp> records) {
        if (records == null || records.isEmpty()) {
            return;
        }

        // 为每个记录设置流程实例类型
        for (SrmTenderContractQueryResp record : records) {
            if (record.getContractCode() != null) {
                // 使用QueryWrapper查询biz_key为合同编号且approval_status为APPROVING的流程实例
                LambdaQueryWrapper<SrmProcessInstance> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SrmProcessInstance::getBizKey, record.getContractCode())
                        .eq(SrmProcessInstance::getApprovalStatus, ApproveStatusEnum.APPROVING)
                        .orderByDesc(SrmProcessInstance::getUpdateTime)
                        .last("LIMIT 1");


                SrmProcessInstance processInstance = srmProcessInstanceService.getOne(queryWrapper, false);
                if (processInstance != null) {
                    record.setProcessInstanceType(processInstance.getType());
                }
            }
        }

    }

    @Override
    public List<SrmProcurementProject> filterProjectsWithAllSectionsContracted(List<SrmProcurementProject> projects) {
        // 过滤掉所有标段都已有合同的项目
        return projects.stream().filter(project -> {
            // 查询该项目的所有标段
            LambdaQueryWrapper<SrmProcurementProjectSection> sectionQuery = new LambdaQueryWrapper<>();
            sectionQuery.eq(SrmProcurementProjectSection::getProjectId, project.getId())
                    .eq(SrmProcurementProjectSection::getDelFlag, 0);
            List<SrmProcurementProjectSection> sections = procurementProjectSectionService.list(sectionQuery);

            // 如果没有标段，保留该项目
            if (CollectionUtils.isEmpty(sections)) {
                return true;
            }

            // 检查每个标段是否所有中标供应商都有合同
            for (SrmProcurementProjectSection section : sections) {
                // 查询该标段下所有中标供应商
                LambdaQueryWrapper<SrmTenderBidderQuoteItem> quoteItemQuery = new LambdaQueryWrapper<>();
                quoteItemQuery.eq(SrmTenderBidderQuoteItem::getSectionId, section.getId())
                        .eq(SrmTenderBidderQuoteItem::getAwarded, 1)
                        .eq(SrmTenderBidderQuoteItem::getDelFlag, 0)
                        .select(SrmTenderBidderQuoteItem::getTenantSupplierId)
                        .groupBy(SrmTenderBidderQuoteItem::getTenantSupplierId);

                List<SrmTenderBidderQuoteItem> awardedSuppliers = bidQuoteDetailService.list(quoteItemQuery);

                // 如果该标段没有中标供应商，保留该项目
                if (CollectionUtils.isEmpty(awardedSuppliers)) {
                    return true;
                }

                // 检查是否所有中标供应商都有合同
                boolean allSuppliersHaveContract = true;
                for (SrmTenderBidderQuoteItem supplier : awardedSuppliers) {
                    if (!hasExistingContract(section.getId(), supplier.getTenantSupplierId())) {
                        allSuppliersHaveContract = false;
                        break;
                    }
                }

                // 如果有任何一个中标供应商没有合同，则保留该项目
                if (!allSuppliersHaveContract) {
                    return true;
                }
            }

            // 所有标段的所有中标供应商都有合同，过滤掉该项目
            return false;
        }).collect(Collectors.toList());
    }
    @Override
    public boolean hasExistingContract(Long sectionId, Long tenantSupplierId) {
        // 检查指定标段和供应商是否已经存在合同
        LambdaQueryWrapper<SrmTenderContract> contractQuery = new LambdaQueryWrapper<>();
        contractQuery.eq(SrmTenderContract::getSectionId, sectionId)
                .eq(SrmTenderContract::getTenantSupplierId, tenantSupplierId)
                .eq(SrmTenderContract::getDelFlag, 0)
                .ne(SrmTenderContract::getContractStatus, ContractStatusEnum.REVOKED)
                .ne(SrmTenderContract::getContractStatus, ContractStatusEnum.INVALID);
        return this.count(contractQuery) > 0;
    }

}




