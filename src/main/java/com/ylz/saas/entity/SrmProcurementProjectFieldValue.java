package com.ylz.saas.entity;

import com.baomidou.mybatisplus.annotation.*;

import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.common.core.util.TenantTable;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 采购立项字段值表
 * @TableName srm_procurement_project_field_value
 */
@TableName(value ="srm_procurement_project_field_value")
@Data
@TenantTable
public class SrmProcurementProjectFieldValue {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 部门ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long deptId;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 采购立项明细ID
     */
    private Long projectItemId;

    /**
     * 服务类型ID
     */
    private Long serviceTypeId;

    /**
     * 字段编码
     */
    private String fieldCode;

    /**
     * 字段类型（string-字符串、int-整数、decimal-小数、date-日期、datetime-日期时间、enum-枚举）
     */
    private BaseServiceTypeFieldService.FieldTypeEnum fieldType;

    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 枚举值（JSON格式，当field_type为enums时使用）
     */
    private String enumValues;

    /**
     * 字段值
     */
    private String fieldValue;

    /**
     * 删除标识（0-正常、1-删除）
     */
    private Integer delFlag;

    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createById;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateById;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateByName;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}