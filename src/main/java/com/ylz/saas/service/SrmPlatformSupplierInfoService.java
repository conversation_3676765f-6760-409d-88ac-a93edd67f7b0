package com.ylz.saas.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.entity.SrmPlatformSupplierInfo;
import com.ylz.saas.entity.SrmTenantSupplierInfo;
import com.ylz.saas.vo.SrmPlatformSupplierInfoVo;
import com.ylz.saas.vo.SupplierBasicInfoVo;
import com.ylz.saas.vo.SupplierBasicInfoDetailVo;
import com.ylz.saas.vo.SupplierCompleteInfoVo;
import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.validation.Valid;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【srm_platform_supplier_info(供应商基础信息表)】的数据库操作Service
* @createDate 2025-06-10 18:08:09
*/
public interface SrmPlatformSupplierInfoService extends IService<SrmPlatformSupplierInfo> {

    /**
     * 保存供应商基本信息
     * 根据统一信用代码查询供应商，存在则关联，不存在则新建
     *
     * @param supplierBasicInfoVo 供应商基本信息VO
     * @return 是否保存成功
     */
    SrmTenantSupplierInfo saveSupplierBasicInfo(SupplierBasicInfoVo supplierBasicInfoVo);

    /**
     * 分页查询平台供应商信息
     * 只返回审核通过的供应商，并标识当前用户部门是否与该供应商已建立关系
     *
     * @param info 查询条件
     * @param pageParams 分页参数
     * @return 分页平台供应商信息列表
     */
    IPage<SrmPlatformSupplierInfoVo> getPlatformSupplierPage(SrmPlatformSupplierInfo info, Page<SrmPlatformSupplierInfo> pageParams);

    /**
     * 检查平台供应商与当前用户部门的关系
     *
     * @param platformSupplierId 平台供应商ID
     * @param deptId 部门ID
     * @return 是否已建立关系
     */
    boolean checkSupplierRelation(Long platformSupplierId, Long deptId);

    /**
     * 根据租户供应商ID查询基础信息详情
     * 用于编辑和回显saveBasicInfo接口保存的信息
     *
     * @param tenantSupplierCode 租户供应商ID
     * @return 供应商基础信息详情
     */
    SupplierBasicInfoDetailVo getSupplierBasicInfoDetail(String tenantSupplierCode);
    /**
     *根据部门关联id查询供应商
     * @param supplierDeptId
     * @return 供应商基础信息详情
     */
    SupplierBasicInfoDetailVo getSupplierBySupplierDeptId(Long supplierDeptId);

    /**
     * 保存供应商完整信息（包含所有子表信息）
     * 支持新增、更新、删除操作的一体化处理
     *
     * @param supplierCompleteInfoVo 供应商完整信息VO
     * @return 是否保存成功
     */
    SrmTenantSupplierInfo saveSupplierCompleteInfo(SupplierCompleteInfoVo supplierCompleteInfoVo);


    /**
     * 注册供应商提交
     * 供应商门户注册的供应商提交审核
     * @param supplierCompleteInfoVo 供应商完整信息
     * @return 是否提交成功
     */
    boolean submitRegisterSupplier(@Valid SupplierCompleteInfoVo supplierCompleteInfoVo);

    /**
     * 供应商管理提交
     * SRM系统内部创建的供应商提交审核
     * @param supplierCompleteInfoVo 供应商完整信息
     * @return 是否提交成功
     */
    boolean submitSupplierInfo(@Valid SupplierCompleteInfoVo supplierCompleteInfoVo);


    /**
     * 邀请供应商并修改信息
     * 建立部门关联关系的同时允许修改供应商信息，并提交审核
     * @param supplierCompleteInfoVo 供应商完整信息（包含部门关联信息）
     * @return 是否邀请成功
     */
    boolean inviteAndUpdateSupplier(@Valid SupplierCompleteInfoVo supplierCompleteInfoVo);


    /**
     * 查询当前联系人的供应商
     * @return
     */
    SupplierBasicInfoDetailVo getOwnSupplier();


}
