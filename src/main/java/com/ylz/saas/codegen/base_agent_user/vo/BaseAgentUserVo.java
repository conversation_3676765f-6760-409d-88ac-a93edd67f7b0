package com.ylz.saas.codegen.base_agent_user.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 代理机构用户表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:11
 */
@Data
public class BaseAgentUserVo implements Serializable {

    /**
  	 * 主键ID
   	 */
    private Long id;

    /**
  	 * 组织ID
   	 */
    private Long deptId;

    /**
  	 * 代理结构id
   	 */
    private Long agentOrgId;

    /**
  	 * 用户ID
   	 */
    private Long userId;

    /**
  	 * 用户名称
   	 */
    private String userName;

    /**
  	 * 登录账号
   	 */
    private String loginAccount;

    /**
  	 * 联系电话
   	 */
    private String contactPhone;

    // ========== 创建账号相关字段 ==========

    /**
     * 是否创建账号（true-创建，false-不创建）
     */
    private Boolean generateAccount;

    /**
     * 用户密码（创建账号时使用，如果不提供则使用默认密码123456）
     */
    private String password;

    /**
     * 用户昵称（创建账号时使用，如果不提供则使用用户名称）
     */
    private String nickname;

    /**
     * 联系邮箱（创建账号时使用）
     */
    private String contactEmail;

    /**
     * 锁定标记（创建账号时使用，0-正常，9-锁定，默认为0）
     */
    private String lockFlag;

    /**
  	 * 创建人名称
   	 */
    private String createByName;

    /**
  	 * 修改人名称
   	 */
    private String updateByName;

    /**
  	 * 创建人
   	 */
    private String createBy;

    /**
  	 * 创建时间
   	 */
    private LocalDateTime createTime;

    /**
  	 * 修改人
   	 */
    private String updateBy;

    /**
  	 * 修改时间
   	 */
    private LocalDateTime updateTime;

    /**
  	 * 删除标识（0-正常、1-删除）
   	 */
    private int delFlag;

    /**
  	 * 租户ID
   	 */
    private Long tenantId;

    /**
  	 * 创建人ID
   	 */
    private Long createById;

    /**
  	 * 更新人ID
   	 */
    private Long updateById;
}