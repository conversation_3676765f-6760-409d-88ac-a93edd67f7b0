package com.ylz.saas.service;

import com.ylz.saas.entity.BaseSceneFields;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【base_scene_fields(场景字段表)】的数据库操作Service
* @createDate 2025-06-11 13:07:56
*/
public interface BaseSceneFieldsService extends IService<BaseSceneFields> {

    List<BaseSceneFields> getSceneFieldsBySceneId(Long sceneId);

    boolean saveField(BaseSceneFields field);

}
