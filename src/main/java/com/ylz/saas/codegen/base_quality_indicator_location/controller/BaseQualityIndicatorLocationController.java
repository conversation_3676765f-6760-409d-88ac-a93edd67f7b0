package com.ylz.saas.codegen.base_quality_indicator_location.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylz.saas.codegen.base_quality_indicator_location.entity.BaseQualityIndicatorLocationEntity;
import com.ylz.saas.codegen.base_quality_indicator_location.excel.BaseQualityIndicatorLocationResp;
import com.ylz.saas.codegen.base_quality_indicator_location.param.BaseQualityIndicatorLocationParam;
import com.ylz.saas.codegen.base_quality_indicator_location.service.BaseQualityIndicatorLocationService;
import com.ylz.saas.codegen.base_quality_indicator_location.vo.BaseQualityIndicatorLocationVo;
import com.ylz.saas.common.core.util.R;
import com.ylz.saas.common.excel.annotation.ResponseExcel;
import com.ylz.saas.common.log.annotation.SysLog;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * 质量指标与场地关联表
 *
 * <AUTHOR>
 * @date 2025-06-05 19:18:27
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/baseQualityIndicatorLocation" )
@Tag(description = "baseQualityIndicatorLocation" , name = "质量指标与场地关联表管理" )
@SecurityRequirement(name = HttpHeaders.AUTHORIZATION)
public class BaseQualityIndicatorLocationController {

    private final BaseQualityIndicatorLocationService baseQualityIndicatorLocationService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param baseQualityIndicatorLocation 质量指标与场地关联表
     * @return
     */
    @Operation(summary = "分页查询" , description = "分页查询" )
    @PostMapping("/page" )
    public R getBaseQualityIndicatorLocationPage(@RequestBody BaseQualityIndicatorLocationParam param, Page page) {
        QueryWrapper<BaseQualityIndicatorLocationEntity> queryWrapper = param.toWrapper(); 
        return R.ok(baseQualityIndicatorLocationService.page(page, queryWrapper));
    }


    /**
     * 通过id查询质量指标与场地关联表
     * @param id id
     * @return R
     */
    @Operation(summary = "通过id查询" , description = "通过id查询" )
    @GetMapping("/{id}" )
    public R getById(@PathVariable("id" ) Long id) {
        return R.ok(baseQualityIndicatorLocationService.getById(id));
    }

    /**
     * 新增质量指标与场地关联表
     * @param baseQualityIndicatorLocationVo 质量指标与场地关联表
     * @return R
     */
    @Operation(summary = "新增质量指标与场地关联表" , description = "新增质量指标与场地关联表" )
    @SysLog("新增质量指标与场地关联表" )
    @PostMapping("/save")
    public R save(@Valid @RequestBody BaseQualityIndicatorLocationVo baseQualityIndicatorLocationVo) {
        return R.ok(baseQualityIndicatorLocationService.saveBaseQualityIndicatorLocation(baseQualityIndicatorLocationVo));
    }

    /**
     * 修改质量指标与场地关联表
     * @param baseQualityIndicatorLocationVo 质量指标与场地关联表
     * @return R
     */
    @Operation(summary = "修改质量指标与场地关联表" , description = "修改质量指标与场地关联表" )
    @SysLog("修改质量指标与场地关联表" )
    @PostMapping("/update")
    public R updateById(@Valid @RequestBody BaseQualityIndicatorLocationVo baseQualityIndicatorLocationVo) {
        return R.ok(baseQualityIndicatorLocationService.updateBaseQualityIndicatorLocation(baseQualityIndicatorLocationVo));
    }

    /**
     * 通过id删除质量指标与场地关联表
     * @param ids id列表
     * @return R
     */
    @Operation(summary = "通过id删除质量指标与场地关联表" , description = "通过id删除质量指标与场地关联表" )
    @SysLog("通过id删除质量指标与场地关联表" )
    @GetMapping("remove")
    public R removeById(@RequestParam List<Long> ids) {
		boolean remove = baseQualityIndicatorLocationService.removeByIds(ids);
		return R.ok(remove);
    }


    /**
     * 导出excel 表格
     * @param baseQualityIndicatorLocation 查询条件
     * @param ids 导出指定ID
     * @return excel 文件流
     */
    @ResponseExcel
    @PostMapping("/export")
	@Operation(summary = "导出excel" , description = "导出excel" )
    public List<BaseQualityIndicatorLocationResp> export(@RequestBody BaseQualityIndicatorLocationParam param) {
		QueryWrapper<BaseQualityIndicatorLocationEntity> queryWrapper = param.toWrapper(); 
		List<BaseQualityIndicatorLocationEntity> entityList = baseQualityIndicatorLocationService.list(queryWrapper);
        return BeanUtil.copyToList(entityList, BaseQualityIndicatorLocationResp.class);
    }
	
	/**
     * 导入excel 表格
     * @param file
     * @param response
     * @return
     */
    @PostMapping("/import")
    @Operation(summary = "导入excel" , description = "导入excel" )
    public R uploadData(@RequestParam("file") MultipartFile file,HttpServletResponse response) {
        baseQualityIndicatorLocationService.uploadData(file,response);
        return R.ok();
    }

    /**
     * excel模板下载
     * @param response
     */
    @GetMapping("/template")
    @Operation(summary = "excel模板下载" , description = "excel模板下载" )
    public void template(HttpServletResponse response) {
        baseQualityIndicatorLocationService.template(response);
    }
}