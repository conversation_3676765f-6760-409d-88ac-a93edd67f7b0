package com.ylz.saas.codegen.base_payment_method_period.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 支付方式账期时间表
 *
 * <AUTHOR>
 * @date 2025-06-05 15:43:34
 */
@Data
public class BasePaymentMethodPeriodReq implements Serializable {




    /**
  	 * 账期天数
   	 */
	@ExcelProperty(value = "账期天数")
    private BigDecimal periodDays;


    /**
  	 * 账期名称
   	 */
	@ExcelProperty(value = "账期名称")
    private String periodName;


    /**
  	 * 账期描述
   	 */
	@ExcelProperty(value = "账期描述")
    private String description;


    /**
  	 * 状态（DISABLED-禁用、ENABLED-启用）
   	 */
	@ExcelProperty(value = "状态（DISABLED-禁用、ENABLED-启用）")
    private String status;












}