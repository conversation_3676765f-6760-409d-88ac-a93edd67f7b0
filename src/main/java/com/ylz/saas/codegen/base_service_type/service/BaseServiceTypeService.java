package com.ylz.saas.codegen.base_service_type.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylz.saas.codegen.base_service_type.entity.BaseServiceTypeEntity;
import com.ylz.saas.codegen.base_service_type.vo.BaseServiceTypeVo;
import com.ylz.saas.codegen.base_service_type_field.service.BaseServiceTypeFieldService;
import com.ylz.saas.common.core.exception.ApiResultCode;
import lombok.AllArgsConstructor;
import lombok.Generated;
import lombok.Getter;

import java.util.List;

public interface BaseServiceTypeService extends IService<BaseServiceTypeEntity> {
    /**
     * 新增服务类型表
     *
     * @param baseServiceTypeVo 服务类型表
     */
    boolean saveBaseServiceType(BaseServiceTypeVo baseServiceTypeVo);

    boolean upsertTypeFields(BaseServiceTypeVo baseServiceTypeVo);

    /**
     * 修改服务类型表
     *
     * @param baseServiceTypeVo 服务类型表
     */
    boolean updateBaseServiceType(BaseServiceTypeVo baseServiceTypeVo);

    /**
     * 查询服务类型表详情
     *
     * @param id
     */
    BaseServiceTypeEntity getByIdBaseServiceType(Long id);

    /**
     * 根据服务类型id和寻源方式查询数据
     *
     * @param id
     * @param buyWay
     * @return
     */
    BaseServiceTypeEntity getByIdAndBuyWay(Long id, BaseServiceTypeFieldService.BuyWayEnum buyWay);

    /**
     * 通过id删除服务类型表
     */
    boolean removeByIdsBaseServiceType(List<Long> ids);

    /**
     * 获取可用的服务类型名列表数据
     *
     * @return
     */
    List<BaseServiceTypeEntity> getEnabled();

    public enum BaseServiceTypeResultCode implements ApiResultCode {
        TYPE_NAME_REPEAT_EXCEPTION("X01001", "类型名字重复"), INVALID_PARAMS("X02", "参数错误"), NETWORK_FAILURE("X03", "网络故障"), INVALID_STATE("X04", "不正确状态"), EVENT_HANDLED_TIMEOUT("X05", "消息处理耗时太长");

        private final String bizCode;
        private final String message;

        @Generated
        public String getBizCode() {
            return this.bizCode;
        }

        @Generated
        public String getMessage() {
            return this.message;
        }

        @Generated
        private BaseServiceTypeResultCode(final String bizCode, final String message) {
            this.bizCode = bizCode;
            this.message = message;
        }
    }

    // 记录是否可用 枚举
    @Getter
    @AllArgsConstructor
    enum StatusEnum {

        DISABLED("禁用"),
        ENABLED("启用");

        private String desc;
    }
}